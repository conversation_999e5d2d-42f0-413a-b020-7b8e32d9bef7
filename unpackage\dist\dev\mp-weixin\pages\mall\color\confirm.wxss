/* 水平间距 */
/* 水平间距 */
/* 全局样式修复 - 确保页面占满屏幕宽度 */
page.data-v-a051f7e1 {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
}

/* 通用容器样式 */
.container.data-v-a051f7e1, .page-container.data-v-a051f7e1 {
  width: 100%;
  min-height: 100vh;
  box-sizing: border-box;
}

/* 修复小程序页面宽度问题 */
uni-page-body.data-v-a051f7e1 {
  width: 100% !important;
}

/* 确保所有view元素正确继承宽度 */
view.data-v-a051f7e1 {
  box-sizing: border-box;
}

/* 添加页面级别样式，修复右侧空白问题 */
page.data-v-a051f7e1 {
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  padding: 0;
  margin: 0;
  box-sizing: border-box;
  background-color: #f5f5f5;
  overscroll-behavior: none;
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
}

/* 补充uni-app全局样式，确保内容不超出屏幕 */
.data-v-a051f7e1:root,
uni-page.data-v-a051f7e1,
uni-page-head.data-v-a051f7e1,
uni-page-wrapper.data-v-a051f7e1,
uni-page-body.data-v-a051f7e1,
uni-app.data-v-a051f7e1,
uni-window.data-v-a051f7e1 {
  width: 100vw;
  max-width: 100vw;
  min-width: 100vw;
  box-sizing: border-box;
  overflow-x: hidden;
  margin: 0;
  padding: 0;
}

/* 固定宽度防止溢出 */
view.data-v-a051f7e1 {
  max-width: 100vw;
  box-sizing: border-box;
}
.app-container.data-v-a051f7e1 {
  width: 100vw;
  max-width: 100vw;
  margin: 0;
  padding: 0;
  overflow: hidden;
  position: relative;
  height: 100vh;
  box-sizing: border-box;
  left: 0;
  right: 0;
}
.fixed-container.data-v-a051f7e1 {
  width: 100vw;
  max-width: 100vw;
  left: 0;
  position: relative;
  /* 改为相对定位 */
  right: 0;
  position: absolute;
}
.fixed-content.data-v-a051f7e1 {
  width: 100%;
  max-width: 100vw;
  box-sizing: border-box;
  left: 0;
  right: 0;
  padding-left: 0;
  padding-right: 0;
  margin-left: 0;
  margin-right: 0;
}
.content.data-v-a051f7e1 {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  width: 100vw;
  background-color: #f5f5f5;
  padding-bottom: 30rpx;
  box-sizing: border-box;
  overflow-x: hidden;
  overflow-y: auto;
  /* 添加垂直滚动 */
  position: relative;
  left: 0;
  right: 0;
}

/* 确保设备信息和模式标签固定在顶部 */
.device-info.data-v-a051f7e1,
.mode-tabs.data-v-a051f7e1 {
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  z-index: 100;
  background-color: #fff;
}
.master-scroll.data-v-a051f7e1 {
  max-height: 480rpx;
  margin-top: 20rpx;
}
.header.data-v-a051f7e1 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #4285f4;
  color: #fff;
  padding: 20rpx 30rpx;
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  z-index: 100;
  width: 100%;
  box-sizing: border-box;
}
.back-btn.data-v-a051f7e1 {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.back-icon.data-v-a051f7e1 {
  font-size: 40rpx;
}
.title.data-v-a051f7e1 {
  font-size: 36rpx;
  font-weight: 500;
}
.header-icons.data-v-a051f7e1 {
  display: flex;
  align-items: center;
}
.header-icon.data-v-a051f7e1 {
  margin-left: 20rpx;
  font-size: 30rpx;
}
.device-info.data-v-a051f7e1 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 30rpx;
  background-color: #fff;
  border-bottom: 1rpx solid #f0f0f0;
  width: 100%;
  box-sizing: border-box;
}
.brand-text.data-v-a051f7e1 {
  font-size: 28rpx;
  color: #333;
}
.camera-preview.data-v-a051f7e1 {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin: 40rpx auto;
  width: 270rpx;
  height: 270rpx;
  border-radius: 50%;
  background-color: #fff;
  flex-shrink: 0;
  /* 防止被压缩 */
  box-shadow: 0 0 30rpx rgba(0, 0, 0, 0.1);
}
.camera-icon.data-v-a051f7e1 {
  margin-bottom: 10rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.emoji-icon.data-v-a051f7e1 {
  font-size: 80rpx;
  color: #4285f4;
}
.camera-text.data-v-a051f7e1 {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 5rpx;
}
.camera-subtext.data-v-a051f7e1 {
  font-size: 24rpx;
  color: #666;
}
.mode-tabs.data-v-a051f7e1 {
  display: flex;
  width: 100%;
  background-color: #fff;
  padding: 10rpx 30rpx;
  margin-bottom: 20rpx;
  box-sizing: border-box;
  border-radius: 10rpx;
}
.mode-tab.data-v-a051f7e1 {
  flex: 1;
  text-align: center;
  padding: 20rpx 0;
  font-size: 30rpx;
  color: #666;
  border-radius: 10rpx;
  transition: all 0.3s ease;
  position: relative;
  margin: 0 10rpx;
}
.mode-tab.active.data-v-a051f7e1 {
  background-color: #4285f4;
  color: #fff;
  font-weight: 500;
}
.mode-tab.data-v-a051f7e1:not(.active) {
  background-color: #f5f5f5;
}
.color-settings.data-v-a051f7e1 {
  flex: 1;
  /* 占据剩余空间 */
  overflow-y: auto;
  /* 允许内部滚动 */
  min-height: 300rpx;
  /* 确保有足够高度 */
  background-color: #fff;
  padding: 30rpx;
  margin-bottom: 20rpx;
  width: 100%;
  box-sizing: border-box;
}
.setting-item.data-v-a051f7e1 {
  margin-bottom: 30rpx;
  display: flex;
  flex-direction: column;
  width: 100%;
}
.setting-label.data-v-a051f7e1 {
  font-size: 30rpx;
  color: #333;
  margin-bottom: 20rpx;
  display: flex;
  align-items: center;
}
.color-preview.data-v-a051f7e1 {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  margin-left: 10rpx;
  border: 1px solid #ddd;
}
.color-circle.data-v-a051f7e1 {
  margin-left: 10rpx;
  border: 1px solid #ddd;
}
.slider-with-value.data-v-a051f7e1 {
  display: flex;
  align-items: center;
  width: 100%;
}
.slider-value.data-v-a051f7e1 {
  margin-left: 10rpx;
  font-size: 28rpx;
  color: #4285f4;
  font-weight: bold;
  min-width: 40rpx;
  text-align: center;
}
.online.data-v-a051f7e1 {
  color: blue;
  font-size: 28rpx;
  margin-left: 20rpx;
}
.offline.data-v-a051f7e1 {
  color: #625f5f;
  font-size: 28rpx;
  margin-left: 20rpx;
  margin-top: 10rpx;
}
.slider.data-v-a051f7e1 {
  width: 100%;
  margin: 10rpx 0;
}
.ratio-options.data-v-a051f7e1 {
  display: flex;
  justify-content: space-between;
  margin-top: 20rpx;
  width: 100%;
}
.ratio-option.data-v-a051f7e1 {
  flex: 1;
  text-align: center;
  padding: 15rpx 0;
  margin: 0 10rpx;
  background-color: #f5f5f5;
  border-radius: 10rpx;
  font-size: 28rpx;
  color: #333;
}
.ratio-option.active.data-v-a051f7e1 {
  background-color: #4285f4;
  color: #fff;
}
.bowl-container.data-v-a051f7e1 {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 60rpx 0;
  width: 100%;
  height: 100rpx;
  box-sizing: border-box;
}
.bowl-icon.data-v-a051f7e1 {
  margin-bottom: 10rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.bowl-text.data-v-a051f7e1 {
  font-size: 30rpx;
  color: #666;
}
.start-button.data-v-a051f7e1 {
  background-color: #4285f4;
  color: #fff;
  text-align: center;
  padding: 25rpx 0;
  border-radius: 40rpx;
  font-size: 32rpx;
  font-weight: 500;
  width: calc(100% - 60rpx);
  box-sizing: border-box;
}

/* 确保所有内容区块占满整个宽度 */
.device-info.data-v-a051f7e1,
.mode-tabs.data-v-a051f7e1,
.color-settings.data-v-a051f7e1,
.bowl-container.data-v-a051f7e1,
.start-button.data-v-a051f7e1 {
  width: 100%;
  box-sizing: border-box;
  max-width: 100vw;
  left: 0;
  right: 0;
}

/* 调整底部按钮容器 */
.start-button-container.data-v-a051f7e1 {
  position: -webkit-sticky;
  position: sticky;
  bottom: 0;
  background-color: #f5f5f5;
  padding: 20rpx 0;
  z-index: 100;
}

/* 改进圆形相机预览区域的居中显示 */
.camera-preview.data-v-a051f7e1 {
  margin: 40rpx auto;
  width: 320rpx;
  height: 320rpx;
}

/* 添加弹窗样式 */
.modal-mask.data-v-a051f7e1 {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  /* 垂直居中 */
  justify-content: center;
  /* 水平居中 */
  z-index: 1000;
  height: 100rpx;
}
.modal-container.data-v-a051f7e1 {
  width: 80%;
  background-color: #fff;
  border-radius: 16rpx;
  overflow: hidden;
}
.modal-header.data-v-a051f7e1 {
  padding: 30rpx;
  font-size: 32rpx;
  font-weight: bold;
  text-align: center;
  background-color: #f5f5f5;
  border-bottom: 1rpx solid #eee;
}
.modal-content.data-v-a051f7e1 {
  padding: 30rpx;
}
.modal-text.data-v-a051f7e1 {
  font-size: 28rpx;
  margin-bottom: 20rpx;
  text-align: center;
  display: block;
}
.modal-footer.data-v-a051f7e1 {
  display: flex;
  border-top: 1rpx solid #eee;
}
.modal-button.data-v-a051f7e1 {
  flex: 1;
  padding: 25rpx;
  text-align: center;
  font-size: 30rpx;
}
.confirm-button.data-v-a051f7e1 {
  color: #4285f4;
  font-weight: bold;
}

/* 比例选择器样式 */
.slider-container.data-v-a051f7e1 {
  margin: 30rpx 0;
  padding: 0 20rpx;
}
.ratio-slider.data-v-a051f7e1 {
  width: 100%;
}
.ratio-labels.data-v-a051f7e1 {
  display: flex;
  justify-content: space-between;
  margin-top: 10rpx;
}
.ratio-label.data-v-a051f7e1 {
  font-size: 24rpx;
  color: #666;
}
.ratio-desc.data-v-a051f7e1 {
  margin-top: 30rpx;
  padding: 20rpx;
  background-color: #f9f9f9;
  border-radius: 8rpx;
}
.desc-title.data-v-a051f7e1 {
  font-size: 26rpx;
  color: #333;
  font-weight: bold;
  display: block;
  margin-bottom: 10rpx;
}
.desc-text.data-v-a051f7e1 {
  font-size: 24rpx;
  color: #666;
  display: block;
  margin: 5rpx 0;
}
.mode.data-v-a051f7e1 {
  margin-top: 20rpx;
}
.master.data-v-a051f7e1 {
  padding: 20rpx;
}
.color-grid.data-v-a051f7e1 {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
}
.color-item.data-v-a051f7e1 {
  width: 25%;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 20rpx;
}
.color-circle.data-v-a051f7e1 {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  border: 2rpx solid #f0f0f0;
}
.percentage.data-v-a051f7e1 {
  color: #333;
  font-size: 24rpx;
}
.color-name.data-v-a051f7e1 {
  margin-top: 10rpx;
  font-size: 24rpx;
  color: #333;
}
.amount-input.data-v-a051f7e1 {
  width: 100%;
  padding: 20rpx;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  margin-top: 20rpx;
  box-sizing: border-box;
}
.selected-color.data-v-a051f7e1 {
  color: #1565C0 !important;
  /* 明显的蓝色 */
  font-weight: bold;
}
.mode-buttons.data-v-a051f7e1 {
  display: flex;
  justify-content: space-between;
  width: 38%;
}
.mode-button.data-v-a051f7e1 {
  padding: 8rpx 12rpx;
  border-radius: 8rpx;
  font-size: 24rpx;
  text-align: center;
  border: 1rpx solid #ddd;
}
.mode-button.single.data-v-a051f7e1 {
  background-color: #f5f5f5;
  color: #333;
}
.mode-button.single.active.data-v-a051f7e1 {
  background-color: #4285f4;
  color: white;
  border-color: #4285f4;
}
.mode-button.combo.data-v-a051f7e1 {
  background-color: #f5f5f5;
  color: #333;
}
.mode-button.combo.active.data-v-a051f7e1 {
  background-color: #34a853;
  color: white;
  border-color: #34a853;
}

/* 新增的样式代码（添加到原有样式表的最后） */
.setting-item .setting-label.data-v-a051f7e1 {
  width: 65% !important;
  /* 覆盖内联样式 */
  padding-right: 20rpx;
  box-sizing: border-box;
}
.setting-item .mode-buttons.data-v-a051f7e1 {
  width: 30% !important;
  /* 覆盖内联样式 */
  display: flex !important;
  border: 1rpx solid #4285f4 !important;
  border-radius: 30rpx !important;
  overflow: hidden !important;
  height: 60rpx;
}
.setting-item .mode-buttons .mode-button.data-v-a051f7e1 {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 26rpx;
  transition: all 0.3s ease;
}
.setting-item .mode-buttons .mode-button.single.data-v-a051f7e1 {
  background-color: var(--59ac9b4e);
  color: var(--df8dd686);
}
.setting-item .mode-buttons .mode-button.combo.data-v-a051f7e1 {
  background-color: var(--77064b14);
  color: var(--3415ad78);
}

/* 优化动画 */
@keyframes slide-up-a051f7e1 {
from {
    transform: translateY(100%);
    opacity: 0;
}
to {
    transform: translateY(0);
    opacity: 1;
}
}