<template>
    <view class="region-picker">
        <view class="tab-bar">
            <view class="tab-item" :class="{active: currentTab === 0}" @click="switchTab(0)">
                <text>{{province || '请选择'}}</text>
            </view>
            <view class="tab-item" :class="{active: currentTab === 1}" @click="switchTab(1)" v-if="province">
                <text>{{city || '请选择'}}</text>
            </view>
            <view class="tab-item" :class="{active: currentTab === 2}" @click="switchTab(2)" v-if="city">
                <text>{{district || '请选择'}}</text>
            </view>
        </view>
        
        <scroll-view scroll-y class="region-list">
            <block v-if="currentTab === 0">
                <view class="region-item" v-for="(item, index) in provinceList" :key="index" @click="selectProvince(item)">
                    <text>{{item}}</text>
                    <text class="check-icon" v-if="province === item">✓</text>
                </view>
            </block>
            <block v-else-if="currentTab === 1">
                <view class="region-item" v-for="(item, index) in cityList" :key="index" @click="selectCity(item)">
                    <text>{{item}}</text>
                    <text class="check-icon" v-if="city === item">✓</text>
                </view>
            </block>
            <block v-else-if="currentTab === 2">
                <view class="region-item" v-for="(item, index) in districtList" :key="index" @click="selectDistrict(item)">
                    <text>{{item}}</text>
                    <text class="check-icon" v-if="district === item">✓</text>
                </view>
            </block>
        </scroll-view>
    </view>
</template>

<script>
export default {
    data() {
        return {
            currentTab: 0,
            province: '',
            city: '',
            district: '',
            // 省份列表
            provinceList: ['北京市', '天津市', '河北省', '山西省', '内蒙古自治区', '辽宁省', '吉林省', '黑龙江省', '上海市', '江苏省', '浙江省', '安徽省', '福建省', '江西省', '山东省', '河南省', '湖北省', '湖南省', '广东省', '广西壮族自治区', '海南省', '重庆市', '四川省', '贵州省', '云南省', '西藏自治区', '陕西省', '甘肃省', '青海省', '宁夏回族自治区', '新疆维吾尔自治区', '台湾省', '香港特别行政区', '澳门特别行政区'],
            // 城市列表（示例）
            citiesData: {
                '河北省': ['石家庄市', '唐山市', '秦皇岛市', '邯郸市', '邢台市', '保定市', '张家口市', '承德市', '沧州市', '廊坊市', '衡水市'],
                '山西省': ['太原市', '大同市', '阳泉市', '长治市', '晋城市', '朔州市', '晋中市', '运城市', '忻州市', '临汾市', '吕梁市'],
                '辽宁省': ['沈阳市', '大连市', '鞍山市', '抚顺市', '本溪市', '丹东市', '锦州市', '营口市', '阜新市', '辽阳市', '盘锦市', '铁岭市', '朝阳市', '葫芦岛市'],
                '江苏省': ['南京市', '无锡市', '徐州市', '常州市', '苏州市', '南通市', '连云港市', '淮安市', '盐城市', '扬州市', '镇江市', '泰州市', '宿迁市'],
                '浙江省': ['杭州市', '宁波市', '温州市', '嘉兴市', '湖州市', '绍兴市', '金华市', '衢州市', '舟山市', '台州市', '丽水市'],
                '广东省': ['广州市', '韶关市', '深圳市', '珠海市', '汕头市', '佛山市', '江门市', '湛江市', '茂名市', '肇庆市', '惠州市', '梅州市', '汕尾市', '河源市', '阳江市', '清远市', '东莞市', '中山市', '潮州市', '揭阳市', '云浮市'],
                '四川省': ['成都市', '自贡市', '攀枝花市', '泸州市', '德阳市', '绵阳市', '广元市', '遂宁市', '内江市', '乐山市', '南充市', '眉山市', '宜宾市', '广安市', '达州市', '雅安市', '巴中市', '资阳市', '阿坝藏族羌族自治州', '甘孜藏族自治州', '凉山彝族自治州']
                // 直辖市不需要在这里定义，直接在districtsData中定义区县
            },
            // 区县列表（示例）
            districtsData: {
                // 直辖市
                '北京市': ['东城区', '西城区', '朝阳区', '丰台区', '石景山区', '海淀区', '门头沟区', '房山区', '通州区', '顺义区', '昌平区', '大兴区', '怀柔区', '平谷区', '密云区', '延庆区'],
                '天津市': ['和平区', '河东区', '河西区', '南开区', '河北区', '红桥区', '东丽区', '西青区', '津南区', '北辰区', '武清区', '宝坻区', '滨海新区', '宁河区', '静海区', '蓟州区'],
                '上海市': ['黄浦区', '徐汇区', '长宁区', '静安区', '普陀区', '虹口区', '杨浦区', '闵行区', '宝山区', '嘉定区', '浦东新区', '金山区', '松江区', '青浦区', '奉贤区', '崇明区'],
                '重庆市': ['万州区', '涪陵区', '渝中区', '大渡口区', '江北区', '沙坪坝区', '九龙坡区', '南岸区', '北碚区', '綦江区', '大足区', '渝北区', '巴南区', '黔江区', '长寿区', '江津区', '合川区', '永川区', '南川区', '璧山区', '铜梁区', '潼南区', '荣昌区', '开州区', '梁平区', '武隆区'],
                // 河北省主要城市
                '石家庄市': ['长安区', '桥西区', '新华区', '井陉矿区', '裕华区', '藁城区', '鹿泉区', '栾城区', '井陉县', '正定县', '行唐县', '灵寿县', '高邑县', '深泽县', '赞皇县', '无极县', '平山县', '元氏县', '赵县'],
                '保定市': ['竞秀区', '莲池区', '满城区', '清苑区', '徐水区', '涞水县', '阜平县', '定兴县', '唐县', '高阳县', '容城县', '涞源县', '望都县', '安新县', '易县', '曲阳县', '蠡县', '顺平县', '博野县', '雄县'],
                // 江苏省主要城市
                '南京市': ['玄武区', '秦淮区', '建邺区', '鼓楼区', '浦口区', '栖霞区', '雨花台区', '江宁区', '六合区', '溧水区', '高淳区'],
                '苏州市': ['虎丘区', '吴中区', '相城区', '姑苏区', '吴江区', '常熟市', '张家港市', '昆山市', '太仓市'],
                // 广东省主要城市
                '广州市': ['荔湾区', '越秀区', '海珠区', '天河区', '白云区', '黄埔区', '番禺区', '花都区', '南沙区', '从化区', '增城区'],
                '深圳市': ['罗湖区', '福田区', '南山区', '宝安区', '龙岗区', '盐田区', '龙华区', '坪山区', '光明区', '大鹏新区'],
                // 四川省主要城市
                '成都市': ['锦江区', '青羊区', '金牛区', '武侯区', '成华区', '龙泉驿区', '青白江区', '新都区', '温江区', '双流区', '郫都区', '新津区', '金堂县', '大邑县', '蒲江县', '都江堰市', '彭州市', '邛崃市', '崇州市', '简阳市']
            }
        }
    },
    computed: {
        cityList() {
            if (!this.province) return [];
            // 如果是直辖市，返回空数组，直接跳到区县选择
            if (this.isDirectMunicipality(this.province)) {
                return [];
            }
            return this.citiesData[this.province] || [];
        },
        districtList() {
            // 如果是直辖市，直接使用省份名称获取区县
            if (this.isDirectMunicipality(this.province)) {
                return this.districtsData[this.province] || [];
            }
            // 普通省份，使用城市名称获取区县
            if (!this.city) return [];
            return this.districtsData[this.city] || [];
        }
    },
    methods: {
        goBack() {
            uni.navigateBack();
        },
        // 判断是否为直辖市
        isDirectMunicipality(province) {
            return ['北京市', '天津市', '上海市', '重庆市'].includes(province);
        },
        switchTab(index) {
            if (index <= this.getMaxAllowedTab()) {
                this.currentTab = index;
            }
        },
        getMaxAllowedTab() {
            if (this.district) return 2;
            if (this.city || this.isDirectMunicipality(this.province)) return 2;
            if (this.province) return this.isDirectMunicipality(this.province) ? 2 : 1;
            return 0;
        },
        selectProvince(province) {
            this.province = province;
            this.city = '';
            this.district = '';

            // 如果是直辖市，直接跳转到区县选择
            if (this.isDirectMunicipality(province)) {
                this.currentTab = 2;
            } else {
                this.currentTab = 1;
            }
        },
        selectCity(city) {
            this.city = city;
            this.district = '';
            this.currentTab = 2;
        },
        selectDistrict(district) {
            this.district = district;

            // 完成选择，返回上一页并传值
            const pages = getCurrentPages();
            const prevPage = pages[pages.length - 2];

            // 将选中的地址赋值给上一页
            if (prevPage && prevPage.$vm) {
                prevPage.$vm.address.province = this.province;
                // 如果是直辖市，city设置为空字符串或者与province相同
                if (this.isDirectMunicipality(this.province)) {
                    prevPage.$vm.address.city = this.province; // 直辖市的city就是省份名
                } else {
                    prevPage.$vm.address.city = this.city;
                }
                prevPage.$vm.address.district = this.district;
            }

            uni.navigateBack();
        }
    }
}
</script>

<style lang="scss">
.region-picker {
    min-height: 100vh;
    background-color: #f5f5f5;
    display: flex;
    flex-direction: column;
}

.tab-bar {
    display: flex;
    background-color: #ffffff;
    border-bottom: 1rpx solid #f0f0f0;
}

.tab-item {
    flex: 1;
    height: 90rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 28rpx;
    color: #333333;
    position: relative;
    
    &.active {
        color: #4285f4;
        
        &:after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 40rpx;
            height: 4rpx;
            background-color: #4285f4;
        }
    }
}

.region-list {
    flex: 1;
    background-color: #ffffff;
    height: calc(100vh - 90rpx);
}

.region-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 30rpx 40rpx;
    border-bottom: 1rpx solid #f0f0f0;
    font-size: 28rpx;
}

.check-icon {
    color: #4285f4;
    font-weight: bold;
}
</style> 