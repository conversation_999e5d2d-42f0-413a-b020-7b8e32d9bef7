/* 水平间距 */
/* 水平间距 */
/* 全局样式修复 - 确保页面占满屏幕宽度 */
page {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
}

/* 通用容器样式 */
.container, .page-container {
  width: 100%;
  min-height: 100vh;
  box-sizing: border-box;
}

/* 修复小程序页面宽度问题 */
uni-page-body {
  width: 100% !important;
}

/* 确保所有view元素正确继承宽度 */
view {
  box-sizing: border-box;
}
.location-picker {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f5f5;
}
.header {
  background: #4A90E2;
  color: white;
  padding: 20rpx 30rpx;
  padding-top: 150rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
}
.header-title {
  font-size: 36rpx;
  font-weight: 500;
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  max-width: 400rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.header-btn {
  background: none;
  border: none;
  color: white;
  font-size: 32rpx;
  cursor: pointer;
  padding: 10rpx 20rpx;
}
.search-container {
  background: white;
  padding: 30rpx;
  border-bottom: 1rpx solid #eee;
}
.search-box {
  display: flex;
  align-items: center;
  background: #f5f5f5;
  border-radius: 16rpx;
  padding: 20rpx 30rpx;
}
.search-icon {
  font-size: 32rpx;
  margin-right: 20rpx;
  opacity: 0.6;
}
.search-input {
  flex: 1;
  border: none;
  background: none;
  font-size: 32rpx;
  outline: none;
}
.map-container {
  flex: 1;
  position: relative;
  background: #f5f5f5;
}
.map {
  width: 100%;
  height: 100%;
}
.map-placeholder {
  text-align: center;
  padding: 40rpx;
}
.map-logo {
  font-size: 80rpx;
  margin-bottom: 20rpx;
}
.map-text {
  font-size: 36rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 10rpx;
}
.map-desc {
  font-size: 28rpx;
  color: #666;
  opacity: 0.8;
}
.location-list {
  background: white;
  max-height: 600rpx;
}
.location-item {
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  transition: background-color 0.2s;
}
.location-item:active {
  background: #f8f8f8;
}
.location-item.selected {
  background: #f0f8ff;
}
.location-item:last-child {
  border-bottom: none;
}
.location-name {
  font-size: 32rpx;
  color: #333;
  margin-bottom: 10rpx;
}
.location-address {
  font-size: 28rpx;
  color: #666;
}
.current-location {
  background: white;
  padding: 30rpx;
  border-bottom: 1rpx solid #eee;
  display: flex;
  align-items: center;
}
.location-icon {
  font-size: 48rpx;
  margin-right: 30rpx;
  color: #4A90E2;
}
.current-location-text {
  flex: 1;
}
.current-location-name {
  font-size: 32rpx;
  color: #333;
  margin-bottom: 6rpx;
}
.current-location-detail {
  font-size: 28rpx;
  color: #666;
}
.relocate-btn {
  background: none;
  border: none;
  color: #4A90E2;
  font-size: 28rpx;
  cursor: pointer;
  padding: 10rpx;
}