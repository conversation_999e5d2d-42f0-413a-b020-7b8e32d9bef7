/* 水平间距 */
/* 水平间距 */
/* 全局样式修复 - 确保页面占满屏幕宽度 */
page.data-v-4925d87e {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
}

/* 通用容器样式 */
.container.data-v-4925d87e, .page-container.data-v-4925d87e {
  width: 100%;
  min-height: 100vh;
  box-sizing: border-box;
}

/* 修复小程序页面宽度问题 */
uni-page-body.data-v-4925d87e {
  width: 100% !important;
}

/* 确保所有view元素正确继承宽度 */
view.data-v-4925d87e {
  box-sizing: border-box;
}
.location-picker.data-v-4925d87e {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
}
.header.data-v-4925d87e {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 30rpx;
  background-color: #fff;
  border-bottom: 1rpx solid #eee;
}
.header-title.data-v-4925d87e {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  flex: 1;
  text-align: center;
}
.header-btn.data-v-4925d87e {
  font-size: 28rpx;
  color: #4285f4;
  padding: 10rpx 20rpx;
}
.search-container.data-v-4925d87e {
  padding: 20rpx 30rpx;
  background-color: #fff;
  border-bottom: 1rpx solid #eee;
}
.search-box.data-v-4925d87e {
  display: flex;
  align-items: center;
  background-color: #f5f5f5;
  border-radius: 20rpx;
  padding: 20rpx;
}
.search-icon.data-v-4925d87e {
  margin-right: 20rpx;
  font-size: 28rpx;
}
.search-input.data-v-4925d87e {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}
.current-location.data-v-4925d87e {
  display: flex;
  align-items: center;
  padding: 30rpx;
  background-color: #fff;
  border-bottom: 1rpx solid #eee;
}
.location-icon.data-v-4925d87e {
  font-size: 32rpx;
  margin-right: 20rpx;
}
.current-location-text.data-v-4925d87e {
  flex: 1;
}
.current-location-name.data-v-4925d87e {
  font-size: 30rpx;
  color: #333;
  font-weight: 500;
}
.current-location-detail.data-v-4925d87e {
  font-size: 24rpx;
  color: #999;
  margin-top: 10rpx;
}
.relocate-btn.data-v-4925d87e {
  font-size: 24rpx;
  color: #4285f4;
  padding: 10rpx 20rpx;
  border: 1rpx solid #4285f4;
  border-radius: 20rpx;
}
.map-container.data-v-4925d87e {
  height: 400rpx;
  background-color: #fff;
  border-bottom: 1rpx solid #eee;
}
.map.data-v-4925d87e {
  width: 100%;
  height: 100%;
}
.location-list.data-v-4925d87e {
  flex: 1;
  background-color: #fff;
}
.location-item.data-v-4925d87e {
  padding: 30rpx;
  border-bottom: 1rpx solid #eee;
}
.location-name.data-v-4925d87e {
  font-size: 30rpx;
  color: #333;
  font-weight: 500;
}
.location-address.data-v-4925d87e {
  font-size: 24rpx;
  color: #999;
  margin-top: 10rpx;
}