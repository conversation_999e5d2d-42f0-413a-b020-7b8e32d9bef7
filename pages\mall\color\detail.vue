<template>
	<view class="app-container">
		<scroll-view scroll-y="true" class="scrollable-content">
			<!-- 颜色信息 -->
			<view class="color-info">
				<view class="category-line">
					<text>芭佰邑-{{categoryName}}</text>
					<!-- <text class="offline-tag" v-if="!$store.state.connectedDevice">设备离线</text>
					<text class="online-tag" v-else>设备在线</text> -->
				</view>

				<view class="color-preview">
					<!-- <view class="color-circle" :style="{backgroundColor: colorHex}"></view> -->
					<image class="color-circle" :src="iconPath" mode="aspectFill"></image>
				</view>

				<view class="color-name">{{colorName}}</view>
				<view class="color-category">智能染发-{{categoryName}}</view>
				<view class="dye-count">已染色{{dyeCount}}次</view>

				<view class="divider"></view>
				<view class="color-structure">颜色构造</view>
				<!-- 颜色滚轮 -->
				<view class="color-composition">
					<view class="color-bar-container">
						<view class="color-bar" v-for="(item, index) in normalizedColorData" :key="index">
							<view class="color-block" v-if="item.weight" :class="item.colorClass"
								:style="{ height: item.height + 'px' }">
							</view>
							<view class="color-label" v-if="item.weight">
								<text v-for="(text, textIndex) in item.labels" :key="textIndex">{{ text }}</text>
							</view>
						</view>
					</view>
				</view>
			</view>

			<!-- 染色操作方法 -->
			<!-- <view class="dye-instruction fixed-content">
				<view class="instruction-title">配方染膏操作方法:</view>
				
				<view class="step">
					<text class="step-num">1</text>
					<text class="step-desc">原生发:</text>
				</view>
				<view class="step-detail">发根：目标配方+20VOL1:1先涂抹发根2～3厘米厚度。</view>
				
				<view class="step">
					<text class="step-num">2</text>
					<text class="step-desc">发中/发根:</text>
				</view>
				<view class="step-detail">目标配方+30VOL/40VOL1:1发根处到发尾巴一次性涂抹。自然停放30-45分钟有白头发50分钟。</view>
				
				<view class="step">
					<text class="step-num">3</text>
					<text class="step-desc">发尾处可以晚20分钟涂抹也可以。</text>
				</view>
				
				<view class="step">
					<text class="step-num">4</text>
					<text class="step-desc">或者剩余染膏里面添+40VOL1:2稀释后直接涂抹发尾处无需等待。</text>
				</view>
				
				<view class="base-color-info">
					<text>多段发3.5.8度底色</text>
				</view>
				
				<view class="base-color-detail">1.灰白0度底色 位置：日晒配方+温水补充：1:1已染过</view>
			</view> -->

			<view class="dye-instruction">
				<view class="instruction-title">配方染膏操作方法:</view>

				<!-- <view class="step">
					<text class="step-num">1</text>
					<text class="step-desc">原生发:</text>
				</view> -->
				<!-- <view class="step-detail">发根：目标配方+20VOL1:1先涂抹发根2～3厘米厚度。</view> -->
				<view class="step-detail">{{description}}</view>
			</view>

			<!-- 底部占位区域，避免内容被固定按钮遮挡 -->
			<view class="bottom-spacer"></view>
		</scroll-view>

		<!-- 底部操作栏 -->
		<view class="bottom-actions">
				<view class="action-icon like">
					<text class="icon">♡</text>
					<text class="count">0</text>
				</view>
				<view class="action-icon star">
					<text class="icon">☆</text>
					<text class="count">0</text>
				</view>
				<view class="action-icon share">
					<text class="icon">↗</text>
					<text class="count">0</text>
				</view>

				<view class="dye-btn" @click="startDye">
					去染它
				</view>
			</view>
	</view>
</template>

<script>
	export default {
		// 添加meta信息
		meta: {
			viewportWidth: 'device-width',
			viewportInitialScale: 1,
			viewportMaximum: 1,
			viewportScalable: false
		},
		data() {
			return {
				iconPath: '',
				categoryName: '',
				colorName: '',
				colorHex: '',
				dyeCount: 1,
				description: '',
				colorData: [
					// {
					// 	colorClass: 'red',
					// 	weight: 20,
					// 	labels: ['红色', '10g']
					// },
					// {
					// 	colorClass: 'blue',
					// 	weight: 5,
					// 	labels: ['蓝色', '5g']
					// },
					// {
					// 	colorClass: 'black',
					// 	weight: 1,
					// 	labels: ['自然黑', '1g']
					// },
					// {
					// 	colorClass: 'white',
					// 	weight: 10.67,
					// 	labels: ['双氧乳', '10VOL', '10.67g']
					// },
					// {
					// 	colorClass: 'white',
					// 	weight: 5.33,
					// 	labels: ['双氧乳', '40VOL', '5.33g']
					// }
				],
				developerComponents: null,
				colorComponents: null
			}
		},
		computed: {
			normalizedColorData() {
				const maxWeight = 20; // 每个颜色最大克重
				const adjustedColorData = this.colorData.map(item => {
					const adjustedWeight = Math.min(item.weight, maxWeight);
					return {
						...item,
						adjustedWeight
					};
				});

				const totalWeight = adjustedColorData.reduce((acc, item) => acc + item.adjustedWeight, 0);
				const maxHeight = 100; // 设定一个最大高度，你可以根据需要调整

				return adjustedColorData.map(item => {
					const percentage = item.adjustedWeight / totalWeight;
					const height = percentage * maxHeight;
					return {
						...item,
						height
					};
				});
			}
		},
		onLoad(options) {
			console.log('颜色详情页面加载，参数:', options);

			// 尝试从URL参数获取颜色信息
			if (options && options.name) {
				this.colorName = decodeURIComponent(options.name || '未知颜色');
				this.categoryName = decodeURIComponent(options.category || '未知分类');
				this.colorHex = decodeURIComponent(options.color || '#000000');
				this.description = decodeURIComponent(options.description || '未设置使用方法')
				this.iconPath = decodeURIComponent(options.iconPath || '未知图片')
				this.colorComponents = decodeURIComponent(options.colorComponents || [])
				this.developerComponents = decodeURIComponent(options.developerComponents || [])
				this.colorComponents = JSON.parse(this.colorComponents)
				this.developerComponents = JSON.parse(this.developerComponents)
				// console.log("this.developerComponents", this.developerComponents)
				this.colorData = [
					 // private BigDecimal gray;           // 灰色0.1 (原字段)
					 //    private BigDecimal green;         // 绿色G9.33 (原字段)
					 //    private BigDecimal yellow;        // 黄色Y9.33 (原字段)
					 //    private BigDecimal orange;        // 橙色6.64 (原字段)
					 //    private BigDecimal red;           // 红色0.6 (原字段)
					 //    private BigDecimal purple;        // 紫色0.2 (原字段)
					 //    private BigDecimal brown;         // 棕色5.52 (原字段)
					 //    private BigDecimal blue;         // 蓝色B8.11 (原字段)
					 //    private BigDecimal black;        // 基色（自然黑）3.0 (原字段)
					 //    private BigDecimal faded_bleached; // 0/00（退色）(原字段)
					{
						colorClass: 'gray',
						weight: this.colorComponents.gray,
						labels: ["灰色", `${this.colorComponents.gray}g`]
					},
					{
						colorClass: 'green',
						weight: this.colorComponents.green,
						labels: ["绿色", `${this.colorComponents.green}g`]
					},
					{
						colorClass: 'yellow',
						weight: this.colorComponents.yellow,
						labels: ["黄色", `${this.colorComponents.yellow}g`]
					},
					{
						colorClass: 'orange',
						weight: this.colorComponents.orange,
						labels: ["橙色", `${this.colorComponents.orange}g`]
					},
					{
						colorClass: 'red',
						weight: this.colorComponents.red,
						labels: ["红色", `${this.colorComponents.red}g`]
					},
					{
						colorClass: 'purple',
						weight: this.colorComponents.purple,
						labels: ["紫色", `${this.colorComponents.purple}g`]
					},
					{
						colorClass: 'brown',
						weight: this.colorComponents.brown,
						labels: ["棕色", `${this.colorComponents.brown}g`]
					},
					
					{
						colorClass: 'blue',
						weight: this.colorComponents.blue,
						labels: ["蓝色", `${this.colorComponents.blue}g`]
					},
					
					{
						colorClass: 'black',
						weight: this.colorComponents.black,
						labels: ["黑色", `${this.colorComponents.black}g`]
					},
					{
						colorClass: 'faded_bleached',
						weight: this.colorComponents.faded_bleached,
						labels: ["淡化剂", `${this.colorComponents.faded_bleached}g`]
					},
					// private BigDecimal bleached;      // 极浅灰金色12.11 (用bleached字段对应)
					//     private BigDecimal silver;        // 梦幻紫V10.21 (用silver字段对应)
					//     private BigDecimal gold;         // 浅棕色5.0 (用gold字段对应)
					//     private BigDecimal linen;        // 深灰亚麻色7.11 (原字段)
					{
						colorClass: 'bleached',
						weight: this.colorComponents.bleached,
						labels: ["极浅灰金色", `${this.colorComponents.bleached}g`]
					},
					{
						colorClass: 'silver',
						weight: this.colorComponents.silver,
						labels: ["梦幻紫", `${this.colorComponents.silver}g`]
					},
					{
						colorClass: 'gold',
						weight: this.colorComponents.gold,
						labels: ["浅棕色", `${this.colorComponents.gold}g`]
					},
					{
						colorClass: 'linen',
						weight: this.colorComponents.linen,
						labels: ["深灰亚麻色", `${this.colorComponents.linen}g`]
					},
					
					//双氧乳显示
					// private BigDecimal hydrogenPeroxide3Percent;
					//     private BigDecimal hydrogenPeroxide12Percent;
					{
						colorClass: 'white',
						weight: this.developerComponents.hydrogenPeroxide3Percent,
						labels: ["双氧乳10VOL", `${this.developerComponents.hydrogenPeroxide3Percent}g`]
					},
					{
						colorClass: 'white',
						weight: this.developerComponents.hydrogenPeroxide3Percent,
						labels: ["双氧乳20VOL2:1", `${this.developerComponents.hydrogenPeroxide6Percent}g`]
					},
					{
						colorClass: 'white',
						weight: this.developerComponents.hydrogenPeroxide3Percent,
						labels: ["双氧乳30VOL1:2", `${this.developerComponents.hydrogenPeroxide9Percent}g`]
					},
					{
						colorClass: 'white',
						weight: this.developerComponents.hydrogenPeroxide12Percent,
						labels: ["双氧乳40VOL", `${this.developerComponents.hydrogenPeroxide12Percent}g`]
					}
				]
				// console.log("双氧乳构造",this.developerComponents)
				// console.log("颜色构造",this.colorComponents)
				
				// 强制触发更新（可选）
				    this.$nextTick(() => {
				      this.$forceUpdate();
				    });
				// console.log('从URL参数获取数据成功');
			} else {
				// 如果URL参数获取失败，从缓存中读取
				try {
					const cachedData = uni.getStorageSync('selectedColorInfo');
					if (cachedData) {
						this.colorName = cachedData.name || '未知颜色';
						this.categoryName = cachedData.category || '未知分类';
						this.colorHex = cachedData.hex || '#000000';
						console.log('从缓存读取数据成功');
					} else {
						console.error('无法获取颜色信息');
						uni.showToast({
							title: '加载失败',
							icon: 'none'
						});
					}
				} catch (e) {
					console.error('读取缓存失败:', e);
				}
			}
		},
		methods: {
			goBack() {
				uni.navigateBack();
			},
			startDye() {
				// 保存当前颜色信息到存储，方便颜色确认页面读取
				try {
					uni.setStorageSync('dyeColorInfo', {
						name: this.colorName,
						category: this.categoryName,
						hex: this.colorHex,
						mode: 'smart', // 默认标记为智能模式
						colorComponents : this.colorComponents,
						developerComponents : this.developerComponents
					});
					console.log('保存颜色信息到缓存成功');
				} catch (e) {
					console.error('保存颜色信息到缓存失败:', e);
				}

				// 优先使用完整路径，确保跳转成功
				const url = '/pages/mall/color/confirm?mode=smart';
				// console.log('尝试跳转到页面:', url);

				uni.navigateTo({
					url: url,
					success: () => {
						console.log('跳转到确认调色页面成功');
					},
					fail: (err) => {
						console.error('跳转失败:', err);
						// 显示错误提示
						uni.showModal({
							title: '提示',
							content: '跳转失败，请退出应用后重试',
							showCancel: false
						});
					}
				});
			},
			switchToMasterMode() {
				// 实现切换到大师模式的功能
				console.log('切换到大师模式');

				// 保存当前颜色信息
				try {
					uni.setStorageSync('dyeColorInfo', {
						name: this.colorName,
						category: this.categoryName,
						hex: this.colorHex,
						mode: 'master' // 标记为大师模式
					});
					console.log('保存颜色信息到缓存成功，模式：大师模式');
				} catch (e) {
					console.error('保存颜色信息到缓存失败:', e);
				}

				// 跳转到确认调色页面并传递模式参数
				const url = '/pages/mall/color/confirm?mode=master';
				console.log('尝试跳转到页面:', url);

				uni.navigateTo({
					url: url,
					success: () => {
						console.log('跳转到大师模式页面成功');
					},
					fail: (err) => {
						console.error('跳转失败:', err);
						// 显示错误提示
						uni.showModal({
							title: '提示',
							content: '跳转失败，请退出应用后重试',
							showCancel: false
						});
					}
				});
			}
		}
	}
</script>

<style lang="scss" scoped>
	/* CSS Variables for better maintainability */
	:root {
		--color-bar-gap: 4px;
		--color-block-min-width: 25px;
		--color-block-max-width: 40px;
		--composition-padding: 8px 12px;
		--composition-min-height: 120rpx;
		--color-bar-height: 140rpx;
	}

	/* 添加页面级别样式，修复右侧空白问题 */
	page {
		width: 100vw;
		height: 100vh;
		overflow-x: hidden;
		padding: 0;
		margin: 0;
		box-sizing: border-box;
		background-color: #ffffff;
		overscroll-behavior: none;
	}

	/* 补充uni-app全局样式，确保内容不超出屏幕 */
	:root,
	uni-page,
	uni-page-head,
	uni-page-wrapper,
	uni-page-body,
	uni-app,
	uni-window {
		width: 100vw;
		max-width: 100vw;
		min-width: 100vw;
		box-sizing: border-box;
		overflow-x: hidden;
		margin: 0;
		padding: 0;
	}

	/* 固定宽度防止溢出 */
	view {
		max-width: 100vw;
		box-sizing: border-box;
	}

	.app-container {
		width: 100vw;
		max-width: 100vw;
		margin: 0;
		padding: 0;
		position: relative;
		height: 100vh;
		box-sizing: border-box;
		left: 0;
		right: 0;
	}

	.scrollable-content {
		height: calc(100vh - 120rpx);
		width: 100%;
		overflow-y: auto;
		-webkit-overflow-scrolling: touch;
		scroll-behavior: smooth;
		overscroll-behavior: contain;
		box-sizing: border-box;
	}

	.bottom-spacer {
		height: 120rpx;
		width: 100%;
	}



	.header {
		display: flex;
		align-items: center;
		justify-content: space-between;
		background-color: #4285f4;
		color: #fff;
		padding: 20rpx 30rpx;
		position: sticky;
		top: 0;
		z-index: 100;
		width: 100%;
		box-sizing: border-box;
	}

	.back-btn {
		width: 60rpx;
		height: 60rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.back-icon {
		font-size: 40rpx;
	}

	.title {
		font-size: 36rpx;
		font-weight: 500;
	}

	.more-btn {
		width: 60rpx;
		height: 60rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.more-icon {
		font-size: 30rpx;
	}

	.color-info {
		display: flex;
		flex-direction: column;
		align-items: center;
		padding: 20rpx;
		border-bottom: 1rpx solid #f0f0f0;
		width: 100%;
		box-sizing: border-box;
	}

	.category-line {
		display: flex;
		align-items: center;
		width: 100%;
		font-size: 26rpx;
		color: #333;
		margin-bottom: 15rpx;
	}

	.offline-tag {
		margin-left: 20rpx;
		font-size: 24rpx;
		color: #999;
	}

	.online-tag {
		margin-left: 20rpx;
		font-size: 24rpx;
		color: #4CAF50;
	}

	.color-preview {
		width: 200rpx;
		height: 200rpx;
		border-radius: 50%;
		overflow: hidden;
		transition: all 0.3s ease;
		position: relative;
		z-index: 1;
		display: block;
		border: none;
		outline: none;
		background: transparent;
	}

	.color-circle {
	    width: 100%;
	    height: 100%;
	    display: block;
	    border: none;
	    outline: none;
	    object-fit: cover;
	}

	.color-name {
		font-size: 30rpx;
		font-weight: bold;
		margin-bottom: 5rpx;
	}

	.color-category {
		font-size: 24rpx;
		color: #666;
		margin-bottom: 5rpx;
	}

	.dye-count {
		font-size: 24rpx;
		color: #999;
		margin-bottom: 15rpx;
	}

	.mode-tabs {
		display: flex;
		align-items: center;
		margin-bottom: 20rpx;
	}

	.mode-tab {
		padding: 10rpx 20rpx;
		background-color: #f0f0f0;
		border-radius: 40rpx;
		margin-right: 10rpx;
		font-size: 28rpx;
		color: #333;
		cursor: pointer;
	}

	.active {
		background-color: #4285f4;
		color: #fff;
	}

	.divider {
		width: 100%;
		height: 1rpx;
		background-color: #f0f0f0;
		margin-bottom: 10rpx;
	}

	.color-structure {
		font-size: 26rpx;
		font-weight: bold;
		align-self: flex-start;
		margin-bottom: 10rpx;
	}

	.dye-instruction {
		padding: 30rpx;
		border-bottom: 1rpx solid #f0f0f0;
		width: 100%;
		box-sizing: border-box;
		margin-bottom: 20rpx;
	}

	.instruction-title {
		font-size: 30rpx;
		font-weight: bold;
		margin-bottom: 20rpx;
	}

	.step {
		display: flex;
		align-items: center;
		margin-bottom: 10rpx;
	}

	.step-num {
		width: 40rpx;
		height: 40rpx;
		background-color: #4285f4;
		color: #fff;
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 24rpx;
		margin-right: 10rpx;
	}

	.step-desc {
		font-size: 28rpx;
		color: #333;
	}

	.step-detail {
	font-size: 28rpx;
	color: #666;
	margin-left: 20rpx;
	margin-bottom: 20rpx;
	white-space: pre-wrap;
	word-wrap: break-word;
	line-height: 1.8;
}

	.base-color-info {
		margin-top: 20rpx;
		font-size: 28rpx;
		font-weight: bold;
		color: #333;
	}

	.base-color-detail {
		font-size: 26rpx;
		color: #666;
		margin-top: 10rpx;
		line-height: 1.5;
	}

	.bottom-actions {
		display: flex;
		align-items: center;
		padding: 20rpx 30rpx;
		background-color: #fff;
		border-top: 1rpx solid #f0f0f0;
		box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.1);
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		z-index: 100;
		width: 100%;
		box-sizing: border-box;
	}

	.action-icon {
		display: flex;
		flex-direction: column;
		align-items: center;
		margin-right: 30rpx;
	}

	.icon {
		font-size: 40rpx;
		color: #666;
	}

	.count {
		font-size: 24rpx;
		color: #999;
		margin-top: 4rpx;
	}

	.dye-btn {
		flex: 1;
		background-color: #4285f4;
		color: #fff;
		height: 80rpx;
		border-radius: 40rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 30rpx;
		margin-left: 20rpx;
	}

	/* 确保所有内容区块占满整个宽度 */
	.color-info,
	.dye-instruction {
		width: 100%;
		box-sizing: border-box;
		max-width: 100vw;
	}

	/* Color composition container with adaptive height */
	.color-composition {
		min-height: var(--composition-min-height);
		padding: var(--composition-padding);
	}

	.title {
		text-align: center;
		font-size: 20px;
		margin-bottom: 20px;
		color: #666;
	}

	/* Horizontal scrollable container for color bars */
	.color-bar-container {
		height: var(--color-bar-height);
		display: flex;
		justify-content: flex-start;
		align-items: flex-end;
		overflow-x: auto;
		white-space: nowrap;
		gap: var(--color-bar-gap);
		scroll-behavior: smooth;
	}

	.color-bar {
		display: flex;
		flex-direction: column;
		align-items: center;
	}

	/* Responsive color block with flexible width */
	.color-block {
		width: 40px;
		min-width: var(--color-block-min-width);
		max-width: var(--color-block-max-width);
		// height: 200rpx;
		border-radius: 5rpx;
	}

	.gray {
	    background-color: gray;
	}
	
	.green {
	    background-color: green;
	}
	
	.yellow {
	    background-color: yellow;
	}
	
	.orange {
	    background-color: orange;
	}
	
	.red {
	    background-color: red;
	}
	
	.purple {
	    background-color: purple;
	}
	
	.brown {
	    background-color: brown;
	}
	
	.blue {
	    background-color: blue;
	}
	
	.black {
	    background-color: black;
	}
	
	.bleached {
	    background-color: #f0f0f0; /* Light gray to simulate bleached/white */
	}
	
	.white {
	    background-color: #f0f0f0; /* Light gray to simulate white on white background */
	}

	.color-label {
		margin-top: 5px;
		text-align: center;
		display: flex;
		flex-direction: column;
	}
</style>