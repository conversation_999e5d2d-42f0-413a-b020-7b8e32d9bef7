<template>
	<view class="search-page">
		<!-- 搜索框 -->
		<view class="search-bar">
			<view class="search-input-box">
				<input
					type="text"
					class="search-input"
					v-model="searchText"
					confirm-type="search"
					placeholder="搜索内容"
					focus
					@confirm="doSearch"
				/>
				<text class="clear-btn" v-if="searchText" @click="clearSearch">×</text>
				<text class="search-btn-inline" @click="doSearch">搜索</text>
			</view>
		</view>
		
		<!-- 搜索历史和推荐 -->
		<view class="search-container" v-if="!showResults">
			<!-- 搜索历史 - 优先显示 -->
			<view class="search-section" v-if="searchHistory.length > 0">
				<view class="section-header">
					<text class="section-title">最近搜索</text>
					<text class="clear-history" @click="clearHistory">清空</text>
				</view>
				<view class="history-list">
					<view class="history-item" v-for="(item, index) in displaySearchHistory" :key="'his-'+index" @click="quickSearch(item)">
						<text class="history-icon">🕒</text>
						<text class="history-text">{{item}}</text>
						<text class="history-time">{{historyTimes[index]}}</text>
					</view>
				</view>
			</view>

			<!-- 热门搜索 - 在搜索历史之后显示 -->
			<view class="search-section" v-if="recommends.length > 0">
				<view class="section-header">
					<text class="section-title">热门搜索</text>
				</view>
				<view class="recommend-list">
					<view class="recommend-item" v-for="(item, index) in recommends" :key="'rec-'+index" @click="quickSearch(item)">
						{{item}}
					</view>
				</view>
			</view>

			<!-- 空状态 - 只有当既没有搜索历史也没有推荐词时才显示 -->
			<view class="empty-state" v-if="searchHistory.length === 0 && recommends.length === 0 && !loading">
				<text class="empty-text">暂无搜索记录</text>
				<text class="empty-tip">搜索商品名称或关键词</text>
			</view>
		</view>
		
		<!-- 搜索结果 -->
		<view class="search-results" v-else>
			<!-- 分类筛选提示 -->
			<view class="category-filter" v-if="categoryName">
				<text class="filter-text">分类：{{categoryName}}</text>
				<text class="clear-filter" @click="clearCategoryFilter">清除</text>
			</view>
			<view class="result-count">找到{{searchResults.length}}个相关商品</view>
			<view class="result-list">
				<view class="result-item" v-for="(item, index) in searchResults" :key="index" @click="goToProduct(item)">
					<image class="result-image" :src="item.image" mode="aspectFill"></image>
					<view class="result-info">
						<text class="result-name">{{item.name}}</text>
						<text class="result-price">¥{{item.price}}</text>
						<text class="result-sold">已售{{item.sold}}件</text>
					</view>
				</view>
			</view>
			
			<view class="no-more" v-if="searchResults.length > 0">没有更多结果了</view>
			<view class="no-result" v-else>
				<image class="no-result-image" src="/static/images/no-result.png" mode="aspectFit"></image>
				<text class="no-result-text">暂无搜索结果</text>
			</view>
		</view>
	</view>
</template>

<script>
import { apiService } from '@/utils/request.js';

export default {
	data() {
		return {
			baseUrl : "https://www.narenqiqige.com/api",
			searchText: '',
			showResults: false,
			searchResults: [],
			categoryId: null,
			categoryName: '',
			searchHistory: [],
			historyTimes: [],
			recommends: [],
			loading: false
		};
	},
	onLoad(options) {
		// 先加载本地搜索历史，立即显示
		this.loadLocalSearchHistory();

		// 然后加载远程搜索数据
		this.loadSearchData();

		// 如果有搜索词参数
		if (options.keyword) {
			this.searchText = options.keyword;
			this.doSearch();
		}
		// 如果有分类参数
		if (options.categoryId) {
			this.categoryId = options.categoryId;
			this.categoryName = options.categoryName || '';
			this.searchText = this.categoryName;
			this.doSearch();
		}
	},
	computed: {
		// 显示最近10个搜索历史
		displaySearchHistory() {
			return this.searchHistory.slice(0, 10);
		}
	},
	methods: {
		// 加载搜索数据
		async loadSearchData() {
			this.loading = true;
			try {
				// 并行加载推荐词和搜索历史
				const [recommendsResult, historyResult] = await Promise.allSettled([
					this.loadRecommendations(),
					this.loadSearchHistory()
				]);

				// 处理推荐词结果
				if (recommendsResult.status === 'fulfilled') {
					this.recommends = recommendsResult.value || [];
				} else {
					console.warn('加载推荐词失败:', recommendsResult.reason);
					this.recommends = [];
				}

				// 处理搜索历史结果
				if (historyResult.status === 'fulfilled') {
					const remoteHistory = historyResult.value;
					if (remoteHistory && remoteHistory.keywords && remoteHistory.keywords.length > 0) {
						// 如果远程有数据，使用远程数据（已经限制为10个）
						this.searchHistory = remoteHistory.keywords;
						this.historyTimes = remoteHistory.times;
					}
					// 如果远程没有数据，保持本地数据不变
				} else {
					console.warn('加载搜索历史失败:', historyResult.reason);
					// 远程加载失败时，保持已加载的本地数据
				}
			} catch (error) {
				console.error('加载搜索数据异常:', error);
				this.recommends = [];
				// 不需要重复加载本地历史，因为在onLoad中已经加载过了
			} finally {
				this.loading = false;
			}
		},

		// 加载热门关键词
		async loadRecommendations() {
			try {
				const response = await apiService.mall.search.hotKeywords();
				if (response.code === 200 && response.data) {
					return response.data || [];
				}
				throw new Error('热门关键词数据格式错误');
			} catch (error) {
				console.error('获取热门关键词失败:', error);
				throw error;
			}
		},

		// 加载搜索历史
		async loadSearchHistory() {
			try {
				const response = await apiService.mall.search.history();
				if (response.code === 200 && response.data) {
					const historyData = response.data.slice(0, 10); // 限制为最近10个
					return {
						keywords: historyData.map(item => item.keyword || item),
						times: historyData.map(item => this.formatTime(item.createTime || item.time))
					};
				}
				throw new Error('搜索历史数据格式错误');
			} catch (error) {
				console.error('获取搜索历史失败:', error);
				throw error;
			}
		},

		// 加载本地搜索历史（降级方案）
		loadLocalSearchHistory() {
			const savedHistory = uni.getStorageSync('searchHistory');
			const savedTimes = uni.getStorageSync('historyTimes');

			if (savedHistory && Array.isArray(savedHistory)) {
				// 限制为最近10个
				this.searchHistory = savedHistory.slice(0, 10);
			}

			if (savedTimes && Array.isArray(savedTimes)) {
				// 对应的时间也限制为10个
				this.historyTimes = savedTimes.slice(0, 10);
			}
		},

		// 格式化时间
		formatTime(timeStr) {
			if (!timeStr) return '未知';

			try {
				const time = new Date(timeStr);
				const now = new Date();
				const diff = now - time;

				const minutes = Math.floor(diff / (1000 * 60));
				const hours = Math.floor(diff / (1000 * 60 * 60));
				const days = Math.floor(diff / (1000 * 60 * 60 * 24));
				const weeks = Math.floor(days / 7);

				if (minutes < 1) return '刚刚';
				if (minutes < 60) return `${minutes}分钟前`;
				if (hours < 24) return `${hours}小时前`;
				if (days < 7) return `${days}天前`;
				if (weeks < 52) return `${weeks}w`;

				return `${Math.floor(weeks / 52)}年前`;
			} catch (error) {
				return timeStr;
			}
		},

		clearSearch() {
			this.searchText = '';
			this.showResults = false;
		},
		doSearch() {
			if (!this.searchText.trim()) {
				uni.showToast({
					title: '请输入搜索内容',
					icon: 'none'
				});
				return;
			}

			// 保存搜索历史
			this.saveSearchHistory(this.searchText);

			// 显示加载中
			uni.showLoading({
				title: '搜索中...'
			});

			// 调用搜索API
			this.performSearch();
		},

		// 保存搜索历史
		async saveSearchHistory(keyword) {
			if (!keyword || this.searchHistory.includes(keyword)) {
				return;
			}

			try {
				// 先保存到后端
				await apiService.mall.search.saveHistory(keyword);

				// 更新本地数据
				this.searchHistory.unshift(keyword);
				this.historyTimes.unshift('刚刚');

				// 限制历史记录数量
				if (this.searchHistory.length > 10) {
					this.searchHistory.pop();
					this.historyTimes.pop();
				}

				// 同时保存到本地存储作为备份
				uni.setStorageSync('searchHistory', this.searchHistory);
				uni.setStorageSync('historyTimes', this.historyTimes);
			} catch (error) {
				console.error('保存搜索历史失败:', error);

				// 如果后端保存失败，至少保存到本地
				this.searchHistory.unshift(keyword);
				this.historyTimes.unshift('刚刚');

				if (this.searchHistory.length > 10) {
					this.searchHistory.pop();
					this.historyTimes.pop();
				}

				uni.setStorageSync('searchHistory', this.searchHistory);
				uni.setStorageSync('historyTimes', this.historyTimes);
			}
		},
		// 执行搜索
		async performSearch() {
			try {
				const params = {
					keyword: this.searchText,
					page: 1,
					pageSize: 20
				};

				// 如果有分类ID，添加到搜索参数
				if (this.categoryId) {
					params.categoryId = this.categoryId;
				}

				const response = await apiService.mall.search.products(params);

				uni.hideLoading();

				if (response.code === 200) {
					// 处理搜索API返回的数据格式
					const products = response.data.products || response.data.records || response.data || [];
					this.searchResults = products.map(product => ({
						id: product.id,
						name: product.productName || product.name,
						price: product.price,
						sold: product.sales || 0,
						image: this.getProductImage(product)
					}));
					console.log("搜索返回的结果",this.searchResults)
				} else {
					this.searchResults = [];
					console.error('搜索失败:', response.message);
				}

				this.showResults = true;
			} catch (error) {
				uni.hideLoading();
				console.error('搜索异常:', error);
				uni.showToast({
					title: '搜索失败，请重试',
					icon: 'none'
				});
				this.searchResults = [];
				this.showResults = true;
			}
		},

		// 获取商品图片
		getProductImage(product) {
			if (!product.images) return '';
			let imageUrl = '';
			if (typeof product.images === 'string') {
				const imageArray = product.images.split(',');
				imageUrl = imageArray[0] || '';
			} else {
				imageUrl = product.images[0] || '';
			}

			// 处理完整的图片URL
			return this.getFullImageUrl(imageUrl);
		},

		// 获取完整的图片URL
		getFullImageUrl(url) {
			if (!url) return '';
			if (url.startsWith('http')) {
				return url; // 已经是完整URL
			}
			if (url.startsWith('/static') || url.startsWith('/uploads')) {
				return 'https://www.narenqiqige.com/api' + url; // 添加服务器基础URL
			}
			// 如果不是以/开头，添加/uploads/前缀（假设是相对路径的图片文件名）
			if (!url.startsWith('/')) {
				return 'https://www.narenqiqige.com/api/uploads/' + url;
			}
			return url;
		},

		quickSearch(keyword) {
			this.searchText = keyword;
			this.doSearch();
		},

		// 清空搜索历史
		async clearHistory() {
			try {
				uni.showModal({
					title: '提示',
					content: '确定要清空搜索历史吗？',
					success: async (res) => {
						if (res.confirm) {
							// 调用清空历史接口
							await apiService.mall.search.clearHistory();

							// 清空本地数据
							this.searchHistory = [];
							this.historyTimes = [];
							uni.removeStorageSync('searchHistory');
							uni.removeStorageSync('historyTimes');

							uni.showToast({
								title: '已清空历史',
								icon: 'none'
							});
						}
					}
				});
			} catch (error) {
				console.error('清空搜索历史失败:', error);
				uni.showToast({
					title: '清空失败',
					icon: 'none'
				});
			}
		},

		goToProduct(product) {
			uni.navigateTo({
				url: '/pages/mall/product/detail?id=' + product.id
			});
		},

		// 清除分类筛选
		clearCategoryFilter() {
			this.categoryId = null;
			this.categoryName = '';
			this.doSearch();
		}
	}
}
</script>

<style lang="scss">
.search-page {
	min-height: 100vh;
	background-color: #f8f8f8;
	padding-bottom: constant(safe-area-inset-bottom);
	padding-bottom: env(safe-area-inset-bottom);
	width: 100%;
	box-sizing: border-box;
}

.search-bar {
	background: linear-gradient(180deg, #4285f4 0%, #f8f8f8 100%);
	padding: 20rpx;
	border-bottom: 1rpx solid #e0e0e0;
	position: sticky;
	top: 0;
	z-index: 100;
	width: 100%;
	box-sizing: border-box;
}

.search-input-box {
	background-color: #fff;
	border-radius: 30rpx;
	display: flex;
	align-items: center;
	padding: 0 20rpx;
	height: 70rpx;
	position: relative;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
	border: 1rpx solid #e0e0e0;
	width: 100%;
	box-sizing: border-box;
}

.search-input {
	flex: 1;
	height: 70rpx;
	font-size: 28rpx;
	border: none;
	outline: none;
	background: transparent;
	min-width: 0;
}

.clear-btn {
	font-size: 40rpx;
	color: #ccc;
	padding: 0 10rpx;
	width: 60rpx;
	height: 60rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.search-btn-inline {
	color: #4285f4;
	font-size: 28rpx;
	padding: 0 15rpx;
	height: 50rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	background-color: #f0f8ff;
	border-radius: 25rpx;
	margin-left: 10rpx;
	white-space: nowrap;
	flex-shrink: 0;
}

.search-container {
	padding: 20rpx;
	min-height: calc(100vh - 200rpx);
	width: 100%;
	box-sizing: border-box;
}

.search-section {
	margin-bottom: 40rpx;
}

.section-header {
	display: flex;
	justify-content: space-between;
	margin-bottom: 20rpx;
}

.section-title {
	font-size: 30rpx;
	color: #333;
	font-weight: bold;
}

.clear-history {
	font-size: 28rpx;
	color: #4285f4;
}

/* 空状态 */
.empty-state {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 100rpx 40rpx;
	text-align: center;
	flex: 1;
}

.empty-text {
	font-size: 32rpx;
	color: #666;
	margin-bottom: 20rpx;
}

.empty-tip {
	font-size: 26rpx;
	color: #999;
}

.recommend-list {
	display: flex;
	flex-wrap: wrap;
	width: 100%;
}

.recommend-item {
	font-size: 26rpx;
	color: #666;
	background-color: #fff;
	padding: 10rpx 30rpx;
	margin-right: 20rpx;
	margin-bottom: 20rpx;
	border-radius: 30rpx;
	flex: 0 0 auto;
	max-width: calc(50% - 10rpx);
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.history-list {
	background-color: #fff;
	border-radius: 10rpx;
	padding: 10rpx 0;
	width: 100%;
	box-sizing: border-box;
}

.history-item {
	display: flex;
	align-items: center;
	padding: 20rpx 30rpx;
	border-bottom: 1rpx solid #f0f0f0;
}

.history-item:last-child {
	border-bottom: none;
}

.history-icon {
	font-size: 30rpx;
	color: #999;
	margin-right: 10rpx;
}

.history-text {
	flex: 1;
	font-size: 28rpx;
	color: #333;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.history-time {
	font-size: 24rpx;
	color: #999;
}

.search-results {
	padding: 20rpx;
	width: 100%;
	box-sizing: border-box;
}

.category-filter {
	display: flex;
	justify-content: space-between;
	align-items: center;
	background-color: #f0f8ff;
	padding: 20rpx 30rpx;
	margin-bottom: 20rpx;
	border-radius: 10rpx;
	border-left: 6rpx solid #4285f4;
}

.filter-text {
	font-size: 28rpx;
	color: #4285f4;
	font-weight: bold;
}

.clear-filter {
	font-size: 26rpx;
	color: #666;
	padding: 10rpx 20rpx;
	background-color: #fff;
	border-radius: 20rpx;
	border: 1rpx solid #ddd;
}

.result-count {
	font-size: 28rpx;
	color: #666;
	margin-bottom: 20rpx;
}

.result-list {
	background-color: #fff;
	border-radius: 10rpx;
	padding: 20rpx;
	width: 100%;
	box-sizing: border-box;
}

.result-item {
	display: flex;
	padding: 20rpx 0;
	border-bottom: 1rpx solid #f0f0f0;
}

.result-item:last-child {
	border-bottom: none;
}

.result-image {
	width: 160rpx;
	height: 160rpx;
	border-radius: 10rpx;
	margin-right: 20rpx;
	flex-shrink: 0;
}

.result-info {
	flex: 1;
	display: flex;
	flex-direction: column;
	justify-content: space-between;
	min-width: 0; /* 确保弹性项可以缩小到其最小内容尺寸以下 */
}

.result-name {
	font-size: 28rpx;
	color: #333;
	margin-bottom: 10rpx;
	overflow: hidden;
	text-overflow: ellipsis;
	display: -webkit-box;
	-webkit-line-clamp: 2;
	-webkit-box-orient: vertical;
}

.result-price {
	font-size: 32rpx;
	color: #ff0036;
	font-weight: bold;
}

.result-sold {
	font-size: 24rpx;
	color: #999;
}

.no-more, .no-result {
	text-align: center;
	padding: 40rpx 0;
	color: #999;
	font-size: 28rpx;
}

.no-result {
	display: flex;
	flex-direction: column;
	align-items: center;
}

.no-result-image {
	width: 200rpx;
	height: 200rpx;
	margin-bottom: 20rpx;
}

.no-result-text {
	font-size: 28rpx;
	color: #999;
}
</style> 