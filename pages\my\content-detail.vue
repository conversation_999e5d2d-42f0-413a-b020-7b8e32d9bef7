<template>
  <view class="content-detail">
    <!-- 图片轮播区域 -->
    <swiper class="image-swiper" indicator-dots autoplay circular>
      <swiper-item v-for="(image, index) in contentInfo.images" :key="index">
        <image class="swiper-image" :src="baseUrl + image" mode="aspectFill"></image>
      </swiper-item>
    </swiper>
    
    <!-- 动态内容 -->
    <view class="content-box">
      <text class="content-text">{{contentInfo.content}}</text>
    </view>
    
    <!-- 发布信息 -->
    <view class="meta-info">
      <view class="user-info">
        <image class="user-avatar" :src="contentInfo.avatar || '/static/logo.png'"></image>
        <text class="meta-user">{{contentInfo.userName}}</text>
      </view>
      <text class="meta-time">{{contentInfo.createTime}}</text>
    </view>
    
    <!-- 互动数据 -->
    <view class="interaction-stats">
      <view class="stat-item">
        <text class="stat-num">{{contentInfo.commentCount || 0}}</text>
        <text class="stat-label">评论</text>
      </view>
      <view class="stat-item">
        <text class="stat-num">{{contentInfo.likeCount || 0}}</text>
        <text class="stat-label">点赞</text>
      </view>
      <view class="stat-item">
        <text class="stat-num">{{contentInfo.viewCount || 0}}</text>
        <text class="stat-label">浏览</text>
      </view>
    </view>
    
    <!-- 评论区 -->
    <view class="comment-section">
      <view class="comment-title">评论</view>
      <view v-if="comments.length === 0" class="no-comment">暂无评论</view>
      <view v-else class="comment-list">
        <view v-for="comment in comments" :key="comment.id" class="comment-item">
          <view class="comment-user-info">
            <text class="comment-user">{{comment.userName}}</text>
            <text class="comment-time">{{comment.createTime}}</text>
          </view>
          <text class="comment-text">{{comment.content}}</text>
        </view>
      </view>
    </view>
    
    <!-- 评论输入框 -->
    <view class="comment-input">
      <input 
        v-model="commentContent" 
        placeholder="文明城市,友善交流..." 
        class="input-box"
      />
      <button class="send-btn" @click="sendComment">发送</button>
    </view>
  </view>
</template>

<script>
import { apiService } from '../../utils/request';

export default {
  data() {
    return {
	  baseUrl : "https://www.narenqiqige.com",
      contentId: '',
      contentInfo: {
        images: [], 
        content: "",
        userName: "",
        createTime: "",
        commentCount: 0,
        likeCount: 0,
        viewCount: 0
      },
      comments: [],
      commentContent: ''
    }
  },
  onLoad(options) {
	  console.log("传入的id",options.id)
    this.contentId = options.id;
    this.loadContentDetail(options.id)
  },
  methods: {
    async loadContentDetail(contentId) {
      const response = await apiService.content.getDetail(contentId)
	  this.contentInfo = response.data
	  this.contentId = response.data.id
	  const date = new Date(this.contentInfo.createTime);
	  this.contentInfo.createTime = `${date.getFullYear()}-${(date.getMonth()+1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')} ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}:${date.getSeconds().toString().padStart(2, '0')}`;
    },
    loadComments() {
      // uni.request({
      //   url: `/api/comment/list/${this.contentId}`,
      //   success: (res) => {
      //     this.comments = res.data.data;
      //   }
      // });
    },
    sendComment() {
      if (!this.commentContent.trim()) return;
      
      const newComment = {
        id: Date.now(),
        userName: "当前用户",
        content: this.commentContent,
        createTime: new Date().toLocaleString()
      };
      
      this.comments.push(newComment);
      this.commentContent = '';
      this.contentInfo.commentCount += 1;
      
      uni.pageScrollTo({
        scrollTop: 99999,
        duration: 300
      });
      
      // 实际项目中应该使用API请求
      /*
      uni.request({
        url: '/api/comment/add',
        method: 'POST',
        data: {
          contentId: this.contentId,
          content: this.commentContent
        },
        success: (res) => {
          this.commentContent = '';
          this.loadComments();
          this.contentInfo.commentCount += 1;
        }
      });
      */
    }
  }
}
</script>

<style lang="scss">
.content-detail {
  background-color: #fff;
  min-height: 100vh;
  padding-bottom: 120rpx;
  
  /* 图片轮播区域 */
  .image-swiper {
    width: 100%;
    height: 500rpx;
    
    .swiper-image {
      width: 100%;
      height: 100%;
    }
  }
  
  /* 动态内容 */
  .content-box {
    padding: 30rpx;
    border-bottom: 1rpx solid #f0f0f0;
    
    .content-text {
      font-size: 32rpx;
      line-height: 1.6;
      color: #333;
    }
  }
  
  /* 发布信息 */
  .meta-info {
    padding: 20rpx 30rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1rpx solid #f0f0f0;
    
    .user-info {
      display: flex;
      align-items: center;
      
      .user-avatar {
        width: 60rpx;
        height: 60rpx;
        border-radius: 50%;
        margin-right: 15rpx;
      }
      
      .meta-user {
        font-size: 28rpx;
        color: #333;
      }
    }
    
    .meta-time {
      font-size: 24rpx;
      color: #999;
    }
  }
  
  /* 互动数据 */
  .interaction-stats {
    padding: 25rpx 30rpx;
    display: flex;
    border-bottom: 1rpx solid #f0f0f0;
    
    .stat-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-right: 50rpx;
      
      .stat-num {
        font-size: 28rpx;
        color: #333;
        font-weight: bold;
        margin-bottom: 5rpx;
      }
      
      .stat-label {
        font-size: 24rpx;
        color: #999;
      }
      
      &:last-child {
        margin-right: 0;
      }
    }
  }
  
  /* 评论区 */
  .comment-section {
    padding: 30rpx;
    
    .comment-title {
      font-size: 30rpx;
      font-weight: bold;
      margin-bottom: 20rpx;
      color: #333;
    }
    
    .no-comment {
      text-align: center;
      font-size: 28rpx;
      color: #999;
      padding: 40rpx 0;
    }
    
    .comment-list {
      .comment-item {
        padding: 20rpx 0;
        border-bottom: 1rpx solid #f5f5f5;
        
        .comment-user-info {
          display: flex;
          align-items: center;
          margin-bottom: 10rpx;
          
          .comment-user {
            font-size: 28rpx;
            color: #576b95;
            margin-right: 15rpx;
          }
          
          .comment-time {
            font-size: 24rpx;
            color: #999;
          }
        }
        
        .comment-text {
          font-size: 28rpx;
          color: #333;
          line-height: 1.5;
        }
      }
    }
  }
  
  /* 评论输入框 */
  .comment-input {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: #fff;
    padding: 20rpx;
    display: flex;
    align-items: center;
    border-top: 1rpx solid #eee;
    z-index: 100;
    
    .input-box {
      flex: 1;
      background-color: #f5f5f5;
      height: 70rpx;
      border-radius: 35rpx;
      padding: 0 30rpx;
      font-size: 28rpx;
      margin-right: 20rpx;
    }
    
    .send-btn {
      background-color: #4285f4;
      color: #fff;
      font-size: 28rpx;
      height: 70rpx;
      line-height: 70rpx;
      padding: 0 30rpx;
      border-radius: 35rpx;
      margin: 0;
      
      &::after {
        border: none;
      }
    }
  }
}
</style>