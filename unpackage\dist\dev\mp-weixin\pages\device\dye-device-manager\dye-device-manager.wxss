/* 水平间距 */
/* 水平间距 */
/* 全局样式修复 - 确保页面占满屏幕宽度 */
page {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
}

/* 通用容器样式 */
.container, .page-container {
  width: 100%;
  min-height: 100vh;
  box-sizing: border-box;
}

/* 修复小程序页面宽度问题 */
uni-page-body {
  width: 100% !important;
}

/* 确保所有view元素正确继承宽度 */
view {
  box-sizing: border-box;
}
.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f8f8f8;
}
.device-list {
  flex: 1;
  overflow: hidden;
  padding: 10px;
}
.device-list .uni-list-item__content {
  padding-right: 15px;
}
.action-buttons {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 8px;
  margin-top: 8px;
}
.action-buttons .action-btn {
  margin: 0;
  padding: 0 12px;
  height: 28px;
  line-height: 28px;
  font-size: 12px;
  border-radius: 4px;
}
.action-buttons .action-btn.mini-btn {
  min-width: 60px;
}
.empty-tip {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 0;
  color: #999;
  font-size: 14px;
}
.empty-tip image {
  width: 120px;
  height: 120px;
  margin-bottom: 20px;
  opacity: 0.6;
}

/* 分配权限弹窗样式 */
.popup-content {
  background-color: #fff;
  border-radius: 16px 16px 0 0;
  padding: 20px;
}
.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}
.popup-header .popup-title {
  font-size: 18px;
  font-weight: bold;
  color: #333;
}
.popup-body {
  padding: 10px 0;
}
.popup-body .form-item {
  margin-bottom: 20px;
}
.popup-body .form-item .label {
  display: block;
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
}
.popup-body .form-item .value {
  display: block;
  font-size: 15px;
  color: #333;
  padding: 8px 0;
}
.popup-body .form-item .picker {
  padding: 10px;
  border: 1px solid #eee;
  border-radius: 4px;
  font-size: 15px;
  color: #333;
}
.popup-footer {
  display: flex;
  justify-content: space-between;
  margin-top: 20px;
}
.popup-footer .footer-btn {
  flex: 1;
  height: 44px;
  line-height: 44px;
  border-radius: 22px;
  font-size: 16px;
}
.popup-footer .footer-btn.cancel-btn {
  background-color: #f8f8f8;
  color: #666;
  margin-right: 15px;
}
.popup-footer .footer-btn.confirm-btn {
  background-color: #2979ff;
  color: #fff;
}
.popup-footer .footer-btn[disabled] {
  opacity: 0.6;
}

/* 新增的绑定按钮样式 */
.header-actions {
  padding: 10px 15px;
  background-color: #fff;
  border-bottom: 1px solid #f5f5f5;
}
.header-actions .bind-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 36px;
  font-size: 14px;
}
.header-actions .bind-btn .uni-icons {
  margin-right: 5px;
}