"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      messages: []
    };
  },
  methods: {
    goBack() {
      common_vendor.index.navigateBack();
    },
    markAllAsRead() {
      if (this.messages.length === 0) {
        common_vendor.index.showToast({
          title: "暂无消息",
          icon: "none"
        });
        return;
      }
      common_vendor.index.showToast({
        title: "已全部标记为已读",
        icon: "success"
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.o((...args) => $options.markAllAsRead && $options.markAllAsRead(...args))
  };
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/my/message.js.map
