"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_request = require("../../utils/request.js");
const _sfc_main = {
  data() {
    return {
      staffList: [],
      currentUser: null,
      allMembersList: [],
      totalMemberCount: 0
    };
  },
  onLoad() {
    this.loadStaffList();
    this.loadCurrentUser();
  },
  onShow() {
    this.loadStaffList();
  },
  methods: {
    getPosition(positionValue) {
      if (positionValue == 0) {
        return "店长";
      } else if (positionValue == 2) {
        return "管理员";
      } else if (positionValue == 3) {
        return "店员";
      }
      return "未知成员";
    },
    goBack() {
      common_vendor.index.navigateBack();
    },
    async loadCurrentUser() {
      const response = await utils_request.apiService.mall.stores.listStoreMember();
      this.allMembersList = response.data;
    },
    loadStaffList() {
      const staffData = common_vendor.index.getStorageSync("staffList");
      if (staffData) {
        this.staffList = JSON.parse(staffData);
      } else {
        this.staffList = [];
      }
      this.updateMembersList();
    },
    updateMembersList() {
      this.allMembersList = [];
      if (this.currentUser) {
        this.allMembersList.push(this.currentUser);
      }
      this.staffList.forEach((staff) => {
        this.allMembersList.push({
          ...staff,
          isCurrentUser: false
        });
      });
      this.totalMemberCount = this.allMembersList.length;
    },
    addStaff() {
      common_vendor.index.navigateTo({
        url: "/pages/my/add-member"
      });
    },
    editStaff(staff) {
      common_vendor.index.showToast({
        title: "编辑功能开发中",
        icon: "none"
      });
    },
    deleteStaff(staff) {
      common_vendor.index.showToast({
        title: "删除功能开发中",
        icon: "none"
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.t($data.totalMemberCount),
    b: $data.allMembersList.length === 0
  }, $data.allMembersList.length === 0 ? {} : {}, {
    c: common_vendor.f($data.allMembersList, (staff, index, i0) => {
      return {
        a: common_vendor.t(staff.name),
        b: common_vendor.t(staff.phone),
        c: common_vendor.t($options.getPosition(staff.position)),
        d: index
      };
    }),
    d: common_vendor.o((...args) => $options.addStaff && $options.addStaff(...args))
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/my/staff-manage.js.map
