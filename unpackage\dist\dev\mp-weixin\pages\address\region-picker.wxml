<view class="region-picker"><view class="tab-bar"><view class="{{['tab-item', b && 'active']}}" bindtap="{{c}}"><text>{{a}}</text></view><view wx:if="{{d}}" class="{{['tab-item', f && 'active']}}" bindtap="{{g}}"><text>{{e}}</text></view><view wx:if="{{h}}" class="{{['tab-item', j && 'active']}}" bindtap="{{k}}"><text>{{i}}</text></view></view><scroll-view scroll-y class="region-list"><block wx:if="{{l}}"><view wx:for="{{m}}" wx:for-item="item" wx:key="c" class="region-item" bindtap="{{item.d}}"><text>{{item.a}}</text><text wx:if="{{item.b}}" class="check-icon">✓</text></view></block><block wx:elif="{{n}}"><view wx:for="{{o}}" wx:for-item="item" wx:key="c" class="region-item" bindtap="{{item.d}}"><text>{{item.a}}</text><text wx:if="{{item.b}}" class="check-icon">✓</text></view></block><block wx:elif="{{p}}"><view wx:for="{{q}}" wx:for-item="item" wx:key="c" class="region-item" bindtap="{{item.d}}"><text>{{item.a}}</text><text wx:if="{{item.b}}" class="check-icon">✓</text></view></block></scroll-view></view>