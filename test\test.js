import {
  writeDataChunked,
  transferManager,
} from "../utils/writeDataChunked.js";

// import {
// 	getCommand,
// 	BLUETOOTH_COMMANDS
// } from '../utils/bleCommandSet.js';

// import { writeDataChunked } from './bluetoothTransfer.js';

// 测试调用
const wifiInfo = {
  deviceId: "BABAIYI-PSY000004",
  password: "123456789",
  SSID: "CMCC-26D-335-2.4G",
};

writeDataChunked({
  wifiInfo,
  len: 14 + 9 + 17,
});

// console.log("长度为",14 + 9 + 17)

// setTimeout(() => {transferManager.handleSuccess()},10000) 
setTimeout(() => {transferManager.handleSuccess()},600) 
setTimeout(() => {transferManager.handleSuccess()},600) 
setTimeout(() => {transferManager.handleSuccess()},600) 
setTimeout(() => {transferManager.handleSuccess()},600) 
setTimeout(() => {transferManager.handleSuccess()},600) 
setTimeout(() => {transferManager.handleSuccess()},600) 
setTimeout(() => {transferManager.handleSuccess()},600) 
setTimeout(() => {transferManager.handleSuccess()},500)
// setTimeout(() => {transferManager.handleSuccess()},500) 
// setTimeout(() => {transferManager.handleSuccess()},500) 
// setTimeout(() => {transferManager.handleSuccess()},500) 
// setTimeout(() => {transferManager.handleSuccess()},500)
// setTimeout(() => {transferManager.handleSuccess()},500) 
// setTimeout(() => {transferManager.handleSuccess()},500) 
// setTimeout(() => {transferManager.handleSuccess()},500) 
// setTimeout(() => {transferManager.handleSuccess()},500) 
// setTimeout(() => {transferManager.handleSuccess()},500) 
// setTimeout(() => {transferManager.handleSuccess()},500) 
// setTimeout(() => {transferManager.handleError(new Error('失败'))},5000) 
// setTimeout(() => {transferManager.handleError(new Error('失败'))},5000)
// transferManager.handleError();

setTimeout(() => {
  const wifiInfo = {
    deviceId: "BABAIYI-PSY000004",
    password: "123456789",
    SSID: "CMCC-26D-335-2.4G",
  };
  
  writeDataChunked({
    wifiInfo,
    len: 14 + 9 + 17,
  });
  
  setTimeout(() => {transferManager.handleSuccess()},500) 
  setTimeout(() => {transferManager.handleSuccess()},500) 
  setTimeout(() => {transferManager.handleSuccess()},500) 
  setTimeout(() => {transferManager.handleSuccess()},500) 
  setTimeout(() => {transferManager.handleSuccess()},500)
  setTimeout(() => {transferManager.handleSuccess()},500) 
  setTimeout(() => {transferManager.handleSuccess()},500) 
},3000)


setTimeout(() => {
  const wifiInfo = {
    deviceId: "BABAIYI-PSY000004",
    password: "123456789",
    SSID: "CMCC-26D-335-2.4G",
  };
  
  writeDataChunked({
    wifiInfo,
    len: 14 + 9 + 17,
  });
  
  setTimeout(() => {transferManager.handleSuccess()},500) 
  setTimeout(() => {transferManager.handleSuccess()},500) 
  setTimeout(() => {transferManager.handleSuccess()},500) 
  setTimeout(() => {transferManager.handleSuccess()},500) 
  setTimeout(() => {transferManager.handleSuccess()},500)
  setTimeout(() => {transferManager.handleSuccess()},500) 
  setTimeout(() => {transferManager.handleSuccess()},500) 
},8000)

setTimeout(() => {
  const wifiInfo = {
    deviceId: "BABAIYI-PSY000004",
    password: "123456789",
    SSID: "CMCC-26D-335-2.4G",
  };
  
  writeDataChunked({
    wifiInfo,
    len: 14 + 9 + 17,
  });
  
  setTimeout(() => {transferManager.handleSuccess()},500) 
  setTimeout(() => {transferManager.handleSuccess()},500) 
  setTimeout(() => {transferManager.handleSuccess()},500) 
  setTimeout(() => {transferManager.handleSuccess()},500) 
  setTimeout(() => {transferManager.handleSuccess()},500)
  setTimeout(() => {transferManager.handleSuccess()},500) 
  setTimeout(() => {transferManager.handleSuccess()},500) 
},15000)

// transferManager.handleSuccess()
// transferManager.handleError();
// transferManager.handleError();
// transferManager.handleSuccess()
// transferManager.handleSuccess()
// transferManager.handleSuccess()
// transferManager.handleSuccess()
// transferManager.handleSuccess()
// transferManager.handleSuccess()
// transferManager.handleSuccess()
// transferManager.handleSuccess()
// transferManager.handleSuccess()
// transferManager.handleError();
// transferManager.handleError();
// transferManager.handleError();
// transferManager.handleError();
// transferManager.handleError();
// transferManager.handleError();
// transferManager.handleError();
// transferManager.handleError();
// transferManager.handleError();
// transferManager.handleError();

// 连接上之后生成指令
// const command = getCommand('bluetoothSends', 30);
// console.log(command);


// setTimeout(() => {transferManager.handleSuccess()},500) 
// setTimeout(() => {transferManager.handleSuccess()},500)


