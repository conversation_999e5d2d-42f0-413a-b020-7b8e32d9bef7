"use strict";
require("../common/vendor.js");
function debounce(func, delay = 300) {
  let timeoutId;
  return function(...args) {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => func.apply(this, args), delay);
  };
}
class DebounceHelper {
  constructor() {
    this.processingMap = /* @__PURE__ */ new Map();
  }
  /**
   * 检查某个操作是否正在处理中
   * @param {string} key 操作标识
   * @returns {boolean} 是否正在处理中
   */
  isProcessing(key) {
    return this.processingMap.get(key) === true;
  }
  /**
   * 开始处理某个操作
   * @param {string} key 操作标识
   */
  start(key) {
    this.processingMap.set(key, true);
  }
  /**
   * 结束处理某个操作
   * @param {string} key 操作标识
   */
  end(key) {
    this.processingMap.set(key, false);
  }
  /**
   * 重置所有操作状态
   */
  reset() {
    this.processingMap.clear();
  }
  /**
   * 带超时的操作处理
   * @param {string} key 操作标识
   * @param {number} timeout 超时时间（毫秒），默认10秒
   */
  startWithTimeout(key, timeout = 1e4) {
    this.start(key);
    setTimeout(() => {
      this.end(key);
    }, timeout);
  }
}
const buttonDebounce = {
  data() {
    return {
      buttonStates: {}
    };
  },
  methods: {
    /**
     * 检查按钮是否被禁用
     * @param {string} buttonKey 按钮标识
     * @returns {boolean} 是否被禁用
     */
    isButtonDisabled(buttonKey) {
      return this.buttonStates[buttonKey] === true;
    },
    /**
     * 禁用按钮
     * @param {string} buttonKey 按钮标识
     * @param {number} timeout 自动启用的超时时间（毫秒），默认10秒
     */
    disableButton(buttonKey, timeout = 1e4) {
      this.$set(this.buttonStates, buttonKey, true);
      setTimeout(() => {
        this.enableButton(buttonKey);
      }, timeout);
    },
    /**
     * 启用按钮
     * @param {string} buttonKey 按钮标识
     */
    enableButton(buttonKey) {
      this.$set(this.buttonStates, buttonKey, false);
    },
    /**
     * 重置所有按钮状态
     */
    resetAllButtons() {
      this.buttonStates = {};
    }
  }
};
exports.DebounceHelper = DebounceHelper;
exports.buttonDebounce = buttonDebounce;
exports.debounce = debounce;
//# sourceMappingURL=../../.sourcemap/mp-weixin/utils/debounce.js.map
