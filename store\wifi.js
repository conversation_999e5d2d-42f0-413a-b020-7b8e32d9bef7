import {
	api,
	apiService,
	request
} from "../utils/request";
import dyeConsumptionManager from '../utils/dyeConsumptionManager.js';

const BASE_URL = "https://www.narenqiqige.com";
// const BASE_URL = "http://127.0.0.1:6549";

let isPolling = false;
let currentRequest = null;
let forcedshutdown = false;

// 重连相关变量
let reconnectTimer = null;
let reconnectAttempts = 0;
const MAX_RECONNECT_ATTEMPTS = 5;
const RECONNECT_INTERVAL = 3000; // 3秒重连间隔

// SSE连接持久化相关变量
let currentSessionId = null;
let lastEventId = 0;
let connectionPersistent = true; // 连接持久化开关
let isReconnecting = false;

// 会话持久化键名
const SESSION_STORAGE_KEY = 'wifi_session_info';

// 从本地存储恢复会话信息
function restoreSessionInfo(deviceId) {
	try {
		const sessionInfo = uni.getStorageSync(SESSION_STORAGE_KEY);
		if (sessionInfo && sessionInfo.deviceId === deviceId) {
			currentSessionId = sessionInfo.sessionId;
			lastEventId = sessionInfo.lastEventId || 0;
			console.log('恢复会话信息:', { currentSessionId, lastEventId, deviceId });
			return true;
		}
	} catch (error) {
		console.error('恢复会话信息失败:', error);
	}
	return false;
}

// 保存会话信息到本地存储
function saveSessionInfo(deviceId) {
	try {
		const sessionInfo = {
			deviceId,
			sessionId: currentSessionId,
			lastEventId,
			timestamp: Date.now()
		};
		uni.setStorageSync(SESSION_STORAGE_KEY, sessionInfo);
		console.log('保存会话信息:', sessionInfo);
	} catch (error) {
		console.error('保存会话信息失败:', error);
	}
}

// 清除会话信息
function clearSessionInfo() {
	try {
		uni.removeStorageSync(SESSION_STORAGE_KEY);
		currentSessionId = null;
		lastEventId = 0;
		console.log('清除会话信息');
	} catch (error) {
		console.error('清除会话信息失败:', error);
	}
}

// 页面可见性状态
let isPageVisible = true;
let currentDeviceId = null; // 当前连接的设备ID

function decodeUint8Array(data) {
	return String.fromCharCode.apply(null, new Uint8Array(data));
}

// 新增：安全的Base64解码函数（兼容微信小程序环境）
function safeBase64Decode(base64) {
	// 微信小程序环境
	if (typeof wx !== 'undefined' && wx.base64ToArrayBuffer) {
		try {
			const arrayBuffer = wx.base64ToArrayBuffer(base64);
			return arrayBufferToString(arrayBuffer);
		} catch (e) {
			console.warn('微信base64ToArrayBuffer转换失败', e);
		}
	}

	// 浏览器环境（使用polyfill）
	if (typeof atob !== 'undefined') {
		try {
			return decodeURIComponent(escape(atob(base64)));
		} catch (e) {
			console.warn('atob转换失败', e);
			return atob(base64); // 返回原始结果
		}
	}

	// 终极降级方案
	console.error('当前环境不支持Base64解码');
	return base64;
}

// 初始化页面可见性监听
function initVisibilityListener() {
	// 微信小程序环境
	if (typeof wx !== 'undefined') {
		// 监听小程序显示
		wx.onAppShow(() => {
			console.log('小程序回到前台');
			isPageVisible = true;
			handlePageVisible();
		});

		// 监听小程序隐藏
		wx.onAppHide(() => {
			console.log('小程序进入后台');
			isPageVisible = false;
			handlePageHidden();
		});
	}
	// H5环境
	else if (typeof document !== 'undefined') {
		document.addEventListener('visibilitychange', () => {
			if (document.hidden) {
				console.log('页面进入后台');
				isPageVisible = false;
				handlePageHidden();
			} else {
				console.log('页面回到前台');
				isPageVisible = true;
				handlePageVisible();
			}
		});
	}
}

// 处理页面变为可见
function handlePageVisible() {
	if (currentDeviceId && connectionPersistent) {
		console.log('页面回到前台，检查SSE连接状态');

		// 如果当前没有连接或连接已断开，尝试重连
		if (!isPolling && !isReconnecting) {
			console.log('SSE连接已断开，尝试续接连接');
			reconnectWithResume(currentDeviceId);
		}
	}
}

// 处理页面变为隐藏
function handlePageHidden() {
	console.log('页面进入后台，保持SSE连接');
	// 不主动断开连接，让连接在后台保持
}

// 带续接功能的重连
function reconnectWithResume(deviceId) {
	const app = getApp()
	if(app.isBluetoothConnection()) {
		return false;
	}
	if (isPolling || isReconnecting) {
		console.log('连接正在进行中，跳过续接');
		return;
	}

	console.log('开始续接连接:', {
		deviceId,
		currentSessionId,
		lastEventId
	});

	// 使用续接端点
	const reconnectUrl = `${BASE_URL}/api/device/reconnect/${deviceId}`;
	const params = {
		lastEventId: lastEventId,
		sessionId: currentSessionId
	};

	// 构建请求参数
	const queryString = Object.keys(params)
		.filter(key => params[key] !== null && params[key] !== undefined)
		.map(key => `${key}=${encodeURIComponent(params[key])}`)
		.join('&');

	const fullUrl = queryString ? `${reconnectUrl}?${queryString}` : reconnectUrl;

	console.log('续接连接URL:', fullUrl);

	isPolling = true;
	isReconnecting = true;

	const requestParams = {
		url: fullUrl,
		method: 'POST',
		timeout: 0, // 设置为0表示不超时，保持长连接
		header: {
			'Accept': 'text/event-stream',
			'Cache-Control': 'no-cache'
		},
		enableChunked: true,
		success(res) {
			console.log("SSE续接连接请求成功:", res);
			reconnectAttempts = 0;
			isReconnecting = false;
			const app = getApp();
			// 注意：这里不立即设置 deviceStatus = true，等收到第一个消息后再设置
			app.setConnectionMethod('wifi');
		},
		fail(err) {
			console.error("SSE续接连接失败:", err);
			console.error("续接错误详情:", {
				errMsg: err.errMsg,
				statusCode: err.statusCode,
				data: err.data,
				header: err.header
			});
			isPolling = false;
			isReconnecting = false;

			// 根据错误类型决定处理方式
			if (err.errMsg && err.errMsg.includes('timeout')) {
				console.log("续接连接超时，这可能是正常的长连接行为，尝试重连");
			} else if (err.statusCode === 404) {
				console.log("会话不存在，创建新连接");
				// 清除会话信息，创建新连接
				clearSessionInfo();
			}

			// 如果续接失败，尝试普通重连
			scheduleReconnect(deviceId);
		},
		complete() {
			console.log("SSE续接连接完成");
			isPolling = false;
			isReconnecting = false;
		}
	};

	// 处理数据接收
	requestParams.success = function(res) {
		// 处理SSE数据流
		handleSSEData(res.data, deviceId);
	};

	currentRequest = wx.request(requestParams);
}

// 处理SSE数据流
function handleSSEData(data, deviceId) {
	const app = getApp();

	try {
		// 如果是强制关闭那么就直接返回
		if(forcedshutdown) {
			console.warn("当前为强制关闭连接阶段,直接返回")
			return;
		}
		
		// 更健壮的条件判断
		// const shouldStop = (
		// 	isPolling === false && 
		// 	isReconnecting === false
		// );

		// console.warn("设备轮询结果", {
		// 	isPolling,
		// 	currentRequest: !!currentRequest,
		// 	isReconnecting
		// });

		
		// if (shouldStop) {
		// 	console.warn("轮询未运行", {
		// 		isPolling,
		// 		isReconnecting
		// 	});
		// 	return;
		// }

		// 将连接设为wifi
		app.setConnectionMethod('wifi');

		console.log("原始数组", data);

		if (data instanceof ArrayBuffer) {
			data = decodeUint8Array(data);
		}
		console.log("解析后数据类型", typeof data);
		console.log("解析之后的数据", data);

		try {
			// 解析SSE数据格式
			const lines = data.split('\n').filter(line => line.trim() !== '');

			for (const line of lines) {
				// 处理SSE事件ID
				if (line.startsWith('id:')) {
					const eventId = parseInt(line.slice(3).trim());
					if (!isNaN(eventId)) {
						const oldEventId = lastEventId;
						lastEventId = Math.max(lastEventId, eventId);
						console.log("更新事件ID:", lastEventId);
						// 如果事件ID有更新，保存会话信息
						if (lastEventId > oldEventId && currentSessionId) {
							saveSessionInfo(currentDeviceId);
						}
					}
					continue;
				}

				// 处理SSE事件名称
				if (line.startsWith('event:')) {
					const eventName = line.slice(6).trim();
					console.log("接收到事件:", eventName);
					continue;
				}

				// 处理数据内容
				let jsonStr = null;
				if (line.startsWith('data:')) {
					jsonStr = line.slice(5).trim();
				} else if (line.trim().startsWith('{')) {
					jsonStr = line.trim();
				}

				if (jsonStr) {
					console.log("准备解析JSON字符串", jsonStr);
					const jsonData = JSON.parse(jsonStr);
					console.log("接收到的数据", jsonData);

					// 提取会话ID（如果存在）
					if (jsonData.sessionId) {
						currentSessionId = jsonData.sessionId;
						console.log("更新会话ID:", currentSessionId);
						// 保存会话信息到本地存储
						saveSessionInfo(currentDeviceId);
					}

					// 处理特殊的连接消息
					handleSSEMessage(jsonData);
				}
			}
		} catch (e) {
			console.error('JSON 解析失败:', e);
			app.setErrorState({
				errorType: 'WIFI_PARSE_ERROR',
				errorMessage: `WiFi数据解析失败: ${e.message}`,
				errorCode: null
			});
		}
	} catch (error) {
		console.error("分块数据解析失败:", error);
	}
}

export const startPollingWifi = async (connectedDeviceId) => {
	if (isPolling) {
		console.warn("SSE连接已在运行中");
		return;
	}

	// 把强制连接置为false
	forcedshutdown = false

	const app = getApp();
	let deviceId;
	if (!connectedDeviceId) {
		deviceId = app.globalData.connectedDeviceId;
	} else {
		app.globalData.connectedDeviceId = connectedDeviceId
		deviceId = connectedDeviceId;
	}

	// 记录当前设备ID
	currentDeviceId = deviceId;

	// 尝试恢复会话信息（如果没有当前会话信息）
	if (!currentSessionId && !lastEventId) {
		const restored = restoreSessionInfo(deviceId);
		if (restored) {
			console.log('成功恢复会话信息，将尝试续接连接');
		}
	}

	// 初始化页面可见性监听（只初始化一次）
	if (!isPageVisible) {
		initVisibilityListener();
		isPageVisible = true;
	}

	isPolling = true;

	// 判断是否为续接连接
	const isReconnect = isReconnecting || (currentSessionId && lastEventId > 0);
	const connectUrl = isReconnect ?
		`${BASE_URL}/api/device/reconnect/${deviceId}` :
		`${BASE_URL}/api/device/connectSee/${deviceId}`;

	console.log(`开始SSE连接，URL: ${connectUrl}`, {
		isReconnect,
		sessionId: currentSessionId,
		lastEventId
	});

	// 获取存储的token
	const token = uni.getStorageSync('token');

	app.setConnectionMethod('wifi')

	// 构建请求参数
	const requestParams = {
		url: connectUrl,
		method: "POST",
		enableChunked: true,
		timeout: 0, // 设置为0表示不超时，保持长连接
		header: {
			"content-type": "application/json",
			"cache-control": "no-cache",
			...(token ? {
				'Authorization': `Bearer ${token}`
			} : {})
		}
	};

	// 如果是续接连接，添加查询参数
	if (isReconnect) {
		const queryParams = [];
		if (lastEventId > 0) {
			queryParams.push(`lastEventId=${encodeURIComponent(lastEventId.toString())}`);
		}
		if (currentSessionId) {
			queryParams.push(`sessionId=${encodeURIComponent(currentSessionId)}`);
		}
		if (queryParams.length > 0) {
			requestParams.url += '?' + queryParams.join('&');
		}
	}

	currentRequest = wx.request({
		...requestParams,
		success(res) {
			console.log("SSE连接请求成功:", res);
			// 重置重连计数和状态
			reconnectAttempts = 0;
			isReconnecting = false;
			// 注意：这里不立即设置 deviceStatus = true，等收到第一个消息后再设置
			app.setConnectionMethod('wifi');
		},
		fail(err) {
			// 如果是强制关闭那么就直接返回
			if(forcedshutdown) {
				console.warn("当前正在强制关闭")
				return;
			}

			console.error("SSE连接失败:", err);
			console.error("错误详情:", {
				errMsg: err.errMsg,
				statusCode: err.statusCode,
				data: err.data,
				header: err.header
			});
			isPolling = false;

			// 根据错误类型决定是否重连
			if (err.errMsg && err.errMsg.includes('timeout')) {
				console.log("连接超时，这可能是正常的长连接行为，尝试重连");
			} else if (err.statusCode && err.statusCode >= 500) {
				console.log("服务器错误，延迟重连");
			} else {
				console.log("其他连接错误，尝试重连");
			}

			// 启动重连逻辑
			scheduleReconnect(deviceId);
		},
		complete() {
			console.log("SSE连接完成");
			isPolling = false;
			// 如果启用了连接持久化且不是主动停止，启动重连
			if (connectionPersistent && reconnectAttempts < MAX_RECONNECT_ATTEMPTS) {
				scheduleReconnect(deviceId);
			}
		}
	});

	if (typeof currentRequest.onChunkReceived === 'function') {
		currentRequest.onChunkReceived(async (chunk) => {
			await handleSSEData(chunk.data, deviceId);
		});
	} else {
		console.error('当前环境不支持onChunkReceived方法');
		startFallbackPolling(deviceId);
	}
};


function stringToHex(str) {
	let hex = '';
	for (let i = 0; i < str.length; i++) {
		hex += str.charCodeAt(i).toString(16).padStart(2, '0');
	}
	return hex;
};

// 继续发送下一个指令对象
export const executeNextCommand = async () => {
	const app = getApp()


	const command = app.globalData.commandQueue[0];
	console.log("当前执行的指令", command)
	console.log("当前指令队列", app.globalData.commandQueue)

	if (!command) {
		const response = await apiService.mall.command.allEnd(app.globalData.connectedDeviceId)
		if (response.code == 200) {
			console.log("服务器已发送全部出料完成")
			// 清空全局状态
			app.allExecutionCompleted()
		}
		return
	}

	app.setCurrentCommand(command);
	// commit('SET_EXECUTING_QUEUE',true)
	console.log("指令", command.value)
	// commit('ADD_LOG', `开始执行指令: ${command.description}`);
	console.log(`开始执行指令: ${command.description}`)
	// 发送指令
	// 网络请求到后端接口
	try {
		const deviceCode = app.globalData.connectedDeviceId;
		if (deviceCode) {
			// 准备指令数据发送给后端
			const commandData = {
				value: Array.from(command.value).map(b => b.toString(16).padStart(2, '0')).join(''),
				description: command.description
			}

			console.log("🌐 创建后端指令记录:", command);
			const response = await apiService.mall.command.sendCommandin(deviceCode, commandData);

			if (response.code === 200) {
				// && response.data && response.data.commandIds
				console.log("✅ 后端指令记录创建成功，获得ID列表:", response.data.commandIds);

			} else {
				console.warn("⚠️ 后端指令记录创建失败:", response.message);
				commands.forEach(cmd => {
					cmd.backendCreated = false;
				});
			}
		}
	} catch (error) {
		console.error("❌ 创建后端指令记录异常:", error);
	}
	// 将app里面的当前执行指令放入
	app.globalData.currentCommand = command;
	console.log(`指令发送成功: ${command.description}`)


	// 指令执行成功，从队列移除
	app.removeFromCommandQueue();
	// 注意：不要立即清空 currentCommand，保留到出料完成时再清空
}


// 处理WiFi指令
// async function handleWifiCommand(data, commandHex) {
// 	try {
// 		const app = getApp();
// 		const deviceCode = app.globalData.connectedDeviceId;

// 		console.log("操作码", commandHex)
// 		switch (commandHex) {
// 			case '24':
// 				console.log('WiFi收到单次出料完成指令:',commandHex);
// 				if (data.colorCode && data.weight) {
// 					await recordWifiDyeConsumption(deviceCode, data.colorCode, data.weight);
// 				}

// 				// 继续发送下料指令
// 				executeNextCommand()

// 				// app.updateCommandExecutionDetail({
// 				// 	progress: 50,
// 				// 	currentStep: '单次出料完成',
// 				// });
// 				break;

// 			case '25':
// 				console.log('WiFi收到全部出料完成指令:', data);
// 				app.allExecutionCompleted()
// 				break;

// 			case '05':
// 				console.log('WiFi收到已放碗指令');
// 				app.globalData.wan_color = true
// 				app.updateCommandExecutionDetail({
// 					currentStep: '已放碗',
// 					progress: 10
// 				});
// 				break;

// 			// 注意：控制指令响应(0x21、0x27、0x22)通过SSE的commandResult消息处理
// 			// 这里不再重复处理，避免双重处理

// 			default:
// 				console.log('WiFi收到未知指令:', commandHex);
// 				app.setErrorState({
// 					errorType: 'UNKNOWN_WIFI_COMMAND',
// 					errorMessage: `未知WiFi指令: ${commandHex}`,
// 					errorCode: commandHex
// 				});
// 		}
// 	} catch (error) {
// 		console.error('处理WiFi指令失败:', error);
// 	}
// }

// 记录WiFi染膏消耗
async function recordWifiDyeConsumption(deviceCode, colorCode, weight) {
	try {
		if (!deviceCode) {
			console.warn('设备编码为空，无法记录染膏消耗');
			return;
		}

		const app = getApp();
		const currentCommand = app.globalData.currentCommand;
		const commandId = currentCommand ? currentCommand.backendId : null;

		const consumptionData = {
			consumption: {
				[colorCode]: {
					amount: weight,
					colorCode: colorCode
				}
			}
		};

		const success = await dyeConsumptionManager.recordConsumption(
			deviceCode,
			consumptionData,
			commandId
		);

		if (success) {
			app.updateDyeConsumption({
				currentUsage: {
					[colorCode]: weight
				},
				totalConsumption: app.globalData.dyeConsumption.totalConsumption + weight
			});
			console.log(`✅ WiFi染膏消耗记录成功: 颜色${colorCode}, 用量${weight}g, 后端指令ID: ${commandId}`);
		} else {
			console.log(`WiFi染膏消耗记录失败`);
		}
	} catch (error) {
		console.error('WiFi记录染膏消耗失败:', error);
	}
}

function calculateProgress(status) {
	const progressMap = {
		'已放碗': 10,
		'开始出料': 20,
		'出料中': 50,
		'出料完成': 100,
		'待机': 0
	};
	return progressMap[status] || 0;
}

// export const stopPollingWifi = () => {
// 	if (!isPolling) {
// 		console.warn("轮询未运行");
// 		return;
// 	}

// 	isPolling = false;

// 	// 清除重连定时器
// 	resetReconnectState();

// 	if (currentRequest) {
// 		currentRequest.abort();
// 		currentRequest = null;
// 	}

// 	// 清除设备状态和 store 状态
// 	const app = getApp();
// 	if (app) {
// 		app.globalData.deviceStatus = false;

// 		// 清除 Vuex store 中的连接状态
// 		const store = app.$store || app.store;
// 		if (store && store.commit) {
// 			store.commit('CLEAR_CONNECTED_DEVICE');
// 			console.log('WiFi连接停止，已清除store状态');
// 		}
// 	}

// 	// 清除会话信息
// 	clearSessionInfo();

// 	console.log("分块传输轮询已停止");
// };

export const stopPollingWifi = () => {

    if (!isPolling && !currentRequest && !isReconnecting) {
        console.warn("轮询未运行");
        return;
    }


	// 标记为停止状态
    isPolling = false;
    isReconnecting = false;

	// console.warn("当前停止状态",{
	// 	isPolling,
	// 	isReconnecting
	// })

    // 清除重连定时器
    resetReconnectState();

    // 关闭当前请求
    if (currentRequest) {
        try {
            // 标准HTTP请求关闭方式
            if (currentRequest.abort) {
				// 将强制关闭的开关打开
				forcedshutdown = true;
                currentRequest.abort();
            }
            
            // 微信小程序特定关闭方式
            if (typeof wx !== 'undefined' && currentRequest.close) {
				// 将强制关闭的开关打开
				forcedshutdown = true;
                currentRequest.close();
            }
            
            console.log("SSE连接已强制关闭");
        } catch (e) {
            console.error("关闭连接时出错:", e);
        } finally {
            currentRequest = null;
        }
    }

    // 清除设备状态和 store 状态
    const app = getApp();
    if (app) {
        app.globalData.deviceStatus = false;

        // 清除 Vuex store 中的连接状态
        const store = app.$store || app.store;
        if (store && store.commit) {
            store.commit('CLEAR_CONNECTED_DEVICE');
            console.log('WiFi连接停止，已清除store状态');
        }
    }

    // 清除会话信息
    clearSessionInfo();

    console.log("分块传输轮询已完全停止");
};


// 降级轮询方案
function startFallbackPolling(deviceId) {
	const pollInterval = setInterval(async () => {
		if(app.isBluetoothConnection()) {
			return false;
		}
		
		if (!isPolling) {
			clearInterval(pollInterval);
			return;
		}

		try {
			const res = await request({
				url: `/api/device/connectSee/${deviceId}`,
				method: "POST"
			});
			console.log('轮询响应:', res);
		} catch (error) {
			console.error('轮询请求失败:', error);
		}
	}, 3000);
}

// 重连调度函数
function scheduleReconnect(deviceId) {
	if (reconnectAttempts >= MAX_RECONNECT_ATTEMPTS) {
		const app = getApp()
		app.scheduleReconnect('')
		app.globalData.deviceStatus = false;

		// 清除 Vuex store 中的连接状态
		const store = app.$store || app.store;
		if (store && store.commit) {
			store.commit('CLEAR_CONNECTED_DEVICE');
			console.log('达到最大重连次数，已清除store状态');
		}

		// 清除会话信息
		clearSessionInfo();

		console.error('达到最大重连次数，停止重连');
		return;
	}

	if (reconnectTimer) {
		clearTimeout(reconnectTimer);
	}

	reconnectAttempts++;
	isReconnecting = true; // 标记为重连状态
	console.log(`准备第${reconnectAttempts}次重连，${RECONNECT_INTERVAL/1000}秒后开始`);

	reconnectTimer = setTimeout(() => {
		if (!isPolling) { // 确保当前没有连接
			console.log(`开始第${reconnectAttempts}次重连`);
			startPollingWifi(deviceId);
		}
	}, RECONNECT_INTERVAL);
}

// 重置重连状态
export const resetReconnectState = () => {
	reconnectAttempts = 0;
	isReconnecting = false;
	if (reconnectTimer) {
		clearTimeout(reconnectTimer);
		reconnectTimer = null;
	}
};

// 网络状态监听
function setupNetworkListener() {
	uni.onNetworkStatusChange((res) => {
		if (res.isConnected && !isPolling && reconnectAttempts > 0) {
			console.log('网络恢复，尝试重连WiFi');
			resetReconnectState();
			const app = getApp();
			if (app.globalData.connectedDeviceId) {
				startPollingWifi(app.globalData.connectedDeviceId);
			}
		}
	});
}

// 初始化网络监听
setupNetworkListener();

// SSE消息处理函数
async function handleSSEMessage(jsonData) {
	try {
		console.log("jsonData", jsonData)

		const app = getApp()

		// 处理连接相关消息
		if (jsonData.type === 'welcome' || jsonData.type === 'reconnect') {
			// 处理设备状态消息
			if (jsonData.online === true) {
				console.log("设备在线")
				app.globalData.deviceStatus = true;

				// 更新 Vuex store 中的连接状态
				const store = app.$store || app.store;
				if (store && store.commit) {
					const mockDevice = {
						deviceId: app.globalData.connectedDeviceId,
						name: app.globalData.connectedDeviceId,
						connectionType: 'wifi'
					};
					store.commit('SET_CONNECTED_DEVICE', mockDevice);
					console.log('WiFi连接成功，已更新store状态:', mockDevice);
				}

				// 同时更新设备状态详情
				app.updateDeviceStatusDetail({
					isOnline: true,
					lastHeartbeat: new Date().toISOString(),
					connectionQuality: 90
				});
			} else if (jsonData.online === false) {
				console.log("设备离线")
				app.globalData.deviceStatus = false;

				// 清除 Vuex store 中的连接状态
				const store = app.$store || app.store;
				if (store && store.commit) {
					store.commit('CLEAR_CONNECTED_DEVICE');
					console.log('设备离线，已清除store状态');
				}

				// 同时更新设备状态详情
				app.updateDeviceStatusDetail({
					isOnline: false,
					lastHeartbeat: new Date().toISOString(),
					connectionQuality: 0
				});
			}
			if (jsonData.sessionId) {
				currentSessionId = jsonData.sessionId;
				console.log('会话ID已更新:', currentSessionId);
				// 保存会话信息到本地存储
				saveSessionInfo(currentDeviceId);
			}
			return;
		}

		// 处理历史消息标记
		if (jsonData.type === 'history_start') {
			console.log('开始接收历史消息，数量:', jsonData.count);
			return;
		}

		if (jsonData.type === 'history_end') {
			console.log('历史消息接收完成，数量:', jsonData.count);
			return;
		}

		// 处理设备上线消息
		if (jsonData.type === 'deviceOnline') {
			console.log('设备上线:', jsonData.data ? jsonData.data.deviceCode : 'unknown');
			app.globalData.deviceStatus = true;

			// 更新 Vuex store 中的连接状态
			const store = app.$store || app.store;
			if (store && store.commit) {
				const mockDevice = {
					deviceId: app.globalData.connectedDeviceId,
					name: app.globalData.connectedDeviceId,
					connectionType: 'wifi'
				};
				store.commit('SET_CONNECTED_DEVICE', mockDevice);
				console.log('设备上线，已更新store状态:', mockDevice);
			}

			app.updateDeviceStatusDetail({
				isOnline: true,
				lastHeartbeat: new Date().toISOString(),
				connectionQuality: 90
			});
			return;
		}

		// 处理设备离线消息
		if (jsonData.type === 'deviceOffline') {
			console.log('设备离线:', jsonData.data ? jsonData.data.deviceCode : 'unknown');

			// 检查是否在任务完成缓冲期内
			if (app.globalData.taskCompletionBuffer) {
				console.log('当前在任务完成缓冲期内，忽略设备离线消息');
				return;
			}

			app.globalData.deviceStatus = false;

			// 清除 Vuex store 中的连接状态
			const store = app.$store || app.store;
			if (store && store.commit) {
				store.commit('CLEAR_CONNECTED_DEVICE');
				console.log('设备离线，已清除store状态');
			}

			app.updateDeviceStatusDetail({
				isOnline: false,
				lastHeartbeat: new Date().toISOString(),
				connectionQuality: 0
			});
			return;
		}

		// 处理心跳消息
		if (jsonData.type === 'heartbeat') {
			console.log('收到设备心跳:', jsonData.data ? jsonData.data.deviceCode : 'unknown');
			app.globalData.deviceStatus = true;

			// 更新 Vuex store 中的连接状态
			const store = app.$store || app.store;
			if (store && store.commit && !store.state.connectedDevice) {
				const mockDevice = {
					deviceId: app.globalData.connectedDeviceId,
					name: app.globalData.connectedDeviceId,
					connectionType: 'wifi'
				};
				store.commit('SET_CONNECTED_DEVICE', mockDevice);
				console.log('收到心跳，已更新store状态:', mockDevice);
			}

			app.updateDeviceStatusDetail({
				isOnline: true,
				lastHeartbeat: new Date().toISOString(),
				connectionQuality: 90
			});
			return;
		}

		// 处理设备状态消息
		if (jsonData.online === true) {
			console.log("设备在线")
			app.globalData.deviceStatus = true;

			// 更新 Vuex store 中的连接状态
			const store = app.$store || app.store;
			if (store && store.commit && !store.state.connectedDevice) {
				const mockDevice = {
					deviceId: app.globalData.connectedDeviceId,
					name: app.globalData.connectedDeviceId,
					connectionType: 'wifi'
				};
				store.commit('SET_CONNECTED_DEVICE', mockDevice);
				console.log('设备状态在线，已更新store状态:', mockDevice);
			}
		} else if (jsonData.online === false) {
			console.log("设备离线")

			// 检查是否在任务完成缓冲期内
			if (app.globalData.taskCompletionBuffer) {
				console.log('当前在任务完成缓冲期内，忽略设备离线状态');
				return;
			}

			app.globalData.deviceStatus = false;

			// 清除 Vuex store 中的连接状态
			const store = app.$store || app.store;
			if (store && store.commit) {
				store.commit('CLEAR_CONNECTED_DEVICE');
				console.log('设备状态离线，已清除store状态');
			}
		}

		app.updateDeviceStatusDetail({
			isOnline: jsonData.online === 'true' || jsonData.online === true,
			lastHeartbeat: new Date().toISOString(),
			connectionQuality: 90
		});

		// 处理指令消息
		// if (jsonData.data.result) {
		// 	// const commandHex = stringToHex(jsonData.command);
		// 	// jsonData.command
		// 	await handleWifiCommand(jsonData, jsonData.data.result);
		// }

		// 处理指令结果消息
		if (jsonData.type === 'commandResult') {
			const { result, message } = jsonData.data;
			console.log('WiFi收到指令结果:', result, message);

			switch(result) {
				case 'single_complete':  // 单次出料完成
					console.log("wifi收到单次出料完成")
					const deviceCode = app.globalData.connectedDeviceId
					if (jsonData.colorCode && jsonData.weight) {
						await recordWifiDyeConsumption(deviceCode, jsonData.colorCode, jsonData.weight);
					}
					// 继续发送下料指令
					executeNextCommand()
				    break;
				case 'bowl_already_placed': // 放碗指令
					console.log('WiFi收到已放碗指令');
					app.globalData.wan_color = true
					app.globalData.commandStatus = true
					app.updateCommandExecutionDetail({
						startTime: new Date().toISOString(),
						progress: 0,
						currentStep: '执行中',
						totalSteps: Object.keys(jsonData.data).length
					});
					break;
				case 'paused':
					app.setPaused(true);
					app.setExecutingQueue(false);
					uni.showToast({ title: '设备已暂停', icon: 'success' });
					break;
				case 'resumed':
					app.setPaused(false);
					app.setExecutingQueue(true);
					uni.showToast({ title: '设备已恢复', icon: 'success' });
					break;
				case 'cancelled':
					app.setExecutingQueue(false);
					app.setPaused(false);
					app.globalData.commandQueue = [];
					uni.showToast({ title: '操作已取消', icon: 'success' });
					break;
				case 'all_end_sent':
					// 全部出料完成
					console.log('WiFi收到全部出料完成确认');
					// funallCommandEnd();
					app.allExecutionCompleted()
					break;
				case 'pause_sent':
				case 'continue_sent':
				case 'cancel_sent':
				case 'all_end_sent':
					// 指令发送成功，等待设备确认
					console.log('指令发送成功，等待设备确认');
					break;
				case 'pause_cached':
				case 'continue_cached':
				case 'cancel_cached':
				case 'all_end_cached':
					// 指令已缓存，设备离线
					uni.showToast({
						title: '设备离线，指令已缓存',
						icon: 'none',
						duration: 3000
					});
					console.log('设备离线，指令已缓存，将在设备上线后自动发送');
					break;
				case 'cached_commands_sent':
					// 缓存指令发送成功
					uni.showToast({
						title: '缓存指令已发送',
						icon: 'success'
					});
					console.log('设备上线，缓存指令发送成功');
					break;
				case 'cached_commands_partial_failed':
				case 'cached_commands_send_failed':
					// 缓存指令发送失败
					uni.showToast({
						title: '缓存指令发送失败',
						icon: 'error'
					});
					console.log('缓存指令发送失败:', message);
					break;
				case 'pause_failed':
				case 'continue_failed':
				case 'cancel_failed':
				case 'all_end_failed':
					// 指令发送失败
					uni.showToast({ title: message || '指令发送失败', icon: 'error' });
					break;
			}
		}


		// if(jsonData.deviceState) {
		// 	console.log('设备状态:', jsonData.deviceState);
		// 	if (jsonData.deviceState === 'online') {
		// 		app.globalData.deviceStatus = true
		// 	}
		// 	app.updateCommandExecutionDetail({
		// 		currentStep: jsonData.content,
		// 		progress: calculateProgress(jsonData.content)
		// 	});
		// }

		// 处理状态消息
		if (jsonData.type === 'status') {
			console.log('设备状态:', jsonData.content);
			app.updateCommandExecutionDetail({
				currentStep: jsonData.content,
				progress: calculateProgress(jsonData.content)
			});
		}

		// 处理心跳消息
		if (jsonData.type === 'heartbeat') {
			console.log('收到SSE心跳:', jsonData.timestamp);

			// 如果这是连接后的第一个心跳，标记连接成功
			const wasDisconnected = !app.globalData.deviceStatus;

			// 心跳表示连接正常，更新连接状态
			app.globalData.deviceStatus = true;

			// 更新 Vuex store 中的连接状态
			const store = app.$store || app.store;
			if (store && store.commit && !store.state.connectedDevice) {
				const mockDevice = {
					deviceId: app.globalData.connectedDeviceId,
					name: app.globalData.connectedDeviceId,
					connectionType: 'wifi'
				};
				store.commit('SET_CONNECTED_DEVICE', mockDevice);
				console.log('收到SSE心跳，已更新store状态:', mockDevice);
			}

			app.updateDeviceStatusDetail({
				isOnline: true,
				lastHeartbeat: new Date().toISOString(),
				connectionQuality: 95
			});

			// 重置重连计数
			reconnectAttempts = 0;
			isReconnecting = false;

			// 如果是从断开状态恢复，触发连接成功事件
			if (wasDisconnected) {
				console.log('🔗 SSE连接已建立 - 收到第一个心跳');
			}
		}

		if (jsonData.type === 'deviceOnline') {
			console.log('收到设备上线:', jsonData.timestamp);

			// 如果这是连接后的第一个设备上线消息，标记连接成功
			const wasDisconnected = !app.globalData.deviceStatus;

			// 设备上线表示连接正常，更新连接状态
			app.globalData.deviceStatus = true;

			// 更新 Vuex store 中的连接状态
			const store = app.$store || app.store;
			if (store && store.commit) {
				const mockDevice = {
					deviceId: app.globalData.connectedDeviceId,
					name: app.globalData.connectedDeviceId,
					connectionType: 'wifi'
				};
				store.commit('SET_CONNECTED_DEVICE', mockDevice);
				console.log('收到设备上线消息，已更新store状态:', mockDevice);
			}

			app.updateDeviceStatusDetail({
				isOnline: true,
				lastHeartbeat: new Date().toISOString(),
				connectionQuality: 95
			});

			// 重置重连计数
			reconnectAttempts = 0;
			isReconnecting = false;

			// 如果是从断开状态恢复，触发连接成功事件
			if (wasDisconnected) {
				console.log('🔗 SSE连接已建立 - 收到设备上线消息');
			}
		}

	} catch (error) {
		console.error('处理SSE消息失败:', error);
	}
}

// 获取连接状态
export const getConnectionStatus = () => {
	return {
		isPolling,
		currentSessionId,
		lastEventId,
		reconnectAttempts,
		connectionPersistent
	};
};

// 设置连接持久化
export const setConnectionPersistent = (persistent) => {
	connectionPersistent = persistent;
	console.log('连接持久化设置:', persistent);
};

// 手动断开连接
export const disconnectSSE = () => {
	connectionPersistent = false;
	
	// 执行关闭操作
    if (currentRequest) {
        // 使用Promise确保关闭完成
        new Promise((resolve) => {
            try {
                if (currentRequest.abort) {
                    currentRequest.abort();
                }
                if (typeof wx !== 'undefined' && currentRequest.close) {
                    currentRequest.close();
                }
                resolve();
            } catch (e) {
                console.error("关闭出错:", e);
                resolve();
            }
        }).then(() => {
            currentRequest = null;
            console.log('SSE连接已手动断开');
        });
    } else {
        console.log('没有活动的SSE连接');
    }

	isPolling = false;
	resetReconnectState();
	console.log('SSE连接已手动断开');
};

// 为了给前台切后台改变,能够将
export const setIsPolling = (polling) => {
	isPolling = polling
}

// 导出续传连接函数
export { reconnectWithResume };