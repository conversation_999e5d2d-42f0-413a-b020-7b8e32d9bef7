<template>
  <view class="settings-container">
    <!-- 顶部时间显示 -->
    <!-- <view class="time-display">{{currentTime}}</view> -->
    
    <!-- 设置标题 -->
    <view class="settings-title">设置</view>
    
    <!-- 设置选项列表 -->
    <view class="settings-list">
      <view class="setting-item" @click="navigateToProfile">
        <text>个人资料</text>
        <image src="/static/images/arrow-right.png" class="arrow-icon"></image>
      </view>
      
      <view class="setting-item" @click="navigateToAccountSettings">
        <text>账号设置</text>
        <image src="/static/images/arrow-right.png" class="arrow-icon"></image>
      </view>
      
      <view class="setting-item logout" @click="handleLogout">
        <text>退出登录</text>
      </view>
      
      <view class="setting-item delete-account" @click="handleDeleteAccount">
        <text>注销账号</text>
      </view>
    </view>
  </view>
</template>

<script>
import { clearUserStatus } from '@/utils/request.js'
export default {
  data() {
    return {
      currentTime: this.getCurrentTime()
    };
  },
  methods: {
    getCurrentTime() {
      const now = new Date();
      const hours = now.getHours().toString().padStart(2, '0');
      const minutes = now.getMinutes().toString().padStart(2, '0');
      return `${hours}:${minutes}`;
    },
    
    navigateToProfile() {
      uni.navigateTo({
        url: '/pages/profile/index'
      });
    },
    
    navigateToAccountSettings() {
      uni.navigateTo({
        url: '/pages/account/settings'
      });
    },
    
    handleLogout() {
      uni.showModal({
        title: '提示',
        content: '确定要退出登录吗？',
        success: (res) => {
          if (res.confirm) {
            // 执行退出登录逻辑
            clearUserStatus()
            uni.switchTab({
              url: '/pages/index/index'
            });
          }
        }
      });
    },
    
    handleDeleteAccount() {
      uni.showModal({
        title: '警告',
        content: '注销账号将永久删除您的所有数据，确定要继续吗？',
        confirmText: '确定注销',
        confirmColor: '#FF0000',
        success: (res) => {
          if (res.confirm) {
            // 执行注销账号逻辑
            this.deleteAccount();
          }
        }
      });
    },
    
    async deleteAccount() {
      try {
        // 调用API注销账号
        await uni.request({
          url: '/api/user/delete',
          method: 'POST',
          header: {
            'Authorization': 'Bearer ' + uni.getStorageSync('token')
          }
        });
        
        uni.showToast({
          title: '账号已注销',
          icon: 'success'
        });
        
        uni.reLaunch({
          url: '/pages/login/index'
        });
      } catch (error) {
        uni.showToast({
          title: '注销失败',
          icon: 'none'
        });
      }
    }
  }
}
</script>

<style lang="scss">
.settings-container {
  width: 100%;
  height: 100%;
  padding: 32rpx;
  background-color: #f8f9fa;
  min-height: 100vh;
}

.time-display {
  font-size: 28rpx;
  color: #6c757d;
  margin-bottom: 48rpx;
  font-weight: 500;
}

.settings-title {
  font-size: 48rpx;
  font-weight: 700;
  margin-bottom: 48rpx;
  color: #212529;
  letter-spacing: 0.5rpx;
}

.settings-list {
  background-color: #fff;
  border-radius: 24rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.setting-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1rpx solid #f1f3f5;
  font-size: 32rpx;
  transition: all 0.2s ease;
  
  &:active {
    background-color: #f8f9fa;
  }
  
  &:last-child {
    border-bottom: none;
  }
}

.arrow-icon {
  width: 32rpx;
  height: 32rpx;
  opacity: 0.6;
}

.logout {
  color: #495057;
  font-weight: 500;
}

.delete-account {
  color: #ff4d4f;
  font-weight: 500;
}
</style>