<template>
  <view class="guide-detail-container">
    <view class="header">
      <view class="back-btn" @click="goBack">
        <text class="iconfont icon-arrow-left"></text>
      </view>
      <view class="title">{{ pageTitle }}</view>
    </view>
    
    <view class="content">
      <!-- 加载状态 -->
      <view v-if="loading" class="loading-container">
        <text>加载中...</text>
      </view>
      
      <!-- 错误状态 -->
      <view v-else-if="error" class="error-container">
        <text>{{ error }}</text>
        <button @click="loadGuideDetail" class="retry-btn">重试</button>
      </view>
      
      <!-- 引导详情 -->
      <view v-else-if="guideDetail" class="guide-detail">
        <!-- 标题区域 -->
        <view class="detail-header">
          <text class="detail-title">{{ guideDetail.title }}</text>
          <view class="detail-meta">
            <text class="guide-type">{{ getGuideTypeText(guideDetail.guideType) }}</text>
            <text class="view-count">查看 {{ guideDetail.viewCount || 0 }} 次</text>
          </view>
        </view>
        
        <!-- 描述 -->
        <view v-if="guideDetail.description" class="detail-description">
          <text>{{ guideDetail.description }}</text>
        </view>
        
        <!-- 图片 -->
        <view v-if="guideDetail.imageUrl" class="detail-image">
          <image :src="guideDetail.imageUrl" mode="widthFix" @click="previewImage"></image>
        </view>
        
        <!-- 内容 -->
        <view v-if="guideDetail.content" class="detail-content">
          <rich-text :nodes="formatContent(guideDetail.content)"></rich-text>
        </view>
        
        <!-- 步骤信息 -->
        <view class="detail-footer">
          <text class="step-info">步骤 {{ guideDetail.stepOrder || 1 }}</text>
          <text class="update-time">更新时间：{{ formatTime(guideDetail.updateTime) }}</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { get } from '@/utils/request.js'

export default {
  data() {
    return {
      guideId: '',
      pageTitle: '引导详情',
      guideDetail: null,
      loading: false,
      error: ''
    }
  },
  onLoad(options) {
    if (options.id) {
      this.guideId = options.id
    }
    if (options.title) {
      this.pageTitle = decodeURIComponent(options.title)
    }
    
    if (this.guideId) {
      this.loadGuideDetail()
    } else {
      this.error = '缺少引导ID参数'
    }
  },
  methods: {
    goBack() {
      uni.navigateBack({
        delta: 1
      });
    },
    
    async loadGuideDetail() {
      try {
        this.loading = true
        this.error = ''
        
        const response = await get(`/api/guide/${this.guideId}`)
        
        if (response && response.code === 200) {
          this.guideDetail = response.data
        } else {
          this.error = response?.message || '获取引导详情失败'
        }
      } catch (error) {
        console.error('获取引导详情失败:', error)
        this.error = '网络错误，请稍后重试'
      } finally {
        this.loading = false
      }
    },
    
    previewImage() {
      if (this.guideDetail.imageUrl) {
        uni.previewImage({
          urls: [this.guideDetail.imageUrl],
          current: this.guideDetail.imageUrl
        })
      }
    },
    
    formatContent(content) {
      if (!content) return ''
      // 简单的换行处理
      return content.replace(/\n/g, '<br/>')
    },
    
    formatTime(time) {
      if (!time) return ''
      const date = new Date(time)
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`
    },
    
    getGuideTypeText(type) {
      const typeMap = {
        'general': '通用',
        'device': '设备',
        'operation': '操作',
        'maintenance': '维护'
      }
      return typeMap[type] || '通用'
    }
  }
}
</script>

<style lang="scss">
.guide-detail-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100%;
  background-color: #f7f8fa;
  box-sizing: border-box;
}

.header {
  display: flex;
  align-items: center;
  height: 88rpx;
  background-color: #ffffff;
  padding: 0 30rpx;
  position: relative;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.back-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.title {
  flex: 1;
  text-align: center;
  font-size: 36rpx;
  font-weight: 500;
  margin-right: 60rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.content {
  flex: 1;
  padding: 20rpx;
}

.loading-container, .error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400rpx;
  color: #999;
}

.retry-btn {
  margin-top: 20rpx;
  padding: 10rpx 30rpx;
  background-color: #007AFF;
  color: white;
  border-radius: 10rpx;
  border: none;
}

.guide-detail {
  background-color: #ffffff;
  border-radius: 10rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.detail-header {
  margin-bottom: 30rpx;
}

.detail-title {
  font-size: 40rpx;
  font-weight: bold;
  color: #333;
  line-height: 1.4;
  display: block;
  margin-bottom: 20rpx;
}

.detail-meta {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.guide-type {
  font-size: 24rpx;
  color: #007AFF;
  background-color: #f0f5ff;
  padding: 6rpx 16rpx;
  border-radius: 12rpx;
}

.view-count {
  font-size: 24rpx;
  color: #999;
}

.detail-description {
  margin-bottom: 30rpx;
  padding: 20rpx;
  background-color: #f8f9fa;
  border-radius: 8rpx;
  border-left: 4rpx solid #007AFF;
}

.detail-description text {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
}

.detail-image {
  margin-bottom: 30rpx;
  text-align: center;
}

.detail-image image {
  max-width: 100%;
  border-radius: 8rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.detail-content {
  margin-bottom: 30rpx;
  font-size: 30rpx;
  color: #333;
  line-height: 1.8;
}

.detail-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-top: 20rpx;
  border-top: 1rpx solid #eee;
}

.step-info {
  font-size: 26rpx;
  color: #007AFF;
  font-weight: 500;
}

.update-time {
  font-size: 24rpx;
  color: #999;
}
</style>
