<template>
  <view class="container">
    <!-- 报修表单 -->
    <view class="form-container">
      <!-- 反馈内容 -->
      <view class="form-section">
        <view class="section-title">我要反馈</view>
        <textarea 
          class="feedback-textarea" 
          placeholder="您可以在这里输入反馈内容..." 
          v-model="feedbackContent"
        ></textarea>
      </view>

      <!-- 上传图片 -->
      <view class="form-section">
        <view class="section-title">上传图片(选填)</view>

        <!-- 已选择的图片 -->
        <view class="image-list" v-if="imageList.length > 0">
          <view class="image-item" v-for="(image, index) in imageList" :key="index">
            <image :src="image" class="preview-image" mode="aspectFill"></image>
            <view class="remove-btn" @click="removeImage(index)">×</view>
          </view>
        </view>

        <!-- 添加图片按钮 -->
        <view class="upload-box" @click="chooseImage" v-if="imageList.length < 3">
          <view class="upload-placeholder">
            <text class="upload-icon">📷</text>
            <text class="upload-text">添加图片</text>
          </view>
        </view>
      </view>

      <!-- 联系方式 -->
      <view class="form-section">
        <view class="section-title">联系方式</view>
        <input 
          type="text" 
          class="contact-input" 
          placeholder="请输入联系方式" 
          v-model="contactInfo"
        />
      </view>

      <!-- 提交按钮 -->
      <view class="submit-btn" @click="submitFeedback">提交</view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      feedbackContent: '',
      imageList: [],
      contactInfo: '',
      deviceCode: '', // 设备编码
      deviceName: ''  // 设备名称
    };
  },
  onLoad(options) {
    // 如果从设备页面跳转过来，可能会传递设备信息
    if (options.deviceCode) {
      this.deviceCode = options.deviceCode;
    }
    if (options.deviceName) {
      this.deviceName = decodeURIComponent(options.deviceName);
    }

    // 获取用户信息，自动填充联系方式
    this.getUserInfo();
  },
  methods: {
    goBack() {
      uni.navigateBack();
    },

    getUserInfo() {
      // 从本地存储获取用户信息
      const userInfo = uni.getStorageSync('userInfo');
      if (userInfo && userInfo.phone) {
        this.contactInfo = userInfo.phone;
      }
    },

    chooseImage() {
      uni.chooseImage({
        count: 3, // 最多选择3张图片
        sizeType: ['compressed'],
        sourceType: ['album', 'camera'],
        success: (res) => {
          this.imageList = this.imageList.concat(res.tempFilePaths);
          // 这里可以上传图片到服务器
          this.uploadImages(res.tempFilePaths);
        },
        fail: (err) => {
          console.error('选择图片失败：', err);
          uni.showToast({
            title: '选择图片失败',
            icon: 'none'
          });
        }
      });
    },

    uploadImages(imagePaths) {
      // 上传图片到服务器
      imagePaths.forEach(imagePath => {
        uni.uploadFile({
          url: 'https://www.narenqiqige.com/api/upload/image',
          filePath: imagePath,
          name: 'file',
          header: {
            'Authorization': 'Bearer ' + uni.getStorageSync('token')
          },
          success: (uploadRes) => {
            console.log('图片上传成功：', uploadRes);
            // 这里可以保存上传后的图片URL
          },
          fail: (err) => {
            console.error('图片上传失败：', err);
          }
        });
      });
    },

    removeImage(index) {
      this.imageList.splice(index, 1);
    },

    submitFeedback() {
      // 表单验证
      if (!this.feedbackContent.trim()) {
        uni.showToast({
          title: '请输入问题描述',
          icon: 'none'
        });
        return;
      }

      if (!this.contactInfo.trim()) {
        uni.showToast({
          title: '请输入联系方式',
          icon: 'none'
        });
        return;
      }

      // 准备提交数据
      const repairData = {
        deviceCode: this.deviceCode,
        deviceName: this.deviceName,
        problemDesc: this.feedbackContent.trim(),
        contactInfo: this.contactInfo.trim(),
        images: this.imageList.join(',') // 多个图片用逗号分隔
      };

      uni.showLoading({
        title: '提交中...'
      });

      // 调用API提交报修信息
      uni.request({
        url: 'https://www.narenqiqige.com/api/device-repair/submit',
        method: 'POST',
        header: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer ' + uni.getStorageSync('token')
        },
        data: repairData,
        success: (res) => {
          uni.hideLoading();

          if (res.data.code === 200) {
            uni.showToast({
              title: '报修提交成功',
              icon: 'success',
              duration: 2000,
              success: () => {
                setTimeout(() => {
                  uni.navigateBack();
                }, 2000);
              }
            });
          } else {
            uni.showToast({
              title: res.data.message || '提交失败',
              icon: 'none'
            });
          }
        },
        fail: (err) => {
          uni.hideLoading();
          console.error('提交报修失败：', err);
          uni.showToast({
            title: '网络错误，请重试',
            icon: 'none'
          });
        }
      });
    }
  }
};
</script>

<style lang="scss">
.container {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx;
  background-color: #4285f4;
  color: #fff;
}

.back-btn {
  font-size: 40rpx;
  width: 60rpx;
  display: flex;
  align-items: center;
}

.title {
  font-size: 36rpx;
  font-weight: 500;
}

.header-icons {
  display: flex;
  align-items: center;
}

.header-icon {
  margin-left: 15rpx;
  font-size: 24rpx;
}

.form-container {
  padding: 30rpx;
}

.form-section {
  margin-bottom: 40rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.feedback-textarea {
  width: 100%;
  height: 220rpx;
  background-color: #fff;
  border-radius: 10rpx;
  padding: 20rpx;
  font-size: 28rpx;
  box-sizing: border-box;
}

.upload-box {
  width: 100%;
  background-color: #fff;
  border-radius: 10rpx;
  padding: 30rpx 0;
  display: flex;
  flex-wrap: wrap;
}

.upload-placeholder {
  width: 180rpx;
  height: 180rpx;
  border: 1px dashed #ddd;
  border-radius: 8rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  margin: 0 20rpx;
}

.upload-icon {
  font-size: 60rpx;
  color: #ccc;
  margin-bottom: 10rpx;
}

.upload-text {
  font-size: 24rpx;
  color: #999;
}

.image-list {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
  margin-bottom: 30rpx;
  padding: 0 20rpx;
}

.image-item {
  position: relative;
  width: 160rpx;
  height: 160rpx;
  border-radius: 16rpx;
  overflow: hidden;
}

.preview-image {
  width: 100%;
  height: 100%;
}

.remove-btn {
  position: absolute;
  top: -10rpx;
  right: -10rpx;
  width: 40rpx;
  height: 40rpx;
  background-color: #ff4757;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: bold;
}

.contact-input {
  width: 100%;
  height: 90rpx;
  background-color: #fff;
  border-radius: 10rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  box-sizing: border-box;
}

.submit-btn {
  width: 100%;
  height: 90rpx;
  background-color: #4285f4;
  color: #fff;
  border-radius: 45rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 32rpx;
  margin-top: 60rpx;
}
</style> 