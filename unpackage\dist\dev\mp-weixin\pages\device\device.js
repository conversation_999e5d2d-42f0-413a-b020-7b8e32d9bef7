"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      deviceList: [],
      activeCategory: "全部"
    };
  },
  onLoad() {
    this.loadDevices();
  },
  methods: {
    loadDevices() {
      this.deviceList = [];
    },
    switchCategory(category) {
      this.activeCategory = category;
    },
    addDevice() {
    },
    scanQRCode() {
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {};
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/device/device.js.map
