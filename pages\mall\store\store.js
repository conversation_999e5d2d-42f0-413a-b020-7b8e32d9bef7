Page({
  data: {
    storeId: null,
    isFollowed: false,
    storeInfo: {
      id: 1,
      name: '芭佰邑智能染发',
      logo: '/static/images/shop-logo.jpg',
      description: '专业智能染发设备提供商和日化用品销售，提供专业化妆品和美发产品',
      productCount: 36,
      followerCount: 1028,
      rating: {
        products: 4.8,
        service: 4.9,
        logistics: 4.7
      }
    },
    storeProducts: [
      {
        id: 1,
        name: '内立柔9合一直发膏',
        price: 99.99,
        soldCount: 120,
        image: 'https://img.alicdn.com/imgextra/i1/2208857268292/O1CN01QxJYYY1QxJYYY1QxJ_!!2208857268292.jpg'
      },
      {
        id: 2,
        name: '炫即染发膏',
        price: 1200.00,
        soldCount: 58,
        image: 'https://img.alicdn.com/imgextra/i2/2208857268292/O1CN01QxJYYY1QxJYYY1QxJ_!!2208857268292.jpg'
      },
      {
        id: 3,
        name: '库存产品',
        price: 26800.00,
        soldCount: 3,
        image: 'https://img.alicdn.com/imgextra/i3/2208857268292/O1CN01QxJYYY1QxJYYY1QxJ_!!2208857268292.jpg'
      },
      {
        id: 4,
        name: '炫即染发膏',
        price: 1200.00,
        soldCount: 25,
        image: 'https://img.alicdn.com/imgextra/i4/2208857268292/O1CN01QxJYYY1QxJYYY1QxJ_!!2208857268292.jpg'
      }
    ]
  },
  onLoad: function(options) {
    if (options.id) {
      this.setData({
        storeId: options.id
      });
      // 加载店铺信息
      this.loadStoreInfo();
    }
  },
  loadStoreInfo: function() {
    // 这里应该调用接口获取店铺信息
    console.log('加载店铺ID:', this.data.storeId);
  },
  goBack: function() {
    wx.navigateBack();
  },
  followStore: function() {
    this.setData({
      isFollowed: !this.data.isFollowed
    });
    wx.showToast({
      title: this.data.isFollowed ? '已关注店铺' : '已取消关注',
      icon: 'none'
    });
  },
  goToProduct: function(e) {
    const productId = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: '/pages/mall/product/detail?id=' + productId
    });
  }
}) 