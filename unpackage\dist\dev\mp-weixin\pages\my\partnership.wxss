/* 水平间距 */
/* 水平间距 */
/* 全局样式修复 - 确保页面占满屏幕宽度 */
page {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
}

/* 通用容器样式 */
.container, .page-container {
  width: 100%;
  min-height: 100vh;
  box-sizing: border-box;
}

/* 修复小程序页面宽度问题 */
uni-page-body {
  width: 100% !important;
}

/* 确保所有view元素正确继承宽度 */
view {
  box-sizing: border-box;
}
.partnership-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100%;
  background-color: #f7f8fa;
  box-sizing: border-box;
}
.header {
  display: flex;
  align-items: center;
  height: 88rpx;
  background-color: #ffffff;
  padding: 0 30rpx;
  position: relative;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}
.back-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.title {
  flex: 1;
  text-align: center;
  font-size: 36rpx;
  font-weight: 500;
  margin-right: 60rpx;
}
.content {
  flex: 1;
  padding: 20rpx;
  overflow-y: auto;
}
.loading-container, .error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400rpx;
  color: #999;
}
.retry-btn {
  margin-top: 20rpx;
  padding: 10rpx 30rpx;
  background-color: #007AFF;
  color: white;
  border-radius: 10rpx;
  border: none;
}
.partnership-detail {
  display: flex;
  flex-direction: column;
}
.partnership-header {
  background-color: #ffffff;
  border-radius: 10rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  text-align: center;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}
.partnership-image {
  width: 200rpx;
  height: 200rpx;
  border-radius: 10rpx;
  margin-bottom: 20rpx;
}
.partnership-title {
  font-size: 40rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 15rpx;
}
.partnership-desc {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
}
.partnership-content, .partnership-section, .contact-section {
  background-color: #ffffff;
  border-radius: 10rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}
.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  padding-bottom: 10rpx;
  border-bottom: 2rpx solid #f0f0f0;
}
.advantage-list, .requirement-list {
  display: flex;
  flex-direction: column;
}
.advantage-item, .requirement-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 15rpx;
}
.advantage-icon {
  width: 40rpx;
  height: 40rpx;
  background-color: #52c41a;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  margin-right: 15rpx;
  flex-shrink: 0;
}
.requirement-number {
  width: 40rpx;
  height: 40rpx;
  background-color: #007AFF;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  margin-right: 15rpx;
  flex-shrink: 0;
}
.advantage-text, .requirement-text {
  font-size: 28rpx;
  color: #333;
  line-height: 1.6;
  flex: 1;
}
.contact-list {
  display: flex;
  flex-direction: column;
}
.contact-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}
.contact-item:last-child {
  border-bottom: none;
}
.contact-icon {
  font-size: 32rpx;
  margin-right: 20rpx;
  width: 50rpx;
  text-align: center;
}
.contact-text {
  font-size: 28rpx;
  color: #333;
  flex: 1;
}
/* 水平间距 */
/* 水平间距 */
/* 全局样式修复 - 确保页面占满屏幕宽度 */
page {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
}

/* 通用容器样式 */
.container, .page-container {
  width: 100%;
  min-height: 100vh;
  box-sizing: border-box;
}

/* 修复小程序页面宽度问题 */
uni-page-body {
  width: 100% !important;
}

/* 确保所有view元素正确继承宽度 */
view {
  box-sizing: border-box;
}
.partnership-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f7f8fa;
}
.header {
  display: flex;
  align-items: center;
  height: 88rpx;
  background-color: #ffffff;
  padding: 0 30rpx;
  position: relative;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}
.back-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.title {
  flex: 1;
  text-align: center;
  font-size: 36rpx;
  font-weight: 500;
  margin-right: 60rpx;
}
.content {
  flex: 1;
  padding: 20rpx;
  overflow-y: auto;
}
.loading-container, .error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400rpx;
  color: #999;
}
.retry-btn {
  margin-top: 20rpx;
  padding: 10rpx 30rpx;
  background-color: #007AFF;
  color: white;
  border-radius: 10rpx;
  border: none;
}
.partnership-detail {
  display: flex;
  flex-direction: column;
}
.partnership-header {
  background-color: #ffffff;
  border-radius: 10rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  text-align: center;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}
.partnership-image {
  width: 200rpx;
  height: 200rpx;
  border-radius: 10rpx;
  margin-bottom: 20rpx;
}
.partnership-title {
  font-size: 40rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 15rpx;
}
.partnership-desc {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
}
.partnership-content, .partnership-section, .contact-section {
  background-color: #ffffff;
  border-radius: 10rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}
.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  padding-bottom: 10rpx;
  border-bottom: 2rpx solid #f0f0f0;
}
.advantage-list, .requirement-list {
  display: flex;
  flex-direction: column;
}
.advantage-item, .requirement-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 15rpx;
}
.advantage-icon {
  width: 40rpx;
  height: 40rpx;
  background-color: #52c41a;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  margin-right: 15rpx;
  flex-shrink: 0;
}
.requirement-number {
  width: 40rpx;
  height: 40rpx;
  background-color: #007AFF;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  margin-right: 15rpx;
  flex-shrink: 0;
}
.advantage-text, .requirement-text {
  font-size: 28rpx;
  color: #333;
  line-height: 1.6;
  flex: 1;
}
.process-list {
  display: flex;
  flex-direction: column;
}
.process-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 30rpx;
}
.process-step {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-right: 20rpx;
}
.step-number {
  width: 50rpx;
  height: 50rpx;
  background-color: #007AFF;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 26rpx;
  font-weight: bold;
}
.step-line {
  width: 2rpx;
  height: 40rpx;
  background-color: #ddd;
  margin-top: 10rpx;
}
.process-text {
  font-size: 28rpx;
  color: #333;
  line-height: 1.6;
  flex: 1;
  padding-top: 10rpx;
}
.contact-list {
  display: flex;
  flex-direction: column;
}
.contact-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}
.contact-item:last-child {
  border-bottom: none;
}
.contact-icon {
  font-size: 32rpx;
  margin-right: 20rpx;
  width: 50rpx;
  text-align: center;
}
.contact-text {
  font-size: 28rpx;
  color: #333;
  flex: 1;
}