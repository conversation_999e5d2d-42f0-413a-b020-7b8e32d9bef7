"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_request = require("../../utils/request.js");
const _sfc_main = {
  data() {
    return {
      baseUrl: "https://www.narenqiqige.com",
      previewImages: [],
      // 图片预览URL数组
      tempFilePaths: [],
      // 临时文件路径
      userInfo: {
        username: "",
        forwardCount: 0,
        likeCount: 0,
        followingCount: 0,
        followerCount: 0
      },
      tabs: [
        {
          type: "post",
          name: "作品"
        },
        {
          type: "collect",
          name: "收藏"
        },
        {
          type: "like",
          name: "喜欢"
        }
      ],
      activeTab: "post",
      contentList: [
        // {
        // 	content: "测试内容",
        // 	userName: "微信用户",
        // 	distance: 1460.75,
        // 	image: "/static/icons/qrcode-icon.png"
        // },
        // {
        // 	content: "深紫色62",
        // 	userName: "微信用户",
        // 	distance: 1460.75,
        // 	image: "/static/icons/qrcode-icon.png"
        // }
      ],
      // 添加记录的弹窗
      showModal: false,
      newContent: "",
      selectedSubject: "",
      subjects: [
        "日常",
        "语言",
        "数学",
        "物理学",
        "化学",
        "经济学",
        "管理学",
        "生物学",
        "哲学",
        "工程学",
        "医学",
        "管理学",
        "会计学",
        "法律",
        "计算机",
        "农林环",
        "建筑学",
        "新闻传",
        "历史人",
        "旅游管",
        "餐饮管",
        "健康体",
        "心理学",
        "考古学",
        "地理学",
        "传播学",
        "神学",
        "政治学",
        "社会学",
        "地质学",
        "宗教学",
        "建筑学"
      ]
    };
  },
  onLoad() {
    this.initContentList();
    this.initUserInfo();
  },
  computed: {
    subjectRows() {
      const rows = [];
      for (let i = 0; i < this.subjects.length; i += 4) {
        rows.push(this.subjects.slice(i, i + 4));
      }
      return rows;
    }
  },
  methods: {
    initUserInfo() {
      const userInfo = common_vendor.index.getStorageSync("userInfo");
      this.userInfo = userInfo;
      this.userName = userInfo.nickname;
      common_vendor.index.__f__("log", "at pages/my/collection.vue:192", "缓存中的用户数据", userInfo);
    },
    async initContentList() {
      const response = await utils_request.apiService.content.listContent();
      common_vendor.index.__f__("log", "at pages/my/collection.vue:196", "内容初始化", response);
      this.contentList = response.data;
    },
    // 选择图片
    chooseImage() {
      common_vendor.index.chooseImage({
        count: 9 - this.previewImages.length,
        success: (res) => {
          this.previewImages = [...this.previewImages, ...res.tempFilePaths];
          this.tempFilePaths = [...this.tempFilePaths, ...res.tempFilePaths];
        }
      });
    },
    uploadImages(imagePaths) {
      imagePaths.forEach((imagePath) => {
        common_vendor.index.uploadFile({
          url: "https://www.narenqiqige.com/api/upload/single",
          filePath: imagePath,
          name: "file",
          header: {
            "Authorization": "Bearer " + common_vendor.index.getStorageSync("token")
          },
          success: (uploadRes) => {
            common_vendor.index.__f__("log", "at pages/my/collection.vue:224", "图片上传成功：", uploadRes);
          },
          fail: (err) => {
            common_vendor.index.__f__("error", "at pages/my/collection.vue:229", "图片上传失败：", err);
          }
        });
      });
    },
    // 删除图片
    removeImage(index) {
      this.previewImages.splice(index, 1);
      this.tempFilePaths.splice(index, 1);
    },
    // 选择主题或者分类
    selectSubject(subject) {
      this.selectedSubject = this.selectedSubject === subject ? "" : subject;
    },
    showProductTip() {
      common_vendor.index.showToast({
        title: "当前账号不支持插入商品",
        icon: "none"
      });
    },
    // 添加记录数据
    // 修改 addNewContent 方法
    async addNewContent() {
      if (!this.newContent.trim()) {
        common_vendor.index.showToast({
          title: "请输入内容",
          icon: "none"
        });
        return;
      }
      common_vendor.index.showLoading({
        title: "发布中...",
        mask: true
      });
      try {
        const uploadedImages = [];
        for (const filePath of this.tempFilePaths) {
          const res = await this.uploadSingleImage(filePath);
          common_vendor.index.__f__("log", "at pages/my/collection.vue:276", "当前上传的图片返回结果", res);
          if (res && res.fileUrls) {
            uploadedImages.push(...res.fileUrls);
          }
        }
        const postData = {
          content: this.newContent,
          images: uploadedImages,
          subject: this.selectedSubject
        };
        common_vendor.index.__f__("log", "at pages/my/collection.vue:288", "发送的数据", postData);
        const response = await utils_request.apiService.content.createContent(postData);
        if (response.code === 200) {
          common_vendor.index.__f__("log", "at pages/my/collection.vue:293", "发布成功", response);
          this.contentList.unshift(response.data);
          this.resetForm();
          common_vendor.index.showToast({
            title: "发布成功"
          });
        } else {
          throw new Error(response.message || "发布失败");
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/my/collection.vue:304", "发布失败:", error);
        common_vendor.index.showToast({
          title: error.message || "发布失败",
          icon: "none"
        });
      } finally {
        common_vendor.index.hideLoading();
      }
    },
    // 单独的上传图片方法
    uploadSingleImage(filePath) {
      return new Promise((resolve, reject) => {
        common_vendor.index.uploadFile({
          url: "https://www.narenqiqige.com/api/upload/single",
          filePath,
          name: "file",
          header: {
            "Authorization": "Bearer " + common_vendor.index.getStorageSync("token")
          },
          success: (uploadRes) => {
            const resData = JSON.parse(uploadRes.data);
            resolve(resData.data);
          },
          fail: (err) => {
            common_vendor.index.__f__("error", "at pages/my/collection.vue:329", "图片上传失败:", err);
            reject(err);
          }
        });
      });
    },
    // 重置表单
    resetForm() {
      this.newContent = "";
      this.selectedSubject = "";
      this.previewImages = [];
      this.tempFilePaths = [];
      this.showModal = false;
    },
    previewImage(url) {
      common_vendor.index.previewImage({
        urls: this.contentList.map((item) => item.image).filter(Boolean),
        current: url
      });
    },
    navigateToDetail(content) {
      common_vendor.index.navigateTo({
        url: `/pages/my/content-detail?id=${content.id}`,
        success: () => {
        }
      });
    },
    loadContentDetail() {
      common_vendor.index.request({
        url: `/api/content/detail/${this.contentId}`,
        success: (res) => {
          this.contentInfo = res.data.data;
        }
      });
    },
    loadComments() {
      common_vendor.index.request({
        url: `/api/comment/list/${this.contentId}`,
        success: (res) => {
          this.comments = res.data.data;
        }
      });
    },
    sendComment() {
      if (!this.commentContent.trim())
        return;
      common_vendor.index.request({
        url: "/api/comment/add",
        method: "POST",
        data: {
          contentId: this.contentId,
          content: this.commentContent
        },
        success: (res) => {
          this.commentContent = "";
          this.loadComments();
          this.contentInfo.commentCount += 1;
        }
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.userInfo.avatar || "/static/logo.png",
    b: common_vendor.t($data.userInfo.nickname),
    c: common_vendor.o((...args) => _ctx.navigateToEdit && _ctx.navigateToEdit(...args)),
    d: common_vendor.t($data.userInfo.forwardCount || 0),
    e: common_vendor.t($data.userInfo.likeCount || 0),
    f: common_vendor.t($data.userInfo.followingCount || 0),
    g: common_vendor.t($data.userInfo.followerCount || 0),
    h: common_vendor.f($data.tabs, (tab, k0, i0) => {
      return {
        a: common_vendor.t(tab.name),
        b: tab.type,
        c: common_vendor.n($data.activeTab === tab.type ? "active" : ""),
        d: common_vendor.o(($event) => _ctx.switchTab(tab.type), tab.type)
      };
    }),
    i: common_vendor.f($data.contentList, (item, index, i0) => {
      return common_vendor.e({
        a: item.image
      }, item.image ? {
        b: $data.baseUrl + item.image
      } : {}, {
        c: common_vendor.t(item.content),
        d: common_vendor.t(item.userName),
        e: common_vendor.t(item.distance || 1460.75),
        f: index,
        g: common_vendor.o(($event) => $options.navigateToDetail(item), index)
      });
    }),
    j: common_vendor.o(($event) => $data.showModal = true),
    k: $data.showModal
  }, $data.showModal ? common_vendor.e({
    l: common_vendor.o(($event) => $data.showModal = false),
    m: $data.newContent,
    n: common_vendor.o(($event) => $data.newContent = $event.detail.value),
    o: common_vendor.f($data.previewImages, (image, index, i0) => {
      return {
        a: image,
        b: common_vendor.o(($event) => $options.removeImage(index), index),
        c: index
      };
    }),
    p: $data.previewImages.length < 9
  }, $data.previewImages.length < 9 ? {
    q: common_vendor.o((...args) => $options.chooseImage && $options.chooseImage(...args))
  } : {}, {
    r: common_vendor.o((...args) => $options.showProductTip && $options.showProductTip(...args)),
    s: common_vendor.f($options.subjectRows, (row, rowIndex, i0) => {
      return {
        a: common_vendor.f(row, (subject, k1, i1) => {
          return {
            a: common_vendor.t(subject),
            b: subject,
            c: $data.selectedSubject === subject ? 1 : "",
            d: common_vendor.o(($event) => $options.selectSubject(subject), subject)
          };
        }),
        b: rowIndex
      };
    }),
    t: common_vendor.o(($event) => $data.showModal = false),
    v: common_vendor.o((...args) => $options.addNewContent && $options.addNewContent(...args)),
    w: common_vendor.o(() => {
    }),
    x: common_vendor.o(($event) => $data.showModal = false)
  }) : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/my/collection.js.map
