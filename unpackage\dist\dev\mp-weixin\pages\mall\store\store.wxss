/* 水平间距 */
/* 水平间距 */
/* 全局样式修复 - 确保页面占满屏幕宽度 */
page {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
}

/* 通用容器样式 */
.container, .page-container {
  width: 100%;
  min-height: 100vh;
  box-sizing: border-box;
}

/* 修复小程序页面宽度问题 */
uni-page-body {
  width: 100% !important;
}

/* 确保所有view元素正确继承宽度 */
view {
  box-sizing: border-box;
}
.store-container {
  background-color: #f5f5f5;
  min-height: 100vh;
}
.store-header {
  position: relative;
  margin-bottom: 20rpx;
}
.store-bg-gradient {
  background: linear-gradient(180deg, #c62828 0%, #d32f2f 30%, #b71c1c 100%);
  padding: 20rpx 30rpx 30rpx;
  position: relative;
  overflow: hidden;
  margin-top: -10rpx;
}
.store-bg-gradient::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0) 100%);
  pointer-events: none;
}
.store-info {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  position: relative;
  z-index: 1;
}
.store-detail {
  flex: 1;
  color: #ffffff;
}
.store-name {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
  color: #ffffff;
}
.store-rating-badge {
  display: inline-block;
  background-color: rgba(255, 165, 0, 0.9);
  color: #ffffff;
  font-size: 24rpx;
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
  margin-bottom: 12rpx;
  font-weight: bold;
}
.store-license {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.4;
}
.follow-btn {
  background-color: rgba(255, 255, 255, 0.2);
  color: #ffffff;
  padding: 12rpx 24rpx;
  border-radius: 30rpx;
  font-size: 28rpx;
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  transition: all 0.3s ease;
  -webkit-backdrop-filter: blur(10rpx);
          backdrop-filter: blur(10rpx);
}
.follow-btn.followed {
  background-color: rgba(255, 255, 255, 0.9);
  color: #d32f2f;
  border-color: rgba(255, 255, 255, 0.9);
}
.follow-btn.loading {
  background-color: rgba(255, 255, 255, 0.1);
  opacity: 0.7;
}
.category-nav {
  display: flex;
  background-color: #ffffff;
  padding: 0 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}
.nav-item {
  padding: 25rpx 20rpx;
  font-size: 30rpx;
  position: relative;
  color: #999999;
  margin-right: 40rpx;
}
.nav-item.active {
  color: #333333;
  font-weight: 500;
}
.nav-item.active:after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 4rpx;
  background-color: #d32f2f;
  border-radius: 2rpx;
}
.filter-bar {
  display: flex;
  background-color: #ffffff;
  padding: 20rpx 30rpx;
  margin-bottom: 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}
.filter-item {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 28rpx;
  color: #666666;
}
.arrow {
  font-size: 20rpx;
  margin-left: 8rpx;
  color: #999999;
  transition: transform 0.3s ease;
}
.product-list {
  padding: 0 15rpx;
  background-color: #ffffff;
}
.loading-container, .empty-container {
  text-align: center;
  padding: 100rpx 0;
  color: #999999;
  font-size: 28rpx;
  background-color: #ffffff;
}
.product-grid {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  padding: 20rpx 15rpx;
}
.product-item {
  width: 48%;
  background-color: #ffffff;
  margin-bottom: 20rpx;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
  border: 1rpx solid #f5f5f5;
}
.product-image {
  width: 100%;
  height: 320rpx;
  background-color: #f8f8f8;
}
.product-info {
  padding: 20rpx 15rpx;
}
.product-name {
  font-size: 28rpx;
  color: #333333;
  line-height: 1.4;
  height: 80rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  margin-bottom: 12rpx;
}
.product-price-row {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
}
.product-price {
  font-size: 32rpx;
  color: #d32f2f;
  font-weight: bold;
}
.product-sold {
  font-size: 24rpx;
  color: #999999;
}