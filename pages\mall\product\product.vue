<template>
  <view class="content">
    <!-- 顶部导航 -->
    <view class="top-nav">
      <view class="back-btn">
        <text class="back-icon">←</text>
      </view>
      <view class="search-bar">
        <view class="search-icon">🔍</view>
        <text class="search-placeholder">日照香炉生紫烟</text>
      </view>
      <view class="action-btns">
        <view class="star-btn">★</view>
        <view class="share-btn">↗</view>
        <view class="more-btn">•••</view>
        <view class="line-btn">—</view>
        <view class="circle-btn">◯</view>
      </view>
    </view>
    
    <!-- 商品图片轮播 -->
    <view class="product-images">
      <view class="image-placeholder"></view>
      <view class="image-counter">商品 1/2</view>
    </view>
    
    <!-- 商品价格信息 -->
    <view class="product-price-section">
      <view class="price-container">
        <text class="price-symbol">¥</text>
        <text class="price-value">99.99</text>
        <text class="coupon-tag">券立减1 ></text>
      </view>
      <view class="product-title">内立柔9合一直发膏</view>
      <view class="product-sold">已售</view>
    </view>
    
    <!-- 保障信息 -->
    <view class="guarantee-section">
      <view class="guarantee-title">保障</view>
      <view class="guarantee-content">7天无理由退货 · 极速退款</view>
      <view class="guarantee-arrow">></view>
    </view>
    
    <!-- 规格选择 -->
    <view class="spec-section">
      <view class="spec-title">选择</view>
      <view class="spec-content">共0种规格可选</view>
      <view class="spec-arrow">></view>
    </view>
    
    <!-- 配送信息 -->
    <view class="delivery-section">
      <view class="delivery-title">物流</view>
      <view class="delivery-content">发货 河北省 保定市 | 免运费</view>
      <view class="delivery-arrow">></view>
    </view>
    
    <!-- 底部操作栏 -->
    <view class="bottom-actions">
      <view class="action-item store">
        <view class="action-icon">🏪</view>
        <text class="action-text">进店</text>
      </view>
      <view class="action-item cart">
        <view class="action-icon">🛒</view>
        <text class="action-text">购物车</text>
      </view>
      <view class="add-cart-btn">加入购物车</view>
      <view class="buy-now-btn">领券购买</view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      productId: null,
      productInfo: {
        id: 1,
        name: '内立柔9合一直发膏',
        price: 99.99,
        originalPrice: 129.99,
        coupon: 1,
        images: ['../../static/product1.png'],
        sold: 0,
        specs: [
          {
            name: '规格',
            options: []
          }
        ],
        delivery: {
          from: '河北省 保定市',
          fee: '免运费'
        },
        guarantee: '7天无理由退货 · 极速退款'
      }
    };
  },
  onLoad(options) {
    if (options.id) {
      this.productId = options.id;
      // 这里可以加载实际商品数据
    }
  },
  methods: {
    goBack() {
      uni.navigateBack();
    },
    addToCart() {
      uni.showToast({
        title: '已加入购物车',
        icon: 'success'
      });
    },
    buyNow() {
      uni.showToast({
        title: '购买功能开发中',
        icon: 'none'
      });
    }
  }
};
</script>

<style lang="scss">
.content {
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.top-nav {
  display: flex;
  align-items: center;
  background-color: #ffffff;
  padding: 20rpx;
  position: sticky;
  top: 0;
  z-index: 100;
}

.back-btn {
  padding: 10rpx 20rpx;
  font-size: 40rpx;
  color: #333;
}

.search-bar {
  flex: 1;
  background-color: #f5f5f5;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  padding: 10rpx 20rpx;
  margin: 0 20rpx;
}

.search-icon {
  margin-right: 10rpx;
  color: #999;
}

.search-placeholder {
  color: #999;
  font-size: 28rpx;
}

.action-btns {
  display: flex;
  align-items: center;
}

.star-btn, .share-btn, .more-btn, .line-btn, .circle-btn {
  margin-left: 10rpx;
  font-size: 30rpx;
  color: #333;
}

.product-images {
  width: 100%;
  height: 600rpx;
  position: relative;
  background-color: #fff;
}

.image-placeholder {
  width: 100%;
  height: 100%;
  background-color: #f0f0f0;
}

.image-counter {
  position: absolute;
  bottom: 20rpx;
  right: 20rpx;
  background-color: rgba(0, 0, 0, 0.5);
  color: #fff;
  font-size: 24rpx;
  padding: 4rpx 20rpx;
  border-radius: 20rpx;
}

.product-price-section {
  background-color: #fff;
  padding: 30rpx 20rpx;
  margin-bottom: 20rpx;
}

.price-container {
  display: flex;
  align-items: baseline;
  margin-bottom: 20rpx;
}

.price-symbol {
  color: #f44336;
  font-size: 32rpx;
  font-weight: bold;
}

.price-value {
  color: #f44336;
  font-size: 48rpx;
  font-weight: bold;
}

.coupon-tag {
  color: #4285f4;
  font-size: 24rpx;
  margin-left: 20rpx;
}

.product-title {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.product-sold {
  font-size: 24rpx;
  color: #999;
}

.guarantee-section, .spec-section, .delivery-section {
  display: flex;
  background-color: #fff;
  padding: 30rpx 20rpx;
  margin-bottom: 2rpx;
  align-items: center;
}

.guarantee-title, .spec-title, .delivery-title {
  width: 120rpx;
  color: #666;
  font-size: 28rpx;
}

.guarantee-content, .spec-content, .delivery-content {
  flex: 1;
  font-size: 28rpx;
}

.guarantee-arrow, .spec-arrow, .delivery-arrow {
  color: #999;
  font-size: 24rpx;
}

.bottom-actions {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  height: 100rpx;
  background-color: #fff;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100rpx;
}

.action-icon {
  font-size: 40rpx;
  margin-bottom: 4rpx;
}

.action-text {
  font-size: 20rpx;
  color: #666;
}

.add-cart-btn, .buy-now-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  font-size: 30rpx;
}

.add-cart-btn {
  background-color: #fff;
  color: #4285f4;
  border-top: 1rpx solid #4285f4;
}

.buy-now-btn {
  background-color: #4285f4;
  color: #fff;
}
</style> 