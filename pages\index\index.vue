<template>
	<view class="content">
		<!-- 色彩分类标签 -->
		<scroll-view class="color-category" scroll-x show-scrollbar="false" :scroll-into-view="scrollToCategoryId">
			<view class="category-item" :class="{ active: activeIndex === index }"
				v-for="(item, index) in colorTypeList" :key="index" :id="'category-' + index" @click="handleCategoryTap"
				:data-index="index" :data-category="item">
				{{ item }}
			</view>
		</scroll-view>

		<scroll-view class="color-images" scroll-x :show-scrollbar="false" enhanced :scroll-with-animation="true"
			:enable-flex="true" :scroll-left="scrollLeft" @scroll="onCategoryScroll">
			<view class="color-image-item" :class="{ active: activeIndex === index }"
				v-for="(item, index) in colorTypeList" :key="index" :id="'image-' + index" @click="handleColorTap"
				:data-index="index" :data-category="item">
				<!-- 图片或颜色块 -->
				<image v-if="colorTypeIcons[index]" class="color-image-placeholder"
					:src="baseurl + colorTypeIcons[index]" mode="scaleToFill" lazy-load></image>
				<view v-else class="color-image-placeholder" :style="{ backgroundColor: item.color || '#DCDCDC' }">
				</view>
				<view class="color-name">
					<view v-if="(item.name || item || '未命名').includes('/')" class="color-name-split">
						<view class="color-name-chinese">{{ (item.name || item || '未命名').split('/')[0] }}</view>
						<view class="color-name-english">{{ (item.name || item || '未命名').split('/')[1] }}</view>
					</view>
					<view v-else class="color-name-single">
						{{ item.name || item || '未命名' }}
					</view>
				</view>
			</view>
		</scroll-view>

		<!-- 潮色标题和筛选 -->
		<view class="trend-title">
			<view class="trend-text">{{currentCategory}} <text
					class="subtitle">共{{colorList ? colorList.length : 0}}个颜色</text></view>
			<!-- <view>
				<view class="filter-icon-placeholder"></view>
				
			</view> -->
			<view class="filter">
				<picker :range="colorNameList" @change="handleColorNameChange">
					<view class="filter">
						<image src="/static/color-name.png"
							style="width: 34rpx;height: 34rpx;margin-top: 10rpx;margin-right: 5rpx;"
							class="filter-icon-placeholder"></image>
						<text style="margin-right: 30rpx; margin-top: 6rpx;">{{colorName}}</text>
					</view>
				</picker>
			</view>
		</view>

		<!-- 色彩网格 -->
		<view class="color-grid">
			<view class="color-grid-item" v-for="(color, index) in colorList" :key="index" @click="selectColor(color)">
				<!-- <view class="color-circle" :style="{'background-color': color.hex}"></view> -->
				<view class="color-circle-container">
					<image class="color-circle" :src="color.iconPath" mode="aspectFill" lazy-load></image>
				</view>
				<view class="color-name">
					<view v-if="color.name && color.name.includes('/')" class="color-name-split">
						<view class="color-name-chinese">{{ color.name.split('/')[0] }}</view>
						<view class="color-name-english">{{ color.name.split('/')[1] }}</view>
					</view>
					<view v-else class="color-name-single">
						{{ color.name }}
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	// import { Color } from 'XrFrame/xrFrameSystem';
	import {
		apiService
	} from '../../utils/request';
	import swatchesCache from '../../utils/swatchesCache';

	export default {
		data() {
			return {
				baseurl: 'https://www.narenqiqige.com/api',
				// array: ['红色', '黄色', '绿色', '紫色'],
				activeIndex: -1, // 初始化为-1，表示未选中状态
				// currentCategory: '潮色',
				currentCategory: '',
				colorName: '色调/Swatche',
				// 控制滚动行为的变量
				scrollToCategoryId: '',
				scrollToImageId: '',
				scrollLeft: 0,
				currentScrollLeft: 0, // 跟踪实际滚动位置

				colorList: [
					// {
					// 	"id": 1,
					// 	"name": "测试配方5g",
					// 	"programCode": null,
					// 	"gramWeight": 0,
					// 	"colorType": "一步染/One-step",
					// 	"categoryName": null,
					// 	"iconPath": null,
					// 	"iconPaths": null,
					// 	"colorComponents": {
					// 		"gray": 5,
					// 		"green": 5,
					// 		"yellow": 5,
					// 		"orange": 5,
					// 		"red": 5,
					// 		"purple": 5,
					// 		"brown": 5,
					// 		"blue": 5,
					// 		"black": 5,
					// 		"faded_bleached": 5,
					// 		"bleached": 5,
					// 		"silver": 5,
					// 		"gold": 5,
					// 		"linen": 5,
					// 		"custom1": 5,
					// 		"custom2": 5,
					// 		"custom3": 5,
					// 		"custom4": 5
					// 	},
					// 	"developerComponents": {
					// 		"hydrogenPeroxide3Percent": 5,
					// 		"hydrogenPeroxide6Percent": 5,
					// 		"hydrogenPeroxide9Percent": 5,
					// 		"hydrogenPeroxide12Percent": 5
					// 	},
					// 	"sortOrder": 0,
					// 	"description": "",
					// 	"creatorId": 1,
					// 	"createTime": 1749004276000,
					// 	"updateTime": 1749004276000
					// },
					// {
					// 	"id": 2,
					// 	"name": "测试配方3g",
					// 	"programCode": 3,
					// 	"gramWeight": 16,
					// 	"colorType": "一步染/One-step",
					// 	"categoryName": null,
					// 	"iconPath": null,
					// 	"iconPaths": null,
					// 	"colorComponents": {
					// 		"gray": 3,
					// 		"green": 3,
					// 		"yellow": 3,
					// 		"orange": 3,
					// 		"red": 3,
					// 		"purple": 3,
					// 		"brown": 3,
					// 		"blue": 3,
					// 		"black": 3,
					// 		"faded_bleached": 3,
					// 		"bleached": 3,
					// 		"silver": 3,
					// 		"gold": 3,
					// 		"linen": 3,
					// 		"custom1": 3,
					// 		"custom2": 3,
					// 		"custom3": 3,
					// 		"custom4": 3
					// 	},
					// 	"developerComponents": {
					// 		"hydrogenPeroxide3Percent": 3,
					// 		"hydrogenPeroxide6Percent": 3,
					// 		"hydrogenPeroxide9Percent": 3,
					// 		"hydrogenPeroxide12Percent": 3
					// 	},
					// 	"sortOrder": 0,
					// 	"description": "一步染发技术全解析\n\n适用范围\n\n本技术适用于各类发质，头发底色在3-6度时效果堪称最佳。为了安全染出理想发色，染发前务必进行皮肤过敏测试，避免过敏反应。\n\n发根操作\n\n1.调配:根据想要的发色，将目标配方与20VOL双氧奶以1:1的比例倒入调色碗，再用染发刷充分搅拌均匀。\n\n2.涂抹:用染发刷从贴近头皮却不触碰头皮之处涂抹染膏，仔细梳理，确保发根都被染膏覆盖。\n\n3.强化:取用相同目标配方，与40VOL双氧奶按1:1混合，增强染膏的上色能力。\n\n4.全涂:从发根开始，顺着发丝将强化后的染膏涂抹至发尾，让每一处头发都浸满染膏。\n\n5.等待:涂抹完毕后，让头发自然停放约40分钟，具体时长参考染发剂说明书，结合期望色度调整。\n\n6.冲洗:待头发色度统一，用温水彻底冲洗染膏，直至冲洗的水变得清澈。\n\n发尾操作(7度以上发色)\n\n1.预处理(可选):\n\n·温水打底:若发尾受损，将目标配方与温水按1:1混合，涂抹在发尾，停放10分钟。\n\n·特殊配方打底:把“0/77+0/43 +2/0 (10:3:1)”和温水1:1混合，涂在发尾7-9度位置，停放10分钟不冲洗，接着用目标配方与6%双氧奶1:1混合涂抹发尾，等色度统一后冲洗。\n\n2.上色:无论是否预处理，都将目标配方和40VOL双氧奶按1:1混合调配染膏。\n\n3.涂抹:从发根开始，用6%双氧奶调配的染膏着重涂抹发根，发中与发尾部分则用12%双氧奶调配的染膏顺着发片均匀涂抹。\n\n4.等待:涂抹完成后停放40分钟以上，直至发尾色度与其他部位一致。\n\n5.冲洗:发尾色度达标后，用温水冲净染膏。\n\n注意事项1.选双氧奶:双氧奶浓度越高上色越快，不过对头发损伤越大，需依据自身发质和期望染发效果合理选择。\n\n2.监测头皮:染发时若头皮感到不适、刺痛，立即停止操作，及时找专业人士求助。\n\n3.染后护理:染完发后，建议日常使用滋润型洗发水和护发素，定期使用发膜，减少染发对头发的损伤，维持头发健康亮泽A Comprehensive Analysis of One-Step Hair Dyeing Technology\n\nScope of Application\nThis technology is applicable to all types of hair textures. It achieves the best results when the base color of the hair is between 3 and 6 degrees. To safely achieve the desired hair color, it is essential to conduct a skin allergy test before hair dyeing to avoid allergic reactions.\n\nRoot Operation\n1. Preparation: According to the desired hair color, pour the target formula and 20VOL hydrogen peroxide cream into a mixing bowl at a ratio of 1:1, and then stir thoroughly with a hair dye brush.\n2. Application: Use a hair dye brush to apply the hair dye cream starting from a point close to the scalp but not touching it. Comb carefully to ensure that all hair roots are covered with the hair dye cream.\n3. Enhancement: Take the same target formula and mix it with 40VOL hydrogen peroxide cream at a ratio of 1:1 to enhance the color - fixing ability of the hair dye cream.\n4. Full Application: Starting from the hair roots, apply the enhanced hair dye cream along the hair strands to the hair ends, ensuring that every part of the hair is saturated with the hair dye cream.\n5. Waiting: After application, let the hair rest naturally for about 40 minutes. The specific duration should refer to the hair dye instructions and be adjusted according to the desired color intensity.\n6. Rinsing: When the hair color is uniform, thoroughly rinse the hair dye cream with warm water until the rinsing water becomes clear.\n\nEnd Operation (for hair color above 7 degrees)\n1. Pretreatment (Optional):\n· Warm Water Priming: If the hair ends are damaged, mix the target formula with warm water at a ratio of 1:1, apply it to the hair ends, and let it rest for 10 minutes.\n· Special Formula Priming: Mix \"0/77 + 0/43 + 2/0 (10:3:1)\" with warm water at a ratio of 1:1, apply it to the 7 - 9 degree position of the hair ends, let it rest for 10 minutes without rinsing, then mix the target formula with 6% hydrogen peroxide cream at a ratio of 1:1 and apply it to the hair ends. Rinse after the color intensity is uniform.\n2. Coloring: Whether or not pretreatment is carried out, mix the target formula and 40VOL hydrogen peroxide cream at a ratio of 1:1 to prepare the hair dye cream.\n3. Application: Starting from the hair roots, apply the hair dye cream prepared with 6% hydrogen peroxide cream to the hair roots with emphasis. For the middle and end parts of the hair, apply the hair dye cream prepared with 12% hydrogen peroxide cream evenly along the hair sections.\n4. Waiting: After application, let it rest for more than 40 minutes until the color intensity of the hair ends is the same as that of other parts.\n5. Rinsing: After the color intensity of the hair ends meets the standard, rinse the hair dye cream thoroughly with warm water.\n\nPrecautions\n1. Selection of Hydrogen Peroxide Cream: The higher the concentration of hydrogen peroxide cream, the faster the color - fixing, but the greater the damage to the hair. It is necessary to choose reasonably according to your own hair texture and desired hair - dyeing effect.\n2. Monitor the Scalp: If you feel discomfort or tingling on the scalp during hair dyeing, stop the operation immediately and seek help from a professional in a timely manner.\n3. Post - Dyeing Care: After hair dyeing, it is recommended to use moisturizing shampoos and conditioners daily, and use hair masks regularly to reduce the damage to the hair caused by hair dyeing and maintain the health and luster of the hair.",
					// 	"creatorId": 1,
					// 	"createTime": 1749004276000,
					// 	"updateTime": 1749376513000
					// },
					// {
					// 	"id": 3,
					// 	"name": "深粉棕色",
					// 	"programCode": null,
					// 	"gramWeight": 15,
					// 	"colorType": "一步染/One-step",
					// 	"categoryName": null,
					// 	"iconPath": null,
					// 	"iconPaths": null,
					// 	"colorComponents": {
					// 		"gray": 1,
					// 		"green": null,
					// 		"yellow": 1,
					// 		"orange": null,
					// 		"red": 4,
					// 		"purple": null,
					// 		"brown": null,
					// 		"blue": 1,
					// 		"black": null,
					// 		"faded_bleached": null,
					// 		"bleached": null,
					// 		"silver": null,
					// 		"gold": null,
					// 		"linen": null,
					// 		"custom1": null,
					// 		"custom2": null,
					// 		"custom3": null,
					// 		"custom4": null
					// 	},
					// 	"developerComponents": {
					// 		"hydrogenPeroxide3Percent": null,
					// 		"hydrogenPeroxide6Percent": null,
					// 		"hydrogenPeroxide9Percent": null,
					// 		"hydrogenPeroxide12Percent": 8
					// 	},
					// 	"sortOrder": 0,
					// 	"description": "",
					// 	"creatorId": 1,
					// 	"createTime": 1749004276000,
					// 	"updateTime": 1749004276000
					// },
					// {
					// 	"id": 4,
					// 	"name": "6度底色",
					// 	"programCode": null,
					// 	"gramWeight": 20,
					// 	"colorType": "一步染/One-step",
					// 	"categoryName": null,
					// 	"iconPath": null,
					// 	"iconPaths": null,
					// 	"colorComponents": {
					// 		"gray": null,
					// 		"green": null,
					// 		"yellow": null,
					// 		"orange": null,
					// 		"red": null,
					// 		"purple": null,
					// 		"brown": null,
					// 		"blue": null,
					// 		"black": null,
					// 		"faded_bleached": null,
					// 		"bleached": null,
					// 		"silver": null,
					// 		"gold": null,
					// 		"linen": null,
					// 		"custom1": null,
					// 		"custom2": null,
					// 		"custom3": null,
					// 		"custom4": null
					// 	},
					// 	"developerComponents": {
					// 		"hydrogenPeroxide3Percent": null,
					// 		"hydrogenPeroxide6Percent": null,
					// 		"hydrogenPeroxide9Percent": null,
					// 		"hydrogenPeroxide12Percent": 10
					// 	},
					// 	"sortOrder": 0,
					// 	"description": "",
					// 	"creatorId": 1,
					// 	"createTime": 1749004276000,
					// 	"updateTime": 1749004276000
					// }

				],
				// 初始化示例数据（根据实际业务调整）
				colorTypeList: [
					"一步染/Apply at one time",
					"人工色/免漂染(Bath dye / Painting dye)",
					"冷棕色系/Cool brown",
					"多段色/Multi-segment color (different base colors)",
					"微潮色系/Slightly trendy color",
					"日系/Japanese and Korean series color chart",
					"暖棕色系/Warm brown",
					"欧系/European series hair dye",
					"浅染深/Light brown",
					"深染浅/Dark to light",
					"潮色系/Trendy color",
					"爆顶染/Burst top dye",
					"生活色系/Life Color",
					"画染/Painting and dyeing",
					"盖白发/Covering white hair",
					"網紅色系/Internet-famous color"
				],

				colorNameList: [
					'色调/Swatche',
					"暖棕/Warm Brown",
					"淡化剂/Faded",
					"灰色/Grey",
					"绿色/Green",
					"黄色/Yellow",
					"橙色/Orange",
					"红色/Red",
					"紫色/Purple",
					"深棕/Dark Brown",
					"浅棕/Light Brown",
					"蓝色/Blue",
					"黑色/Black",
					"冷棕/Cold Brown",
					"棕色/Brown",
					"灰色"
				],

				colorTypeIcons: [
					"/uploads/file/1.jpg",
					"/uploads/file/2.jpg",
					"/uploads/file/4.jpg",
					"/uploads/file/5.jpg",
					"/uploads/file/6.jpg",
					"/uploads/file/7.jpg",
					"/uploads/file/8.jpg",
					"/uploads/file/9.jpg",
					"/uploads/file/10.jpg",
					"/uploads/file/11.jpg",
					"/uploads/file/12.jpg",
					"/uploads/file/13.jpg",
					"/uploads/file/15.jpg",
					"/uploads/file/16.jpg",
					"/uploads/file/17.jpg",
					"/uploads/file/18.jpg"
				],

			};
		},
		onLoad() {
			// 只调用getSwatches，让它处理初始化逻辑
			this.getSwatches()
		},
		onShow() {

		},
		methods: {
			// 处理滚动事件，更新实际滚动位置
			onCategoryScroll(e) {
				this.currentScrollLeft = e.detail.scrollLeft;
			},

			// 滚动到指定索引的居中位置
			scrollToCenter(index) {
				this.$nextTick(() => {
					// 使用节点查询获取scroll-view和目标卡片的实际位置
					const query = uni.createSelectorQuery().in(this);

					// 同时查询scroll-view和目标卡片
					query.select('.color-images').boundingClientRect();
					query.select(`#image-${index}`).boundingClientRect();

					query.exec((res) => {
						const scrollViewRect = res[0];
						const cardRect = res[1];

						if (scrollViewRect && cardRect) {
							// 获取屏幕宽度
							const screenWidth = uni.getSystemInfoSync().windowWidth;

							// 计算卡片相对于scroll-view的位置
							const cardRelativeLeft = cardRect.left - scrollViewRect.left;
							const cardCenter = cardRelativeLeft + cardRect.width / 2;

							// 计算scroll-view的中心位置
							const scrollViewCenter = scrollViewRect.width / 2;

							// 计算需要滚动的距离
							const scrollDistance = cardCenter - scrollViewCenter;

							// 获取当前滚动位置（通过scroll事件更新的实际值）
							const currentScrollLeft = this.currentScrollLeft || 0;

							// 计算新的滚动位置
							const newScrollLeft = currentScrollLeft + scrollDistance;

							// 设置滚动位置
							this.scrollLeft = Math.max(0, newScrollLeft);

							console.log('精确滚动计算:', {
								index,
								scrollViewRect,
								cardRect,
								cardRelativeLeft,
								cardCenter,
								scrollViewCenter,
								scrollDistance,
								currentScrollLeft,
								newScrollLeft: this.scrollLeft
							});
						}
					});
				});

				// 同时设置分类滚动
				this.scrollToCategoryId = 'category-' + index;

				// 延迟清空滚动ID
				setTimeout(() => {
					this.scrollToCategoryId = '';
				}, 500);
			},

			handleColorNameChange(e) {
				const index = e.detail.value
				console.log("赛选颜色改变的按钮 数据:", index)
				if (index == 0) {
					this.$data.colorName = this.$data.colorNameList[0]
					this.getSwatches()
					return;
				}
				this.$data.colorName = this.$data.colorNameList[index]
				this.getSwatches()
			},
			// handleColorName() {
			// 	console.log("点击筛选颜色的按钮")
			// },
			async getSwatches() {
				// 后端需要的参数
				// colorType 类似 潮色,浅染深 (对应data数据池:currentCategory)
				// colorName 类似 X冰魂 (对应data数据池:colorName)
				const colorName = this.$data.colorNameList[0] == this.$data.colorName ? null : this.$data.colorName
				const data = {
					colorType: this.$data.currentCategory,
					colorName: colorName
				}

				// 先尝试从缓存获取
				let result = { data: swatchesCache.get(data) };

				if (!result.data) {
					// 缓存未命中，调用API
					result = await apiService.formula.getSwatches(data);
					console.log("API返回结果", result);

					// 缓存API结果
					if (result.code === 200 && result.data) {
						swatchesCache.set(data, result.data);
					}
				} else {
					console.log("从缓存获取色板数据");
					result.code = 200; // 设置成功状态码
				}

				if (result.code === 200 && result.data) {
					this.colorList = result.data.formulas;
					this.colorTypeList = result.data.colorTypes;

					// 只在初始化时设置默认选中项，避免覆盖用户的选择
					if (this.activeIndex === -1) {
						// 初始化时使用后端返回的默认分类
						this.currentCategory = result.data.currentCategory;
						if (this.colorTypeList && this.colorTypeList.length > 2) {
							this.activeIndex = 2;
							this.currentCategory = this.colorTypeList[2];
							// 滚动到第3个位置
							this.$nextTick(() => {
								this.scrollToCenter(2);
							});
						}
					} else {
						// 用户已经选择了分类，完全保持用户的选择
						// 更新 currentCategory 为新数据中对应位置的分类名称
						if (this.activeIndex >= 0 && this.activeIndex < this.colorTypeList.length) {
							this.currentCategory = this.colorTypeList[this.activeIndex];
						} else {
							// 如果索引超出范围，回退到第一个
							this.activeIndex = 0;
							this.currentCategory = this.colorTypeList[0];
						}
					}

					this.colorNameList = ['色调/Swatche', ...result.data.colorNames];

					this.colorTypeIcons = result.data.iconPaths;
				} else {

				}
			},
			// 点击颜色分类标签事件
			handleCategoryTap(e) {
				const dataset = e.currentTarget.dataset;
				const clickedIndex = parseInt(dataset.index);

				// 先更新activeIndex和分类，确保getSwatches不会覆盖
				this.activeIndex = clickedIndex;
				this.currentCategory = dataset.category;

				// 滚动到被点击的项，使其居中显示
				this.scrollToCenter(clickedIndex);

				// 最后调用getSwatches获取数据
				this.getSwatches();
			},
			// 点击颜色类别图片分类事件
			handleColorTap(e) {
				// console.log("颜色分类图片", e)
				// 获取点击项的index
				const dataset = e.currentTarget.dataset;
				const clickedIndex = parseInt(dataset.index);
				// console.log(dataset)

				// 先更新activeIndex和分类，确保getSwatches不会覆盖
				this.activeIndex = clickedIndex;
				this.currentCategory = dataset.category;

				// 滚动到被点击的项，使其居中显示
				this.scrollToCenter(clickedIndex);

				// 最后调用getSwatches获取数据
				this.getSwatches();
			},



			selectColor(color) {
				console.log('选择颜色:', color.name);
				const isLogin = uni.getStorageSync('isLoggedIn');

				console.log("是否登录: ", isLogin)
				if (!isLogin) {
					uni.showModal({
						title: '未登录请先登录'
					});
					return;
				} else {
					// 保存当前选择的颜色到存储
					uni.setStorageSync('selectedColorInfo', {
						id : color.id,
						name: color.name,
						category: this.currentCategory,
						hex: color.hex
					});

					// 显示加载提示
					uni.showLoading({
						title: '正在加载...'
					});

					console.log(color.description)


					// 跳转到颜色详情页
					const url = '/pages/mall/color/detail?name=' +
						encodeURIComponent(color.name) +
						'&category=' + encodeURIComponent(this.currentCategory) +
						'&color=' + encodeURIComponent(color.hex) +
						'&description=' + encodeURIComponent(color.description) +
						'&iconPath=' + encodeURIComponent(color.iconPath) +
						'&colorComponents=' + encodeURIComponent(JSON.stringify(color.colorComponents)) +
						'&developerComponents=' + encodeURIComponent(JSON.stringify(color.developerComponents));
					console.log("url", url)
					console.log('颜色构造', color.colorComponents)



					setTimeout(() => {
						uni.hideLoading();
						uni.navigateTo({
							url: url,
							fail: (err) => {
								console.error('页面跳转失败:', err);
								uni.showModal({
									title: '跳转失败',
									content: '无法打开颜色详情页: ' + JSON.stringify(err),
									showCancel: false
								});
							},
							success: () => {
								console.log('跳转成功到:', url);
							}
						});
					}, 300);
				}
			}
		}
	}
</script>

<style lang="scss">
	.content {
		width: 100%;
		min-height: 100vh;
		display: flex;
		flex-direction: column;
		align-items: center;
		background-color: #FFFFFF; // 统一背景色
	}

	/* 色彩分类标签样式 - 卡片式设计 */
	.color-category {
		width: 100%;
		white-space: nowrap;
		padding: 20rpx 0;
		background-color: #f8f9fa;
		display: flex;
		align-items: center;
		height: 120rpx;
		box-sizing: border-box;
		position: relative;
		justify-content: center;
	}

	.category-item {
		display: inline-flex;
		padding: 20rpx 30rpx;
		margin: 0 15rpx;
		font-size: 28rpx;
		color: #666666;
		position: relative;
		transition: all 0.3s ease;
		align-items: center;
		// background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
		border-radius: 25rpx;
		box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
		border: 2rpx solid transparent;
		min-width: 120rpx;
		justify-content: center;
		flex-shrink: 0;
	}

	.category-item.active {
		color: #ffffff;
		font-weight: 600;
		background: linear-gradient(135deg, #4285f4 0%, #5a9bff 100%);
		box-shadow: 0 6rpx 20rpx rgba(66, 133, 244, 0.3);
		transform: translateY(-2rpx);
		border: 2rpx solid #4285f4;
	}

	.category-item:hover {
		transform: translateY(-1rpx);
		box-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.12);
	}



	/* 移除重复的分类标签样式定义 */

	/* 颜色图片区域 - 现代化卡片设计 */
	.color-images {
		width: 100%;
		white-space: nowrap;
		background: linear-gradient(180deg, #f8f9fa 0%, #ffffff 100%);
		position: relative;
		z-index: 1;
		min-height: 260rpx;
		display: flex;
		justify-content: flex-start;
		/* 改为flex-start以支持滚动 */
	}

	.color-images::before {
		content: '';
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		height: 2rpx;
		background: linear-gradient(90deg, transparent 0%, #e0e0e0 50%, transparent 100%);
	}

	/* 确保 scroll-view 不裁剪内容 */
	scroll-view.color-images {
		overflow: visible !important;
		scroll-snap-type: x proximity;
		scroll-behavior: smooth;
	}

	.color-image-item {
		width: calc((100vw - 80rpx) / 5);
		height: 220rpx;
		display: inline-block;
		margin: 0 8rpx;
		margin-top: 16rpx;
		position: relative;
		transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
		transform-origin: center;
		border-radius: 20rpx;
		overflow: hidden;
		box-shadow: 0 8rpx 25rpx rgba(0, 0, 0, 0.1);
		background: #ffffff;
		flex-shrink: 0;
		scroll-snap-align: center;
	}

	.color-image-item::before {
		content: '';
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0) 100%);
		z-index: 1;
		pointer-events: none;
	}

	.color-image-item .color-name {
		position: absolute;
		top: 50%;
		left: 15rpx;
		right: 15rpx;
		transform: translateY(-50%);
		color: #fff;
		font-size: 26rpx;
		font-weight: 600;
		text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.8);
		line-height: 32rpx;
		z-index: 2;
		text-align: center;
		// background: rgba(0, 0, 0, 0.4);
		padding: 10rpx 15rpx;
		border-radius: 15rpx;
		backdrop-filter: blur(10rpx);
	}

	.color-image-item .color-name-split {
		display: flex;
		flex-direction: column;
		align-items: center;
		gap: 2rpx;
	}

	.color-image-item .color-name-chinese {
		font-size: 28rpx;
		font-weight: 700;
		line-height: 1.2;
		white-space: normal;
		word-wrap: break-word;
		overflow: visible;
		text-align: center;
		max-width: 100%;
	}

	.color-image-item .color-name-english {
		font-size: 22rpx;
		font-weight: 500;
		line-height: 1.2;
		opacity: 0.9;
		white-space: normal;
		word-wrap: break-word;
		word-break: break-word;
		overflow: visible;
		text-align: center;
		max-width: 100%;
	}

	.color-image-item .color-name-single {
		font-size: 26rpx;
		font-weight: 600;
		line-height: 1.3;
		white-space: normal;
		word-wrap: break-word;
		overflow: visible;
		text-align: center;
		max-width: 100%;
	}

	.color-image-item.active {
		width: calc((100vw - 40rpx) / 4.5);
		height: 250rpx;
		box-shadow: 0 15rpx 40rpx rgba(66, 133, 244, 0.25);
		border-radius: 25rpx;
		margin-left: -10rpx;
		margin-right: -10rpx;
		z-index: 10;
		transform: translateY(-10rpx) scale(1.02);
	}

	.color-image-item.active::after {
		content: '';
		position: absolute;
		top: -5rpx;
		left: -5rpx;
		right: -5rpx;
		bottom: -5rpx;
		background: linear-gradient(135deg, #4285f4, #5a9bff);
		border-radius: 30rpx;
		z-index: -1;
		opacity: 0.3;
	}

	.color-image-placeholder {
		width: 100%;
		height: 100%;
		border-radius: 20rpx;
		object-fit: cover;
	}

	.trend-title {
		width: 100%;
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 25rpx 30rpx;
		box-sizing: border-box;
		background: #ffffff;
		border-radius: 25rpx 25rpx 0 0;
		position: relative;

		.trend-text {
			flex: 0 0 60%;
			font-size: 32rpx;
			font-weight: 700;
			color: #2c3e50;
			overflow: hidden;
			text-overflow: ellipsis;
			white-space: nowrap;
			position: relative;
		}

		.trend-text::after {
			content: '';
			position: absolute;
			bottom: -5rpx;
			left: 0;
			width: 60rpx;
			height: 4rpx;
			background: linear-gradient(90deg, #4285f4, #5a9bff);
			border-radius: 2rpx;
		}

		.filter {
			flex: 0 0 40%;
			display: flex;
			justify-content: flex-end;
			align-items: center;

			.filter-container {
				display: flex;
				align-items: center;
				background: #ffffff;
				padding: 12rpx 20rpx;
				border-radius: 20rpx;
				box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
				transition: all 0.3s ease;
				max-width: 100%;

				.filter-icon-placeholder {
					width: 32rpx;
					height: 32rpx;
					border-radius: 6rpx;
					margin-right: 10rpx;
				}

				.filter-text {
					font-size: 26rpx;
					color: #666;
					font-weight: 500;
					overflow: hidden;
					text-overflow: ellipsis;
					white-space: nowrap;
				}
			}

			.filter-container:hover {
				box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.12);
				transform: translateY(-2rpx);
			}
		}

		.subtitle {
			font-size: 24rpx;
			color: #7f8c8d;
			font-weight: 400;
			margin-left: 10rpx;
		}
	}

	/* 移除重复的trend-title相关样式定义 */

	.color-grid {
		width: 100%;
		display: flex;
		flex-wrap: wrap;
		padding: 20rpx 15rpx;
		box-sizing: border-box;
		background: #ffffff;
		gap: 10rpx;
		justify-content: flex-start;
		flex: 1;
		min-height: 200rpx;
	}

	.color-grid-item {
		width: calc((100% - 30rpx) / 4);
		display: flex;
		flex-direction: column;
		align-items: center;
		padding: 15rpx 8rpx;
		background: transparent;
		border-radius: 15rpx;
		transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
		position: relative;
		overflow: hidden;
		margin-bottom: 5rpx;
	}

	.color-grid-item:hover {
		transform: translateY(-4rpx);
	}

	.color-circle-container {
		width: 150rpx;
		height: 150rpx;
		border-radius: 50%;
		overflow: hidden;
		transition: all 0.3s ease;
		position: relative;
		z-index: 1;
		display: block;
		border: none;
		outline: none;
		background: transparent;
	}

	.color-circle {
		width: 100%;
		height: 100%;
		display: block;
		border: none;
		outline: none;
		object-fit: cover;
	}

	.color-grid-item:hover .color-circle-container {
		transform: scale(1.05);
	}

	.color-grid-item .color-name {
		margin-top: 10rpx;
		font-size: 22rpx;
		color: #333;
		text-align: center;
		line-height: 28rpx;
		font-weight: 500;
		z-index: 1;
		position: relative;
		transition: color 0.3s ease;
		max-width: 100%;
		white-space: normal;
		overflow: visible;
		min-height: 56rpx;
	}

	.color-grid-item .color-name-split {
		display: flex;
		flex-direction: column;
		align-items: center;
		gap: 2rpx;
	}

	.color-grid-item .color-name-chinese {
		font-size: 24rpx;
		font-weight: 600;
		line-height: 1.2;
		color: inherit;
		white-space: nowrap;
		overflow: visible;
	}

	.color-grid-item .color-name-english {
		font-size: 20rpx;
		font-weight: 400;
		line-height: 1.2;
		color: #666;
		white-space: nowrap;
		overflow: visible;
	}

	.color-grid-item .color-name-single {
		font-size: 22rpx;
		font-weight: 500;
		line-height: 1.3;
		color: inherit;
		white-space: normal;
		word-wrap: break-word;
		overflow: visible;
	}

	.color-grid-item:hover .color-name {
		color: #4285f4;
	}

	.color-grid-item:hover .color-name-chinese {
		color: #4285f4;
	}

	.color-grid-item:hover .color-name-english {
		color: #5a9bff;
	}
</style>