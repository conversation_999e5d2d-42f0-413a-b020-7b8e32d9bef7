<template>
	<view class="success-container">
		<!-- 加载状态 -->
		<view v-if="loading" class="loading-container">
			<view class="loading-spinner"></view>
			<text class="loading-text">正在获取订单信息...</text>
		</view>

		<!-- 成功状态 -->
		<view v-else class="success-content">
			<view class="success-icon">
				<text class="icon">✓</text>
			</view>

			<view class="success-title">
				<text>订单提交成功</text>
			</view>

			<view class="order-info" v-if="orderData">
				<view class="info-item">
					<text class="label">订单金额</text>
					<text class="value">¥{{ orderData.totalAmount || orderData.price || '0.00' }}</text>
				</view>
				<view class="info-item">
					<text class="label">订单编号</text>
					<text class="value">{{ orderData.orderNo || orderNo }}</text>
				</view>
				<view class="info-item">
					<text class="label">创建时间</text>
					<text class="value">{{ formatTime(orderData.createTime) }}</text>
				</view>
				<view class="info-item" v-if="orderData.receiverName">
					<text class="label">收货人</text>
					<text class="value">{{ orderData.receiverName }}</text>
				</view>
			</view>

			<view class="action-btns">
				<button class="btn primary" @click="goPay">立即支付</button>
				<button class="btn secondary" @click="viewOrder">查看订单</button>
				<button class="btn outline" @click="goHome">返回首页</button>
			</view>
		</view>
	</view>
</template>

<script>
import { apiService } from '../../utils/request';
export default {
	data() {
		return {
			orderId: null,
			orderNo: null,
			orderData: null,
			loading: true
		}
	},
	onLoad(options) {
		console.log('订单成功页面参数:', options);
		if (options.id) {
			this.orderId = options.id;
		}
		if (options.orderNo) {
			this.orderNo = options.orderNo;
		}
		this.loadOrderData();
	},
	methods: {
		async loadOrderData() {
			try {
				this.loading = true;

				// 优先使用订单ID获取详情，如果没有则使用订单号
				const token = uni.getStorageSync('token');
				let response;

				response = await uni.request({
					// url: 'http://127.0.0.1:6549/api/order/' + this.orderNo,
					url: 'https://www.narenqiqige.com/api/order/' + this.orderNo,
					method: 'GET',
					header: {
						'Authorization': 'Bearer ' + token,
						'Content-Type': 'application/json'
					}
				});

				// 通过订单号获取详情

				// if (this.orderId) {
				// 	// 通过订单ID获取详情
				// 	response = await uni.request({
				// 		url: 'http://127.0.0.1:6549/api/order/' + this.orderId,
				// 		method: 'GET',
				// 		header: {
				// 			'Authorization': 'Bearer ' + token,
				// 			'Content-Type': 'application/json'
				// 		}
				// 	});
				// } else if (this.orderNo) {
				// 	// 通过订单号获取详情
				// 	response = await uni.request({
				// 		url: 'http://127.0.0.1:6549/api/order/' + this.orderNo,
				// 		method: 'GET',
				// 		header: {
				// 			'Authorization': 'Bearer ' + token,
				// 			'Content-Type': 'application/json'
				// 		}
				// 	});
				// }
				console.log("订单支付页返回订单详情", response)

				if (response && response.data && response.data.code === 200) {
					console.log("返回订单结果", response)
					this.orderData = response.data.data.order;
					// 如果通过订单号获取到了数据，更新订单ID
					if (!this.orderId && this.orderData.id) {
						this.orderId = this.orderData.id;
					}
				}
				console.log("this.orderData",this.orderData)
			} catch (error) {
				console.error('获取订单信息失败:', error);
				uni.showToast({
					title: '获取订单信息失败',
					icon: 'none'
				});
			} finally {
				this.loading = false;
			}
		},

		formatTime(timeStr) {
			if (!timeStr) return new Date().toLocaleString();

			try {
				const date = new Date(timeStr);
				return date.toLocaleString('zh-CN', {
					year: 'numeric',
					month: '2-digit',
					day: '2-digit',
					hour: '2-digit',
					minute: '2-digit',
					second: '2-digit'
				});
			} catch (error) {
				return timeStr;
			}
		},

		goPay() {
			if (this.orderId) {
				console.log("支付中....")
				// uni.navigateTo({
				// 	url: `/pages/payment/payment?orderId=${this.orderId}`
				// });
				apiService.mall.orders.pay(this.orderNo, { paymentMethod: 1 })
					.then(res => {
						// console.log(res.data);
						// console.log(res.data.paySign);
						wx.requestPayment({
							timeStamp: res.data.timeStamp,
							nonceStr: res.data.nonceStr,
							package: res.data.package,
							signType: 'MD5',
							paySign: res.data.paySign,
							success: function (e) {
								console.log(e); // 查看完整返回对象

								// 支付成功处理
								uni.showToast({
									title: '支付成功',
									icon: 'success'
								});

								// 可以跳转到支付成功页面或订单详情页
								setTimeout(() => {
									// 跳转到订单详情页
									uni.redirectTo({
										url: `/pages/my/order-detail`
									});
								}, 1500);
							},
							fail: function (e) {
								console.log("支付失败", e);
								// 支付成功提示
								uni.showToast({
									title: '支付失败',
									icon: 'none',
									duration: 1500,
								});

								setTimeout(() => {
									uni.switchTab({
										url: '/pages/mall/mall'
									});
								}, 1500)
							}
						})
					})
			} else {
				uni.showToast({
					title: '订单信息不完整',
					icon: 'none'
				});
			}
		},

		viewOrder() {
			if (this.orderId) {
				// url: `/pages/order/detail?id=${this.orderId}`
				uni.navigateTo({
					url: `/pages/my/order-detail?id=${this.orderId}`
				});
			} else {
				uni.showToast({
					title: '订单信息不完整',
					icon: 'none'
				});
			}
		},

		goHome() {
			uni.switchTab({
				url: '/pages/mall/mall'
			});
		}
	}
}
</script>

<style lang="scss">
.success-container {
	min-height: 100vh;
	background-color: #fff;
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 100rpx 40rpx;
	box-sizing: border-box;
	width: 100%;
}

.loading-container {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	min-height: 60vh;
}

.loading-spinner {
	width: 60rpx;
	height: 60rpx;
	border: 4rpx solid #f3f3f3;
	border-top: 4rpx solid #007aff;
	border-radius: 50%;
	animation: spin 1s linear infinite;
	margin-bottom: 20rpx;
}

@keyframes spin {
	0% {
		transform: rotate(0deg);
	}

	100% {
		transform: rotate(360deg);
	}
}

.loading-text {
	color: #666;
	font-size: 28rpx;
}

.success-content {
	width: 100%;
	display: flex;
	flex-direction: column;
	align-items: center;
}

.success-icon {
	width: 160rpx;
	height: 160rpx;
	background-color: #4caf50;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-bottom: 40rpx;

	.icon {
		color: #fff;
		font-size: 100rpx;
	}
}

.success-title {
	font-size: 40rpx;
	font-weight: bold;
	margin-bottom: 60rpx;
	color: #333;
}

.order-info {
	width: 100%;
	max-width: 600rpx;
	background-color: #f9f9f9;
	border-radius: 20rpx;
	padding: 30rpx;
	margin-bottom: 60rpx;
	box-sizing: border-box;
}

.info-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 20rpx;
	font-size: 28rpx;

	&:last-child {
		margin-bottom: 0;
	}
}

.label {
	color: #666;
	flex-shrink: 0;
}

.value {
	color: #333;
	font-weight: bold;
	text-align: right;
	word-break: break-all;
}

.action-btns {
	width: 100%;
	max-width: 600rpx;
}

.btn {
	width: 100%;
	height: 90rpx;
	line-height: 90rpx;
	border-radius: 45rpx;
	margin-bottom: 30rpx;
	font-size: 30rpx;
	border: none;

	&.primary {
		background-color: #f44336;
		color: #fff;
	}

	&.secondary {
		background-color: #ff9800;
		color: #fff;
	}

	&.outline {
		background-color: #fff;
		color: #666;
		border: 2rpx solid #ddd;
	}

	&:last-child {
		margin-bottom: 0;
	}
}
</style>