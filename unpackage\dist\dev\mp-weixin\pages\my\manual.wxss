/* 水平间距 */
/* 水平间距 */
/* 全局样式修复 - 确保页面占满屏幕宽度 */
page {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
}

/* 通用容器样式 */
.container, .page-container {
  width: 100%;
  min-height: 100vh;
  box-sizing: border-box;
}

/* 修复小程序页面宽度问题 */
uni-page-body {
  width: 100% !important;
}

/* 确保所有view元素正确继承宽度 */
view {
  box-sizing: border-box;
}
.manual-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100%;
  background-color: #f7f8fa;
  box-sizing: border-box;
}
.header {
  display: flex;
  align-items: center;
  height: 88rpx;
  background-color: #ffffff;
  padding: 0 30rpx;
  position: relative;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}
.back-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.title {
  flex: 1;
  text-align: center;
  font-size: 36rpx;
  font-weight: 500;
  margin-right: 60rpx;
}
.content {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 20rpx;
}
.loading-container, .error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400rpx;
  color: #999;
}
.retry-btn {
  margin-top: 20rpx;
  background-color: #007AFF;
  color: white;
  border-radius: 10rpx;
}
.category-tabs {
  display: flex;
  background-color: #ffffff;
  border-radius: 10rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
}
.tab-item {
  flex: 1;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  color: #666;
  position: relative;
}
.tab-item.active {
  color: #007AFF;
  font-weight: 500;
}
.tab-item.active::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 4rpx;
  background-color: #007AFF;
  border-radius: 2rpx;
}
.resource-list {
  flex: 1;
}
.resource-item {
  display: flex;
  align-items: center;
  background-color: #ffffff;
  padding: 30rpx;
  margin-bottom: 20rpx;
  border-radius: 10rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}
.resource-icon {
  width: 80rpx;
  height: 80rpx;
  background-color: #f0f5ff;
  border-radius: 10rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
}
.resource-icon .iconfont {
  font-size: 40rpx;
  color: #007AFF;
}
.resource-info {
  flex: 1;
}
.resource-name {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 10rpx;
}
.resource-desc {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 10rpx;
}
.resource-meta {
  display: flex;
  align-items: center;
}
.resource-size, .resource-date {
  font-size: 22rpx;
  color: #b2b2b2;
}
.resource-size {
  margin-right: 20rpx;
}
.resource-action {
  display: flex;
  align-items: center;
}
.resource-action .iconfont {
  font-size: 40rpx;
  color: #007AFF;
  padding: 10rpx;
}
.image-list {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -10rpx;
}
.image-list .image-item {
  width: calc(33.33% - 20rpx);
  margin: 0 10rpx 20rpx;
  background-color: #fff;
  border-radius: 8rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}
.image-list .image-item .thumbnail {
  width: 100%;
  height: 180rpx;
  background-color: #f5f5f5;
}
.image-list .image-item .image-name {
  font-size: 24rpx;
  color: #333;
  padding: 16rpx;
  text-align: center;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.video-icon {
  background-color: #fff0f0;
}
.video-icon .iconfont {
  color: #ff4d4f;
}