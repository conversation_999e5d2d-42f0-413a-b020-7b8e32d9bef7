/* 水平间距 */
/* 水平间距 */
/* 全局样式修复 - 确保页面占满屏幕宽度 */
page.data-v-a4b747c5 {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
}

/* 通用容器样式 */
.container.data-v-a4b747c5, .page-container.data-v-a4b747c5 {
  width: 100%;
  min-height: 100vh;
  box-sizing: border-box;
}

/* 修复小程序页面宽度问题 */
uni-page-body.data-v-a4b747c5 {
  width: 100% !important;
}

/* 确保所有view元素正确继承宽度 */
view.data-v-a4b747c5 {
  box-sizing: border-box;
}
.permission-popup.data-v-a4b747c5 {
  background-color: #fff;
  border-radius: 16px 16px 0 0;
  overflow: hidden;
}
.popup-header.data-v-a4b747c5 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 18px 20px;
  border-bottom: 1px solid #f5f5f5;
}
.popup-header .popup-title.data-v-a4b747c5 {
  font-size: 18px;
  font-weight: bold;
  color: #333;
}
.popup-content.data-v-a4b747c5 {
  padding: 0 20px;
}
.info-section.data-v-a4b747c5 {
  padding: 15px 0;
  border-bottom: 1px solid #f5f5f5;
}
.info-section .info-item.data-v-a4b747c5 {
  display: flex;
  margin-bottom: 10px;
}
.info-section .info-item.data-v-a4b747c5:last-child {
  margin-bottom: 0;
}
.info-section .info-item .info-label.data-v-a4b747c5 {
  width: 90px;
  color: #666;
  font-size: 14px;
}
.info-section .info-item .info-value.data-v-a4b747c5 {
  flex: 1;
  color: #333;
  font-size: 14px;
}
.selection-section.data-v-a4b747c5 {
  padding: 15px 0;
}
.selection-section .section-title.data-v-a4b747c5 {
  display: block;
  margin-bottom: 12px;
  color: #333;
  font-size: 16px;
  font-weight: bold;
}
.staff-list .staff-item.data-v-a4b747c5 {
  display: flex;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f5f5f5;
}
.staff-list .staff-item.data-v-a4b747c5:last-child {
  border-bottom: none;
}
.staff-list .staff-item .staff-info.data-v-a4b747c5 {
  margin-left: 10px;
}
.staff-list .staff-item .staff-info .staff-name.data-v-a4b747c5 {
  font-size: 15px;
  color: #333;
}
.staff-list .staff-item .staff-info .staff-level.data-v-a4b747c5 {
  margin-left: 8px;
  font-size: 12px;
  color: #999;
}
.popup-footer.data-v-a4b747c5 {
  display: flex;
  padding: 12px 20px;
  background-color: #fff;
  border-top: 1px solid #f5f5f5;
}
.popup-footer .action-btn.data-v-a4b747c5 {
  flex: 1;
  height: 44px;
  line-height: 44px;
  font-size: 16px;
  border-radius: 6px;
}
.popup-footer .action-btn.cancel-btn.data-v-a4b747c5 {
  margin-right: 12px;
  background-color: #f8f8f8;
  color: #666;
}
.popup-footer .action-btn.confirm-btn.data-v-a4b747c5 {
  background-color: #409EFF;
  color: #fff;
}

/* 安全区域适配 */
@supports (bottom: constant(safe-area-inset-bottom)) or (bottom: env(safe-area-inset-bottom)) {
.popup-footer.data-v-a4b747c5 {
    padding-bottom: constant(safe-area-inset-bottom);
    padding-bottom: env(safe-area-inset-bottom);
}
}