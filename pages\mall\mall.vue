<template>
	<view class="container">
		<!-- 顶部搜索栏 -->
		<view class="header">
			<view class="search-container">
				<view class="search-input" @click="goToSearch">
					<uni-icons type="search" size="18" color="#999"></uni-icons>
					<input type="text" placeholder="搜索商品" v-model="searchKeyword" @confirm="doSearch"
						confirm-type="search" placeholder-class="placeholder-style" />
					<view class="search-btn blue-btn" @click.stop="goToSearch">
						<text>搜索</text>
					</view>
				</view>
			</view>
			<view class="scan-icon" @click="scanCode">
				<uni-icons type="scan" size="24" color="#4285f4"></uni-icons>
			</view>
		</view>

		<!-- 主要内容区域 -->
		<scroll-view class="main-content" scroll-y refresher-enabled :refresher-triggered="refreshing"
			@refresherrefresh="onRefresh" @scrolltolower="loadMoreProducts">
			<!-- 分类专区 -->
			<view class="section category-section">
				<view class="section-header">
					<text class="section-title">自营专区</text>
					<text class="section-subtitle">行业赋能，降本增效</text>
				</view>

				<view class="category-grid">
					<view class="category-item" v-for="(category, index) in categories" :key="category.id"
						@click="navigateToCategory(category)">
						<view class="category-icon">
							<image v-if="category.icon && isImageUrl(category.icon)"
								:src="getFullImageUrl(category.icon)" mode="aspectFit" lazy-load
								@error="onImageError(category.id)" />
							<view v-else class="icon-fallback">
								<text class="icon">{{getDefaultIcon(category.name)}}</text>
							</view>
						</view>
						<text class="category-name">{{category.name}}</text>
					</view>
				</view>
			</view>

			<!-- 热门推荐 -->
			<view class="section product-section">
				<view class="section-header">
					<text class="section-title">热门推荐</text>
					<text class="section-subtitle">大家都在买</text>
				</view>

				<view class="product-grid">
					<view class="product-card" v-for="product in products" :key="product.id"
						@click="navigateToProduct(product.id)">
						<view class="product-image-container">
							<image class="product-image" :src="getFirstImage(product.images)" mode="aspectFill"
								lazy-load :fade-show="false" />
							<view v-if="product.tag" class="product-tag">{{product.tag}}</view>
						</view>
						<view class="product-info">
							<text class="product-name">{{product.productName}}</text>
							<view class="meta-rating-container">
								<view class="price-container">
									<text class="current-price">¥{{product.price}}</text>
									<text v-if="product.originalPrice"
										class="original-price">¥{{product.originalPrice}}</text>
								</view>
								<view class="product-meta">
									<text class="sales">已售{{product.sales || 0}}件</text>
								</view>
							</view>
							<view class="rating">
								<uni-icons type="star-filled" size="17" color="#4285f4"></uni-icons>
								<text>{{product.rating || '5.0'}}%</text>
							</view>
						</view>
					</view>
				</view>

				<!-- 加载状态 -->
				<view v-if="loading" class="loading-container">
					<uni-load-more status="loading"></uni-load-more>
				</view>

				<!-- 没有更多数据 -->
				<view v-if="!hasMore && products.length > 0" class="no-more">
					<text>— 已经到底啦 —</text>
				</view>
			</view>

			<!-- 空状态 -->
			<view v-if="products.length === 0 && !loading" class="empty-state">
				<view class="empty-icon">
					<text class="icon">📦</text>
				</view>
				<text class="empty-text">暂无商品数据</text>
				<text class="empty-desc">商城正在筹备中，敬请期待</text>
				<button class="refresh-btn" @click="refreshData">重新加载</button>
			</view>
		</scroll-view>
	</view>
</template>

<script>
	import {
		apiService
	} from '@/utils/request.js';
	import {
		getFirstImage,
		getFullImageUrl
	} from '@/utils/imageUtils.js';

	export default {
		data() {
			return {
				categories: [],
				products: [],
				loading: false,
				refreshing: false,
				hasMore: true,
				currentPage: 1,
				pageSize: 10,
				searchKeyword: '',
				imageErrors: {}
			};
		},
		onLoad() {
			this.loadInitialData();
		},
		methods: {
			// 扫码功能
			scanCode() {
				// uni.scanCode({
				// 	success: (res) => {
				// 		console.log('扫码结果:', res);
				// 		uni.showToast({
				// 			title: '扫码成功: ' + res.result,
				// 			icon: 'none'
				// 		});
				// 	},
				// 	fail: (err) => {
				// 		console.error('扫码失败:', err);
				// 		uni.showToast({
				// 			title: '扫码失败',
				// 			icon: 'none'
				// 		});
				// 	}
				// });
			},
			async loadInitialData() {
				await Promise.all([
					this.loadCategories(),
					this.loadProducts()
				]);
			},

			async loadCategories() {
				try {
					const response = await apiService.mall.categories.list();
					if (response.code === 200) {
						this.categories = (response.data || []).slice(0, 15); // 限制显示数量
					}
				} catch (error) {
					console.error('获取分类失败:', error);
					uni.showToast({
						title: '获取分类失败',
						icon: 'none'
					});
				}
			},

			async loadProducts(isLoadMore = false) {
				if (this.loading) return;
				this.loading = true;

				try {
					const params = {
						page: isLoadMore ? this.currentPage + 1 : 1,
						pageSize: this.pageSize
					};

					const response = await apiService.mall.products.list(params);

					if (response.code === 200) {
						const newProducts = response.data.products || response.data.records || response.data || [];

						if (isLoadMore) {
							this.products = [...this.products, ...newProducts];
							this.currentPage++;
						} else {
							this.products = newProducts;
							this.currentPage = 1;
						}

						this.hasMore = newProducts.length === this.pageSize;
					}
				} catch (error) {
					console.error('获取商品失败:', error);
				} finally {
					this.loading = false;
					this.refreshing = false;
				}
			},

			onRefresh() {
				if (this.refreshing) return;
				this.refreshing = true;
				this.loadProducts();
				this.loadCategories();
			},

			loadMoreProducts() {
				if (!this.hasMore || this.loading) return;
				this.loadProducts(true);
			},

			// 刷新数据
			refreshData() {
				this.products = [];
				this.categories = [];
				this.currentPage = 1;
				this.hasMore = true;

				// uni.showLoading({
				// 	title: '刷新中...'
				// });

				Promise.all([
					this.loadCategories(),
					this.loadProducts()
				]).finally(() => {
					uni.hideLoading();
					uni.showToast({
						title: '刷新完成',
						icon: 'success'
					});
				});
			},

			navigateToCategory(category) {
				uni.navigateTo({
					url: `/pages/mall/category/category?categoryId=${category.id}&categoryName=${encodeURIComponent(category.name)}`
				});
			},

			navigateToProduct(productId) {
				uni.navigateTo({
					url: `/pages/mall/product/detail?id=${productId}`
				});
			},

			doSearch() {
				const keyword = this.searchKeyword.trim();
				if (!keyword) {
					uni.showToast({
						title: '请输入搜索内容',
						icon: 'none'
					});
					return;
				}

				uni.navigateTo({
					url: `/pages/mall/search/search?keyword=${encodeURIComponent(keyword)}`
				});
			},

			goToSearch() {
				uni.navigateTo({
					url: '/pages/mall/search/search'
				});
			},

			// 图片相关方法
			getFirstImage(images) {
				return getFirstImage(images);
			},

			getFullImageUrl(url) {
				return getFullImageUrl(url);
			},

			isImageUrl(url) {
				if (!url) return false;
				return url.startsWith('http') || url.startsWith('/static') || url.startsWith('data:image');
			},

			onImageError(categoryId) {
				this.$set(this.imageErrors, categoryId, true);
			},

			getDefaultIcon(name) {
				const iconMap = {
					'数码': '📱',
					'服装': '👕',
					'家居': '🏠',
					'美妆': '💄',
					'食品': '🍎',
					'运动': '⚽',
					'图书': '📚',
					'母婴': '👶'
				};
				return iconMap[name] || '🛍️';
			}
		}
	};
</script>

<style lang="scss">
	page {
		background: -webkit-linear-gradient(to bottom, #4285f4, #fff);
		background: linear-gradient(to bottom, #4285f4, #fff);
		height: 100%;
	}

	.container {
		display: flex;
		flex-direction: column;
		height: 100vh;
		position: relative;
		background: transparent;

		.header {
			display: flex;
			align-items: center;
			justify-content: space-between;
			/* 添加这行使两个元素分开 */
			background: transparent;
			padding: 16rpx 24rpx;
			position: sticky;
			top: 0;
			z-index: 100;

			.search-container {
				flex: 1;
				/* 搜索容器占据剩余空间 */
				margin-right: 20rpx;
				/* 添加右边距 */

				.search-input {
					background-color: rgba(255, 255, 255, 0.95);
					border-radius: 48rpx;
					padding: 12rpx 24rpx;
					display: flex;
					align-items: center;
					box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);

					input {
						flex: 1;
						font-size: 28rpx;
						margin-left: 15rpx;
						color: #333;
					}

					.placeholder-style {
						color: #999;
					}

					.search-btn {
						margin-left: 15rpx;
						font-size: 28rpx;
						color: #999;
						padding: 0 15rpx;
					}

					.blue-btn {
						background-color: #4285f4;
						color: white !important;
						padding: 8rpx 20rpx;
						border-radius: 30rpx;
						margin-left: 20rpx;
						margin-right: 3rpx;
					}
				}
			}

			.scan-icon {
				background-color: rgba(255, 255, 255, 0.95);
				width: 80rpx;
				height: 80rpx;
				border-radius: 50%;
				display: flex;
				align-items: center;
				justify-content: center;
				box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
				flex-shrink: 0;
				/* 防止图标被压缩 */
			}

		}

		.main-content {
			flex: 1;
			height: 1px;
			overflow: hidden;
			padding-bottom: env(safe-area-inset-bottom);
			background-color: transparent;
		}
	}

	.empty-icon {
		display: flex;
		justify-content: center; // 水平居中
		align-items: center; // 垂直居中（如果需要）
		// margin-bottom: 15rpx;
		// width: 100%; // 确保占据整行
	}

	.section {
		background-color: #fff;
		border-radius: 20rpx;
		margin: 20rpx;
		padding: 20rpx;
		box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);

		.section-header {
			margin-bottom: 25rpx;

			.section-title {
				font-size: 34rpx;
				font-weight: bold;
				color: #333;
				margin-right: 15rpx;
			}

			.section-subtitle {
				font-size: 24rpx;
				color: #999;
			}
		}
	}

	/* 分类和商品区域调整背景为半透明 */
	.category-section,
	.product-section {
		background-color: rgba(255, 255, 255, 0.92);
		backdrop-filter: blur(10rpx);
	}

	.category-section {
		.category-grid {
			display: grid;
			grid-template-columns: repeat(5, 1fr);
			gap: 20rpx;

			.category-item {
				display: flex;
				flex-direction: column;
				align-items: center;

				.category-icon {
					width: 90rpx;
					height: 90rpx;
					margin-bottom: 15rpx;
					background-color: #f8f8f8;
					// border-radius: 50%;
					display: flex;
					align-items: center;
					justify-content: center;
					overflow: hidden;

					// image {
					// 	width: 60rpx;
					// 	height: 60rpx;
					// }

					.icon-fallback {
						font-size: 50rpx;
					}
				}

				.category-name {
					font-size: 24rpx;
					color: #666;
					text-align: center;
					line-height: 1.4;
				}
			}
		}
	}

	.product-section {
		.product-grid {
			display: grid;
			grid-template-columns: repeat(2, 1fr);
			gap: 20rpx;

			.product-card {
				border-radius: 12rpx;
				overflow: hidden;
				background-color: #fff;
				box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
				transition: transform 0.2s;

				&:active {
					transform: scale(0.98);
				}

				.product-image-container {
					position: relative;
					width: 100%;
					height: 0;
					padding-bottom: 100%;

					.product-image {
						position: absolute;
						width: 100%;
						height: 100%;
					}

					.product-tag {
						position: absolute;
						top: 10rpx;
						left: 10rpx;
						background-color: #ff4757;
						color: #fff;
						font-size: 20rpx;
						padding: 4rpx 12rpx;
						border-radius: 20rpx;
					}
				}

				.product-info {
					padding: 20rpx 15rpx;

					.product-name {
						font-size: 26rpx;
						color: #333;
						display: -webkit-box;
						-webkit-line-clamp: 2;
						-webkit-box-orient: vertical;
						overflow: hidden;
						text-overflow: ellipsis;
						height: 70rpx;
						line-height: 35rpx;
						margin-bottom: 15rpx;
					}

					.price-container {
						display: flex;
						align-items: center;
						margin-bottom: 12rpx;

						.current-price {
							font-size: 30rpx;
							color: #ff4757;
							font-weight: bold;
						}

						.original-price {
							font-size: 24rpx;
							color: #999;
							text-decoration: line-through;
							margin-left: 10rpx;
						}
					}

					.meta-rating-container {
						display: flex;
						justify-content: space-between;
						align-items: center; // 确保垂直居中
						width: 100%;
						height: 40rpx; // 设置固定高度
						line-height: 40rpx; // 与高度相同确保垂直居中
						font-size: 24rpx; // 统一字体大小
					}

					.product-meta {
						display: flex;
						justify-content: space-between;
						align-items: center;
						font-size: 30rpx;

						.sales {
							// font-size: 15rpx;
							font-size: 24rpx;
							color: #999;
						}

						.rating {
							display: flex;
							align-items: center;
							gap: 4px;
							margin-top: 15rpx;

							text {
								font-size: 8rpx;
								color: #4285f4;
								margin-left: 4rpx;
							}
						}
					}
				}
			}
		}
	}

	.empty-icon .icon {
		font-size: 120rpx;
		opacity: 0.6;
	}

	.loading-container,
	.no-more {
		padding: 30rpx 0;
		text-align: center;
		color: #999;
		font-size: 26rpx;
	}

	.empty-state {
		background-color: rgba(255, 255, 255, 0.85);
		border-radius: 24rpx;
		margin: 24rpx;
		padding: 60rpx 0;
		backdrop-filter: blur(10rpx);

		.empty-image {
			width: 200rpx;
			height: 200rpx;
			opacity: 0.6;
			margin-bottom: 30rpx;
		}

		.empty-text {
			font-size: 30rpx;
			color: #666;
			margin-bottom: 5rpx;
			text-align: center;
			display: block !important;
		}

		.empty-desc {
			font-size: 26rpx;
			color: #999;
			margin-bottom: 20rpx;
			text-align: center;
			display: block !important;
		}

		.refresh-btn {
			width: 60%;
			background-color: #4285f4;
			color: #fff;
			border: none;
			border-radius: 50rpx;
			padding: 0 60rpx;
			height: 70rpx;
			line-height: 70rpx;
			font-size: 28rpx;

			&::after {
				border: none;
			}
		}
	}
</style>