"use strict";
const common_vendor = require("../common/vendor.js");
const utils_request = require("./request.js");
class DyeConsumptionManager {
  constructor() {
    this.consumptionCache = /* @__PURE__ */ new Map();
    this.inventoryCache = /* @__PURE__ */ new Map();
    this.alertThresholds = {
      low: 20,
      // 低库存阈值 (%)
      critical: 10
      // 严重不足阈值 (%)
    };
  }
  /**
   * 计算染膏使用量
   * @param {Array} colorComponents - 颜色组件数组
   * @param {Object} recipe - 配方对象
   * @returns {Object} 计算结果
   */
  calculateConsumption(colorComponents, recipe) {
    const consumption = {};
    const totalWeight = this.calculateTotalWeight(recipe);
    Object.keys(recipe).forEach((colorKey) => {
      const amount = recipe[colorKey];
      if (amount && amount > 0) {
        consumption[colorKey] = {
          amount,
          percentage: (amount / totalWeight * 100).toFixed(2),
          colorCode: this.getColorCode(colorKey)
        };
      }
    });
    return {
      totalWeight,
      consumption,
      timestamp: (/* @__PURE__ */ new Date()).toISOString()
    };
  }
  /**
   * 计算配方总重量
   * @param {Object} recipe - 配方对象
   * @returns {number} 总重量
   */
  calculateTotalWeight(recipe) {
    return Object.values(recipe).reduce((total, amount) => {
      return total + (amount || 0);
    }, 0);
  }
  /**
   * 获取颜色代码映射
   * @param {string} colorKey - 颜色键名
   * @returns {string} 颜色代码
   */
  getColorCode(colorKey) {
    const colorCodeMap = {
      "gray": "0x30",
      "green": "0x31",
      "yellow": "0x32",
      "orange": "0x33",
      "red": "0x34",
      "purple": "0x35",
      "brown": "0x36",
      "blue": "0x37",
      "black": "0x38",
      "faded_bleached": "0x39",
      "hydrogenPeroxide3Percent": "0x40",
      "hydrogenPeroxide12Percent": "0x41"
    };
    return colorCodeMap[colorKey] || colorKey;
  }
  /**
   * 记录染膏使用量（带完整调色信息）
   * @param {string} deviceCode - 设备编码
   * @param {Object} consumptionData - 消耗数据
   * @param {number} commandId - 指令ID (可选)
   * @param {Object} dyeInfo - 调色信息 (可选)
   * @returns {Promise<boolean>} 记录结果
   */
  async recordConsumptionWithInfo(deviceCode, consumptionData, commandId = null, dyeInfo = null) {
    try {
      common_vendor.index.__f__("warn", "at utils/dyeConsumptionManager.js:91", "开始记录染膏使用量(完整信息) 设备id", deviceCode);
      common_vendor.index.__f__("warn", "at utils/dyeConsumptionManager.js:92", "开始记录染膏使用量(完整信息) 消耗数据", consumptionData);
      common_vendor.index.__f__("warn", "at utils/dyeConsumptionManager.js:93", "开始记录染膏消耗量(完整信息) commandid", commandId);
      common_vendor.index.__f__("warn", "at utils/dyeConsumptionManager.js:94", "调色信息", dyeInfo);
      const records = [];
      Object.keys(consumptionData.consumption).forEach((colorKey) => {
        const consumption = consumptionData.consumption[colorKey];
        const record = {
          deviceCode,
          commandId,
          colorCode: consumption.colorCode,
          usageAmount: consumption.amount
        };
        if (dyeInfo && records.length === 0) {
          record.mode = dyeInfo.mode || "smart";
          record.formulaName = dyeInfo.formulaName;
          record.colorHex = dyeInfo.colorHex;
          record.colorComponents = dyeInfo.colorComponents ? JSON.stringify(dyeInfo.colorComponents) : null;
        }
        records.push(record);
      });
      const promises = records.map(
        (record) => utils_request.apiService.mall.dyeConsumption.recordUsage(record)
      );
      const results = await Promise.all(promises);
      common_vendor.index.__f__("log", "at utils/dyeConsumptionManager.js:124", "记录返回结果", results);
      const success = results.every((result) => result.code === 200);
      if (success) {
        this.updateConsumptionCache(deviceCode, consumptionData);
        common_vendor.index.__f__("log", "at utils/dyeConsumptionManager.js:130", "染膏使用量记录成功(完整信息):", consumptionData, "指令ID:", commandId, "调色信息:", dyeInfo);
      }
      return success;
    } catch (error) {
      common_vendor.index.__f__("error", "at utils/dyeConsumptionManager.js:135", "记录染膏使用量失败(完整信息):", error);
      return false;
    }
  }
  /**
   * 记录染膏使用量（兼容旧版本）
   * @param {string} deviceCode - 设备编码
   * @param {Object} consumptionData - 消耗数据
   * @param {number} commandId - 指令ID (可选)
   * @returns {Promise<boolean>} 记录结果
   */
  async recordConsumption(deviceCode, consumptionData, commandId = null) {
    try {
      common_vendor.index.__f__("warn", "at utils/dyeConsumptionManager.js:149", "开始记录染膏使用量 设备id", deviceCode);
      common_vendor.index.__f__("warn", "at utils/dyeConsumptionManager.js:150", "开始记录染膏使用量 消耗数据", consumptionData);
      common_vendor.index.__f__("warn", "at utils/dyeConsumptionManager.js:151", "开始记录染膏消耗量 commandid", commandId);
      const records = [];
      Object.keys(consumptionData.consumption).forEach((colorKey) => {
        const consumption = consumptionData.consumption[colorKey];
        records.push({
          deviceCode,
          commandId,
          // 直接使用传入的指令ID
          colorCode: consumption.colorCode,
          usageAmount: consumption.amount
        });
      });
      const promises = records.map(
        (record) => utils_request.apiService.mall.dyeConsumption.recordUsage(record)
      );
      const results = await Promise.all(promises);
      common_vendor.index.__f__("log", "at utils/dyeConsumptionManager.js:170", "记录返回结果", results);
      const success = results.every((result) => result.code === 200);
      if (success) {
        this.updateConsumptionCache(deviceCode, consumptionData);
        common_vendor.index.__f__("log", "at utils/dyeConsumptionManager.js:176", "染膏使用量记录成功:", consumptionData, "指令ID:", commandId);
      }
      return success;
    } catch (error) {
      common_vendor.index.__f__("error", "at utils/dyeConsumptionManager.js:181", "记录染膏使用量失败:", error);
      return false;
    }
  }
  /**
   * 获取设备库存状态
   * @param {string} deviceCode - 设备编码
   * @param {boolean} useCache - 是否使用缓存
   * @returns {Promise<Object>} 库存状态
   */
  async getInventoryStatus(deviceCode, useCache = true) {
    try {
      if (useCache && this.inventoryCache.has(deviceCode)) {
        const cached = this.inventoryCache.get(deviceCode);
        const cacheAge = Date.now() - cached.timestamp;
        if (cacheAge < 3e4) {
          return cached.data;
        }
      }
      const response = await utils_request.apiService.mall.dyeConsumption.getInventoryStatus(deviceCode);
      if (response.code === 200) {
        const inventoryData = this.processInventoryData(response.data);
        this.inventoryCache.set(deviceCode, {
          data: inventoryData,
          timestamp: Date.now()
        });
        return inventoryData;
      }
      throw new Error(response.message || "获取库存状态失败");
    } catch (error) {
      common_vendor.index.__f__("error", "at utils/dyeConsumptionManager.js:218", "获取库存状态失败:", error);
      return this.getDefaultInventoryStatus();
    }
  }
  /**
   * 处理库存数据
   * @param {Array|Object} rawData - 原始库存数据（可能是数组或包含inventory字段的对象）
   * @returns {Object} 处理后的库存数据
   */
  processInventoryData(rawData) {
    const inventory = {};
    const alerts = [];
    let inventoryData = [];
    if (Array.isArray(rawData)) {
      inventoryData = rawData;
    } else if (rawData && typeof rawData === "object") {
      if (rawData.inventory && typeof rawData.inventory === "object") {
        inventoryData = Object.values(rawData.inventory);
      } else if (rawData.alerts && rawData.totalColors !== void 0) {
        return rawData;
      } else {
        common_vendor.index.__f__("warn", "at utils/dyeConsumptionManager.js:247", "processInventoryData: 无法识别的数据结构", rawData);
        return this.getDefaultInventoryStatus();
      }
    } else {
      common_vendor.index.__f__("warn", "at utils/dyeConsumptionManager.js:251", "processInventoryData: rawData 不是有效的数据结构", rawData);
      return this.getDefaultInventoryStatus();
    }
    inventoryData.forEach((item) => {
      const percentage = this.calculateStockPercentage(item.currentQuantity, item.maxCapacity);
      inventory[item.colorCode] = {
        colorName: item.colorName,
        currentQuantity: item.currentQuantity,
        maxCapacity: item.maxCapacity,
        percentage,
        unit: item.unit || "g",
        status: this.getStockStatus(percentage),
        lastUpdated: item.lastUpdated
      };
      if (percentage <= this.alertThresholds.critical) {
        alerts.push({
          colorCode: item.colorCode,
          colorName: item.colorName,
          percentage,
          level: "critical"
        });
      } else if (percentage <= this.alertThresholds.low) {
        alerts.push({
          colorCode: item.colorCode,
          colorName: item.colorName,
          percentage,
          level: "warning"
        });
      }
    });
    return {
      inventory,
      alerts,
      totalColors: Object.keys(inventory).length,
      lowStockCount: alerts.length,
      lastUpdateTime: (/* @__PURE__ */ new Date()).toISOString()
    };
  }
  /**
   * 计算库存百分比
   * @param {number} current - 当前数量
   * @param {number} max - 最大容量
   * @returns {number} 百分比
   */
  calculateStockPercentage(current, max) {
    if (!max || max === 0)
      return 0;
    return Math.round(current / max * 100);
  }
  /**
   * 获取库存状态
   * @param {number} percentage - 库存百分比
   * @returns {string} 状态
   */
  getStockStatus(percentage) {
    if (percentage <= this.alertThresholds.critical)
      return "critical";
    if (percentage <= this.alertThresholds.low)
      return "warning";
    return "normal";
  }
  /**
   * 获取默认库存状态
   * @returns {Object} 默认库存状态
   */
  getDefaultInventoryStatus() {
    const defaultColors = [
      "gray",
      "green",
      "yellow",
      "orange",
      "red",
      "purple",
      "brown",
      "blue",
      "black",
      "faded_bleached",
      "hydrogenPeroxide3Percent",
      "hydrogenPeroxide12Percent"
    ];
    const inventory = {};
    defaultColors.forEach((color) => {
      inventory[color] = {
        colorName: color,
        currentQuantity: 0,
        maxCapacity: 100,
        percentage: 0,
        unit: "g",
        status: "critical",
        lastUpdated: (/* @__PURE__ */ new Date()).toISOString()
      };
    });
    return {
      inventory,
      alerts: [],
      totalColors: defaultColors.length,
      lowStockCount: defaultColors.length
    };
  }
  /**
   * 更新消耗缓存
   * @param {string} deviceCode - 设备编码
   * @param {Object} consumptionData - 消耗数据
   */
  updateConsumptionCache(deviceCode, consumptionData) {
    if (!this.consumptionCache.has(deviceCode)) {
      this.consumptionCache.set(deviceCode, []);
    }
    const cache = this.consumptionCache.get(deviceCode);
    cache.push(consumptionData);
    if (cache.length > 100) {
      cache.splice(0, cache.length - 100);
    }
  }
  /**
   * 检查染膏余量是否足够
   * @param {string} deviceCode - 设备编码
   * @param {Object} recipe - 配方
   * @returns {Promise<Object>} 检查结果
   */
  async checkSufficientStock(deviceCode, recipe) {
    try {
      const inventoryStatus = await this.getInventoryStatus(deviceCode);
      const insufficient = [];
      const warnings = [];
      Object.keys(recipe).forEach((colorKey) => {
        const requiredAmount = recipe[colorKey];
        if (requiredAmount && requiredAmount > 0) {
          const colorCode = this.getColorCode(colorKey);
          const stock = inventoryStatus.inventory[colorCode];
          if (stock) {
            if (stock.currentQuantity < requiredAmount) {
              insufficient.push({
                colorKey,
                colorName: stock.colorName,
                required: requiredAmount,
                available: stock.currentQuantity,
                shortage: requiredAmount - stock.currentQuantity
              });
            } else if (stock.currentQuantity - requiredAmount < stock.maxCapacity * 0.1) {
              warnings.push({
                colorKey,
                colorName: stock.colorName,
                required: requiredAmount,
                remaining: stock.currentQuantity - requiredAmount
              });
            }
          }
        }
      });
      return {
        sufficient: insufficient.length === 0,
        insufficient,
        warnings,
        canProceed: insufficient.length === 0
      };
    } catch (error) {
      common_vendor.index.__f__("error", "at utils/dyeConsumptionManager.js:415", "检查库存失败:", error);
      return {
        sufficient: false,
        insufficient: [],
        warnings: [],
        canProceed: false,
        error: error.message
      };
    }
  }
  /**
   * 初始化染膏消耗管理器
   * @param {string} deviceCode - 设备编码
   */
  initialize(deviceCode) {
    if (!deviceCode) {
      common_vendor.index.__f__("warn", "at utils/dyeConsumptionManager.js:432", "设备编码为空，无法初始化染膏消耗管理器");
      return;
    }
    common_vendor.index.__f__("log", "at utils/dyeConsumptionManager.js:436", `染膏消耗管理器已初始化: ${deviceCode}`);
  }
  /**
   * 清除缓存
   * @param {string} deviceCode - 设备编码 (可选)
   */
  clearCache(deviceCode = null) {
    if (deviceCode) {
      this.consumptionCache.delete(deviceCode);
      this.inventoryCache.delete(deviceCode);
    } else {
      this.consumptionCache.clear();
      this.inventoryCache.clear();
    }
  }
}
const dyeConsumptionManager = new DyeConsumptionManager();
exports.dyeConsumptionManager = dyeConsumptionManager;
//# sourceMappingURL=../../.sourcemap/mp-weixin/utils/dyeConsumptionManager.js.map
