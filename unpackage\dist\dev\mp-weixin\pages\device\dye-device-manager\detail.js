"use strict";
const common_vendor = require("../../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      device: {
        managers: []
        // 确保managers数组存在
      },
      assignedStaff: []
    };
  },
  onLoad(options) {
    this.loadDeviceDetail(options.id);
  },
  methods: {
    async loadDeviceDetail(id) {
      common_vendor.index.showLoading({ title: "加载中..." });
      try {
        const [deviceRes, staffRes] = await Promise.all([
          this.$api.dyeDevice.getDetail({ id }),
          this.$api.dyeDevice.getAssignedStaff({ deviceId: id })
        ]);
        this.device = deviceRes.data;
        if (!this.device.managers) {
          this.device.managers = [];
        }
        this.assignedStaff = staffRes.data;
      } catch (e) {
        common_vendor.index.showToast({ title: "加载失败", icon: "none" });
      } finally {
        common_vendor.index.hideLoading();
      }
    },
    getPermissionText(type) {
      switch (type) {
        case 1:
          return "普通权限";
        case 2:
          return "管理员权限";
        case 3:
          return "维护权限";
        default:
          return "未知权限";
      }
    },
    getPermissionLevel(type) {
      switch (type) {
        case 1:
          return "普通";
        case 2:
          return "管理员";
        case 3:
          return "维护";
        default:
          return "未知";
      }
    },
    getPermissionColor(type) {
      switch (type) {
        case 1:
          return "#4cd964";
        case 2:
          return "#2979ff";
        case 3:
          return "#ff9900";
        default:
          return "#999";
      }
    },
    goToLog() {
      common_vendor.index.navigateTo({
        url: `/pages/device/dye-device-manager/log?id=${this.device.id}`
      });
    },
    back() {
      common_vendor.index.navigateBack();
    }
  }
};
if (!Array) {
  const _easycom_uni_nav_bar2 = common_vendor.resolveComponent("uni-nav-bar");
  _easycom_uni_nav_bar2();
}
const _easycom_uni_nav_bar = () => "../../../uni_modules/uni-nav-bar/components/uni-nav-bar/uni-nav-bar.js";
if (!Math) {
  _easycom_uni_nav_bar();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.o($options.back),
    b: common_vendor.p({
      title: "染膏设备详情",
      ["left-icon"]: "back"
    }),
    c: common_vendor.t($data.device.name),
    d: common_vendor.t($data.device.sn),
    e: common_vendor.t($data.device.type),
    f: common_vendor.t($data.device.purchaseDate),
    g: common_vendor.t($data.device.status === 1 ? "已绑定" : "未绑定"),
    h: $data.device.status === 1 ? "#4cd964" : "#dd524d",
    i: $data.device.status === 1
  }, $data.device.status === 1 ? {
    j: common_vendor.t($data.device.storeName)
  } : {}, {
    k: $data.device.status === 1
  }, $data.device.status === 1 ? {
    l: common_vendor.t($data.device.manager)
  } : {}, {
    m: $data.device.status === 1
  }, $data.device.status === 1 ? {
    n: common_vendor.t($data.device.bindTime)
  } : {}, {
    o: $data.device.status === 1
  }, $data.device.status === 1 ? common_vendor.e({
    p: common_vendor.f($data.device.managers, (manager, k0, i0) => {
      return {
        a: common_vendor.t(manager.name),
        b: common_vendor.t($options.getPermissionText(manager.permissionType)),
        c: common_vendor.t($options.getPermissionLevel(manager.permissionType)),
        d: $options.getPermissionColor(manager.permissionType),
        e: manager.id
      };
    }),
    q: $data.device.managers && $data.device.managers.length === 0
  }, $data.device.managers && $data.device.managers.length === 0 ? {} : {}) : {}, {
    r: $data.device.status === 1
  }, $data.device.status === 1 ? common_vendor.e({
    s: common_vendor.f($data.assignedStaff, (staff, k0, i0) => {
      return {
        a: common_vendor.t(staff.name),
        b: common_vendor.t($options.getPermissionText(staff.permissionType)),
        c: common_vendor.t($options.getPermissionLevel(staff.permissionType)),
        d: $options.getPermissionColor(staff.permissionType),
        e: staff.id
      };
    }),
    t: $data.assignedStaff.length === 0
  }, $data.assignedStaff.length === 0 ? {} : {}) : {}, {
    v: common_vendor.o((...args) => $options.goToLog && $options.goToLog(...args))
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-f83b36bf"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/pages/device/dye-device-manager/detail.js.map
