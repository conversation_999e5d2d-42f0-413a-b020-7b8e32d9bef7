/* 水平间距 */
/* 水平间距 */
/* 全局样式修复 - 确保页面占满屏幕宽度 */
page {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
}

/* 通用容器样式 */
.container, .page-container {
  width: 100%;
  min-height: 100vh;
  box-sizing: border-box;
}

/* 修复小程序页面宽度问题 */
uni-page-body {
  width: 100% !important;
}

/* 确保所有view元素正确继承宽度 */
view {
  box-sizing: border-box;
}
.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f5f5;
}
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400rpx;
}
.loading-text {
  font-size: 30rpx;
  color: #999;
}
.form-container {
  background-color: #fff;
  padding: 0 20rpx;
}
.form-item {
  display: flex;
  align-items: center;
  padding: 30rpx 0;
  border-bottom: 1rpx solid #eee;
}
.form-label {
  width: 180rpx;
  font-size: 30rpx;
  color: #663c00;
  font-weight: 500;
}
.form-input {
  flex: 1;
  font-size: 30rpx;
  color: #999;
}
.form-input.disabled {
  color: #999;
  background-color: #f8f8f8;
  padding: 10rpx;
  border-radius: 8rpx;
  font-style: italic;
}
.form-input-container {
  flex: 1;
  display: flex;
  height: 50rpx;
  align-items: center;
  position: relative;
}
.form-input-field {
  flex: 1;
  font-size: 30rpx;
  height: 70rpx;
  color: #333;
  padding: 10rpx;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  background-color: #fff;
}
.form-input-field:disabled {
  background-color: #f8f8f8;
  color: #999;
}
.bind-status {
  position: absolute;
  right: 10rpx;
  font-size: 24rpx;
  color: #4285f4;
  background-color: #e3f2fd;
  padding: 4rpx 8rpx;
  border-radius: 4rpx;
}
.form-input.selector, .form-input.address {
  display: flex;
  justify-content: space-between;
}
.dropdown-icon, .location-icon {
  color: #999;
  font-size: 24rpx;
}
.form-detail-address {
  background-color: #fff;
  padding: 20rpx 0 30rpx 180rpx;
  border-bottom: 1rpx solid #eee;
}
.detail-input {
  font-size: 28rpx;
  color: #999;
}
.save-btn {
  position: fixed;
  bottom: 40rpx;
  left: 40rpx;
  right: 40rpx;
  height: 90rpx;
  background-color: #4285f4;
  border-radius: 45rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #fff;
  font-size: 32rpx;
}
.save-btn.disabled {
  background-color: #ccc;
  color: #999;
}