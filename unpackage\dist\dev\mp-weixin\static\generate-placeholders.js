/**
 * 占位图片生成脚本
 * 
 * 此脚本可以自动生成应用所需的所有占位图片
 * 运行方法: node generate-placeholders.js
 */
const fs = require('fs');
const path = require('path');
const { createCanvas } = require('canvas');

// 确保目录存在
function ensureDirectoryExists(dirPath) {
  if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath, { recursive: true });
  }
}

// 创建占位图片
function createPlaceholder(width, height, bgColor, text, outputPath) {
  const canvas = createCanvas(width, height);
  const ctx = canvas.getContext('2d');
  
  // 绘制背景
  ctx.fillStyle = bgColor;
  ctx.fillRect(0, 0, width, height);
  
  // 绘制文字
  ctx.fillStyle = '#ffffff';
  ctx.textAlign = 'center';
  ctx.textBaseline = 'middle';
  
  // 根据图片尺寸调整字体大小
  const fontSize = Math.max(12, Math.min(30, Math.floor(width / 10)));
  ctx.font = `${fontSize}px Arial`;
  
  // 如果文字太长，分行显示
  const words = text.split(' ');
  if (words.length > 1 && width < 150) {
    const lineHeight = fontSize * 1.2;
    const y = height / 2 - lineHeight / 2;
    
    words.forEach((word, i) => {
      ctx.fillText(word, width / 2, y + i * lineHeight);
    });
  } else {
    ctx.fillText(text, width / 2, height / 2);
  }
  
  // 显示尺寸信息
  ctx.font = `${Math.max(10, fontSize / 2)}px Arial`;
  ctx.fillText(`${width}x${height}`, width / 2, height - 10);
  
  // 保存到文件
  const buffer = canvas.toBuffer('image/png');
  fs.writeFileSync(outputPath, buffer);
  
  console.log(`Created: ${outputPath} (${width}x${height})`);
}

// 创建所有需要的图片
function generateAllPlaceholders() {
  const staticPath = path.resolve(__dirname);
  const tabbarPath = path.join(staticPath, 'tabbar');
  
  ensureDirectoryExists(staticPath);
  ensureDirectoryExists(tabbarPath);
  
  const images = [
    // 底部导航栏图标
    { name: 'home', path: tabbarPath, width: 40, height: 40, color: '#999999', text: '首页' },
    { name: 'home_selected', path: tabbarPath, width: 40, height: 40, color: '#4285f4', text: '首页' },
    { name: 'palette', path: tabbarPath, width: 40, height: 40, color: '#999999', text: '色板' },
    { name: 'palette_selected', path: tabbarPath, width: 40, height: 40, color: '#4285f4', text: '色板' },
    { name: 'device', path: tabbarPath, width: 40, height: 40, color: '#999999', text: '设备' },
    { name: 'device_selected', path: tabbarPath, width: 40, height: 40, color: '#4285f4', text: '设备' },
    { name: 'user', path: tabbarPath, width: 40, height: 40, color: '#999999', text: '我的' },
    { name: 'user_selected', path: tabbarPath, width: 40, height: 40, color: '#4285f4', text: '我的' },
    
    // Logo
    { name: 'soruda-logo', path: staticPath, width: 60, height: 60, color: '#4285f4', text: 'Logo' },
    
    // 颜色分类图片
    { name: 'color1', path: staticPath, width: 200, height: 150, color: '#5D4037', text: '生活色' },
    { name: 'color2', path: staticPath, width: 200, height: 150, color: '#00BCD4', text: '微潮色' },
    { name: 'color3', path: staticPath, width: 200, height: 150, color: '#FFEB3B', text: '潮色' },
    { name: 'color4', path: staticPath, width: 200, height: 150, color: '#9E9E9E', text: '深染浅' },
    { name: 'color5', path: staticPath, width: 200, height: 150, color: '#9C27B0', text: '浅染深' },
    
    // 图标
    { name: 'filter', path: staticPath, width: 32, height: 32, color: '#757575', text: '筛选' },
    { name: 'location', path: staticPath, width: 30, height: 30, color: '#4285f4', text: '位置' },
    { name: 'bluetooth', path: staticPath, width: 60, height: 60, color: '#757575', text: '蓝牙' },
    { name: 'wifi', path: staticPath, width: 60, height: 60, color: '#4285f4', text: 'WiFi' },
    
    // 我的页面图标
    { name: 'default-avatar', path: staticPath, width: 120, height: 120, color: '#E0E0E0', text: '头像' },
    { name: 'store', path: staticPath, width: 60, height: 60, color: '#4285f4', text: '门店' },
    { name: 'qrcode', path: staticPath, width: 60, height: 60, color: '#4285f4', text: '二维码' },
    { name: 'color-test', path: staticPath, width: 60, height: 60, color: '#4CAF50', text: '色膏' },
    { name: 'color-record', path: staticPath, width: 60, height: 60, color: '#2196F3', text: '记录' },
    { name: 'statistics', path: staticPath, width: 60, height: 60, color: '#673AB7', text: '统计' },
    { name: 'order', path: staticPath, width: 60, height: 60, color: '#FF9800', text: '订单' },
    { name: 'guide-banner', path: staticPath, width: 80, height: 80, color: '#FF9800', text: '指引' },
    { name: 'repair', path: staticPath, width: 50, height: 50, color: '#F44336', text: '报修' },
    { name: 'policy', path: staticPath, width: 50, height: 50, color: '#3F51B5', text: '政策' },
    { name: 'notice', path: staticPath, width: 50, height: 50, color: '#FFC107', text: '消息' },
    { name: 'about', path: staticPath, width: 50, height: 50, color: '#607D8B', text: '关于' },
    { name: 'manual', path: staticPath, width: 50, height: 50, color: '#795548', text: '手册' },
    { name: 'download', path: staticPath, width: 50, height: 50, color: '#009688', text: '下载' },
    { name: 'guide', path: staticPath, width: 50, height: 50, color: '#FF5722', text: '引导' },
    { name: 'cooperation', path: staticPath, width: 50, height: 50, color: '#8BC34A', text: '合作' },
    { name: 'customer-service', path: staticPath, width: 60, height: 60, color: '#4285f4', text: '客服' },
    
    // 商城图标
    { name: 'scan', path: staticPath, width: 50, height: 50, color: '#FFFFFF', text: '扫描' },
    
    // 服务类别图标
    { name: 'service1', path: staticPath, width: 80, height: 80, color: '#E91E63', text: '洗发水' },
    { name: 'service2', path: staticPath, width: 80, height: 80, color: '#9C27B0', text: '护理' },
    { name: 'service3', path: staticPath, width: 80, height: 80, color: '#673AB7', text: '护肤' },
    { name: 'service4', path: staticPath, width: 80, height: 80, color: '#3F51B5', text: '化妆' },
    { name: 'service5', path: staticPath, width: 80, height: 80, color: '#2196F3', text: '美容' },
    { name: 'service6', path: staticPath, width: 80, height: 80, color: '#03A9F4', text: '护发素' },
    { name: 'service7', path: staticPath, width: 80, height: 80, color: '#00BCD4', text: '发膜' },
    { name: 'service8', path: staticPath, width: 80, height: 80, color: '#009688', text: '精油' },
    { name: 'service9', path: staticPath, width: 80, height: 80, color: '#4CAF50', text: '造型' },
    { name: 'service10', path: staticPath, width: 80, height: 80, color: '#8BC34A', text: '药水' },
    { name: 'service11', path: staticPath, width: 80, height: 80, color: '#CDDC39', text: '烫发' },
    { name: 'service12', path: staticPath, width: 80, height: 80, color: '#FFEB3B', text: '染发剂' },
    { name: 'service13', path: staticPath, width: 80, height: 80, color: '#FFC107', text: '染发' },
    { name: 'service14', path: staticPath, width: 80, height: 80, color: '#FF9800', text: '课程' },
    { name: 'service15', path: staticPath, width: 80, height: 80, color: '#FF5722', text: '其它' },
    
    // 产品图片
    { name: 'product1', path: staticPath, width: 200, height: 200, color: '#42A5F5', text: '产品1' },
    { name: 'product2', path: staticPath, width: 200, height: 200, color: '#66BB6A', text: '产品2' }
  ];
  
  // 生成所有图片
  images.forEach(img => {
    const outputPath = path.join(img.path, `${img.name}.png`);
    createPlaceholder(img.width, img.height, img.color, img.text, outputPath);
  });
  
  console.log(`成功生成了 ${images.length} 个占位图片！`);
}

// 执行生成
generateAllPlaceholders(); 