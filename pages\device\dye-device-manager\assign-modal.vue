<template>
  <!-- 底部弹出层 -->
  <uni-popup 
    ref="popup" 
    type="bottom" 
    :safe-area="true"
    @change="handlePopupChange"
  >
    <view class="permission-popup">
      <!-- 头部 -->
      <view class="popup-header">
        <text class="popup-title">分配染膏设备使用权限</text>
        <uni-icons 
          type="closeempty" 
          size="22" 
          color="#999" 
          @click="handleClose"
        />
      </view>
      
      <!-- 内容区域 -->
      <scroll-view 
        scroll-y 
        class="popup-content"
        :style="{maxHeight: contentMaxHeight + 'px'}"
      >
        <!-- 设备信息 -->
        <view class="info-section">
          <view class="info-item">
            <text class="info-label">设备名称：</text>
            <text class="info-value">{{ deviceInfo.name || '--' }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">设备编号：</text>
            <text class="info-value">{{ deviceInfo.sn || deviceInfo.device_code || '--' }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">当前负责人：</text>
            <text class="info-value">{{ deviceInfo.manager?.name || '未绑定' }}</text>
          </view>
        </view>
        
        <!-- 发型师选择 -->
        <view class="selection-section">
          <text class="section-title">选择可使用的发型师</text>
          <view class="staff-list">
            <checkbox-group @change="handleStaffSelection">
              <label 
                class="staff-item" 
                v-for="staff in staffList" 
                :key="staff.id"
              >
                <checkbox 
                  :value="staff.id" 
                  :checked="selectedStaff.includes(staff.id)" 
                  color="#409EFF"
                />
                <view class="staff-info">
                  <text class="staff-name">{{ staff.nickname || staff.username }}</text>
                  <text class="staff-level">{{ getUserTypeText(staff.userType) }}</text>
                </view>
              </label>
            </checkbox-group>
          </view>
        </view>
      </scroll-view>
      
      <!-- 底部操作按钮 -->
      <view class="popup-footer">
        <button 
          class="action-btn cancel-btn" 
          @click="handleClose"
          :disabled="isLoading"
        >
          取消
        </button>
        <button 
          class="action-btn confirm-btn" 
          type="primary" 
          @click="handleConfirm"
          :loading="isLoading"
        >
          确认分配
        </button>
      </view>
    </view>
  </uni-popup>
</template>

<script>
export default {
  name: 'AssignPermissionPopup',
  
  props: {
    // 内容区域最大高度（根据屏幕高度自动计算）
    contentMaxHeight: {
      type: Number,
      default: 400
    }
  },
  
  data() {
    return {
      deviceInfo: {},       // 设备信息
      staffList: [],        // 发型师列表
      selectedStaff: [],     // 已选中的发型师
      selectedPermissionLevel: 2, // 选中的权限级别，默认控制权限
      isLoading: false,     // 加载状态
      isVisible: false      // 弹窗显示状态
    }
  },
  
  methods: {
    // 打开弹窗
    async open(device) {
      this.deviceInfo = device || {}
      this.isLoading = true
      
      try {
        await this.fetchStaffList()
        await this.fetchAssignedStaff()
        this.$refs.popup.open()
        this.isVisible = true
      } catch (error) {
        console.error('弹窗打开失败:', error)
        uni.showToast({
          title: '加载失败',
          icon: 'none'
        })
      } finally {
        this.isLoading = false
      }
    },
    
    // 关闭弹窗
    close() {
      if (this.isLoading) return
      this.$refs.popup.close()
      this.isVisible = false
    },
    
    // 弹窗状态变化
    handlePopupChange(e) {
      this.isVisible = e.show
      if (!e.show) {
        this.$emit('close')
      }
    },
    
    // 手动关闭
    handleClose() {
      this.close()
      this.$emit('cancel')
    },
    
    // 获取可分配权限的员工列表
    async fetchStaffList() {
      try {
        const response = await this.$api.device.getAvailableStaff(this.deviceInfo.id)
        this.staffList = response.data || []
      } catch (error) {
        console.error('获取员工列表失败:', error)
        uni.showToast({
          title: '获取员工列表失败',
          icon: 'none'
        })
        this.staffList = []
      }
    },
    
    // 获取已分配权限的员工
    async fetchAssignedStaff() {
      try {
        const response = await this.$api.device.getAssignedStaff(this.deviceInfo.id)
        const assignedStaff = response.data || []
        // 提取已分配员工的ID列表
        this.selectedStaff = assignedStaff.map(staff => staff.userId)
      } catch (error) {
        console.error('获取已分配员工失败:', error)
        this.selectedStaff = []
      }
    },
    
    // 选择发型师
    handleStaffSelection(e) {
      this.selectedStaff = e.detail.value
    },

    // 获取用户类型文本
    getUserTypeText(userType) {
      const typeMap = {
        1: '普通用户',
        2: '商家用户',
        3: '管理员',
        4: '系统管理员'
      }
      return typeMap[userType] || '未知'
    },
    
    // 确认分配
    async handleConfirm() {
      if (this.isLoading) return
      
      this.isLoading = true
      
      try {
        // 调用权限分配API
        const response = await this.$api.device.assignPermission({
          deviceId: this.deviceInfo.id,
          userIds: this.selectedStaff,
          permissionLevel: this.selectedPermissionLevel || 2 // 默认控制权限
        })

        if (response.success) {
          uni.showToast({
            title: '权限分配成功',
            icon: 'success'
          })

          this.$emit('confirm', {
            deviceId: this.deviceInfo.id,
            staffIds: this.selectedStaff
          })
        } else {
          throw new Error(response.message || '权限分配失败')
        }
        
        this.close()
      } catch (error) {
        uni.showToast({
          title: error.message || '分配失败',
          icon: 'none'
        })
      } finally {
        this.isLoading = false
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.permission-popup {
  background-color: #fff;
  border-radius: 16px 16px 0 0;
  overflow: hidden;
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 18px 20px;
  border-bottom: 1px solid #f5f5f5;
  
  .popup-title {
    font-size: 18px;
    font-weight: bold;
    color: #333;
  }
}

.popup-content {
  padding: 0 20px;
}

.info-section {
  padding: 15px 0;
  border-bottom: 1px solid #f5f5f5;
  
  .info-item {
    display: flex;
    margin-bottom: 10px;
    
    &:last-child {
      margin-bottom: 0;
    }
    
    .info-label {
      width: 90px;
      color: #666;
      font-size: 14px;
    }
    
    .info-value {
      flex: 1;
      color: #333;
      font-size: 14px;
    }
  }
}

.selection-section {
  padding: 15px 0;
  
  .section-title {
    display: block;
    margin-bottom: 12px;
    color: #333;
    font-size: 16px;
    font-weight: bold;
  }
}

.staff-list {
  .staff-item {
    display: flex;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #f5f5f5;
    
    &:last-child {
      border-bottom: none;
    }
    
    .staff-info {
      margin-left: 10px;
      
      .staff-name {
        font-size: 15px;
        color: #333;
      }
      
      .staff-level {
        margin-left: 8px;
        font-size: 12px;
        color: #999;
      }
    }
  }
}

.popup-footer {
  display: flex;
  padding: 12px 20px;
  background-color: #fff;
  border-top: 1px solid #f5f5f5;
  
  .action-btn {
    flex: 1;
    height: 44px;
    line-height: 44px;
    font-size: 16px;
    border-radius: 6px;
    
    &.cancel-btn {
      margin-right: 12px;
      background-color: #f8f8f8;
      color: #666;
    }
    
    &.confirm-btn {
      background-color: #409EFF;
      color: #fff;
    }
  }
}

/* 安全区域适配 */
@supports (bottom: constant(safe-area-inset-bottom)) or (bottom: env(safe-area-inset-bottom)) {
  .popup-footer {
    padding-bottom: constant(safe-area-inset-bottom);
    padding-bottom: env(safe-area-inset-bottom);
  }
}
</style>