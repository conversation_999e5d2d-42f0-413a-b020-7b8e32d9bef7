/* 水平间距 */
/* 水平间距 */
/* 全局样式修复 - 确保页面占满屏幕宽度 */
page {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
}

/* 通用容器样式 */
.container, .page-container {
  width: 100%;
  min-height: 100vh;
  box-sizing: border-box;
}

/* 修复小程序页面宽度问题 */
uni-page-body {
  width: 100% !important;
}

/* 确保所有view元素正确继承宽度 */
view {
  box-sizing: border-box;
}
page {
  background: linear-gradient(to bottom, #4285f4, #fff);
  height: 100%;
}
.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  position: relative;
  background: transparent;
}
.container .header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  /* 添加这行使两个元素分开 */
  background: transparent;
  padding: 16rpx 24rpx;
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  z-index: 100;
}
.container .header .search-container {
  flex: 1;
  /* 搜索容器占据剩余空间 */
  margin-right: 20rpx;
  /* 添加右边距 */
}
.container .header .search-container .search-input {
  background-color: rgba(255, 255, 255, 0.95);
  border-radius: 48rpx;
  padding: 12rpx 24rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}
.container .header .search-container .search-input input {
  flex: 1;
  font-size: 28rpx;
  margin-left: 15rpx;
  color: #333;
}
.container .header .search-container .search-input .placeholder-style {
  color: #999;
}
.container .header .search-container .search-input .search-btn {
  margin-left: 15rpx;
  font-size: 28rpx;
  color: #999;
  padding: 0 15rpx;
}
.container .header .search-container .search-input .blue-btn {
  background-color: #4285f4;
  color: white !important;
  padding: 8rpx 20rpx;
  border-radius: 30rpx;
  margin-left: 20rpx;
  margin-right: 3rpx;
}
.container .header .scan-icon {
  background-color: rgba(255, 255, 255, 0.95);
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  flex-shrink: 0;
  /* 防止图标被压缩 */
}
.container .main-content {
  flex: 1;
  height: 1px;
  overflow: hidden;
  padding-bottom: env(safe-area-inset-bottom);
  background-color: transparent;
}
.empty-icon {
  display: flex;
  justify-content: center;
  align-items: center;
}
.section {
  background-color: #fff;
  border-radius: 20rpx;
  margin: 20rpx;
  padding: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
}
.section .section-header {
  margin-bottom: 25rpx;
}
.section .section-header .section-title {
  font-size: 34rpx;
  font-weight: bold;
  color: #333;
  margin-right: 15rpx;
}
.section .section-header .section-subtitle {
  font-size: 24rpx;
  color: #999;
}

/* 分类和商品区域调整背景为半透明 */
.category-section,
.product-section {
  background-color: rgba(255, 255, 255, 0.92);
  -webkit-backdrop-filter: blur(10rpx);
          backdrop-filter: blur(10rpx);
}
.category-section .category-grid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 20rpx;
}
.category-section .category-grid .category-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.category-section .category-grid .category-item .category-icon {
  width: 90rpx;
  height: 90rpx;
  margin-bottom: 15rpx;
  background-color: #f8f8f8;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}
.category-section .category-grid .category-item .category-icon .icon-fallback {
  font-size: 50rpx;
}
.category-section .category-grid .category-item .category-name {
  font-size: 24rpx;
  color: #666;
  text-align: center;
  line-height: 1.4;
}
.product-section .product-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
}
.product-section .product-grid .product-card {
  border-radius: 12rpx;
  overflow: hidden;
  background-color: #fff;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  transition: transform 0.2s;
}
.product-section .product-grid .product-card:active {
  transform: scale(0.98);
}
.product-section .product-grid .product-card .product-image-container {
  position: relative;
  width: 100%;
  height: 0;
  padding-bottom: 100%;
}
.product-section .product-grid .product-card .product-image-container .product-image {
  position: absolute;
  width: 100%;
  height: 100%;
}
.product-section .product-grid .product-card .product-image-container .product-tag {
  position: absolute;
  top: 10rpx;
  left: 10rpx;
  background-color: #ff4757;
  color: #fff;
  font-size: 20rpx;
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
}
.product-section .product-grid .product-card .product-info {
  padding: 20rpx 15rpx;
}
.product-section .product-grid .product-card .product-info .product-name {
  font-size: 26rpx;
  color: #333;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  height: 70rpx;
  line-height: 35rpx;
  margin-bottom: 15rpx;
}
.product-section .product-grid .product-card .product-info .price-container {
  display: flex;
  align-items: center;
  margin-bottom: 12rpx;
}
.product-section .product-grid .product-card .product-info .price-container .current-price {
  font-size: 30rpx;
  color: #ff4757;
  font-weight: bold;
}
.product-section .product-grid .product-card .product-info .price-container .original-price {
  font-size: 24rpx;
  color: #999;
  text-decoration: line-through;
  margin-left: 10rpx;
}
.product-section .product-grid .product-card .product-info .meta-rating-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  height: 40rpx;
  line-height: 40rpx;
  font-size: 24rpx;
}
.product-section .product-grid .product-card .product-info .product-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 30rpx;
}
.product-section .product-grid .product-card .product-info .product-meta .sales {
  font-size: 24rpx;
  color: #999;
}
.product-section .product-grid .product-card .product-info .product-meta .rating {
  display: flex;
  align-items: center;
  gap: 4px;
  margin-top: 15rpx;
}
.product-section .product-grid .product-card .product-info .product-meta .rating text {
  font-size: 8rpx;
  color: #4285f4;
  margin-left: 4rpx;
}
.empty-icon .icon {
  font-size: 120rpx;
  opacity: 0.6;
}
.loading-container,
.no-more {
  padding: 30rpx 0;
  text-align: center;
  color: #999;
  font-size: 26rpx;
}
.empty-state {
  background-color: rgba(255, 255, 255, 0.85);
  border-radius: 24rpx;
  margin: 24rpx;
  padding: 60rpx 0;
  -webkit-backdrop-filter: blur(10rpx);
          backdrop-filter: blur(10rpx);
}
.empty-state .empty-image {
  width: 200rpx;
  height: 200rpx;
  opacity: 0.6;
  margin-bottom: 30rpx;
}
.empty-state .empty-text {
  font-size: 30rpx;
  color: #666;
  margin-bottom: 5rpx;
  text-align: center;
  display: block !important;
}
.empty-state .empty-desc {
  font-size: 26rpx;
  color: #999;
  margin-bottom: 20rpx;
  text-align: center;
  display: block !important;
}
.empty-state .refresh-btn {
  width: 60%;
  background-color: #4285f4;
  color: #fff;
  border: none;
  border-radius: 50rpx;
  padding: 0 60rpx;
  height: 70rpx;
  line-height: 70rpx;
  font-size: 28rpx;
}
.empty-state .refresh-btn::after {
  border: none;
}