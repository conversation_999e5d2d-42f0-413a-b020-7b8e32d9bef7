/* 水平间距 */
/* 水平间距 */
/* 全局样式修复 - 确保页面占满屏幕宽度 */
page {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
}

/* 通用容器样式 */
.container, .page-container {
  width: 100%;
  min-height: 100vh;
  box-sizing: border-box;
}

/* 修复小程序页面宽度问题 */
uni-page-body {
  width: 100% !important;
}

/* 确保所有view元素正确继承宽度 */
view {
  box-sizing: border-box;
}
.detail-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f5f5;
}
.detail-scroll {
  flex: 1;
  margin-bottom: 100rpx;
}
.detail-swiper {
  height: 600rpx;
}
.detail-swiper image {
  width: 100%;
  height: 100%;
}
.product-title-section {
  background-color: #ffffff;
  padding: 30rpx 20rpx;
  margin-bottom: 20rpx;
}
.product-title {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
}
.product-desc {
  font-size: 28rpx;
  color: #666666;
  line-height: 1.5;
  margin-bottom: 20rpx;
}
.product-price-section {
  display: flex;
  align-items: baseline;
  margin-bottom: 15rpx;
}
.current-price {
  font-size: 36rpx;
  color: #f44336;
  font-weight: bold;
  margin-right: 20rpx;
}
.original-price {
  font-size: 28rpx;
  color: #999999;
  text-decoration: line-through;
}
.product-stock {
  font-size: 26rpx;
  color: #666666;
}
.specs-section, .detail-content, .recommend-section {
  background-color: #ffffff;
  padding: 30rpx 20rpx;
  margin-bottom: 20rpx;
}
.section-title {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 30rpx;
  position: relative;
  padding-left: 20rpx;
}
.section-title:before {
  content: "";
  position: absolute;
  left: 0;
  top: 6rpx;
  width: 8rpx;
  height: 32rpx;
  background-color: #4285f4;
  border-radius: 4rpx;
}
.specs-list {
  display: flex;
  flex-wrap: wrap;
}
.specs-item {
  width: 50%;
  margin-bottom: 20rpx;
  display: flex;
}
.specs-label {
  width: 160rpx;
  color: #999999;
  font-size: 28rpx;
}
.specs-value {
  flex: 1;
  color: #333333;
  font-size: 28rpx;
}
.detail-images {
  margin-top: 30rpx;
}
.recommend-list {
  display: flex;
  overflow-x: auto;
  padding-bottom: 20rpx;
}
.recommend-item {
  width: 200rpx;
  margin-right: 20rpx;
  flex-shrink: 0;
}
.recommend-item image {
  width: 200rpx;
  height: 200rpx;
  border-radius: 8rpx;
  margin-bottom: 10rpx;
}
.recommend-name {
  font-size: 24rpx;
  color: #333333;
  display: block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-bottom: 6rpx;
}
.recommend-price {
  font-size: 26rpx;
  color: #f44336;
  font-weight: bold;
}
.bottom-actions {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  height: 100rpx;
  background-color: #ffffff;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  z-index: 99;
}
.action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 80rpx;
}
.action-icon {
  font-size: 36rpx;
  margin-bottom: 4rpx;
}
.action-text {
  font-size: 20rpx;
  color: #666666;
}
.action-item.favorited .action-text {
  color: #ff4757;
}
.add-cart-btn, .buy-now-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  font-size: 28rpx;
}
.add-cart-btn {
  background-color: #ff9800;
  color: #ffffff;
  transition: all 0.3s ease;
}
.add-cart-btn.in-cart {
  background-color: #4285f4;
  color: #ffffff;
}
.buy-now-btn {
  background-color: #f44336;
  color: #ffffff;
}