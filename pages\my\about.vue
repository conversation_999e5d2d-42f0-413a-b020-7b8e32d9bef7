<template>
  <view class="about-container">
    <view class="header">
      <image class="logo" :src="aboutInfo.logoUrl || '/static/logo.png'" mode="aspectFit"></image>
      <text class="title">{{ aboutInfo.companyName || '关于我们' }}</text>
    </view>

    <view class="content" v-if="!loading">
      <view class="section" v-if="aboutInfo.companyIntro">
        <text class="section-title">公司简介</text>
        <text class="section-text">{{ aboutInfo.companyIntro }}</text>
      </view>

      <view class="section">
        <text class="section-title">联系方式</text>
        <view class="contact-item" v-if="aboutInfo.phone">
          <text class="label">电话：</text>
          <text class="value">{{ aboutInfo.phone }}</text>
        </view>
        <view class="contact-item" v-if="aboutInfo.email">
          <text class="label">邮箱：</text>
          <text class="value">{{ aboutInfo.email }}</text>
        </view>
        <view class="contact-item" v-if="aboutInfo.address">
          <text class="label">地址：</text>
          <text class="value">{{ aboutInfo.address }}</text>
        </view>
        <view class="contact-item" v-if="aboutInfo.website">
          <text class="label">官网：</text>
          <text class="value">{{ aboutInfo.website }}</text>
        </view>
        <view class="contact-item" v-if="aboutInfo.qq">
          <text class="label">客服QQ: </text>
          <text class="value">{{ aboutInfo.qq }}</text>
        </view>
        <view class="contact-item" v-if="aboutInfo.wechat">
          <text class="label">微信：</text>
          <text class="value">{{ aboutInfo.wechat }}</text>
        </view>
        <view class="contact-item" v-if="aboutInfo.businessHours">
          <text class="label">营业时间：</text>
          <text class="value">{{ aboutInfo.businessHours }}</text>
        </view>
      </view>

      <view class="section" v-if="aboutInfo.version">
        <text class="section-title">版本信息</text>
        <text class="version">{{ aboutInfo.version }}</text>
      </view>
    </view>

    <!-- 加载状态 -->
    <view class="loading" v-if="loading">
      <text>加载中...</text>
    </view>

    <!-- 错误状态 -->
    <view class="error" v-if="error">
      <text>{{ error }}</text>
      <button @click="loadAboutInfo" class="retry-btn">重试</button>
    </view>
  </view>
</template>

<script>
import { get } from '@/utils/request.js'

export default {
  data() {
    return {
      aboutInfo: {
        companyName: '',
        companyIntro: '',
        phone: '',
        email: '',
        address: '',
        version: '',
        logoUrl: '',
        website: '',
        qq: '',
        wechat: '',
        businessHours: ''
      },
      loading: true,
      error: ''
    }
  },
  onLoad() {
    this.loadAboutInfo()
  },
  methods: {
    async loadAboutInfo() {
      try {
        this.loading = true
        this.error = ''

        const response = await get('/api/about/info')

        if (response && response.data) {
          this.aboutInfo = {
            companyName: response.data.companyName || '芭佰邑智能染发机',
            companyIntro: response.data.companyIntro || '我们是一家专注于提供高质量服务的企业，致力于为用户创造价值，提供便捷、高效的解决方案。',
            phone: response.data.phone || '***********',
            email: response.data.email || '<EMAIL>',
            address: response.data.address || '广州白云石井街芭百邑化妆品商行广州市白云区石槎路979号239室',
            version: response.data.version || 'v1.0.0',
            logoUrl: response.data.logoUrl || '',
            website: response.data.website || '',
            qq: response.data.qq || '',
            wechat: response.data.wechat || '',
            businessHours: response.data.businessHours || ''
          }
        }
      } catch (error) {
        console.error('获取关于我们信息失败:', error)
        this.error = '获取信息失败，请检查网络连接'

        // 使用默认数据
        this.aboutInfo = {
          companyName: '芭佰邑智能染发机',
          companyIntro: '我们是一家专注于提供高质量服务的企业，致力于为用户创造价值，提供便捷、高效的解决方案。',
          phone: '***********',
          email: '<EMAIL>',
          address: '广州白云石井街芭百邑化妆品商行广州市白云区石槎路979号239室',
          version: 'v1.0.0',
          logoUrl: '',
          website: '',
          qq: '',
          wechat: '',
          businessHours: ''
        }
      } finally {
        this.loading = false
      }
    }
  }
}
</script>

<style lang="scss">
.about-container {
  width: 100%;
  box-sizing: border-box;
  padding: 30rpx;
}

.header {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 50rpx;

  .logo {
    width: 150rpx;
    height: 150rpx;
    margin-bottom: 20rpx;
  }

  .title {
    font-size: 36rpx;
    font-weight: bold;
  }
}

.content {
  .section {
    margin-bottom: 40rpx;

    .section-title {
      font-size: 32rpx;
      font-weight: bold;
      margin-bottom: 20rpx;
      color: #333;
    }

    .section-text {
      font-size: 28rpx;
      color: #666;
      line-height: 1.6;
    }

    .contact-item {
      display: flex;
      margin-bottom: 10rpx;

      .label {
        font-size: 28rpx;
        color: #333;
        width: 100rpx;
      }

      .value {
        font-size: 28rpx;
        color: #666;
        flex: 1;
      }
    }

    .version {
      font-size: 28rpx;
      color: #999;
    }
  }
}

.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400rpx;

  text {
    font-size: 28rpx;
    color: #999;
  }
}

.error {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 400rpx;

  text {
    font-size: 28rpx;
    color: #999;
    margin-bottom: 20rpx;
  }

  .retry-btn {
    background-color: #4285f4;
    color: white;
    border: none;
    border-radius: 10rpx;
    // padding: 20rpx 40rpx;
    font-size: 28rpx;
  }
}
</style> 