"use strict";
const common_vendor = require("../common/vendor.js");
const utils_request = require("../utils/request.js");
const utils_dyeConsumptionManager = require("../utils/dyeConsumptionManager.js");
const BASE_URL = "https://www.narenqiqige.com";
let isPolling = false;
let currentRequest = null;
let forcedshutdown = false;
let reconnectTimer = null;
let reconnectAttempts = 0;
const MAX_RECONNECT_ATTEMPTS = 5;
const RECONNECT_INTERVAL = 3e3;
let currentSessionId = null;
let lastEventId = 0;
let connectionPersistent = true;
let isReconnecting = false;
const SESSION_STORAGE_KEY = "wifi_session_info";
function restoreSessionInfo(deviceId) {
  try {
    const sessionInfo = common_vendor.index.getStorageSync(SESSION_STORAGE_KEY);
    if (sessionInfo && sessionInfo.deviceId === deviceId) {
      currentSessionId = sessionInfo.sessionId;
      lastEventId = sessionInfo.lastEventId || 0;
      common_vendor.index.__f__("log", "at store/wifi.js:37", "恢复会话信息:", { currentSessionId, lastEventId, deviceId });
      return true;
    }
  } catch (error) {
    common_vendor.index.__f__("error", "at store/wifi.js:41", "恢复会话信息失败:", error);
  }
  return false;
}
function saveSessionInfo(deviceId) {
  try {
    const sessionInfo = {
      deviceId,
      sessionId: currentSessionId,
      lastEventId,
      timestamp: Date.now()
    };
    common_vendor.index.setStorageSync(SESSION_STORAGE_KEY, sessionInfo);
    common_vendor.index.__f__("log", "at store/wifi.js:56", "保存会话信息:", sessionInfo);
  } catch (error) {
    common_vendor.index.__f__("error", "at store/wifi.js:58", "保存会话信息失败:", error);
  }
}
function clearSessionInfo() {
  try {
    common_vendor.index.removeStorageSync(SESSION_STORAGE_KEY);
    currentSessionId = null;
    lastEventId = 0;
    common_vendor.index.__f__("log", "at store/wifi.js:68", "清除会话信息");
  } catch (error) {
    common_vendor.index.__f__("error", "at store/wifi.js:70", "清除会话信息失败:", error);
  }
}
let currentDeviceId = null;
function decodeUint8Array(data) {
  return String.fromCharCode.apply(null, new Uint8Array(data));
}
function handleSSEData(data, deviceId) {
  const app2 = getApp();
  try {
    if (forcedshutdown) {
      common_vendor.index.__f__("warn", "at store/wifi.js:262", "当前为强制关闭连接阶段,直接返回");
      return;
    }
    app2.setConnectionMethod("wifi");
    common_vendor.index.__f__("log", "at store/wifi.js:290", "原始数组", data);
    if (data instanceof ArrayBuffer) {
      data = decodeUint8Array(data);
    }
    common_vendor.index.__f__("log", "at store/wifi.js:295", "解析后数据类型", typeof data);
    common_vendor.index.__f__("log", "at store/wifi.js:296", "解析之后的数据", data);
    try {
      const lines = data.split("\n").filter((line) => line.trim() !== "");
      for (const line of lines) {
        if (line.startsWith("id:")) {
          const eventId = parseInt(line.slice(3).trim());
          if (!isNaN(eventId)) {
            const oldEventId = lastEventId;
            lastEventId = Math.max(lastEventId, eventId);
            common_vendor.index.__f__("log", "at store/wifi.js:309", "更新事件ID:", lastEventId);
            if (lastEventId > oldEventId && currentSessionId) {
              saveSessionInfo(currentDeviceId);
            }
          }
          continue;
        }
        if (line.startsWith("event:")) {
          const eventName = line.slice(6).trim();
          common_vendor.index.__f__("log", "at store/wifi.js:321", "接收到事件:", eventName);
          continue;
        }
        let jsonStr = null;
        if (line.startsWith("data:")) {
          jsonStr = line.slice(5).trim();
        } else if (line.trim().startsWith("{")) {
          jsonStr = line.trim();
        }
        if (jsonStr) {
          common_vendor.index.__f__("log", "at store/wifi.js:334", "准备解析JSON字符串", jsonStr);
          const jsonData = JSON.parse(jsonStr);
          common_vendor.index.__f__("log", "at store/wifi.js:336", "接收到的数据", jsonData);
          if (jsonData.sessionId) {
            currentSessionId = jsonData.sessionId;
            common_vendor.index.__f__("log", "at store/wifi.js:341", "更新会话ID:", currentSessionId);
            saveSessionInfo(currentDeviceId);
          }
          handleSSEMessage(jsonData);
        }
      }
    } catch (e) {
      common_vendor.index.__f__("error", "at store/wifi.js:351", "JSON 解析失败:", e);
      app2.setErrorState({
        errorType: "WIFI_PARSE_ERROR",
        errorMessage: `WiFi数据解析失败: ${e.message}`,
        errorCode: null
      });
    }
  } catch (error) {
    common_vendor.index.__f__("error", "at store/wifi.js:359", "分块数据解析失败:", error);
  }
}
const startPollingWifi = async (connectedDeviceId) => {
  if (isPolling) {
    common_vendor.index.__f__("warn", "at store/wifi.js:365", "SSE连接已在运行中");
    return;
  }
  forcedshutdown = false;
  const app2 = getApp();
  let deviceId;
  if (!connectedDeviceId) {
    deviceId = app2.globalData.connectedDeviceId;
  } else {
    app2.globalData.connectedDeviceId = connectedDeviceId;
    deviceId = connectedDeviceId;
  }
  currentDeviceId = deviceId;
  if (!currentSessionId && !lastEventId) {
    const restored = restoreSessionInfo(deviceId);
    if (restored) {
      common_vendor.index.__f__("log", "at store/wifi.js:388", "成功恢复会话信息，将尝试续接连接");
    }
  }
  isPolling = true;
  const isReconnect = isReconnecting || currentSessionId && lastEventId > 0;
  const connectUrl = isReconnect ? `${BASE_URL}/api/device/reconnect/${deviceId}` : `${BASE_URL}/api/device/connectSee/${deviceId}`;
  common_vendor.index.__f__("log", "at store/wifi.js:406", `开始SSE连接，URL: ${connectUrl}`, {
    isReconnect,
    sessionId: currentSessionId,
    lastEventId
  });
  const token = common_vendor.index.getStorageSync("token");
  app2.setConnectionMethod("wifi");
  const requestParams = {
    url: connectUrl,
    method: "POST",
    enableChunked: true,
    timeout: 0,
    // 设置为0表示不超时，保持长连接
    header: {
      "content-type": "application/json",
      "cache-control": "no-cache",
      ...token ? {
        "Authorization": `Bearer ${token}`
      } : {}
    }
  };
  if (isReconnect) {
    const queryParams = [];
    if (lastEventId > 0) {
      queryParams.push(`lastEventId=${encodeURIComponent(lastEventId.toString())}`);
    }
    if (currentSessionId) {
      queryParams.push(`sessionId=${encodeURIComponent(currentSessionId)}`);
    }
    if (queryParams.length > 0) {
      requestParams.url += "?" + queryParams.join("&");
    }
  }
  currentRequest = common_vendor.wx$1.request({
    ...requestParams,
    success(res) {
      common_vendor.index.__f__("log", "at store/wifi.js:449", "SSE连接请求成功:", res);
      reconnectAttempts = 0;
      isReconnecting = false;
      app2.setConnectionMethod("wifi");
    },
    fail(err) {
      if (forcedshutdown) {
        common_vendor.index.__f__("warn", "at store/wifi.js:459", "当前正在强制关闭");
        return;
      }
      common_vendor.index.__f__("error", "at store/wifi.js:463", "SSE连接失败:", err);
      common_vendor.index.__f__("error", "at store/wifi.js:464", "错误详情:", {
        errMsg: err.errMsg,
        statusCode: err.statusCode,
        data: err.data,
        header: err.header
      });
      isPolling = false;
      if (err.errMsg && err.errMsg.includes("timeout")) {
        common_vendor.index.__f__("log", "at store/wifi.js:474", "连接超时，这可能是正常的长连接行为，尝试重连");
      } else if (err.statusCode && err.statusCode >= 500) {
        common_vendor.index.__f__("log", "at store/wifi.js:476", "服务器错误，延迟重连");
      } else {
        common_vendor.index.__f__("log", "at store/wifi.js:478", "其他连接错误，尝试重连");
      }
      scheduleReconnect(deviceId);
    },
    complete() {
      common_vendor.index.__f__("log", "at store/wifi.js:485", "SSE连接完成");
      isPolling = false;
      if (connectionPersistent && reconnectAttempts < MAX_RECONNECT_ATTEMPTS) {
        scheduleReconnect(deviceId);
      }
    }
  });
  if (typeof currentRequest.onChunkReceived === "function") {
    currentRequest.onChunkReceived(async (chunk) => {
      await handleSSEData(chunk.data);
    });
  } else {
    common_vendor.index.__f__("error", "at store/wifi.js:499", "当前环境不支持onChunkReceived方法");
    startFallbackPolling(deviceId);
  }
};
const executeNextCommand = async () => {
  const app2 = getApp();
  const command = app2.globalData.commandQueue[0];
  common_vendor.index.__f__("log", "at store/wifi.js:519", "当前执行的指令", command);
  common_vendor.index.__f__("log", "at store/wifi.js:520", "当前指令队列", app2.globalData.commandQueue);
  if (!command) {
    const response = await utils_request.apiService.mall.command.allEnd(app2.globalData.connectedDeviceId);
    if (response.code == 200) {
      common_vendor.index.__f__("log", "at store/wifi.js:525", "服务器已发送全部出料完成");
      app2.allExecutionCompleted();
    }
    return;
  }
  app2.setCurrentCommand(command);
  common_vendor.index.__f__("log", "at store/wifi.js:534", "指令", command.value);
  common_vendor.index.__f__("log", "at store/wifi.js:536", `开始执行指令: ${command.description}`);
  try {
    const deviceCode = app2.globalData.connectedDeviceId;
    if (deviceCode) {
      const commandData = {
        value: Array.from(command.value).map((b) => b.toString(16).padStart(2, "0")).join(""),
        description: command.description
      };
      common_vendor.index.__f__("log", "at store/wifi.js:548", "🌐 创建后端指令记录:", command);
      const response = await utils_request.apiService.mall.command.sendCommandin(deviceCode, commandData);
      if (response.code === 200) {
        common_vendor.index.__f__("log", "at store/wifi.js:553", "✅ 后端指令记录创建成功，获得ID列表:", response.data.commandIds);
      } else {
        common_vendor.index.__f__("warn", "at store/wifi.js:556", "⚠️ 后端指令记录创建失败:", response.message);
        commands.forEach((cmd) => {
          cmd.backendCreated = false;
        });
      }
    }
  } catch (error) {
    common_vendor.index.__f__("error", "at store/wifi.js:563", "❌ 创建后端指令记录异常:", error);
  }
  app2.globalData.currentCommand = command;
  common_vendor.index.__f__("log", "at store/wifi.js:567", `指令发送成功: ${command.description}`);
  app2.removeFromCommandQueue();
};
async function recordWifiDyeConsumption(deviceCode, colorCode, weight) {
  try {
    if (!deviceCode) {
      common_vendor.index.__f__("warn", "at store/wifi.js:633", "设备编码为空，无法记录染膏消耗");
      return;
    }
    const app2 = getApp();
    const currentCommand = app2.globalData.currentCommand;
    const commandId = currentCommand ? currentCommand.backendId : null;
    const consumptionData = {
      consumption: {
        [colorCode]: {
          amount: weight,
          colorCode
        }
      }
    };
    const success = await utils_dyeConsumptionManager.dyeConsumptionManager.recordConsumption(
      deviceCode,
      consumptionData,
      commandId
    );
    if (success) {
      app2.updateDyeConsumption({
        currentUsage: {
          [colorCode]: weight
        },
        totalConsumption: app2.globalData.dyeConsumption.totalConsumption + weight
      });
      common_vendor.index.__f__("log", "at store/wifi.js:663", `✅ WiFi染膏消耗记录成功: 颜色${colorCode}, 用量${weight}g, 后端指令ID: ${commandId}`);
    } else {
      common_vendor.index.__f__("log", "at store/wifi.js:665", `WiFi染膏消耗记录失败`);
    }
  } catch (error) {
    common_vendor.index.__f__("error", "at store/wifi.js:668", "WiFi记录染膏消耗失败:", error);
  }
}
function calculateProgress(status) {
  const progressMap = {
    "已放碗": 10,
    "开始出料": 20,
    "出料中": 50,
    "出料完成": 100,
    "待机": 0
  };
  return progressMap[status] || 0;
}
const stopPollingWifi = () => {
  if (!isPolling && !currentRequest && !isReconnecting) {
    common_vendor.index.__f__("warn", "at store/wifi.js:721", "轮询未运行");
    return;
  }
  isPolling = false;
  isReconnecting = false;
  resetReconnectState();
  if (currentRequest) {
    try {
      if (currentRequest.abort) {
        forcedshutdown = true;
        currentRequest.abort();
      }
      if (typeof common_vendor.wx$1 !== "undefined" && currentRequest.close) {
        forcedshutdown = true;
        currentRequest.close();
      }
      common_vendor.index.__f__("log", "at store/wifi.js:755", "SSE连接已强制关闭");
    } catch (e) {
      common_vendor.index.__f__("error", "at store/wifi.js:757", "关闭连接时出错:", e);
    } finally {
      currentRequest = null;
    }
  }
  const app2 = getApp();
  if (app2) {
    app2.globalData.deviceStatus = false;
    const store = app2.$store || app2.store;
    if (store && store.commit) {
      store.commit("CLEAR_CONNECTED_DEVICE");
      common_vendor.index.__f__("log", "at store/wifi.js:772", "WiFi连接停止，已清除store状态");
    }
  }
  clearSessionInfo();
  common_vendor.index.__f__("log", "at store/wifi.js:779", "分块传输轮询已完全停止");
};
function startFallbackPolling(deviceId) {
  const pollInterval = setInterval(async () => {
    if (app.isBluetoothConnection()) {
      return false;
    }
    if (!isPolling) {
      clearInterval(pollInterval);
      return;
    }
    try {
      const res = await utils_request.request({
        url: `/api/device/connectSee/${deviceId}`,
        method: "POST"
      });
      common_vendor.index.__f__("log", "at store/wifi.js:800", "轮询响应:", res);
    } catch (error) {
      common_vendor.index.__f__("error", "at store/wifi.js:802", "轮询请求失败:", error);
    }
  }, 3e3);
}
function scheduleReconnect(deviceId) {
  if (reconnectAttempts >= MAX_RECONNECT_ATTEMPTS) {
    const app2 = getApp();
    app2.scheduleReconnect("");
    app2.globalData.deviceStatus = false;
    const store = app2.$store || app2.store;
    if (store && store.commit) {
      store.commit("CLEAR_CONNECTED_DEVICE");
      common_vendor.index.__f__("log", "at store/wifi.js:818", "达到最大重连次数，已清除store状态");
    }
    clearSessionInfo();
    common_vendor.index.__f__("error", "at store/wifi.js:824", "达到最大重连次数，停止重连");
    return;
  }
  if (reconnectTimer) {
    clearTimeout(reconnectTimer);
  }
  reconnectAttempts++;
  isReconnecting = true;
  common_vendor.index.__f__("log", "at store/wifi.js:834", `准备第${reconnectAttempts}次重连，${RECONNECT_INTERVAL / 1e3}秒后开始`);
  reconnectTimer = setTimeout(() => {
    if (!isPolling) {
      common_vendor.index.__f__("log", "at store/wifi.js:838", `开始第${reconnectAttempts}次重连`);
      startPollingWifi(deviceId);
    }
  }, RECONNECT_INTERVAL);
}
const resetReconnectState = () => {
  reconnectAttempts = 0;
  isReconnecting = false;
  if (reconnectTimer) {
    clearTimeout(reconnectTimer);
    reconnectTimer = null;
  }
};
function setupNetworkListener() {
  common_vendor.index.onNetworkStatusChange((res) => {
    if (res.isConnected && !isPolling && reconnectAttempts > 0) {
      common_vendor.index.__f__("log", "at store/wifi.js:858", "网络恢复，尝试重连WiFi");
      resetReconnectState();
      const app2 = getApp();
      if (app2.globalData.connectedDeviceId) {
        startPollingWifi(app2.globalData.connectedDeviceId);
      }
    }
  });
}
setupNetworkListener();
async function handleSSEMessage(jsonData) {
  try {
    common_vendor.index.__f__("log", "at store/wifi.js:874", "jsonData", jsonData);
    const app2 = getApp();
    if (jsonData.type === "welcome" || jsonData.type === "reconnect") {
      if (jsonData.online === true) {
        common_vendor.index.__f__("log", "at store/wifi.js:882", "设备在线");
        app2.globalData.deviceStatus = true;
        const store = app2.$store || app2.store;
        if (store && store.commit) {
          const mockDevice = {
            deviceId: app2.globalData.connectedDeviceId,
            name: app2.globalData.connectedDeviceId,
            connectionType: "wifi"
          };
          store.commit("SET_CONNECTED_DEVICE", mockDevice);
          common_vendor.index.__f__("log", "at store/wifi.js:894", "WiFi连接成功，已更新store状态:", mockDevice);
        }
        app2.updateDeviceStatusDetail({
          isOnline: true,
          lastHeartbeat: (/* @__PURE__ */ new Date()).toISOString(),
          connectionQuality: 90
        });
      } else if (jsonData.online === false) {
        common_vendor.index.__f__("log", "at store/wifi.js:904", "设备离线");
        app2.globalData.deviceStatus = false;
        const store = app2.$store || app2.store;
        if (store && store.commit) {
          store.commit("CLEAR_CONNECTED_DEVICE");
          common_vendor.index.__f__("log", "at store/wifi.js:911", "设备离线，已清除store状态");
        }
        app2.updateDeviceStatusDetail({
          isOnline: false,
          lastHeartbeat: (/* @__PURE__ */ new Date()).toISOString(),
          connectionQuality: 0
        });
      }
      if (jsonData.sessionId) {
        currentSessionId = jsonData.sessionId;
        common_vendor.index.__f__("log", "at store/wifi.js:923", "会话ID已更新:", currentSessionId);
        saveSessionInfo(currentDeviceId);
      }
      return;
    }
    if (jsonData.type === "history_start") {
      common_vendor.index.__f__("log", "at store/wifi.js:932", "开始接收历史消息，数量:", jsonData.count);
      return;
    }
    if (jsonData.type === "history_end") {
      common_vendor.index.__f__("log", "at store/wifi.js:937", "历史消息接收完成，数量:", jsonData.count);
      return;
    }
    if (jsonData.type === "deviceOnline") {
      common_vendor.index.__f__("log", "at store/wifi.js:943", "设备上线:", jsonData.data ? jsonData.data.deviceCode : "unknown");
      app2.globalData.deviceStatus = true;
      const store = app2.$store || app2.store;
      if (store && store.commit) {
        const mockDevice = {
          deviceId: app2.globalData.connectedDeviceId,
          name: app2.globalData.connectedDeviceId,
          connectionType: "wifi"
        };
        store.commit("SET_CONNECTED_DEVICE", mockDevice);
        common_vendor.index.__f__("log", "at store/wifi.js:955", "设备上线，已更新store状态:", mockDevice);
      }
      app2.updateDeviceStatusDetail({
        isOnline: true,
        lastHeartbeat: (/* @__PURE__ */ new Date()).toISOString(),
        connectionQuality: 90
      });
      return;
    }
    if (jsonData.type === "deviceOffline") {
      common_vendor.index.__f__("log", "at store/wifi.js:968", "设备离线:", jsonData.data ? jsonData.data.deviceCode : "unknown");
      if (app2.globalData.taskCompletionBuffer) {
        common_vendor.index.__f__("log", "at store/wifi.js:972", "当前在任务完成缓冲期内，忽略设备离线消息");
        return;
      }
      app2.globalData.deviceStatus = false;
      const store = app2.$store || app2.store;
      if (store && store.commit) {
        store.commit("CLEAR_CONNECTED_DEVICE");
        common_vendor.index.__f__("log", "at store/wifi.js:982", "设备离线，已清除store状态");
      }
      app2.updateDeviceStatusDetail({
        isOnline: false,
        lastHeartbeat: (/* @__PURE__ */ new Date()).toISOString(),
        connectionQuality: 0
      });
      return;
    }
    if (jsonData.type === "heartbeat") {
      common_vendor.index.__f__("log", "at store/wifi.js:995", "收到设备心跳:", jsonData.data ? jsonData.data.deviceCode : "unknown");
      app2.globalData.deviceStatus = true;
      const store = app2.$store || app2.store;
      if (store && store.commit && !store.state.connectedDevice) {
        const mockDevice = {
          deviceId: app2.globalData.connectedDeviceId,
          name: app2.globalData.connectedDeviceId,
          connectionType: "wifi"
        };
        store.commit("SET_CONNECTED_DEVICE", mockDevice);
        common_vendor.index.__f__("log", "at store/wifi.js:1007", "收到心跳，已更新store状态:", mockDevice);
      }
      app2.updateDeviceStatusDetail({
        isOnline: true,
        lastHeartbeat: (/* @__PURE__ */ new Date()).toISOString(),
        connectionQuality: 90
      });
      return;
    }
    if (jsonData.online === true) {
      common_vendor.index.__f__("log", "at store/wifi.js:1020", "设备在线");
      app2.globalData.deviceStatus = true;
      const store = app2.$store || app2.store;
      if (store && store.commit && !store.state.connectedDevice) {
        const mockDevice = {
          deviceId: app2.globalData.connectedDeviceId,
          name: app2.globalData.connectedDeviceId,
          connectionType: "wifi"
        };
        store.commit("SET_CONNECTED_DEVICE", mockDevice);
        common_vendor.index.__f__("log", "at store/wifi.js:1032", "设备状态在线，已更新store状态:", mockDevice);
      }
    } else if (jsonData.online === false) {
      common_vendor.index.__f__("log", "at store/wifi.js:1035", "设备离线");
      if (app2.globalData.taskCompletionBuffer) {
        common_vendor.index.__f__("log", "at store/wifi.js:1039", "当前在任务完成缓冲期内，忽略设备离线状态");
        return;
      }
      app2.globalData.deviceStatus = false;
      const store = app2.$store || app2.store;
      if (store && store.commit) {
        store.commit("CLEAR_CONNECTED_DEVICE");
        common_vendor.index.__f__("log", "at store/wifi.js:1049", "设备状态离线，已清除store状态");
      }
    }
    app2.updateDeviceStatusDetail({
      isOnline: jsonData.online === "true" || jsonData.online === true,
      lastHeartbeat: (/* @__PURE__ */ new Date()).toISOString(),
      connectionQuality: 90
    });
    if (jsonData.type === "commandResult") {
      const { result, message } = jsonData.data;
      common_vendor.index.__f__("log", "at store/wifi.js:1069", "WiFi收到指令结果:", result, message);
      switch (result) {
        case "single_complete":
          common_vendor.index.__f__("log", "at store/wifi.js:1073", "wifi收到单次出料完成");
          const deviceCode = app2.globalData.connectedDeviceId;
          if (jsonData.colorCode && jsonData.weight) {
            await recordWifiDyeConsumption(deviceCode, jsonData.colorCode, jsonData.weight);
          }
          executeNextCommand();
          break;
        case "bowl_already_placed":
          common_vendor.index.__f__("log", "at store/wifi.js:1082", "WiFi收到已放碗指令");
          app2.globalData.wan_color = true;
          app2.globalData.commandStatus = true;
          app2.updateCommandExecutionDetail({
            startTime: (/* @__PURE__ */ new Date()).toISOString(),
            progress: 0,
            currentStep: "执行中",
            totalSteps: Object.keys(jsonData.data).length
          });
          break;
        case "paused":
          app2.setPaused(true);
          app2.setExecutingQueue(false);
          common_vendor.index.showToast({ title: "设备已暂停", icon: "success" });
          break;
        case "resumed":
          app2.setPaused(false);
          app2.setExecutingQueue(true);
          common_vendor.index.showToast({ title: "设备已恢复", icon: "success" });
          break;
        case "cancelled":
          app2.setExecutingQueue(false);
          app2.setPaused(false);
          app2.globalData.commandQueue = [];
          common_vendor.index.showToast({ title: "操作已取消", icon: "success" });
          break;
        case "all_end_sent":
          common_vendor.index.__f__("log", "at store/wifi.js:1110", "WiFi收到全部出料完成确认");
          app2.allExecutionCompleted();
          break;
        case "pause_sent":
        case "continue_sent":
        case "cancel_sent":
        case "all_end_sent":
          common_vendor.index.__f__("log", "at store/wifi.js:1119", "指令发送成功，等待设备确认");
          break;
        case "pause_cached":
        case "continue_cached":
        case "cancel_cached":
        case "all_end_cached":
          common_vendor.index.showToast({
            title: "设备离线，指令已缓存",
            icon: "none",
            duration: 3e3
          });
          common_vendor.index.__f__("log", "at store/wifi.js:1131", "设备离线，指令已缓存，将在设备上线后自动发送");
          break;
        case "cached_commands_sent":
          common_vendor.index.showToast({
            title: "缓存指令已发送",
            icon: "success"
          });
          common_vendor.index.__f__("log", "at store/wifi.js:1139", "设备上线，缓存指令发送成功");
          break;
        case "cached_commands_partial_failed":
        case "cached_commands_send_failed":
          common_vendor.index.showToast({
            title: "缓存指令发送失败",
            icon: "error"
          });
          common_vendor.index.__f__("log", "at store/wifi.js:1148", "缓存指令发送失败:", message);
          break;
        case "pause_failed":
        case "continue_failed":
        case "cancel_failed":
        case "all_end_failed":
          common_vendor.index.showToast({ title: message || "指令发送失败", icon: "error" });
          break;
      }
    }
    if (jsonData.type === "status") {
      common_vendor.index.__f__("log", "at store/wifi.js:1174", "设备状态:", jsonData.content);
      app2.updateCommandExecutionDetail({
        currentStep: jsonData.content,
        progress: calculateProgress(jsonData.content)
      });
    }
    if (jsonData.type === "heartbeat") {
      common_vendor.index.__f__("log", "at store/wifi.js:1183", "收到SSE心跳:", jsonData.timestamp);
      const wasDisconnected = !app2.globalData.deviceStatus;
      app2.globalData.deviceStatus = true;
      const store = app2.$store || app2.store;
      if (store && store.commit && !store.state.connectedDevice) {
        const mockDevice = {
          deviceId: app2.globalData.connectedDeviceId,
          name: app2.globalData.connectedDeviceId,
          connectionType: "wifi"
        };
        store.commit("SET_CONNECTED_DEVICE", mockDevice);
        common_vendor.index.__f__("log", "at store/wifi.js:1200", "收到SSE心跳，已更新store状态:", mockDevice);
      }
      app2.updateDeviceStatusDetail({
        isOnline: true,
        lastHeartbeat: (/* @__PURE__ */ new Date()).toISOString(),
        connectionQuality: 95
      });
      reconnectAttempts = 0;
      isReconnecting = false;
      if (wasDisconnected) {
        common_vendor.index.__f__("log", "at store/wifi.js:1215", "🔗 SSE连接已建立 - 收到第一个心跳");
      }
    }
    if (jsonData.type === "deviceOnline") {
      common_vendor.index.__f__("log", "at store/wifi.js:1220", "收到设备上线:", jsonData.timestamp);
      const wasDisconnected = !app2.globalData.deviceStatus;
      app2.globalData.deviceStatus = true;
      const store = app2.$store || app2.store;
      if (store && store.commit) {
        const mockDevice = {
          deviceId: app2.globalData.connectedDeviceId,
          name: app2.globalData.connectedDeviceId,
          connectionType: "wifi"
        };
        store.commit("SET_CONNECTED_DEVICE", mockDevice);
        common_vendor.index.__f__("log", "at store/wifi.js:1237", "收到设备上线消息，已更新store状态:", mockDevice);
      }
      app2.updateDeviceStatusDetail({
        isOnline: true,
        lastHeartbeat: (/* @__PURE__ */ new Date()).toISOString(),
        connectionQuality: 95
      });
      reconnectAttempts = 0;
      isReconnecting = false;
      if (wasDisconnected) {
        common_vendor.index.__f__("log", "at store/wifi.js:1252", "🔗 SSE连接已建立 - 收到设备上线消息");
      }
    }
  } catch (error) {
    common_vendor.index.__f__("error", "at store/wifi.js:1257", "处理SSE消息失败:", error);
  }
}
const getConnectionStatus = () => {
  return {
    isPolling,
    currentSessionId,
    lastEventId,
    reconnectAttempts,
    connectionPersistent
  };
};
const setConnectionPersistent = (persistent) => {
  connectionPersistent = persistent;
  common_vendor.index.__f__("log", "at store/wifi.js:1275", "连接持久化设置:", persistent);
};
const disconnectSSE = () => {
  connectionPersistent = false;
  if (currentRequest) {
    new Promise((resolve) => {
      try {
        if (currentRequest.abort) {
          currentRequest.abort();
        }
        if (typeof common_vendor.wx$1 !== "undefined" && currentRequest.close) {
          currentRequest.close();
        }
        resolve();
      } catch (e) {
        common_vendor.index.__f__("error", "at store/wifi.js:1295", "关闭出错:", e);
        resolve();
      }
    }).then(() => {
      currentRequest = null;
      common_vendor.index.__f__("log", "at store/wifi.js:1300", "SSE连接已手动断开");
    });
  } else {
    common_vendor.index.__f__("log", "at store/wifi.js:1303", "没有活动的SSE连接");
  }
  isPolling = false;
  resetReconnectState();
  common_vendor.index.__f__("log", "at store/wifi.js:1308", "SSE连接已手动断开");
};
const setIsPolling = (polling) => {
  isPolling = polling;
};
exports.disconnectSSE = disconnectSSE;
exports.executeNextCommand = executeNextCommand;
exports.getConnectionStatus = getConnectionStatus;
exports.setConnectionPersistent = setConnectionPersistent;
exports.setIsPolling = setIsPolling;
exports.startPollingWifi = startPollingWifi;
exports.stopPollingWifi = stopPollingWifi;
//# sourceMappingURL=../../.sourcemap/mp-weixin/store/wifi.js.map
