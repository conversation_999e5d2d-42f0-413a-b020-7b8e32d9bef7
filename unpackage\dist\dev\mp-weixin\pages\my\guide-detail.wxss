/* 水平间距 */
/* 水平间距 */
/* 全局样式修复 - 确保页面占满屏幕宽度 */
page {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
}

/* 通用容器样式 */
.container, .page-container {
  width: 100%;
  min-height: 100vh;
  box-sizing: border-box;
}

/* 修复小程序页面宽度问题 */
uni-page-body {
  width: 100% !important;
}

/* 确保所有view元素正确继承宽度 */
view {
  box-sizing: border-box;
}
.guide-detail-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100%;
  background-color: #f7f8fa;
  box-sizing: border-box;
}
.header {
  display: flex;
  align-items: center;
  height: 88rpx;
  background-color: #ffffff;
  padding: 0 30rpx;
  position: relative;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}
.back-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.title {
  flex: 1;
  text-align: center;
  font-size: 36rpx;
  font-weight: 500;
  margin-right: 60rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.content {
  flex: 1;
  padding: 20rpx;
}
.loading-container, .error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400rpx;
  color: #999;
}
.retry-btn {
  margin-top: 20rpx;
  padding: 10rpx 30rpx;
  background-color: #007AFF;
  color: white;
  border-radius: 10rpx;
  border: none;
}
.guide-detail {
  background-color: #ffffff;
  border-radius: 10rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}
.detail-header {
  margin-bottom: 30rpx;
}
.detail-title {
  font-size: 40rpx;
  font-weight: bold;
  color: #333;
  line-height: 1.4;
  display: block;
  margin-bottom: 20rpx;
}
.detail-meta {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.guide-type {
  font-size: 24rpx;
  color: #007AFF;
  background-color: #f0f5ff;
  padding: 6rpx 16rpx;
  border-radius: 12rpx;
}
.view-count {
  font-size: 24rpx;
  color: #999;
}
.detail-description {
  margin-bottom: 30rpx;
  padding: 20rpx;
  background-color: #f8f9fa;
  border-radius: 8rpx;
  border-left: 4rpx solid #007AFF;
}
.detail-description text {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
}
.detail-image {
  margin-bottom: 30rpx;
  text-align: center;
}
.detail-image image {
  max-width: 100%;
  border-radius: 8rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}
.detail-content {
  margin-bottom: 30rpx;
  font-size: 30rpx;
  color: #333;
  line-height: 1.8;
}
.detail-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-top: 20rpx;
  border-top: 1rpx solid #eee;
}
.step-info {
  font-size: 26rpx;
  color: #007AFF;
  font-weight: 500;
}
.update-time {
  font-size: 24rpx;
  color: #999;
}