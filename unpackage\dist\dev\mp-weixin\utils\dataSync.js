"use strict";
const common_vendor = require("../common/vendor.js");
const utils_request = require("./request.js");
const utils_dyeConsumptionManager = require("./dyeConsumptionManager.js");
const utils_messageHandler = require("./messageHandler.js");
class DataSyncService {
  constructor() {
    this.syncInterval = null;
    this.syncFrequency = 3e4;
    this.isOnline = true;
    this.offlineQueue = [];
    this.maxOfflineQueueSize = 100;
    this.lastSyncTime = null;
    this.syncInProgress = false;
  }
  /**
   * 启动数据同步服务
   * @param {string} deviceCode - 设备编码
   */
  start(deviceCode) {
    if (!deviceCode) {
      common_vendor.index.__f__("warn", "at utils/dataSync.js:26", "设备编码为空，无法启动数据同步");
      return;
    }
    this.deviceCode = deviceCode;
    this.stop();
    this.performSync();
    this.syncInterval = setInterval(() => {
      this.performSync();
    }, this.syncFrequency);
    this.setupNetworkListener();
    this.setupMessageListeners();
    common_vendor.index.__f__("log", "at utils/dataSync.js:47", `数据同步服务已启动，设备: ${deviceCode}`);
  }
  /**
   * 停止数据同步服务
   */
  stop() {
    if (this.syncInterval) {
      clearInterval(this.syncInterval);
      this.syncInterval = null;
    }
    common_vendor.index.__f__("log", "at utils/dataSync.js:58", "数据同步服务已停止");
  }
  /**
   * 执行同步
   */
  async performSync() {
    if (this.syncInProgress) {
      common_vendor.index.__f__("log", "at utils/dataSync.js:66", "同步正在进行中，跳过本次同步");
      return;
    }
    this.syncInProgress = true;
    try {
      await this.checkNetworkStatus();
      if (this.isOnline) {
        await this.syncAllData();
        await this.processOfflineQueue();
      } else {
        common_vendor.index.__f__("log", "at utils/dataSync.js:83", "网络离线，跳过同步");
      }
      this.lastSyncTime = (/* @__PURE__ */ new Date()).toISOString();
    } catch (error) {
      common_vendor.index.__f__("error", "at utils/dataSync.js:88", "数据同步失败:", error);
      this.handleSyncError(error);
    } finally {
      this.syncInProgress = false;
    }
  }
  /**
   * 同步所有数据
   */
  async syncAllData() {
    const promises = [
      this.syncDyeConsumption(),
      this.syncDeviceStatus(),
      this.syncInventoryStatus()
    ];
    const results = await Promise.allSettled(promises);
    results.forEach((result, index) => {
      if (result.status === "rejected") {
        const syncTypes = ["染膏消耗", "设备状态", "库存状态"];
        common_vendor.index.__f__("error", "at utils/dataSync.js:110", `${syncTypes[index]}同步失败:`, result.reason);
      }
    });
  }
  /**
   * 同步染膏消耗数据
   */
  async syncDyeConsumption() {
    try {
      const app = getApp();
      const currentConsumption = app.globalData.dyeConsumption;
      const response = await utils_request.apiService.mall.dyeConsumption.getUsageStatistics(this.deviceCode, {
        timeRange: "1day"
      });
      if (response.code === 200) {
        const serverData = response.data;
        if (serverData.totalConsumption !== currentConsumption.totalConsumption) {
          app.updateDyeConsumption({
            totalConsumption: serverData.totalConsumption,
            lastRecordTime: serverData.lastRecordTime
          });
        }
      }
    } catch (error) {
      common_vendor.index.__f__("error", "at utils/dataSync.js:140", "同步染膏消耗数据失败:", error);
      throw error;
    }
  }
  /**
   * 同步设备状态
   */
  async syncDeviceStatus() {
    try {
      const response = await utils_request.apiService.mall.deviceStatus.getRealTimeStatus(this.deviceCode);
      if (response.code === 200) {
        const statusData = response.data;
        const app = getApp();
        app.updateDeviceStatusDetail({
          isOnline: statusData.isOnline,
          connectionQuality: statusData.connectionQuality,
          firmwareVersion: statusData.firmwareVersion,
          batteryLevel: statusData.batteryLevel
        });
      }
    } catch (error) {
      common_vendor.index.__f__("error", "at utils/dataSync.js:164", "同步设备状态失败:", error);
      throw error;
    }
  }
  /**
   * 同步库存状态
   */
  async syncInventoryStatus() {
    try {
      const inventoryStatus = await utils_dyeConsumptionManager.dyeConsumptionManager.getInventoryStatus(this.deviceCode, false);
      const app = getApp();
      app.updateDyeInventory({
        totalColors: inventoryStatus.totalColors,
        lowStockCount: inventoryStatus.lowStockCount,
        alerts: inventoryStatus.alerts
      });
    } catch (error) {
      common_vendor.index.__f__("error", "at utils/dataSync.js:183", "同步库存状态失败:", error);
      throw error;
    }
  }
  /**
   * 检查网络状态
   */
  async checkNetworkStatus() {
    return new Promise((resolve) => {
      common_vendor.index.getNetworkType({
        success: (res) => {
          this.isOnline = res.networkType !== "none";
          resolve();
        },
        fail: () => {
          this.isOnline = false;
          resolve();
        }
      });
    });
  }
  /**
   * 设置网络状态监听器
   */
  setupNetworkListener() {
    common_vendor.index.onNetworkStatusChange((res) => {
      const wasOnline = this.isOnline;
      this.isOnline = res.isConnected;
      if (!wasOnline && this.isOnline) {
        common_vendor.index.__f__("log", "at utils/dataSync.js:215", "网络已恢复，开始同步离线数据");
        this.performSync();
      }
    });
  }
  /**
   * 设置消息监听器
   */
  setupMessageListeners() {
    utils_messageHandler.messageHandler.registerListener("DISPENSE_COMPLETE", () => {
      setTimeout(() => {
        this.syncInventoryStatus();
      }, 1e3);
    });
    utils_messageHandler.messageHandler.registerListener("DEVICE_STATUS_UPDATE", () => {
      this.syncDeviceStatus();
    });
  }
  /**
   * 添加到离线队列
   * @param {Object} data - 要同步的数据
   */
  addToOfflineQueue(data) {
    if (!this.isOnline) {
      this.offlineQueue.push({
        ...data,
        timestamp: (/* @__PURE__ */ new Date()).toISOString()
      });
      if (this.offlineQueue.length > this.maxOfflineQueueSize) {
        this.offlineQueue.splice(0, this.offlineQueue.length - this.maxOfflineQueueSize);
      }
    }
  }
  /**
   * 处理离线队列
   */
  async processOfflineQueue() {
    if (this.offlineQueue.length === 0)
      return;
    common_vendor.index.__f__("log", "at utils/dataSync.js:262", `处理离线队列，共${this.offlineQueue.length}条数据`);
    const processedItems = [];
    for (const item of this.offlineQueue) {
      try {
        await this.syncOfflineItem(item);
        processedItems.push(item);
      } catch (error) {
        common_vendor.index.__f__("error", "at utils/dataSync.js:271", "处理离线数据失败:", error);
        break;
      }
    }
    this.offlineQueue = this.offlineQueue.filter((item) => !processedItems.includes(item));
    if (processedItems.length > 0) {
      common_vendor.index.__f__("log", "at utils/dataSync.js:280", `成功处理${processedItems.length}条离线数据`);
    }
  }
  /**
   * 同步离线数据项
   * @param {Object} item - 离线数据项
   */
  async syncOfflineItem(item) {
    switch (item.type) {
      case "DYE_CONSUMPTION":
        await utils_request.apiService.dyeConsumption.recordUsage(item.data);
        break;
      case "DEVICE_STATUS":
        await utils_request.apiService.deviceStatus.setDeviceStatus(this.deviceCode, item.data);
        break;
      default:
        common_vendor.index.__f__("warn", "at utils/dataSync.js:297", "未知的离线数据类型:", item.type);
    }
  }
  /**
   * 处理同步错误
   * @param {Error} error - 错误对象
   */
  handleSyncError(error) {
    const app = getApp();
    if (error.message.includes("网络") || error.message.includes("timeout")) {
      this.isOnline = false;
    }
    app.setErrorState({
      errorType: "SYNC_ERROR",
      errorMessage: `数据同步失败: ${error.message}`,
      errorCode: null
    });
  }
  /**
   * 强制同步
   * @param {string} dataType - 数据类型 ('all', 'consumption', 'status', 'inventory')
   */
  async forceSync(dataType = "all") {
    if (this.syncInProgress) {
      common_vendor.index.__f__("log", "at utils/dataSync.js:327", "同步正在进行中，无法强制同步");
      return false;
    }
    try {
      this.syncInProgress = true;
      switch (dataType) {
        case "consumption":
          await this.syncDyeConsumption();
          break;
        case "status":
          await this.syncDeviceStatus();
          break;
        case "inventory":
          await this.syncInventoryStatus();
          break;
        case "all":
        default:
          await this.syncAllData();
          break;
      }
      this.lastSyncTime = (/* @__PURE__ */ new Date()).toISOString();
      return true;
    } catch (error) {
      common_vendor.index.__f__("error", "at utils/dataSync.js:353", "强制同步失败:", error);
      this.handleSyncError(error);
      return false;
    } finally {
      this.syncInProgress = false;
    }
  }
  /**
   * 获取同步状态
   * @returns {Object} 同步状态信息
   */
  getSyncStatus() {
    return {
      isOnline: this.isOnline,
      lastSyncTime: this.lastSyncTime,
      syncInProgress: this.syncInProgress,
      offlineQueueSize: this.offlineQueue.length,
      syncFrequency: this.syncFrequency
    };
  }
  /**
   * 设置同步频率
   * @param {number} frequency - 同步频率（毫秒）
   */
  setSyncFrequency(frequency) {
    this.syncFrequency = frequency;
    if (this.syncInterval && this.deviceCode) {
      this.start(this.deviceCode);
    }
  }
  /**
   * 清除离线队列
   */
  clearOfflineQueue() {
    this.offlineQueue = [];
  }
}
const dataSyncService = new DataSyncService();
exports.dataSyncService = dataSyncService;
//# sourceMappingURL=../../.sourcemap/mp-weixin/utils/dataSync.js.map
