/* 水平间距 */
/* 水平间距 */
/* 全局样式修复 - 确保页面占满屏幕宽度 */
page {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
}

/* 通用容器样式 */
.container, .page-container {
  width: 100%;
  min-height: 100vh;
  box-sizing: border-box;
}

/* 修复小程序页面宽度问题 */
uni-page-body {
  width: 100% !important;
}

/* 确保所有view元素正确继承宽度 */
view {
  box-sizing: border-box;
}
.add-address {
  min-height: 100vh;
  background-color: #f5f5f5;
  width: 100%;
  box-sizing: border-box;
  padding-bottom: 180rpx;
  /* 为底部按钮留出空间 */
}
.form-container {
  background-color: #ffffff;
  margin-top: 20rpx;
}
.form-item {
  display: flex;
  align-items: center;
  padding: 30rpx 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
  position: relative;
}
.label {
  width: 160rpx;
  font-size: 28rpx;
  color: #333333;
}
.input {
  flex: 1;
  height: 60rpx;
  font-size: 28rpx;
}
.textarea {
  flex: 1;
  height: 120rpx;
  font-size: 28rpx;
}
.placeholder {
  color: #999999;
}
.input-icon {
  padding: 0 10rpx;
}
.clear-btn {
  font-size: 36rpx;
  color: #999999;
}
.arrow {
  font-size: 30rpx;
  color: #999999;
}
.area-input {
  display: flex;
  align-items: center;
}
.switch-item {
  justify-content: space-between;
}
.bottom-btn {
  padding: 40rpx 20rpx;
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #fff;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.1);
  /* 适配底部安全区域 */
  padding-bottom: calc(40rpx + env(safe-area-inset-bottom));
}
.save-btn {
  height: 90rpx;
  line-height: 90rpx;
  text-align: center;
  background-color: #4285f4;
  color: #ffffff;
  border-radius: 45rpx;
  font-size: 32rpx;
  width: 100%;
  box-sizing: border-box;
}
.save-btn[disabled] {
  background-color: #ccc;
  color: #999;
}

/* 页面级别样式重置 */
page {
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}