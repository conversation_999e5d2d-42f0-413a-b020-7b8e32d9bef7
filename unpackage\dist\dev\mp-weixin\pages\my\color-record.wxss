/* 水平间距 */
/* 水平间距 */
/* 全局样式修复 - 确保页面占满屏幕宽度 */
page {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
}

/* 通用容器样式 */
.container, .page-container {
  width: 100%;
  min-height: 100vh;
  box-sizing: border-box;
}

/* 修复小程序页面宽度问题 */
uni-page-body {
  width: 100% !important;
}

/* 确保所有view元素正确继承宽度 */
view {
  box-sizing: border-box;
}
.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f5f5;
}
.search-bar {
  display: flex;
  padding: 20rpx;
  background-color: #fff;
}
.search-input {
  flex: 1;
  display: flex;
  align-items: center;
  background-color: #f0f0f0;
  border-radius: 30rpx;
  padding: 10rpx 20rpx;
  margin-right: 20rpx;
}
.search-icon {
  margin-right: 10rpx;
  color: #999;
}
.search-btn {
  padding: 10rpx 30rpx;
  background-color: #4285f4;
  color: #fff;
  border-radius: 30rpx;
  font-size: 28rpx;
}

/* 调色图片样式 */
.record-image-container {
  width: 120rpx;
  height: 120rpx;
  /* 保持正方形 */
  border-radius: 12rpx;
  /* 较小的圆角 */
  overflow: hidden;
  margin-right: 20rpx;
  flex-shrink: 0;
  background-color: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1rpx solid #eee;
}

/* 筛选弹窗样式 */
.filter-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}
.filter-content {
  width: 80%;
  max-height: 60%;
  background-color: #fff;
  border-radius: 10rpx;
  overflow: hidden;
}
.filter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  background-color: #4285f4;
  color: #fff;
}
.filter-title {
  font-size: 32rpx;
  font-weight: 500;
}
.filter-options {
  max-height: 400rpx;
  overflow-y: auto;
}
.filter-option {
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  font-size: 30rpx;
  color: #333;
}
.filter-option:last-child {
  border-bottom: none;
}
.filter-option:active {
  background-color: #f5f5f5;
}
.record-image {
  width: 100%;
  height: 100%;
}

/* 调色记录列表样式 */
.record-list {
  flex: 1;
  background-color: #fff;
}
.record-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
  background-color: #fff;
}
.record-left {
  display: flex;
  align-items: center;
  flex: 1;
}
.record-color {
  width: 100rpx;
  height: 100rpx;
  border-radius: 10rpx;
  margin-right: 20rpx;
  flex-shrink: 0;
}
.record-info {
  flex: 1;
}
.record-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
}
.record-time {
  font-size: 26rpx;
  color: #999;
}
.record-components {
  display: flex;
  flex-wrap: wrap;
  margin-top: 10rpx;
}
.component-tag {
  font-size: 22rpx;
  padding: 4rpx 12rpx;
  background-color: #f0f0f0;
  border-radius: 20rpx;
  margin-right: 10rpx;
  margin-bottom: 6rpx;
  color: #666;
}
.record-right {
  flex-shrink: 0;
}
.share-btn {
  background-color: #4285f4;
  color: #fff;
  padding: 12rpx 24rpx;
  border-radius: 20rpx;
  font-size: 26rpx;
}

/* 空状态提示 */
.empty-state {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

/* 在空状态样式中添加默认图片样式 */
.empty-image {
  width: 300rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
  opacity: 0.7;
}
.empty-text {
  font-size: 28rpx;
  color: #999;
}

/* 配方详情弹窗样式 */
.recipe-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 999;
}
.modal-content {
  width: 80%;
  max-height: 80%;
  background-color: #fff;
  border-radius: 10rpx;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}
.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  background-color: #4285f4;
  color: #fff;
}
.modal-title {
  font-size: 32rpx;
  font-weight: 500;
}
.close-btn {
  font-size: 40rpx;
  line-height: 1;
}
.recipe-content {
  padding: 30rpx;
  overflow-y: auto;
}

/* 配方详情弹窗中的图片样式 */
.recipe-color-preview {
  width: 120rpx;
  height: 120rpx;
  border-radius: 12rpx;
  margin: 0 auto 30rpx;
  border: 1rpx solid #ddd;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}
.recipe-name {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 30rpx;
}
.color-components {
  margin-bottom: 30rpx;
  padding: 20rpx;
  background-color: #f8f9fa;
  border-radius: 8rpx;
}
.components-title {
  font-size: 28rpx;
  font-weight: bold;
  margin-bottom: 15rpx;
  color: #333;
}
.component-item {
  display: flex;
  align-items: center;
  padding: 10rpx 0;
  border-bottom: 1rpx solid #eee;
}
.component-item:last-child {
  border-bottom: none;
}
.component-color {
  width: 30rpx;
  height: 30rpx;
  border-radius: 4rpx;
  margin-right: 15rpx;
  border: 1rpx solid #ddd;
}
.component-name {
  flex: 1;
  font-size: 26rpx;
  color: #333;
}
.component-amount {
  font-size: 26rpx;
  color: #666;
  font-weight: 500;
}
.usage-details {
  margin-bottom: 30rpx;
  padding: 20rpx;
  background-color: #f8f9fa;
  border-radius: 8rpx;
}
.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8rpx 0;
  border-bottom: 1rpx solid #eee;
}
.detail-item:last-child {
  border-bottom: none;
}
.detail-label {
  font-size: 26rpx;
  color: #666;
}
.detail-value {
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
}
.modal-footer {
  display: flex;
  padding: 20rpx;
  border-top: 1rpx solid #eee;
}
.action-btn {
  flex: 1;
  text-align: center;
  padding: 15rpx 0;
  background-color: #4285f4;
  color: #fff;
  border-radius: 6rpx;
  margin: 0 10rpx;
}
.action-btn.secondary {
  background-color: #f5f5f5;
  color: #666;
}
.filter-bar {
  display: flex;
  padding: 20rpx 10rpx;
  background-color: #fff;
  border-top: 1rpx solid #eee;
  border-bottom: 1rpx solid #eee;
  overflow-x: auto;
  white-space: nowrap;
}
.filter-item {
  display: flex;
  align-items: center;
  margin: 0 15rpx;
  font-size: 26rpx;
  color: #666;
}