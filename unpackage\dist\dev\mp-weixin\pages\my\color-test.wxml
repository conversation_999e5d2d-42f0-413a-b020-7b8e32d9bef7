<view class="container data-v-463872ce"><view class="device-info data-v-463872ce"><view class="device-icon data-v-463872ce"><image src="{{a}}" class="device-image data-v-463872ce"></image></view><view class="device-details data-v-463872ce"><view class="device-name data-v-463872ce">芭佰邑-智能染发机</view><view class="device-id data-v-463872ce">编号: {{b}}</view></view></view><view wx:if="{{c}}" class="color-grid data-v-463872ce"><view wx:for="{{d}}" wx:for-item="color" wx:key="e" class="color-item data-v-463872ce" bindtap="{{color.f}}"><view class="color-circle-container data-v-463872ce"><view style="{{'background-color:' + color.a}}" class="{{['color-circle', 'data-v-463872ce', color.b && 'selected']}}"></view><view class="color-progress data-v-463872ce" style="{{'width:' + color.c}}"></view></view><view class="color-name data-v-463872ce">{{color.d}}</view></view></view><view wx:if="{{e}}" class="bottom-buttons data-v-463872ce"><view class="btn-secondary data-v-463872ce" bindtap="{{g}}"><text class="data-v-463872ce">检测 {{f}} 色</text></view><view class="btn-primary data-v-463872ce" bindtap="{{i}}"><image src="{{h}}" class="btn-icon data-v-463872ce"></image><text class="data-v-463872ce" style="font:10rpx">添加染膏</text></view></view><uni-popup wx:if="{{y}}" class="r data-v-463872ce" u-s="{{['d']}}" u-r="popup" u-i="463872ce-0" bind:__l="__l" u-p="{{y}}"><uni-popup-dialog wx:if="{{w}}" class="data-v-463872ce" u-s="{{['d']}}" bindclose="{{t}}" bindconfirm="{{v}}" u-i="463872ce-1,463872ce-0" bind:__l="__l" u-p="{{w}}"><view class="popup-content data-v-463872ce"><view class="color-display data-v-463872ce" style="{{'background-color:' + j}}"></view><view class="color-details data-v-463872ce"><view class="detail-row data-v-463872ce"><text class="detail-label data-v-463872ce">颜色名称:</text><text class="detail-value data-v-463872ce">{{k}}</text></view><view class="detail-row data-v-463872ce"><text class="detail-label data-v-463872ce">仓位编号:</text><text class="detail-value data-v-463872ce">{{l}}</text></view><view class="detail-row data-v-463872ce"><text class="detail-label data-v-463872ce">剩余量:</text><text class="detail-value data-v-463872ce" style="{{'color:' + o}}">{{m}} {{n}}</text></view><view class="progress-container data-v-463872ce"><progress class="data-v-463872ce" percent="{{p}}" stroke-width="10" activeColor="{{q}}" backgroundColor="#f0f0f0"/></view><view class="status-message data-v-463872ce" style="{{'color:' + s}}">{{r}}</view></view></view></uni-popup-dialog></uni-popup></view>