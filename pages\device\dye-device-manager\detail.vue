<template>
  <view class="container">
    <uni-nav-bar title="染膏设备详情" left-icon="back" @clickLeft="back"></uni-nav-bar>
    
    <scroll-view scroll-y class="content">
      <view class="card">
        <view class="section">
          <text class="section-title">基本信息</text>
          <view class="info-item">
            <text class="label">设备名称:</text>
            <text class="value">{{ device.name }}</text>
          </view>
          <view class="info-item">
            <text class="label">设备SN:</text>
            <text class="value">{{ device.sn }}</text>
          </view>
          <view class="info-item">
            <text class="label">设备类型:</text>
            <text class="value">{{ device.type }}</text>
          </view>
          <view class="info-item">
            <text class="label">购买日期:</text>
            <text class="value">{{ device.purchaseDate }}</text>
          </view>
        </view>
        
        <view class="section">
          <text class="section-title">绑定信息</text>
          <view class="info-item">
            <text class="label">绑定状态:</text>
            <text class="value" :style="{color: device.status === 1 ? '#4cd964' : '#dd524d'}">
              {{ device.status === 1 ? '已绑定' : '未绑定' }}
            </text>
          </view>
          <view class="info-item" v-if="device.status === 1">
            <text class="label">所属门店:</text>
            <text class="value">{{ device.storeName }}</text>
          </view>
          <view class="info-item" v-if="device.status === 1">
            <text class="label">负责人:</text>
            <text class="value">{{ device.manager }}</text>
          </view>
          <view class="info-item" v-if="device.status === 1">
            <text class="label">绑定时间:</text>
            <text class="value">{{ device.bindTime }}</text>
          </view>
        </view>
        
        <view class="section" v-if="device.status === 1">
          <text class="section-title">管理员列表</text>
          <view class="staff-list">
            <view class="staff-item" v-for="manager in device.managers" :key="manager.id">
              <view class="staff-info">
                <text class="staff-name">{{ manager.name }}</text>
                <text class="staff-permission">{{ getPermissionText(manager.permissionType) }}</text>
              </view>
              <text class="staff-level" :style="{color: getPermissionColor(manager.permissionType)}">
                {{ getPermissionLevel(manager.permissionType) }}
              </text>
            </view>
            <view v-if="device.managers && device.managers.length === 0" class="empty-tip">
              暂无管理员
            </view>
          </view>
        </view>
        
        <view class="section" v-if="device.status === 1">
          <text class="section-title">使用权限</text>
          <view class="staff-list">
            <view class="staff-item" v-for="staff in assignedStaff" :key="staff.id">
              <view class="staff-info">
                <text>{{ staff.name }}</text>
                <text class="staff-permission">{{ getPermissionText(staff.permissionType) }}</text>
              </view>
              <text class="staff-level" :style="{color: getPermissionColor(staff.permissionType)}">
                {{ getPermissionLevel(staff.permissionType) }}
              </text>
            </view>
            <view v-if="assignedStaff.length === 0" class="empty-tip">
              暂无使用人员
            </view>
          </view>
        </view>
      </view>
    </scroll-view>
    
    <view class="footer-actions">
      <button type="primary" @click="goToLog">查看操作日志</button>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      device: {
        managers: [] // 确保managers数组存在
      },
      assignedStaff: []
    }
  },
  onLoad(options) {
    // 统一使用真实API数据
    this.loadDeviceDetail(options.id)
  },
  methods: {

    
    async loadDeviceDetail(id) {
      uni.showLoading({ title: '加载中...' })
      try {
        const [deviceRes, staffRes] = await Promise.all([
          this.$api.dyeDevice.getDetail({ id }),
          this.$api.dyeDevice.getAssignedStaff({ deviceId: id })
        ])
        this.device = deviceRes.data
        // 确保managers数组存在
        if (!this.device.managers) {
          this.device.managers = []
        }
        this.assignedStaff = staffRes.data
      } catch (e) {
        uni.showToast({ title: '加载失败', icon: 'none' })
      } finally {
        uni.hideLoading()
      }
    },
    
    getPermissionText(type) {
      switch(type) {
        case 1: return '普通权限';
        case 2: return '管理员权限';
        case 3: return '维护权限';
        default: return '未知权限';
      }
    },
    
    getPermissionLevel(type) {
      switch(type) {
        case 1: return '普通';
        case 2: return '管理员';
        case 3: return '维护';
        default: return '未知';
      }
    },
    
    getPermissionColor(type) {
      switch(type) {
        case 1: return '#4cd964'; // 绿色-普通
        case 2: return '#2979ff'; // 蓝色-管理员
        case 3: return '#ff9900'; // 橙色-维护
        default: return '#999';   // 灰色-未知
      }
    },
    
    goToLog() {
      uni.navigateTo({
        url: `/pages/device/dye-device-manager/log?id=${this.device.id}`
      })
    },
    
    back() {
      uni.navigateBack()
    }
  }
}
</script>

<style scoped>
.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f5f5;
}

.content {
  flex: 1;
  padding: 15px;
}

.card {
  background-color: #fff;
  border-radius: 8px;
  padding: 15px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
}

.section {
  margin-bottom: 20px;
}

.section-title {
  display: block;
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 10px;
  padding-bottom: 5px;
  border-bottom: 1px solid #eee;
}

.info-item {
  display: flex;
  margin-bottom: 8px;
}

.label {
  width: 80px;
  color: #666;
}

.value {
  flex: 1;
}

.staff-list {
  margin-top: 10px;
}

.staff-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0;
  border-bottom: 1px solid #f5f5f5;
}

.staff-info {
  display: flex;
  flex-direction: column;
}

.staff-name {
  font-size: 14px;
  margin-bottom: 4px;
}

.staff-permission {
  font-size: 12px;
  color: #999;
}

.staff-level {
  font-size: 12px;
  font-weight: bold;
  padding: 2px 8px;
  border-radius: 10px;
  background-color: rgba(41, 121, 255, 0.1);
}

.empty-tip {
  color: #999;
  text-align: center;
  padding: 10px;
}

.footer-actions {
  padding: 15px;
  background-color: #fff;
  border-top: 1px solid #eee;
}
</style>