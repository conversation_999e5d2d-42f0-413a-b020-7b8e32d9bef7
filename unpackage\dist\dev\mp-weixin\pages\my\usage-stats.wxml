<view class="container"><view class="data-overview"><view class="overview-item"><text class="time-range">近1个月</text><text class="data-value">{{a}}<text class="unit">次</text></text></view><view class="overview-item"><text class="time-range">近3个月</text><text class="data-value">{{b}}<text class="unit">次</text></text></view><view class="overview-item"><text class="time-range">近半年</text><text class="data-value">{{c}}<text class="unit">次</text></text></view><view class="overview-item"><text class="time-range">染发次数</text><text class="data-value">{{d}}<text class="unit">次</text></text></view><view class="overview-item"><text class="time-range">使用天数</text><text class="data-value">{{e}}<text class="unit">天</text></text></view><view class="overview-item"><text class="time-range">染膏使用</text><text class="data-value">{{f}}<text class="unit">g</text></text></view></view><view class="dye-consumption-section"><view class="section-header"><text class="section-title">染膏消耗详情</text><view class="refresh-btn" bindtap="{{g}}"><text>刷新</text></view></view><view wx:if="{{h}}" class="inventory-status"><view class="inventory-header"><text class="inventory-title">库存状态</text><text class="inventory-summary">{{i}}种颜色</text></view><view wx:if="{{j}}" class="alert-section"><text class="alert-title">⚠️ 库存预警 ({{k}})</text><view class="alert-list"><view wx:for="{{l}}" wx:for-item="alert" wx:key="d" class="alert-item"><text class="{{['alert-text', alert.c]}}">{{alert.a}}: {{alert.b}}% </text></view><view wx:if="{{m}}" class="more-alerts"><text>还有{{n}}个预警...</text></view></view></view></view><view wx:if="{{o}}" class="color-breakdown"><text class="breakdown-title">颜色消耗排行</text><view class="color-list"><view wx:for="{{p}}" wx:for-item="color" wx:key="e" class="color-item"><view class="color-rank">{{color.a}}</view><view class="color-info"><text class="color-name">{{color.b}}</text><text class="color-usage">{{color.c}}g</text></view><view class="color-bar"><view class="color-fill" style="{{'width:' + color.d}}"></view></view></view></view></view></view><view class="chart-section"><view class="section-header"><text class="section-title">染发次数</text><text class="section-title right">染膏消耗(g)</text></view><view class="time-filter"><view class="{{['filter-btn', q && 'active']}}" bindtap="{{r}}"><text>近7天</text></view><view class="{{['filter-btn', s && 'active']}}" bindtap="{{t}}"><text>近30天</text></view><view class="{{['filter-btn', v && 'active']}}" bindtap="{{w}}"><text>近半年</text></view><view class="{{['filter-btn', x && 'active']}}" bindtap="{{y}}"><text>近一年</text></view></view><view class="chart-area"><view wx:if="{{z}}" class="chart-placeholder"><view class="chart-line"><view wx:for="{{A}}" wx:for-item="point" wx:key="a" class="chart-point" style="{{'bottom:' + point.b + ';' + ('left:' + point.c)}}"></view></view><view class="chart-x-axis"><text wx:for="{{B}}" wx:for-item="label" wx:key="b">{{label.a}}</text></view></view><view wx:else class="no-chart-data"><view class="no-data"><image class="no-data-icon" src="{{C}}"/><text class="no-data-text">暂无图表数据</text></view></view></view></view><view class="completion-section"><view class="section-title">完成率</view><view class="time-filter"><view class="{{['filter-btn', D && 'active']}}" bindtap="{{E}}"><text>近7天</text></view><view class="{{['filter-btn', F && 'active']}}" bindtap="{{G}}"><text>近30天</text></view><view class="{{['filter-btn', H && 'active']}}" bindtap="{{I}}"><text>近半年</text></view><view class="{{['filter-btn', J && 'active']}}" bindtap="{{K}}"><text>近一年</text></view></view><view wx:if="{{L}}" class="completion-data"><view class="no-data"><image class="no-data-icon" src="{{M}}"/><text class="no-data-text">暂无数据</text></view></view></view></view>