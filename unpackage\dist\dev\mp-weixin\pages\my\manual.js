"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_request = require("../../utils/request.js");
const _sfc_main = {
  data() {
    return {
      currentTab: 0,
      tabs: [
        { name: "PDF文档" },
        { name: "图片" },
        { name: "视频" }
      ],
      manualList: [],
      pdfList: [],
      imageList: [],
      videoList: [],
      loading: false,
      error: ""
    };
  },
  onLoad() {
    this.loadManualList();
  },
  methods: {
    goBack() {
      common_vendor.index.navigateBack({
        delta: 1
      });
    },
    switchTab(index) {
      this.currentTab = index;
    },
    async loadManualList() {
      try {
        this.loading = true;
        this.error = "";
        const response = await utils_request.get("/api/manual/list");
        if (response && response.code === 200) {
          this.manualList = response.data || [];
          this.categorizeManuals();
        } else {
          this.error = (response == null ? void 0 : response.message) || "获取资料手册失败";
          this.loadDefaultData();
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/my/manual.vue:142", "获取资料手册失败:", error);
        this.error = "网络错误，请稍后重试";
        this.loadDefaultData();
      } finally {
        this.loading = false;
      }
    },
    categorizeManuals() {
      this.pdfList = this.manualList.filter(
        (item) => item.fileUrl && (item.fileUrl.toLowerCase().includes(".pdf") || item.title.includes("PDF") || item.title.includes("文档"))
      ).map((item) => ({
        id: item.id,
        name: item.title,
        url: item.fileUrl,
        date: this.formatDate(item.updateTime),
        description: item.description
      }));
      this.imageList = this.manualList.filter(
        (item) => item.fileUrl && (item.fileUrl.toLowerCase().match(/\.(jpg|jpeg|png|gif|webp)$/) || item.title.includes("图片") || item.title.includes("图"))
      ).map((item) => ({
        id: item.id,
        name: item.title,
        url: item.fileUrl || item.coverImage,
        description: item.description
      }));
      this.videoList = this.manualList.filter(
        (item) => item.fileUrl && (item.fileUrl.toLowerCase().match(/\.(mp4|avi|mov|wmv|flv)$/) || item.title.includes("视频") || item.title.includes("教程"))
      ).map((item) => ({
        id: item.id,
        name: item.title,
        url: item.fileUrl,
        date: this.formatDate(item.updateTime),
        description: item.description
      }));
    },
    loadDefaultData() {
      this.pdfList = [
        { name: "用户操作指南", url: "/static/manuals/user_guide.pdf", date: "2023-05-10" },
        { name: "产品说明书", url: "/static/manuals/product_manual.pdf", date: "2023-04-22" },
        { name: "常见问题解答", url: "/static/manuals/faq.pdf", date: "2023-03-15" }
      ];
      this.imageList = [
        { name: "产品示意图", url: "/static/manuals/images/product_diagram.jpg" },
        { name: "使用流程图", url: "/static/manuals/images/usage_flow.jpg" },
        { name: "功能介绍", url: "/static/manuals/images/features.jpg" }
      ];
      this.videoList = [
        { name: "新手入门教程", url: "/static/manuals/videos/beginner_tutorial.mp4", date: "2023-06-01" },
        { name: "高级功能演示", url: "/static/manuals/videos/advanced_demo.mp4", date: "2023-05-15" },
        { name: "使用技巧分享", url: "/static/manuals/videos/tips.mp4", date: "2023-04-20" }
      ];
    },
    formatDate(dateString) {
      if (!dateString)
        return "";
      const date = new Date(dateString);
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, "0")}-${String(date.getDate()).padStart(2, "0")}`;
    },
    openResource(item) {
      common_vendor.index.showLoading({
        title: "加载中"
      });
      common_vendor.index.downloadFile({
        url: item.url,
        success: (res) => {
          common_vendor.index.hideLoading();
          if (res.statusCode === 200) {
            common_vendor.index.openDocument({
              filePath: res.tempFilePath,
              success: function() {
                common_vendor.index.__f__("log", "at pages/my/manual.vue:220", "打开文档成功");
              },
              fail: function() {
                common_vendor.index.showToast({
                  title: "打开文档失败",
                  icon: "none"
                });
              }
            });
          }
        },
        fail: (err) => {
          common_vendor.index.hideLoading();
          common_vendor.index.showToast({
            title: "下载文件失败",
            icon: "none"
          });
          common_vendor.index.__f__("error", "at pages/my/manual.vue:237", "下载文件失败", err);
        }
      });
    },
    previewImage(item) {
      common_vendor.index.previewImage({
        urls: this.imageList.map((img) => img.url),
        current: item.url
      });
    },
    playVideo(item) {
      common_vendor.index.navigateTo({
        url: `/pages/my/video-player?url=${encodeURIComponent(item.url)}&name=${encodeURIComponent(item.name)}`,
        success: function() {
          common_vendor.index.__f__("log", "at pages/my/manual.vue:251", "跳转到视频播放页面成功");
        },
        fail: function() {
          common_vendor.index.showToast({
            title: "视频播放失败",
            icon: "none"
          });
        }
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.loading
  }, $data.loading ? {} : $data.error ? {
    c: common_vendor.t($data.error),
    d: common_vendor.o((...args) => $options.loadManualList && $options.loadManualList(...args))
  } : common_vendor.e({
    e: common_vendor.f($data.tabs, (tab, index, i0) => {
      return {
        a: common_vendor.t(tab.name),
        b: index,
        c: $data.currentTab === index ? 1 : "",
        d: common_vendor.o(($event) => $options.switchTab(index), index)
      };
    }),
    f: $data.currentTab === 0
  }, $data.currentTab === 0 ? {
    g: common_vendor.f($data.pdfList, (item, index, i0) => {
      return {
        a: common_vendor.t(item.name),
        b: common_vendor.t(item.date),
        c: index,
        d: common_vendor.o(($event) => $options.openResource(item), index)
      };
    })
  } : {}, {
    h: $data.currentTab === 1
  }, $data.currentTab === 1 ? {
    i: common_vendor.f($data.imageList, (item, index, i0) => {
      return {
        a: item.url,
        b: common_vendor.t(item.name),
        c: index,
        d: common_vendor.o(($event) => $options.previewImage(item), index)
      };
    })
  } : {}, {
    j: $data.currentTab === 2
  }, $data.currentTab === 2 ? {
    k: common_vendor.f($data.videoList, (item, index, i0) => {
      return {
        a: common_vendor.t(item.name),
        b: common_vendor.t(item.date),
        c: index,
        d: common_vendor.o(($event) => $options.playVideo(item), index)
      };
    })
  } : {}), {
    b: $data.error
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/my/manual.js.map
