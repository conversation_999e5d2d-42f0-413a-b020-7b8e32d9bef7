<!DOCTYPE html>
<html lang="zh-CN">
	<head>
		<meta charset="UTF-8">
		<meta name="viewport" content="width=device-width, initial-scale=1.0">
		<title>选择位置</title>
		<!-- <link rel="stylesheet" href="../styles/common.css"> -->
		<style>
			.location-picker {
				height: 100vh;
				display: flex;
				flex-direction: column;
				background: #f5f5f5;
				padding-top: 200rpx; /* 根据header实际高度调整 */
			}

			.header {
			  background: #4A90E2;
			  color: white;
			  display: flex;
			  align-items: center;
			  justify-content: space-between;
			  padding: 10px 15px;
			  /* 关键修改：添加顶部安全距离 */
			  padding-top: calc(20px + env(safe-area-inset-top));
			  box-sizing: border-box;
			  position: fixed;
			  top: 0;
			  left: 0;
			  right: 0;
			  z-index: 999;
			}
			
			.header-title {
			    font-size: 18px;
			    font-weight: 500;
			    position: absolute;
			    left: 50%;
			    top: 50%;
			    transform: translate(-50%, -50%);
			}

			.header-btn {
				background: none;
				border: none;
				color: white;
				font-size: 16px;
				cursor: pointer;
				padding: 5px 10px;
			}

			.search-container {
				background: white;
				padding: 15px;
				border-bottom: 1px solid #eee;
			}

			.search-box {
				display: flex;
				align-items: center;
				background: #f5f5f5;
				border-radius: 8px;
				padding: 10px 15px;
			}

			.search-icon {
				width: 20px;
				height: 20px;
				margin-right: 10px;
				opacity: 0.6;
			}

			.search-input {
				flex: 1;
				border: none;
				background: none;
				font-size: 16px;
				outline: none;
			}

			.map-container {
				flex: 1;
				position: relative;
				background: #e8f4fd;
				display: flex;
				align-items: center;
				justify-content: center;
				color: #666;
				font-size: 14px;
			}

			.location-list {
				background: white;
				max-height: 300px;
				overflow-y: auto;
			}

			.location-item {
				padding: 15px;
				border-bottom: 1px solid #f0f0f0;
				cursor: pointer;
				transition: background-color 0.2s;
			}

			.location-item:hover {
				background: #f8f8f8;
			}

			.location-item:last-child {
				border-bottom: none;
			}

			.location-name {
				font-size: 16px;
				color: #333;
				margin-bottom: 5px;
			}

			.location-address {
				font-size: 14px;
				color: #666;
			}

			.current-location {
				background: white;
				padding: 15px;
				border-bottom: 1px solid #eee;
				display: flex;
				align-items: center;
			}

			.location-icon {
				width: 24px;
				height: 24px;
				margin-right: 15px;
				color: #4A90E2;
			}

			.current-location-text {
				flex: 1;
			}

			.current-location-name {
				font-size: 16px;
				color: #333;
				margin-bottom: 3px;
			}

			.current-location-detail {
				font-size: 14px;
				color: #666;
			}

			.relocate-btn {
				background: none;
				border: none;
				color: #4A90E2;
				font-size: 14px;
				cursor: pointer;
				padding: 5px;
			}

			.loading {
				text-align: center;
				padding: 20px;
				color: #666;
			}
		</style>
	</head>
	<body>
		<div class="location-picker">
			<!-- 头部 -->
			<div class="header">
				<button class="header-btn" onclick="goBack()">取消</button>
				<div class="header-title">广东省东莞市南城街道</div>
				<button class="header-btn" onclick="confirmLocation()">确定</button>
			</div>

			<!-- 搜索框 -->
			<div class="search-container">
				<div class="search-box">
					<svg class="search-icon" viewBox="0 0 24 24" fill="currentColor">
						<path
							d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z" />
					</svg>
					<input type="text" class="search-input" placeholder="搜索地点" id="searchInput">
				</div>
			</div>

			<!-- 当前位置 -->
			<div class="current-location">
				<svg class="location-icon" viewBox="0 0 24 24" fill="currentColor">
					<path
						d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z" />
				</svg>
				<div class="current-location-text">
					<div class="current-location-name" id="currentLocationName">广东省东莞市南城街道</div>
					<div class="current-location-detail" id="currentLocationDetail">广东省东莞市南城街道</div>
				</div>
				<button class="relocate-btn" onclick="getCurrentLocation()">重新定位</button>
			</div>

			<!-- 地图容器 -->
			<div class="map-container">
				<div>腾讯地图</div>
			</div>

			<!-- 位置列表 -->
			<div class="location-list" id="locationList">
				<div class="location-item" onclick="selectLocation('越秀区广州市人民政府(府前路北)', '广东省广州市越秀区府前路北')">
					<div class="location-name">越秀区广州市人民政府(府前路北)</div>
					<div class="location-address">广东省广州市越秀区府前路北</div>
				</div>
				<div class="location-item" onclick="selectLocation('广州市人民政府', '广东省广州市越秀区北京街道府前路1号')">
					<div class="location-name">广州市人民政府</div>
					<div class="location-address">广东省广州市越秀区北京街道府前路1号</div>
				</div>
				<div class="location-item" onclick="selectLocation('珠江国际大厦', '广东省广州市越秀区越华路112号')">
					<div class="location-name">珠江国际大厦</div>
					<div class="location-address">广东省广州市越秀区越华路112号</div>
				</div>
				<div class="location-item" onclick="selectLocation('北京街道', '广东省广州市越秀区')">
					<div class="location-name">北京街道</div>
					<div class="location-address">广东省广州市越秀区</div>
				</div>
				<div class="location-item" onclick="selectLocation('广州交易广场', '广东省广州市越秀区东风中路268号')">
					<div class="location-name">广州交易广场</div>
					<div class="location-address">广东省广州市越秀区东风中路268号</div>
				</div>
			</div>
		</div>

		<script src="../utils/location.js"></script>
		<script>
			let selectedLocation = {
				name: '广东省东莞市南城街道',
				address: '广东省东莞市南城街道'
			};

			// 页面加载时获取当前位置
			document.addEventListener('DOMContentLoaded', function() {
				getCurrentLocation();
				setupSearch();
			});

			// 获取当前位置
			async function getCurrentLocation() {
				try {
					const locationService = new LocationService();
					const position = await locationService.getCurrentPosition();
					const address = await locationService.reverseGeocode(position.latitude, position.longitude);

					selectedLocation = {
						name: address,
						address: address,
						latitude: position.latitude,
						longitude: position.longitude
					};

					updateCurrentLocation(address);
					updateHeaderTitle(address);
				} catch (error) {
					console.error('获取位置失败:', error);
					// 使用默认位置
					updateCurrentLocation('广东省东莞市南城街道');
					updateHeaderTitle('广东省东莞市南城街道');
				}
			}

			// 更新当前位置显示
			function updateCurrentLocation(address) {
				document.getElementById('currentLocationName').textContent = address;
				document.getElementById('currentLocationDetail').textContent = address;
			}

			// 更新标题
			function updateHeaderTitle(address) {
				document.querySelector('.header-title').textContent = address;
			}

			// 选择位置
			function selectLocation(name, address) {
				selectedLocation = {
					name,
					address
				};
				updateCurrentLocation(name);
				updateHeaderTitle(name);

				// 高亮选中的项目
				document.querySelectorAll('.location-item').forEach(item => {
					item.style.background = '';
				});
				event.currentTarget.style.background = '#f0f8ff';
			}

			// 设置搜索功能
			function setupSearch() {
				const searchInput = document.getElementById('searchInput');
				searchInput.addEventListener('input', function(e) {
					const query = e.target.value.trim();
					if (query) {
						searchLocations(query);
					} else {
						showDefaultLocations();
					}
				});
			}

			// 搜索位置
			function searchLocations(query) {
				// 模拟搜索结果
				const mockResults = [{
						name: `${query}附近`,
						address: `广东省东莞市${query}`
					},
					{
						name: `${query}商圈`,
						address: `广东省东莞市南城街道${query}`
					},
					{
						name: `${query}地铁站`,
						address: `广东省东莞市${query}地铁站`
					}
				];

				updateLocationList(mockResults);
			}

			// 显示默认位置列表
			function showDefaultLocations() {
				const defaultLocations = [{
						name: '越秀区广州市人民政府(府前路北)',
						address: '广东省广州市越秀区府前路北'
					},
					{
						name: '广州市人民政府',
						address: '广东省广州市越秀区北京街道府前路1号'
					},
					{
						name: '珠江国际大厦',
						address: '广东省广州市越秀区越华路112号'
					},
					{
						name: '北京街道',
						address: '广东省广州市越秀区'
					},
					{
						name: '广州交易广场',
						address: '广东省广州市越秀区东风中路268号'
					}
				];

				updateLocationList(defaultLocations);
			}

			// 更新位置列表
			function updateLocationList(locations) {
				const listContainer = document.getElementById('locationList');
				listContainer.innerHTML = locations.map(location => `
                <div class="location-item" onclick="selectLocation('${location.name}', '${location.address}')">
                    <div class="location-name">${location.name}</div>
                    <div class="location-address">${location.address}</div>
                </div>
            `).join('');
			}

			// 确认选择
			function confirmLocation() {
				// 将选择的位置信息传递给父页面
				if (window.opener) {
					window.opener.postMessage({
						type: 'locationSelected',
						location: selectedLocation
					}, '*');
					window.close();
				} else {
					// 如果是在同一页面内，可以使用 localStorage 或其他方式
					localStorage.setItem('selectedLocation', JSON.stringify(selectedLocation));
					history.back();
				}
			}

			// 返回
			function goBack() {
				if (window.opener) {
					window.close();
				} else {
					history.back();
				}
			}
		</script>
	</body>
</html>