"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_request = require("../../utils/request.js");
const common_assets = require("../../common/assets.js");
const _sfc_main = {
  data() {
    return {
      deviceCode: "",
      deviceStatus: false,
      // 设备是否在线
      selectedColorName: "",
      // 新增属性存储选中的颜色名称
      selectedIndex: null,
      colorList: [
        {
          name: "0/11灰色/Grey",
          color_code: "0x30",
          hex: "#8B8B8B",
          value: 20
        },
        {
          name: "0/22绿色/Green",
          color_code: "0x31",
          hex: "#2E7D32",
          value: 20
        },
        {
          name: "0/33黄色/yellow",
          color_code: "0x32",
          hex: "#FFD700",
          value: 35
        },
        {
          name: "0/43橙色/Orange",
          color_code: "0x33",
          hex: "#FFA726",
          value: 32
        },
        {
          name: "0/45红色/Red",
          color_code: "0x34",
          hex: "#D32F2F",
          value: 15
        },
        {
          name: "0/66紫色/Purple",
          color_code: "0x35",
          hex: "#8E24AA",
          value: 15
        },
        {
          name: "0/77棕色/Brown",
          color_code: "0x36",
          hex: "#6D4C41",
          value: 10
        },
        {
          name: "0/88蓝色/Blue",
          color_code: "0x37",
          hex: "#1565C0",
          value: 5
        },
        {
          name: "2/0黑色/Black",
          color_code: "0x38",
          hex: "#000000",
          value: 30
        },
        {
          name: "0/00淡化剂/Faded",
          color_code: "0x39",
          hex: "#FFFFFF",
          value: 25
        },
        {
          name: "12/11极浅灰金色/Bleached",
          color_code: "0x40",
          hex: "#E0E0E0",
          value: 25
        },
        {
          name: "12/16极浅紫金/Silver",
          color_code: "0x41",
          hex: "#E1BEE7",
          value: 25
        },
        {
          name: "浅棕色/Gold",
          color_code: "0x42",
          hex: "#6D4C41",
          value: 25
        },
        {
          name: "深灰亚麻色/Linen",
          color_code: "0x43",
          hex: "#B0BEC5",
          value: 25
        },
        {
          name: "双氧乳3%",
          color_code: "0x0A",
          hex: "#FFFFFF",
          value: 25
        },
        {
          name: "双氧乳12%",
          color_code: "0x0B",
          hex: "#FFFFFF",
          value: 25
        }
      ],
      unit: "g",
      remainingQuantity: 0
    };
  },
  onLoad() {
    const app = getApp();
    this.deviceCode = app.globalData.connectedDeviceId;
    this.deviceStatus = app.globalData.deviceStatus;
  },
  methods: {
    selectColor(color, index) {
      common_vendor.index.__f__("log", "at pages/my/color-test.vue:199", "选择颜色:", color);
      this.selectedIndex = index;
      this.selectedColorName = color.name;
      this.selectedColorHex = color.hex;
      this.selectedColorPosition = color.color_code;
    },
    async checkColorRemaining() {
      if (this.selectedIndex === null) {
        common_vendor.index.showToast({
          title: "请先选择要检测的颜色",
          icon: "none",
          duration: 2e3
        });
        return;
      }
      const selectedColor = this.colorList[this.selectedIndex];
      try {
        common_vendor.index.showLoading({
          title: "检测中...",
          mask: true
        });
        const response = await utils_request.apiService.mall.dyeConsumption.checkRemaining({
          deviceCode: this.deviceCode,
          colorCode: selectedColor.color_code
        });
        if (response.code === 200) {
          this.remainingQuantity = response.data.remainingQuantity || 0;
          this.popupType = this.remainingQuantity <= 50 ? "error" : "success";
          this.popupTitle = "染膏余量检测";
          this.popupContent = `当前颜色余量: ${response.data.proportion}${response.data.unit}`;
          this.unit = response.data.unit;
          this.$refs.popup.open();
        } else {
          throw new Error(response.message || "查询余量失败");
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/my/color-test.vue:258", "检测余量失败:", error);
        common_vendor.index.showToast({
          title: error.message || "检测余量失败",
          icon: "none",
          duration: 2e3
        });
      } finally {
        common_vendor.index.hideLoading();
      }
    },
    popupClose() {
      common_vendor.index.__f__("log", "at pages/my/color-test.vue:269", "弹窗关闭");
    },
    popupConfirm() {
      common_vendor.index.__f__("log", "at pages/my/color-test.vue:273", "弹窗确认");
      this.$refs.popup.close();
    },
    showAllColors() {
      common_vendor.index.__f__("log", "at pages/my/color-test.vue:277", "显示所有颜色");
    },
    startDetection() {
      const app = getApp();
      common_vendor.index.__f__("log", "at pages/my/color-test.vue:282", "开始检测");
      common_vendor.index.scanCode({
        onlyFromCamera: false,
        // 是否只能从相机扫码（不允许从相册选择）
        scanType: ["qrCode", "barCode"],
        // 扫码类型：二维码、条形码
        success: async (res) => {
          common_vendor.index.__f__("log", "at pages/my/color-test.vue:289", "扫码成功:", res);
          const result = res.result;
          res.scanType;
          try {
            const data = JSON.parse(result.replace(/[ \n]+/g, ""));
            const deviceCode = app.globalData.connectedDeviceId ? app.globalData.connectedDeviceId : void 0;
            const response = await utils_request.apiService.mall.dyeConsumption.scanCodeBinding(
              deviceCode,
              data
            );
            if (response.code === 200) {
              common_vendor.index.showToast({
                title: `添加${data.colorname ? data.colorname : ""}染膏成功`,
                icon: "success",
                duration: 3e3
              });
            } else {
              throw new Error("扫码识别,不是染膏类型的二维码");
            }
          } catch (err) {
            common_vendor.index.showToast({
              title: `扫码识别,不是染膏类型的二维码`,
              icon: "none",
              duration: 3e3
            });
          }
        },
        fail: (err) => {
          common_vendor.index.__f__("error", "at pages/my/color-test.vue:332", "扫码失败:", err);
          common_vendor.index.showToast({
            title: "扫码失败，请重试",
            icon: "none",
            duration: 2e3
          });
        }
      });
    },
    getProgressWidth(name) {
      const match = name.match(/\((\d+)-/);
      if (match && match[1]) {
        const num = parseInt(match[1]);
        return `${num / 50 * 100}%`;
      }
      return "0%";
    }
  }
};
if (!Array) {
  const _easycom_uni_popup_dialog2 = common_vendor.resolveComponent("uni-popup-dialog");
  const _easycom_uni_popup2 = common_vendor.resolveComponent("uni-popup");
  (_easycom_uni_popup_dialog2 + _easycom_uni_popup2)();
}
const _easycom_uni_popup_dialog = () => "../../uni_modules/uni-popup/components/uni-popup-dialog/uni-popup-dialog.js";
const _easycom_uni_popup = () => "../../uni_modules/uni-popup/components/uni-popup/uni-popup.js";
if (!Math) {
  (_easycom_uni_popup_dialog + _easycom_uni_popup)();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_assets._imports_0$2,
    b: common_vendor.t($data.deviceCode),
    c: $data.deviceCode && $data.deviceStatus
  }, $data.deviceCode && $data.deviceStatus ? {
    d: common_vendor.f($data.colorList, (color, index, i0) => {
      return {
        a: color.hex,
        b: $data.selectedIndex === index ? 1 : "",
        c: $options.getProgressWidth(color.name),
        d: common_vendor.t(color.name),
        e: index,
        f: common_vendor.o(($event) => $options.selectColor(color, index), index)
      };
    })
  } : {}, {
    e: $data.deviceCode
  }, $data.deviceCode ? {
    f: common_vendor.t($data.selectedColorName || "灰色(1-0/1)"),
    g: common_vendor.o((...args) => $options.checkColorRemaining && $options.checkColorRemaining(...args)),
    h: common_assets._imports_1$3,
    i: common_vendor.o((...args) => $options.startDetection && $options.startDetection(...args))
  } : {}, {
    j: _ctx.selectedColorHex,
    k: common_vendor.t($data.selectedColorName),
    l: common_vendor.t(_ctx.selectedColorPosition),
    m: common_vendor.t($data.remainingQuantity),
    n: common_vendor.t($data.unit),
    o: $data.remainingQuantity < 20 ? "#ff4d4f" : "#52c41a",
    p: $data.remainingQuantity,
    q: $data.remainingQuantity < 20 ? "#ff4d4f" : "#52c41a",
    r: common_vendor.t($data.remainingQuantity < 20 ? "余量不足，请及时补充" : "余量充足"),
    s: $data.remainingQuantity < 20 ? "#ff4d4f" : "#52c41a",
    t: common_vendor.o($options.popupClose),
    v: common_vendor.o($options.popupConfirm),
    w: common_vendor.p({
      type: _ctx.popupType,
      title: _ctx.popupTitle,
      content: _ctx.popupContent,
      duration: 2e3,
      ["before-close"]: true
    }),
    x: common_vendor.sr("popup", "463872ce-0"),
    y: common_vendor.p({
      type: "dialog"
    })
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-463872ce"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/my/color-test.js.map
