<view class="container"><view class="header"><view class="location-bar" bindtap="{{d}}"><view class="location-info"><uni-icons wx:if="{{a}}" u-i="2d381b1c-0" bind:__l="__l" u-p="{{a}}"></uni-icons><text class="location-text">{{b}}</text></view><uni-icons wx:if="{{c}}" u-i="2d381b1c-1" bind:__l="__l" u-p="{{c}}"></uni-icons></view><view class="search-container"><view class="search-input" bindtap="{{j}}"><uni-icons wx:if="{{e}}" u-i="2d381b1c-2" bind:__l="__l" u-p="{{e}}"></uni-icons><input type="text" placeholder="搜索商品" bindconfirm="{{f}}" confirm-type="search" placeholder-class="placeholder-style" value="{{g}}" bindinput="{{h}}"/><view class="search-btn blue-btn" catchtap="{{i}}"><text>搜索</text></view></view></view><view class="scan-icon" bindtap="{{l}}"><uni-icons wx:if="{{k}}" u-i="2d381b1c-3" bind:__l="__l" u-p="{{k}}"></uni-icons></view></view><scroll-view class="main-content" scroll-y refresher-enabled refresher-triggered="{{v}}" bindrefresherrefresh="{{w}}" bindscrolltolower="{{x}}"><view class="section category-section"><view class="section-header"><text class="section-title">自营专区</text><text class="section-subtitle">行业赋能，降本增效</text></view><view class="category-grid"><view wx:for="{{m}}" wx:for-item="category" wx:key="f" class="category-item" bindtap="{{category.g}}"><view class="category-icon"><image wx:if="{{category.a}}" src="{{category.b}}" mode="aspectFit" lazy-load binderror="{{category.c}}"/><view wx:else class="icon-fallback"><text class="icon">{{category.d}}</text></view></view><text class="category-name">{{category.e}}</text></view></view></view><view class="section product-section"><view class="section-header"><text class="section-title">热门推荐</text><text class="section-subtitle">大家都在买</text></view><view class="product-grid"><view wx:for="{{n}}" wx:for-item="product" wx:key="k" class="product-card" bindtap="{{product.l}}"><view class="product-image-container"><image class="product-image" src="{{product.a}}" mode="aspectFill" lazy-load fade-show="{{false}}"/><view wx:if="{{product.b}}" class="product-tag">{{product.c}}</view></view><view class="product-info"><text class="product-name">{{product.d}}</text><view class="meta-rating-container"><view class="price-container"><text class="current-price">¥{{product.e}}</text><text wx:if="{{product.f}}" class="original-price">¥{{product.g}}</text></view><view class="product-meta"><text class="sales">已售{{product.h}}件</text></view></view><view class="rating"><uni-icons wx:if="{{o}}" u-i="{{product.i}}" bind:__l="__l" u-p="{{o}}"></uni-icons><text>{{product.j}}%</text></view></view></view></view><view wx:if="{{p}}" class="loading-container"><uni-load-more wx:if="{{q}}" u-i="2d381b1c-5" bind:__l="__l" u-p="{{q}}"></uni-load-more></view><view wx:if="{{r}}" class="no-more"><text>— 已经到底啦 —</text></view></view><view wx:if="{{s}}" class="empty-state"><view class="empty-icon"><text class="icon">📦</text></view><text class="empty-text">暂无商品数据</text><text class="empty-desc">商城正在筹备中，敬请期待</text><button class="refresh-btn" bindtap="{{t}}">重新加载</button></view></scroll-view></view>