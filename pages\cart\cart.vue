<template>
	<view class="cart-container">
		<block v-if="cartItems.length > 0">
			<!-- 管理按钮 -->
			<view class="manage-section">
				<view class="manage-btn" @click="toggleManageMode">
					{{ isManageMode ? '完成' : '管理' }}
				</view>
			</view>

			<view class="cart-list">
				<view class="cart-item" v-for="(item, index) in cartItems" :key="index">
					<view class="checkbox">
						<checkbox :checked="item.selected" @click="toggleSelect(index)" color="#4285f4"></checkbox>
					</view>
					<image class="product-image" :src="item.image" mode="aspectFill"></image>
					<view class="product-info">
						<text class="product-name">{{item.name}}</text>
						<text class="product-spec">{{item.spec}}</text>
						<text class="stock-info" v-if="item.stock">库存：{{item.stock}}件</text>
						<view class="bottom-row">
							<text class="product-price">¥{{item.price}}</text>
							<view class="quantity-selector">
								<view class="quantity-btn minus" @click="decreaseQuantity(index)">-</view>
								<input type="number" :value="item.quantity" class="quantity-input" disabled />
								<view class="quantity-btn plus" @click="increaseQuantity(index)">+</view>
							</view>
						</view>
					</view>
				</view>
			</view>
			
			<view class="bottom-bar">
				<view class="left-section">
					<view class="select-all">
						<checkbox :checked="allSelected" @click="toggleSelectAll" color="#4285f4"></checkbox>
						<text>全选</text>
					</view>
					<view class="price-info" v-if="!isManageMode">
						<text>合计：</text>
						<text class="total-price">¥{{totalPrice}}</text>
					</view>
				</view>
				<view class="right-section">
					<view class="checkout-btn" v-if="!isManageMode" @click="checkout">
						结算({{selectedCount}})
					</view>
					<view class="delete-btn" v-else @click="deleteSelected">
						删除
					</view>
				</view>
			</view>
		</block>
		
		<view class="empty-cart" v-else>
			<view class="empty-content">
				<view class="empty-icon">
					<image src="/static/images/empty-cart.svg" mode="aspectFit"></image>
				</view>
				<view class="empty-text">
					<text class="empty-title">购物车还是空的</text>
					<text class="empty-subtitle">快去挑选心仪的商品吧~</text>
				</view>
				<button class="go-shopping" @click="goShopping">去逛逛</button>
			</view>

			<!-- 装饰性背景 -->
			<view class="empty-decoration">
				<view class="decoration-circle circle-1"></view>
				<view class="decoration-circle circle-2"></view>
				<view class="decoration-circle circle-3"></view>
			</view>
		</view>
	</view>
</template>

<script>
import { apiService } from '@/utils/request.js';
import { getFullImageUrl } from '@/utils/imageUtils.js';

export default {
	data() {
		return {
			isManageMode: false,
			cartItems: [],
			loading: false,
			checkoutLoading: false
		};
	},
	computed: {
		allSelected() {
			return this.cartItems.length > 0 && this.cartItems.every(item => item.selected);
		},
		selectedCount() {
			return this.cartItems.filter(item => item.selected).length;
		},
		totalPrice() {
			return this.cartItems.filter(item => item.selected)
				.reduce((total, item) => total + (item.price * item.quantity), 0)
				.toFixed(2);
		}
	},
	onLoad() {
		this.loadCartItems();
	},
	onShow() {
		// 重置结算loading状态，防止从订单页面返回时按钮仍然被禁用
		this.checkoutLoading = false;
	},
	methods: {
		// 加载购物车数据
		async loadCartItems() {
			this.loading = true;
			try {
				const response = await apiService.mall.cart.list();

				if (response.code === 200) {
					this.cartItems = (response.data || []).map(item => ({
						...item,
						selected: item.checked === 1,
						image: getFullImageUrl(item.imageUrl || ''),
						name: item.productName || '商品名称',
						price: item.price || 0,
						spec: item.skuName || '默认规格'
					}));
				} else {
					console.error('获取购物车失败:', response.message);
				}
			} catch (error) {
				console.error('获取购物车异常:', error);
				// 如果API失败，尝试从本地存储加载
				const cartItems = uni.getStorageSync('cartItems');
				if (cartItems && cartItems.length > 0) {
					this.cartItems = cartItems;
				}
			} finally {
				this.loading = false;
			}
		},


		toggleManageMode() {
			this.isManageMode = !this.isManageMode;
		},
		async toggleSelect(index) {
			const item = this.cartItems[index];
			const newSelected = !item.selected;

			try {
				const response = await apiService.mall.cart.select({
					cartId: item.id,
					selected: newSelected
				});

				if (response.code === 200) {
					this.cartItems[index].selected = newSelected;
				} else {
					uni.showToast({
						title: '操作失败',
						icon: 'none'
					});
				}
			} catch (error) {
				console.error('选择商品失败:', error);
				// 如果API失败，仍然更新本地状态
				this.cartItems[index].selected = newSelected;
			}
		},

		async toggleSelectAll() {
			const newStatus = !this.allSelected;

			// 批量选择/取消选择
			const promises = this.cartItems.map(item =>
				apiService.mall.cart.select({
					cartId: item.id,
					selected: newStatus
				}).catch(error => {
					console.error('批量选择失败:', error);
					return null;
				})
			);

			try {
				await Promise.all(promises);
				this.cartItems.forEach(item => {
					item.selected = newStatus;
				});
			} catch (error) {
				console.error('批量选择异常:', error);
				// 如果API失败，仍然更新本地状态
				this.cartItems.forEach(item => {
					item.selected = newStatus;
				});
			}
		},

		async decreaseQuantity(index) {
			const item = this.cartItems[index];
			if (item.quantity <= 1) {
				uni.showToast({
					title: '商品数量不能少于1件',
					icon: 'none'
				});
				return;
			}

			const newQuantity = item.quantity - 1;

			try {
				const response = await apiService.mall.cart.update({
					cartId: item.id,
					quantity: newQuantity
				});

				if (response.code === 200) {
					this.cartItems[index].quantity = newQuantity;
				} else {
					uni.showToast({
						title: response.message || '更新失败',
						icon: 'none'
					});
				}
			} catch (error) {
				console.error('更新数量失败:', error);
				uni.showToast({
					title: '网络连接错误',
					icon: 'none'
				});
			}
		},

		async increaseQuantity(index) {
			const item = this.cartItems[index];
			const newQuantity = item.quantity + 1;

			// 检查库存限制
			if (item.stock && newQuantity > item.stock) {
				uni.showModal({
					title: '库存不足',
					content: `该商品库存仅剩${item.stock}件，无法继续添加`,
					showCancel: false,
					confirmText: '知道了'
				});
				return;
			}

			try {
				const response = await apiService.mall.cart.update({
					cartId: item.id,
					quantity: newQuantity
				});

				if (response.code === 200) {
					this.cartItems[index].quantity = newQuantity;
					this.calculateTotal();
				} else if (response.code === 500 && response.message && response.message.includes('库存')) {
					// 后端返回的库存不足错误
					uni.showModal({
						title: '库存不足',
						content: response.message,
						showCancel: false,
						confirmText: '知道了'
					});
				} else {
					uni.showToast({
						title: response.message || '更新失败',
						icon: 'none'
					});
				}
			} catch (error) {
				console.error('更新数量失败:', error);
				// 检查是否是网络错误还是其他错误
				if (error.message && error.message.includes('timeout')) {
					uni.showToast({
						title: '请求超时，请重试',
						icon: 'none'
					});
				} else {
					uni.showToast({
						title: '操作失败，请重试',
						icon: 'none'
					});
				}
			}
		},
		checkout() {
			// 防抖处理 - 防止重复点击
			if (this.checkoutLoading) {
				return;
			}

			if (this.selectedCount === 0) {
				uni.showToast({
					title: '请选择要结算的商品',
					icon: 'none'
				});
				return;
			}

			this.checkoutLoading = true;

			// 获取选中的商品
			const selectedItems = this.cartItems.filter(item => item.selected);

			// 构建订单数据
			const orderData = {
				type: 'cart',
				products: selectedItems.map(item => ({
					id: item.productId || item.id,
					name: item.name,
					price: item.price,
					quantity: item.quantity,
					skuId: item.skuId,
					skuName: item.spec,
					images: item.image,
					cartId: item.id
				}))
			};

			// 传递数据到确认订单页面
			uni.navigateTo({
				url: '/pages/order/confirm?data=' + encodeURIComponent(JSON.stringify(orderData))
			});
		},
		async deleteSelected() {
			if (this.selectedCount === 0) {
				uni.showToast({
					title: '请选择要删除的商品',
					icon: 'none'
				});
				return;
			}

			uni.showModal({
				title: '提示',
				content: '确定要删除选中的商品吗？',
				success: async (res) => {
					if (res.confirm) {
						const selectedItems = this.cartItems.filter(item => item.selected);

						// 批量删除
						const promises = selectedItems.map(item =>
							apiService.mall.cart.delete(item.id).catch(error => {
								console.error('删除商品失败:', error);
								return null;
							})
						);

						try {
							await Promise.all(promises);
							this.cartItems = this.cartItems.filter(item => !item.selected);

							uni.showToast({
								title: '删除成功',
								icon: 'success'
							});

							if (this.cartItems.length === 0) {
								this.isManageMode = false;
							}
						} catch (error) {
							console.error('批量删除异常:', error);
							// 如果API失败，仍然更新本地状态
							this.cartItems = this.cartItems.filter(item => !item.selected);

							uni.showToast({
								title: '删除成功',
								icon: 'success'
							});

							if (this.cartItems.length === 0) {
								this.isManageMode = false;
							}
						}
					}
				}
			});
		},
		goShopping() {
			uni.switchTab({
				url: '/pages/mall/mall'
			});
		},

		// 计算总价
		calculateTotal() {
			// 这个方法实际上不需要做任何事情，因为总价是通过计算属性自动计算的
			// 但为了避免错误，我们保留这个空方法
		}
	}
};
</script>

<style lang="scss">
/* 页面级别样式 */
page {
	width: 100%;
	height: 100%;
	margin: 0;
	padding: 0;
	box-sizing: border-box;
}

.cart-container {
	min-height: 100vh;
	width: 100%;
	background-color: #f5f5f5;
	display: flex;
	flex-direction: column;
	padding-bottom: 120rpx; /* 为底部栏留出空间 */
	box-sizing: border-box;
	margin: 0;
	padding-left: 0;
	padding-right: 0;
}

.manage-section {
	display: flex;
	justify-content: flex-end;
	padding: 20rpx;
	background-color: #f5f5f5;
}

.manage-btn {
	background-color: #4285f4;
	color: #ffffff;
	padding: 10rpx 20rpx;
	border-radius: 20rpx;
	font-size: 28rpx;
	text-align: center;
	min-width: 80rpx;
}

.cart-list {
	padding: 20rpx;
	flex: 1; /* 占满剩余空间 */
}

.cart-item {
	display: flex;
	align-items: center;
	background-color: #ffffff;
	padding: 20rpx;
	margin-bottom: 20rpx;
	border-radius: 10rpx;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.checkbox {
	margin-right: 20rpx;
}

.product-image {
	width: 160rpx;
	height: 160rpx;
	margin-right: 20rpx;
	border-radius: 8rpx;
}

.product-info {
	flex: 1;
	display: flex;
	flex-direction: column;
}

.product-name {
	font-size: 28rpx;
	margin-bottom: 10rpx;
}

.product-spec {
	font-size: 24rpx;
	color: #999;
	margin-bottom: 10rpx;
}

.stock-info {
	font-size: 22rpx;
	color: #666;
	margin-bottom: 20rpx;
}

.bottom-row {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.product-price {
	font-size: 32rpx;
	color: #f44336;
	font-weight: bold;
}

.quantity-selector {
	display: flex;
	align-items: center;
	border: 1rpx solid #eee;
	border-radius: 4rpx;
}

.quantity-btn {
	width: 60rpx;
	height: 60rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	background-color: #f5f5f5;
	font-size: 28rpx;
}

.quantity-input {
	width: 60rpx;
	height: 60rpx;
	text-align: center;
}

.bottom-bar {
	position: fixed;
	left: 0;
	right: 0;
	bottom: 0;
	display: flex;
	align-items: center;
	justify-content: space-between;
	height: 100rpx;
	background-color: #ffffff;
	padding: 0 20rpx;
	box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.left-section {
	display: flex;
	align-items: center;
	flex: 1;
}

.right-section {
	display: flex;
	align-items: center;
}

.select-all {
	display: flex;
	align-items: center;
}

.select-all text {
	font-size: 28rpx;
	margin-left: 10rpx;
}

.price-info {
	display: flex;
	align-items: baseline;
	margin-left: 30rpx;
	font-size: 28rpx;
}

.total-price {
	color: #f44336;
	font-size: 36rpx;
	font-weight: bold;
}

.checkout-btn, .delete-btn {
	padding: 10rpx 30rpx;
	border-radius: 40rpx;
	font-size: 28rpx;
	height: 70rpx;
	line-height: 70rpx;
	text-align: center;
}

.checkout-btn {
	background-color: #f44336;
	color: #ffffff;
	width: 240rpx;
}

.delete-btn {
	background-color: #ff5252;
	color: #ffffff;
	width: 240rpx;
}

.empty-cart {
	flex: 1;
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 40rpx 20rpx;
	position: relative;
	min-height: 60vh;
	background: linear-gradient(135deg, #f8f9ff 0%, #f0f4ff 100%);
}

.empty-content {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	background-color: #ffffff;
	padding: 60rpx 40rpx;
	border-radius: 20rpx;
	box-shadow: 0 8rpx 30rpx rgba(66, 133, 244, 0.1);
	position: relative;
	z-index: 2;
	width: 90%;
	max-width: 600rpx;
}

.empty-icon {
	margin-bottom: 30rpx;
}

.empty-icon image {
	width: 160rpx;
	height: 160rpx;
}

.empty-text {
	display: flex;
	flex-direction: column;
	align-items: center;
	margin-bottom: 40rpx;
}

.empty-title {
	font-size: 32rpx;
	color: #333;
	font-weight: 500;
	margin-bottom: 10rpx;
}

.empty-subtitle {
	font-size: 26rpx;
	color: #666;
	text-align: center;
}

.go-shopping {
	background: linear-gradient(135deg, #4285f4 0%, #5a9bff 100%);
	color: #ffffff;
	padding: 24rpx 60rpx;
	border-radius: 50rpx;
	font-size: 30rpx;
	font-weight: 500;
	box-shadow: 0 6rpx 20rpx rgba(66, 133, 244, 0.3);
	border: none;
}

.go-shopping:active {
	transform: translateY(2rpx);
	box-shadow: 0 4rpx 15rpx rgba(66, 133, 244, 0.3);
}

.empty-decoration {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	z-index: 1;
	overflow: hidden;
}

.decoration-circle {
	position: absolute;
	border-radius: 50%;
	background: linear-gradient(135deg, rgba(66, 133, 244, 0.1) 0%, rgba(90, 155, 255, 0.05) 100%);
}

.circle-1 {
	width: 120rpx;
	height: 120rpx;
	top: 10%;
	left: 10%;
	animation: float 6s ease-in-out infinite;
}

.circle-2 {
	width: 80rpx;
	height: 80rpx;
	top: 20%;
	right: 15%;
	animation: float 8s ease-in-out infinite reverse;
}

.circle-3 {
	width: 100rpx;
	height: 100rpx;
	bottom: 15%;
	left: 20%;
	animation: float 7s ease-in-out infinite;
}

@keyframes float {
	0%, 100% {
		transform: translateY(0px);
	}
	50% {
		transform: translateY(-20rpx);
	}
}
</style> 