
.container {
  padding: 30px 20px;
}
.title {
  font-size: 20px;
  font-weight: bold;
  margin-bottom: 30px;
  text-align: center;
}
.btn-container {
  display: flex;
  justify-content: space-around;
  margin-bottom: 20px;
}
.test-btn {
  padding: 12px 20px;
  background-color: #4285f4;
  color: white;
  border-radius: 5px;
  font-size: 16px;
}
.test-btn.alt {
  background-color: #34a853;
}
.results {
  margin-top: 30px;
  padding: 15px;
  background-color: #f5f5f5;
  border-radius: 8px;
}
.result-title {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 10px;
  display: block;
}
.result-item {
  display: block;
  font-size: 14px;
  color: #333;
  padding: 8px 0;
  border-bottom: 1px solid #e0e0e0;
}
