/* 水平间距 */
/* 水平间距 */
/* 全局样式修复 - 确保页面占满屏幕宽度 */
page {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
}

/* 通用容器样式 */
.container, .page-container {
  width: 100%;
  min-height: 100vh;
  box-sizing: border-box;
}

/* 修复小程序页面宽度问题 */
uni-page-body {
  width: 100% !important;
}

/* 确保所有view元素正确继承宽度 */
view {
  box-sizing: border-box;
}
.content-detail {
  background-color: #fff;
  min-height: 100vh;
  padding-bottom: 120rpx;
  /* 图片轮播区域 */
  /* 动态内容 */
  /* 发布信息 */
  /* 互动数据 */
  /* 评论区 */
  /* 评论输入框 */
}
.content-detail .image-swiper {
  width: 100%;
  height: 500rpx;
}
.content-detail .image-swiper .swiper-image {
  width: 100%;
  height: 100%;
}
.content-detail .content-box {
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}
.content-detail .content-box .content-text {
  font-size: 32rpx;
  line-height: 1.6;
  color: #333;
}
.content-detail .meta-info {
  padding: 20rpx 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1rpx solid #f0f0f0;
}
.content-detail .meta-info .user-info {
  display: flex;
  align-items: center;
}
.content-detail .meta-info .user-info .user-avatar {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  margin-right: 15rpx;
}
.content-detail .meta-info .user-info .meta-user {
  font-size: 28rpx;
  color: #333;
}
.content-detail .meta-info .meta-time {
  font-size: 24rpx;
  color: #999;
}
.content-detail .interaction-stats {
  padding: 25rpx 30rpx;
  display: flex;
  border-bottom: 1rpx solid #f0f0f0;
}
.content-detail .interaction-stats .stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-right: 50rpx;
}
.content-detail .interaction-stats .stat-item .stat-num {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 5rpx;
}
.content-detail .interaction-stats .stat-item .stat-label {
  font-size: 24rpx;
  color: #999;
}
.content-detail .interaction-stats .stat-item:last-child {
  margin-right: 0;
}
.content-detail .comment-section {
  padding: 30rpx;
}
.content-detail .comment-section .comment-title {
  font-size: 30rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
  color: #333;
}
.content-detail .comment-section .no-comment {
  text-align: center;
  font-size: 28rpx;
  color: #999;
  padding: 40rpx 0;
}
.content-detail .comment-section .comment-list .comment-item {
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
}
.content-detail .comment-section .comment-list .comment-item .comment-user-info {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
}
.content-detail .comment-section .comment-list .comment-item .comment-user-info .comment-user {
  font-size: 28rpx;
  color: #576b95;
  margin-right: 15rpx;
}
.content-detail .comment-section .comment-list .comment-item .comment-user-info .comment-time {
  font-size: 24rpx;
  color: #999;
}
.content-detail .comment-section .comment-list .comment-item .comment-text {
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
}
.content-detail .comment-input {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  padding: 20rpx;
  display: flex;
  align-items: center;
  border-top: 1rpx solid #eee;
  z-index: 100;
}
.content-detail .comment-input .input-box {
  flex: 1;
  background-color: #f5f5f5;
  height: 70rpx;
  border-radius: 35rpx;
  padding: 0 30rpx;
  font-size: 28rpx;
  margin-right: 20rpx;
}
.content-detail .comment-input .send-btn {
  background-color: #4285f4;
  color: #fff;
  font-size: 28rpx;
  height: 70rpx;
  line-height: 70rpx;
  padding: 0 30rpx;
  border-radius: 35rpx;
  margin: 0;
}
.content-detail .comment-input .send-btn::after {
  border: none;
}