<view class="manual-container"><view class="content"><view wx:if="{{a}}" class="loading-container"><text>加载中...</text></view><view wx:elif="{{b}}" class="error-container"><text>{{c}}</text><button bindtap="{{d}}" class="retry-btn">重试</button></view><view wx:else><view class="category-tabs"><view wx:for="{{e}}" wx:for-item="tab" wx:key="b" class="{{['tab-item', tab.c && 'active']}}" bindtap="{{tab.d}}"><text>{{tab.a}}</text></view></view><view wx:if="{{f}}" class="resource-list"><view wx:for="{{g}}" wx:for-item="item" wx:key="c" class="resource-item" bindtap="{{item.d}}"><view class="resource-icon"><text class="iconfont icon-pdf"></text></view><view class="resource-info"><text class="resource-name">{{item.a}}</text><text class="resource-date">{{item.b}}</text></view><view class="resource-action"><text class="iconfont icon-download"></text></view></view></view><view wx:if="{{h}}" class="resource-list image-list"><view wx:for="{{i}}" wx:for-item="item" wx:key="c" class="image-item" bindtap="{{item.d}}"><image class="thumbnail" src="{{item.a}}" mode="aspectFill"></image><text class="image-name">{{item.b}}</text></view></view><view wx:if="{{j}}" class="resource-list"><view wx:for="{{k}}" wx:for-item="item" wx:key="c" class="resource-item" bindtap="{{item.d}}"><view class="resource-icon video-icon"><text class="iconfont icon-video"></text></view><view class="resource-info"><text class="resource-name">{{item.a}}</text><text class="resource-date">{{item.b}}</text></view><view class="resource-action"><text class="iconfont icon-play"></text></view></view></view></view></view></view>