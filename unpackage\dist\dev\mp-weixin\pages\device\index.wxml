<view class="content"><view class="connection-tabs"><view class="{{['tab-item', b && 'active']}}" bindtap="{{c}}"><text>连网</text><view wx:if="{{a}}" class="tab-indicator"></view></view><view class="{{['tab-item', e && 'active']}}" bindtap="{{f}}"><text>蓝牙</text><view wx:if="{{d}}" class="tab-indicator"></view></view><view class="{{['tab-item', h && 'active']}}" bindtap="{{i}}"><text>WIFI</text><view wx:if="{{g}}" class="tab-indicator"></view></view></view><view wx:if="{{j}}"><scroll-view class="device-list" scroll-y><view wx:for="{{k}}" wx:for-item="item" wx:key="f" class="device-item" bindtap="{{item.g}}"><view class="device-info"><text class="device-name">{{item.a}}</text><text class="device-id">{{item.b}}</text><text class="device-rssi">信号强度: {{item.c}}dBm</text></view><view class="device-actions"><text class="{{['connect-btn', item.e && 'connected']}}">{{item.d}}</text></view></view><view wx:if="{{l}}" class="empty-tip"><text>正在搜索设备...</text></view><view wx:elif="{{m}}" class="empty-tip"><text>未发现设备，请点击"搜索蓝牙"按钮</text></view></scroll-view><view class="action-buttons" style="position:fixed;bottom:0;left:0;right:0;background:white;padding:20rpx;z-index:100"><view class="{{['scan-btn', o && 'scanning']}}" bindtap="{{p}}"><text class="scan-btn-text">{{n}}</text></view><view wx:if="{{q}}" class="disconnect-btn" bindtap="{{r}}"><text class="scan-btn-text">断开连接</text></view></view></view><view wx:if="{{s}}" class="network-connection"><view class="connection-row"><view class="connection-item" bindtap="{{w}}"><text class="connection-label">连网</text><text class="{{['connection-status', v && 'active']}}">{{t}}</text></view><view class="connection-item" bindtap="{{z}}"><text class="connection-label">蓝牙</text><text class="{{['connection-status', y && 'active']}}">{{x}}</text></view><view class="connection-item" bindtap="{{C}}"><text class="connection-label">WIFI</text><text class="{{['connection-status', B && 'active']}}">{{A}}</text></view></view><view class="scan-device-btn" bindtap="{{D}}"><text>扫描设备</text></view></view><view wx:if="{{E}}" class="wifi-connection"><view class="wifi-form"><view class="form-item"><text class="form-label">设备编号</text><input class="form-input" type="text" placeholder="请输入设备编号" value="{{F}}" bindinput="{{G}}"/></view><view class="form-item"><text class="form-label">wifi名称</text><input class="form-input" type="text" placeholder="请输入wifi名" value="{{H}}" bindinput="{{I}}"/></view><view class="form-item"><text class="form-label">密码</text><input class="form-input" type="password" placeholder="请输入密码" value="{{J}}" bindinput="{{K}}"/></view></view><button class="wifi-property-btn" bindtap="{{L}}">配置设备wifi</button></view></view>