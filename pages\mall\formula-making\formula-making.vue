<template>
	<view class="container">
		<view class="device-status">
			<text class="categoryName">Ai芭佰邑-{{ categoryName }}</text>
			<text class="device-state"
				:class="deviceStatus  ? 'status-online' : 'status-offline'">{{deviceStatus ? '设备在线' : '设备离线'}}</text>
		</view>
		<view class="card">
			<text class="card-title grading" :style="{ color: commandStatus ? 'SlateBlue' : 'black' }"
				:class="{ 'status-text': true, 'command-active': commandStatus }">
				{{ commandStatus ? (executionDetail.currentStep || '调色中') : '请放碗' }}
			</text>

			<!-- 进度条 -->
			<!-- <view v-if="commandStatus && executionDetail.progress > 0" class="progress-container">
				<view class="progress-bar">
					<view class="progress-fill" :style="{ width: executionDetail.progress + '%' }"></view>
				</view>
				<text class="progress-text">{{ executionDetail.progress }}%</text>
			</view> -->

			<image class="bowl-icon"
				:src="isaction ? '/static/icons/tiaosewan_action.png' : '/static/icons/tiaosewan.png'"
				:class="{ 'active': isaction, 'inactive': !isaction }" />
			<text class="bowl-color">{{ colorName }}</text>
			<view class="button-group">
				<!-- 主操作按钮 -->
				    <button 
				      v-if="!isExecutingQueue && !isPaused" 
				      class="start-button" 
				      @click="debounceStarttoning"
				    >
				      开始
				    </button>
				    
				    <button 
				      v-else-if="isExecutingQueue && !isPaused" 
				      class="stop-button" 
				      @click="debounceStopttoning"
				    >
				      暂停
				    </button>
				    
				    <button 
				      v-else-if="isPaused" 
				      class="resume-button" 
				      @click="debounceResumetoning"
				    >
				      恢复
				    </button>
				
				    <!-- 取消按钮 -->
				    <button 
				      v-if="isExecutingQueue || isPaused" 
				      class="cancel-button" 
				      @click="debounceCanceling"
				    >
				      取消
				    </button>
				
				    <!-- Master模式专属按钮 -->
				    <button 
				      v-if="model === 'master'" 
				      class="stop-master-button" 
				      @click="debounceCaseting"
				    >
				      停止
				    </button>
			</view>
		</view>
		<view class="card">
			<text class="card-title">出膏量</text>
			<view v-for="(item, index) in dispensingAmounts" :key="index" class="color-item">
				<view :class="['color-indicator', item.color]" :style="{ backgroundColor: item.color }">
				</view>
				<text>{{ item.name }}</text>
				<text class="amount">{{ item.value }}g ({{ item.used }}g)</text>
			</view>
		</view>

		<!-- 染膏库存状态卡片 -->
	</view>
</template>

<script>
	import {
		api,
		apiService,
		get
	} from '../../../utils/request';
	import HairDyeRecipeExecutor from '@/utils/hairDyeManager';
	import dyeConsumptionManager from '../../../utils/dyeConsumptionManager.js';
	import {
		filter
	} from 'lodash-es';
	import { debounce } from '@/utils/debounce.js'
	export default {
		data() {
			return {
				isaction: false,
				recipeExecutor: null,
				colorComponents: null,
				developerComponents: null,
				continueToExecute: false,
				model: null,
				// 添加状态属性定义
				isExecutingQueue: false,
				isPaused: false,
				deviceStatus: false,
				commandStatus: false,
				// executionDetail: {
				// 	currentStep: '',
				// 	progress: 0
				// },
				dispensingAmounts: [
					// {
					// 	color: 'green',
					// 	name: '绿色',
					// 	// code: '2',
					// 	value: '10',
					// 	used: '0'
					// },
					// {
					// 	color: 'yellow',
					// 	name: '黄色',
					// 	// code: '3',
					// 	total: '5',
					// 	value: '0'
					// },
					// {
					// 	color: 'purple',
					// 	name: '紫色',
					// 	// code: '6',
					// 	total: '1',
					// 	value: '0'
					// }
				],
				deviceStatus: '',
				commandStatus: '',
				inventoryStatus: null, // 染膏库存状态
				executionDetail: { // 指令执行详情
					progress: 0,
					currentStep: '',
					totalSteps: 0,
					startTime: null,
					estimatedEndTime: null
				},

				// 状态监听器相关
				statusWatcher: null,
				lastExecutingState: false,
				lastPausedState: false,
				lastStates: false,
				lastWanColorState: false,
				lastDeviceStates: false,
				lastCommandStatus: false,
				// 颜色分类
				colorName: undefined,
				// 颜色名称
				categoryName: undefined,
				// 为了记录调色记录的id
				formulaId : 0
			};
		},
		mounted() {
			const app = getApp();
			// // 初始化数据
			this.deviceStatus = app.globalData.deviceStatus;
			this.commandStatus = app.globalData.commandStatus;
			this.executionDetail = app.globalData.commandExecutionDetail;


			// // 监听 globalData 变化
			// this.$watch(
			// 	() => getApp().globalData.deviceStatus,
			// 	(newVal) => {
			// 		this.deviceStatus = newVal;
			// 	}
			// );

			// this.$watch(
			// 	() => getApp().globalData.commandStatus,
			// 	(newVal) => {
			// 		this.commandStatus = newVal;
			// 	}
			// );

			// this.$watch(
			// 	() => getApp().globalData.commandExecutionDetail,
			// 	(newVal) => {
			// 		this.executionDetail = newVal;
			// 	}
			// );

			// this.$watch(
			// 	() => getApp().globalData.dyeInventory,
			// 	(newVal) => {
			// 		this.updateInventoryStatus();
			// 	}
			// );

			// // 添加执行状态监听
			// this.$watch(
			// 	() => getApp().globalData.isExecutingQueue,
			// 	(newVal) => {
			// 		console.log('isExecutingQueue 状态变化:', newVal);
			// 		this.$forceUpdate(); // 强制更新视图
			// 	}
			// );

			// this.$watch(
			// 	() => getApp().globalData.isPaused,
			// 	(newVal) => {
			// 		console.log('isPaused 状态变化:', newVal);
			// 		this.$forceUpdate(); // 强制更新视图
			// 	}
			// );

			// // 初始加载库存状态
			this.loadInventoryStatus();
		},
		onShow() {
			// 每次页面显示时刷新库存状态
			this.loadInventoryStatus();
			this.getState();
			// 初始化状态
			this.initializeState();
		},
		onLoad(options) {
			try {
				if (options.data) {
					const dataString = decodeURIComponent(options.data);
					this.dispensingAmounts = JSON.parse(dataString);

					// 将克数小于0.1的过滤掉
					// this.dispensingAmounts = this.dispensingAmounts.filter(item => item.value > 0);

					console.log("参数中的options.model", options.model)
					this.model = options.model
					// console.log("主数据", this.dispensingAmounts);
					console.log("模式", this.model)
				} else {
					// console.warn("缺少主数据参数");
					this.dispensingAmounts = {};
				}

				// 2. 处理 colorComponents
				if (options.colorComponents && options.colorComponents !== 'null') {
					try {
						const colorString = decodeURIComponent(options.colorComponents);
						this.colorComponents = JSON.parse(colorString);
					} catch (e) {
						console.error("解析 colorComponents 失败:", e);
						this.colorComponents = null;
					}
				} else {
					this.colorComponents = null;
				}
				// console.log("colorComponents", this.colorComponents);

				// 3. 处理 developerComponents
				if (options.developerComponents && options.developerComponents !== 'null') {
					try {
						const devString = decodeURIComponent(options.developerComponents);
						this.developerComponents = JSON.parse(devString);
						console.log("双氧乳", this.developerComponents)
					} catch (e) {
						console.error("解析 developerComponents 失败:", e);
						this.developerComponents = null;
					}
				} else {
					this.developerComponents = null;
				}

				try {
					const cachedData = uni.getStorageSync('selectedColorInfo');
					console.log("缓存中的数据", cachedData)
					if (cachedData) {
						this.colorName = cachedData.name || '未知颜色';
						this.categoryName = cachedData.category || '未知分类';
						// this.colorHex = cachedData.hex || '#000000';
						this.formulaId = cachedData.id || 0
						console.log('从缓存读取数据成功');
					} else {
						console.error('无法获取颜色信息');
						uni.showToast({
							title: '加载失败',
							icon: 'none'
						});
					}
				} catch (e) {
					console.error('读取缓存失败:', e);
				}

			} catch (mainError) {
				console.error("页面加载时发生错误:", mainError);
				// 可以在这里初始化默认值或显示错误提示
				this.dispensingAmounts = {};
				this.colorComponents = null;
				this.developerComponents = null;
			}
		},
		computed: {
			// 访问全局状态
			// isExecutingQueue() {
			// 	return getApp().globalData.isExecutingQueue;
			// },
			// isPaused() {
			// 	return getApp().globalData.isPaused;
			// },
			// connectionMethod() {
			// 	return getApp().globalData.connectionMethod;
			// },
			// // 连接方式判断
			// isBluetoothConnection() {
			// 	return this.connectionMethod === 'bluetooth';
			// },
			// isWifiConnection() {
			// 	return this.connectionMethod === 'wifi';
			// }
		},
		created() {
			this.recipeExecutor = new HairDyeRecipeExecutor(this.$store);
			// 启动状态监听器
			this.startStatusWatcher();
		},
		beforeDestroy() {
			// 清理定时器
			if (this.statusWatcher) {
				clearInterval(this.statusWatcher);
			}
		},
		methods: {
			// 初始化状态
			initializeState() {
				const app = getApp();
				this.isExecutingQueue = app.globalData.isExecutingQueue || false;
				this.isPaused = app.globalData.isPaused || false;
				this.deviceStatus = app.globalData.deviceStatus || false;
				this.commandStatus = app.globalData.commandStatus || false;
				// this.wan_color = app.globalData.wan_color || false;
				this.executionDetail = app.globalData.commandExecutionDetail || {
					currentStep: '',
					progress: 0
				};
				console.log('状态初始化完成:', {
					isExecutingQueue: this.isExecutingQueue,
					isPaused: this.isPaused,
					deviceStatus: this.deviceStatus,
					commandStatus: this.commandStatus
				});
			},

			// 启动状态监听器
			startStatusWatcher() {
				// 保存初始状态
				this.lastExecutingState = getApp().globalData.isExecutingQueue; // 队列指令执行状态
				this.lastPausedState = getApp().globalData.isPaused; // 设备是否是暂停状态
				this.lastWanColorState = getApp().globalData.wan_color; //设备碗的颜色
				this.lastDeviceStates = getApp().globalData.deviceStatus; //设备在线状态
				this.lastCommandStatus = getApp().globalData.commandStatus // 指令执行状态
				this.lastExecutionDetail = getApp().globalData.commandExecutionDetail

				console.log("设备是否在线", this.lastDeviceStates)
				console.log("当前页面在线状态", this.deviceStatus)

				// 每500ms检查一次状态变化
				this.statusWatcher = setInterval(() => {
					const app = getApp();
					const currentExecuting = app.globalData.isExecutingQueue;
					const currentPaused = app.globalData.isPaused;
					const currentWanColor = app.globalData.wan_color;
					const currentDeviceStates = app.globalData.deviceStatus;
					const currentCommandStatus = app.globalData.commandStatus;
					const currentCommandExecutionDetail = app.globalData.commandExecutionDetail

					// 如果状态发生变化，强制更新组件
					if (
						currentExecuting !== this.lastExecutingState ||
						currentPaused !== this.lastPausedState ||
						currentWanColor !== this.lastWanColorState ||
						currentDeviceStates != this.deviceStatus ||
						currentCommandStatus != this.lastCommandStatus ||
						currentCommandExecutionDetail != this.lastExecutionDetail
					) {
						this.lastExecutingState = currentExecuting;
						this.lastPausedState = currentPaused;
						this.lastWanColorState = currentWanColor;
						this.lastDeviceStates = currentDeviceStates //设备在线状态
						this.lastCommandStatus = currentCommandStatus // 指令执行状态
						this.lastExecutionDetail = currentCommandExecutionDetail


						this.isExecutingQueue = currentExecuting; // 执行中状态
						this.isPaused = currentPaused; // 停止状态
						this.isaction = currentWanColor; // 现在碗的颜色
						this.deviceStatus = currentDeviceStates //现在设备状态 
						this.commandStatus = currentCommandStatus // 指令执行状态
						this.executionDetail = currentCommandExecutionDetail // 指令执行详情

						console.log("当前状态", this.deviceStatus)
						console.log("调色碗颜色", this.isaction ? '绿色' : '灰色')
						console.log('状态变化检测到，强制更新UI:', {
							executing: currentExecuting,
							paused: currentPaused
						});
						this.$forceUpdate();
					}
				}, 500);
			},

			getState() {
				const app = getApp();
				const currentExecuting = app.globalData.isExecutingQueue;
				const currentPaused = app.globalData.isPaused;
				const currentWanColor = app.globalData.wan_color;

				this.lastExecutingState = currentExecuting;
				this.lastPausedState = currentPaused;
				this.lastWanColorState = currentWanColor;

				this.isExecutingQueue = currentExecuting;
				this.isPaused = currentPaused;
				this.wan_color = currentWanColor;
				this.isaction = currentWanColor;
				this.$forceUpdate();
				console.log('页面退出需要更新:', {
					executing: currentExecuting,
					paused: currentPaused
				});
			},

			// 加载库存状态
			async loadInventoryStatus() {
				try {
					const app = getApp();
					const deviceCode = app.globalData.connectedDeviceId;
					if (deviceCode) {
						this.inventoryStatus = await dyeConsumptionManager.getInventoryStatus(deviceCode);
					}
				} catch (error) {
					console.error('加载库存状态失败:', error);
				}
			},

			// 更新库存状态
			updateInventoryStatus() {
				const app = getApp();
				if (app.globalData.dyeInventory.lastUpdateTime) {
					this.loadInventoryStatus();
				}
			},

			// 检查库存是否足够
			async checkStockBeforeExecution() {
				try {
					const app = getApp();
					const deviceCode = app.globalData.connectedDeviceId;
					if (!deviceCode) return true;

					const recipe = Object.entries({
						...this.colorComponents,
						...this.developerComponents
					}).reduce((acc, [key, value]) => {
						if (value >= 0.1) {
							acc[key] = value;
						}
						return acc;
					}, {});



					const stockCheck = await dyeConsumptionManager.checkSufficientStock(deviceCode, recipe);

					if (!stockCheck.sufficient) {
						let message = '染膏库存不足:\n';
						stockCheck.insufficient.forEach(item => {
							message += `${item.colorName}: 需要${item.required}g, 仅剩${item.available}g\n`;
						});

						uni.showModal({
							title: '库存不足',
							content: message,
							showCancel: true,
							confirmText: '继续执行',
							cancelText: '取消',
							success: (res) => {
								if (res.confirm) {
									this.executeRecipeWithoutCheck();
								}
							}
						});
						return false;
					}

					if (stockCheck.warnings.length > 0) {
						let message = '以下染膏执行后将库存较低:\n';
						stockCheck.warnings.forEach(item => {
							message += `${item.colorName}: 执行后剩余${item.remaining}g\n`;
						});

						uni.showModal({
							title: '库存预警',
							content: message,
							showCancel: true,
							confirmText: '继续执行',
							cancelText: '取消',
							success: (res) => {
								if (res.confirm) {
									this.executeRecipeWithoutCheck();
								}
							}
						});
						return false;
					}

					return true;
				} catch (error) {
					console.error('检查库存失败:', error);
					return true; // 检查失败时允许继续执行
				}
			},

			// 不检查库存直接执行
			async executeRecipeWithoutCheck() {
				const data = {
					...this.colorComponents,
					...this.developerComponents
				};

				console.log("当前指令生成", data)

				const app = getApp();
				app.updateCommandExecutionDetail({
					startTime: new Date().toISOString(),
					progress: 0,
					currentStep: '开始执行',
					totalSteps: Object.keys(data).length
				});



				const response = await this.recipeExecutor.executeRecipe(data, this.model);
				console.log("this.recipeExecutor.executeRecipe返回",response)
				if (!response) {
					uni.showToast({
						title: '设备不在线',
						icon: 'none',
						duration: 2000
					});
					app.updateCommandExecutionDetail({
						progress: 0,
						currentStep: '执行失败'
					});
				}
			},
			// 大师模式停止
			caseting: async function() {
				const app = getApp()
				try {
					if (!this.checkOnline()) {
						return
					}

					if (!this.isExecutingQueue) {
						uni.showToast({
							title: '请开始出料',
							icon: 'none',
							duration: 2000 // 持续时间
						})
						return
					}

					if (app.isBluetoothConnection()) {
						const response = this.recipeExecutor.stopMaster()
						if (!response) {
							uni.showToast({
								title: '大师模式停止出料失败',
								icon: 'none',
								duration: 2000 // 持续时间
							})
							return
						}
					} else if (app.isWifiConnection()) {
						const response = await apiService.mall.command.sendStop(app.globalData.connectedDeviceId, {
							model: 'master'
						})
						if (!response) {
							uni.showToast({
								title: '大师模式停止出料失败',
								icon: 'none',
								duration: 2000 // 持续时间
							})
							return
						}
					}
					// 拿到当前执行中的指令
					const command = app.globalData.currentCommand

					uni.showToast({
						title: '大师模式停止当前`${command.colorCode}`',
						icon: 'none',
						duration: 2000 // 持续时间
					})
					// 提示

				} catch (err) {
					console.log("大师模式停止出料失败", err)
				}
			},
			// 继续执行那么就发送继续的指令
			canceling: async function() {
				try {
					if (!this.checkOnline()) {
						return
					}

					console.log("取消出料")
					const response = await this.recipeExecutor.canceling();
					if (!response) {
						uni.showToast({
							title: '取消出料失败',
							icon: 'none',
							duration: 2000
						})
					} else {
						console.log("取消指令已发送，等待设备确认");
						// 取消之后将队列清空
						const app = getApp()
						app.setCanceling()
					}

				} catch (err) {
					console.log("取消出料失败", err)
					uni.showToast({
						title: '取消出料失败',
						icon: 'none',
						duration: 2000
					})
				}
				// 强制更新组件以反映状态变化
				this.$forceUpdate();
			},
			stopttoning: async function() {
				if (!this.checkOnline()) {
					return
				}
				console.log("暂停出料")
				const response = await this.recipeExecutor.pauseDispensing();
				if (!response) {
					uni.showToast({
						title: '暂停出料失败',
						icon: 'none',
						duration: 2000 // 持续时间
					})
				} else {
					// 暂停
					const app = getApp();
					app.setPaused(true)
					console.log("暂停出料成功，状态已更新");
				}
				// 强制更新组件以反映状态变化
				this.$forceUpdate();
			},
			resumetoning: async function() {
				if (!this.checkOnline()) {
					return
				}
				console.log("恢复出料")
				const response = await this.recipeExecutor.resumeDispensing();
				if (!response) {
					uni.showToast({
						title: '恢复出料失败',
						icon: 'none',
						duration: 2000 // 持续时间
					})
				} else {
					const app = getApp()
					app.setPaused(false)
					console.log("恢复出料成功，状态已更新");
				}
				// 强制更新组件以反映状态变化
				this.$forceUpdate();
			},
			checkOnline() {
				// 设备未连接那么就弹出一个！号的提示框显示设备离线
				if (!this.deviceStatus) {
					uni.showToast({
						title: '设备离线',
						icon: 'error', // 微信小程序支持"success"/"error"/"loading"/"none"
						duration: 1000,
						mask: true // 防止触摸穿透
					});
					return false
				}
				return true
			},
			// 记录调色记录
			recordToningOperation: async function() {
				const data = {
					formulaId : this.formulaId,
					colorComponents : this.colorComponents,
					developerComponents : this.developerComponents
				}
				
				try {
				        const response = apiService.record.createReCode(data);
				
				        if (response.code === 500) {
				            throw new Error("记录失败，HTTP状态码：" + response.code);
				        }
				
				        console.log("调色记录成功");
				    } catch (error) {
				        console.error("记录调色操作失败:", error);
				        // throw error;  // 可选择抛出或静默失败
				    }
				
			},
			starttoning: async function() {
				// this.isaction = !this.isaction
				try {
					// 设备未连接那么就弹出一个！号的提示框显示设备离线
					if (!this.checkOnline()) {
						return
					}
					
					// 异步不管失败还是成功都记录一个调色记录
					this.recordToningOperation()
					
					console.log("开始执行出料")
					

					// 检查库存是否足够
					const stockSufficient = await this.checkStockBeforeExecution();
					if (!stockSufficient) {
						return; // 库存不足或用户取消，不继续执行
					}

					await this.executeRecipeWithoutCheck();
					console.log("开始出料成功，状态已更新");
				} catch (error) {
					console.error('执行出错:', error);
					const app = getApp();
					app.setErrorState({
						errorType: 'EXECUTION_ERROR',
						errorMessage: `执行出错: ${error.message}`,
						errorCode: null
					});
					app.updateCommandExecutionDetail({
						progress: 0,
						currentStep: '执行失败'
					});
					// 执行失败时重置状态
					app.setExecutingQueue(false);
					app.setPausedStatus(false);
				}
				// 强制更新组件以反映状态变化
				this.$forceUpdate();
			},
			
			// 防抖处理方法
			    debounceStarttoning: debounce(function() {
			      this.starttoning();
			    }, 300),
			    
			    debounceStopttoning: debounce(function() {
			      this.stopttoning();
			    }, 300),
			    
			    debounceResumetoning: debounce(function() {
			      this.resumetoning();
			    }, 300),
			    
			    debounceCanceling: debounce(function() {
			      this.canceling();
			    }, 300),
			    
			    debounceCaseting: debounce(function() {
			      this.caseting();
			    }, 300)
		},
	}
</script>

<style>
	.container {
		/* padding: 20px; */
		background-color: #f5f5f5;
		padding: 10rpx;
	}

	.header {
		background-color: #3b99fc;
		padding: 10px;
		text-align: center;
	}

	.online {
		color: blue;
		font-size: 20rpx;
		margin-left: 20rpx;
	}

	.offline {
		color: #999999;
		font-size: 20rpx;
		margin-left: 20rpx;
		margin-top: 10rpx;
	}

	.title {
		color: #ffffff;
		font-size: 18px;
	}

	.device-status {
		display: flex;
		  justify-content: space-between; /* 左右两端对齐 */
		  align-items: center; /* 垂直居中 */
		  width: 100%; /* 确保占满宽度 */
		  padding: 20rpx; /* 调整内边距 */
		  box-sizing: border-box; /* 防止 padding 影响宽度 */
	}
	
	.categoryName {
		font-size: 32rpx;
		  color: #333;
		  white-space: nowrap; /* 防止设备名称换行 */
		  overflow: hidden; /* 超出部分隐藏 */
		  text-overflow: ellipsis; /* 显示省略号 */
		  flex: 1; /* 允许名称占据剩余空间 */
		  min-width: 0; /* 防止 flex 布局溢出 */
		  margin-right: 20rpx; /* 和状态标签保持间距 */
		
	}

	.device-state {
		 font-size: 26rpx;
		  padding: 4rpx 12rpx;
		  border-radius: 20rpx;
		  white-space: nowrap; /* 防止状态标签换行 */
		  flex-shrink: 0; /* 防止状态标签被压缩 */
	}

	.status-online {
		color: #07C160;
		background-color: rgba(7, 193, 96, 0.1);
	}

	.status-offline {
		color: #FA5151;
		background-color: rgba(250, 81, 81, 0.1);
	}

	.card {
		background-color: #ffffff;
		border-radius: 10px;
		margin-top: 10rpx;
		padding: 20px;
		width: 100%;
		justify-content: center;
		/* 水平居中 */
		margin-bottom: 20px;
		box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
	}

	.card-title {
		display: block;
		font-size: 16px;
		margin-bottom: 10px;
		text-align: center;
	}

	.grading {
		font-weight: 100;
		font-size: 45rpx;
		display: block;
		/* 确保text元素变成块级元素 */
		text-align: center;
		/* 文本居中 */
		width: 100%;
		/* 占据全部宽度 */
	}


	.bowl-icon {
		display: block;
		width: 110px;
		height: 60px;
		margin: 0 auto;

		/* 优化过渡效果 */
		transition:
			opacity 0.6s cubic-bezier(0.4, 0, 0.2, 1),
			filter 0.6s cubic-bezier(0.4, 0, 0.2, 1),
			transform 0.4s ease;
		transform-origin: center;
	}

	/* 非激活状态 */
	.bowl-icon:not(.active) {
		opacity: 0.7;
		filter: grayscale(30%) brightness(0.9);
	}

	/* 激活状态 */
	.bowl-icon.active {
		opacity: 1;
		filter: drop-shadow(0 0 15rpx rgba(59, 153, 252, 0.4));
		transform: scale(1.05);
		animation: pulse 3s infinite;
	}

	/* 添加状态切换时的中间态 */
	.bowl-icon.active-add,
	.bowl-icon.active-remove {
		transition:
			opacity 0.8s ease-out,
			filter 0.8s ease-out,
			transform 0.5s cubic-bezier(0.68, -0.55, 0.27, 1.55);
	}

	.bowl-icon.active {
		opacity: 1;
		filter: drop-shadow(0 0 15rpx rgba(59, 153, 252, 0.4));
		transform: scale(1.05);
		animation: pulse 3s infinite;
	}

	@keyframes pulse {
		0% {
			filter: drop-shadow(0 0 15rpx rgba(59, 153, 252, 0.4));
		}

		50% {
			filter: drop-shadow(0 0 25rpx rgba(59, 153, 252, 0.6));
		}

		100% {
			filter: drop-shadow(0 0 15rpx rgba(59, 153, 252, 0.4));
		}
	}

	.bowl-color {
		display: block;
		text-align: center;
		font-size: 26rpx;
		color: #666;
		margin: 15rpx 0;
		transition: all 0.3s ease;
	}


	/* 原有的 .button-group 样式 */
	.button-group {
		display: flex;
		justify-content: center;
		gap: 10rpx;
		/* 使用gap替代margin-right */
		flex-wrap: wrap;
		/* 允许换行 */
		max-width: 80%;
		/* 限制最大宽度 */
		margin: 0 auto;
	}


	/* 统一按钮样式 */
	.button-group button {
		flex: 1;
		/* height: 80rpx; */
		border-radius: 12rpx;
		font-size: 28rpx;
		font-weight: 500;
		display: flex;
		align-items: center;
		justify-content: center;
		transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
		border: none;
		outline: none;
		position: relative;
		overflow: hidden;
	}

	.button-group button:active {
		transform: scale(0.98);
	}

	/* 按钮水波纹效果 */
	.button-group button:after {
		content: "";
		position: absolute;
		top: 50%;
		left: 50%;
		width: 5rpx;
		height: 5rpx;
		background: rgba(255, 255, 255, 0.5);
		opacity: 0;
		border-radius: 100%;
		transform: scale(1, 1) translate(-50%);
		transform-origin: 50% 50%;
	}

	.button-group button:focus:not(:active)::after {
		animation: ripple 0.6s ease-out;
	}

	@keyframes ripple {
		0% {
			transform: scale(0, 0);
			opacity: 0.5;
		}

		100% {
			transform: scale(20, 20);
			opacity: 0;
		}
	}

	/* 各按钮特定样式 */
	.start-button {
		background-color: #3b99fc;
		color: white;
		box-shadow: 0 4rpx 12rpx rgba(59, 153, 252, 0.3);
	}

	.stop-button {
		background-color: #52c41a;
		color: white;
		box-shadow: 0 4rpx 12rpx rgba(82, 196, 26, 0.3);
	}

	.resume-button {
		background-color: #faad14;
		color: white;
		box-shadow: 0 4rpx 12rpx rgba(250, 173, 20, 0.3);
	}

	.cancel-button {
		background-color: #f5f5f5;
		color: #666;
		border: 1rpx solid #e8e8e8;
	}

	.stop-master-button {
		background-color: #ff4d4f;
		color: white;
		box-shadow: 0 4rpx 12rpx rgba(255, 77, 79, 0.3);
	}

	.color-item {
		display: flex;
		align-items: center;
		margin: 10px 0;
	}

	.color-indicator {
		width: 20px;
		height: 20px;
		border-radius: 50%;
		margin-right: 10px;
	}

	/* 过渡动画 */
	.fade-enter-active,
	.fade-leave-active {
		transition: opacity 0.5s ease;
	}

	.fade-enter,
	.fade-leave-to {
		opacity: 0;
	}

	.slide-fade-enter-active {
		transition: all 0.3s ease-out;
	}

	.slide-fade-leave-active {
		transition: all 0.3s cubic-bezier(1, 0.5, 0.8, 1);
	}

	.slide-fade-enter,
	.slide-fade-leave-to {
		transform: translateY(20rpx);
		opacity: 0;
	}

	/* 加载指示器 */
	.loading-overlay {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: rgba(255, 255, 255, 0.7);
		display: flex;
		justify-content: center;
		align-items: center;
		z-index: 999;
	}

	.loading-spinner {
		width: 80rpx;
		height: 80rpx;
		border: 6rpx solid #f3f3f3;
		border-top: 6rpx solid #3b99fc;
		border-radius: 50%;
		animation: spin 1s linear infinite;
	}

	@keyframes spin {
		0% {
			transform: rotate(0deg);
		}

		100% {
			transform: rotate(360deg);
		}
	}


	.status-text {
		display: inline-block;
		transition:
			color 0.6s cubic-bezier(0.25, 0.1, 0.25, 1),
			transform 0.4s ease;
	}

	.status-text.command-active {
		animation: gentleScale 2s infinite;
	}

	@keyframes gentleScale {
		0% {
			transform: scale(1);
		}

		50% {
			transform: scale(1.25);
			color: #5a4fcf;
			/* 稍亮的SlateBlue */
		}

		100% {
			transform: scale(1);
		}
	}

	.amount {
		margin-left: auto;
	}

	/* 进度条样式 */
	.progress-container {
		margin: 20rpx 0;
		display: flex;
		align-items: center;
		gap: 20rpx;
	}

	.progress-bar {
		flex: 1;
		height: 8px;
		background-color: #f0f0f0;
		border-radius: 4px;
		overflow: hidden;
	}

	.progress-fill {
		height: 100%;
		background-color: #3b99fc;
		transition: width 0.3s ease;
	}

	.progress-text {
		font-size: 12px;
		color: #666;
		min-width: 35px;
	}

	/* 库存预警样式 */
	.alert-section {
		margin-bottom: 15px;
	}

	.alert-title {
		font-size: 14px;
		font-weight: bold;
		color: #ff6b35;
		margin-bottom: 8px;
		display: block;
	}

	.alert-item {
		margin-bottom: 5px;
	}

	.alert-text {
		font-size: 12px;
		padding: 2px 6px;
		border-radius: 3px;
	}

	.alert-text.warning {
		background-color: #fff3cd;
		color: #856404;
	}

	.alert-text.critical {
		background-color: #f8d7da;
		color: #721c24;
	}

	.inventory-summary {
		display: flex;
		justify-content: space-between;
		padding-top: 10px;
		border-top: 1px solid #eee;
	}

	.summary-text {
		font-size: 12px;
		color: #666;
	}
</style>