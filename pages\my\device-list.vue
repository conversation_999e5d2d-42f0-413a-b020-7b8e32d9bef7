<template>
	<view class="container">
		<!-- 状态标签栏 -->
		<view class="status-tabs">
			<scroll-view scroll-x class="tab-scroll" :scroll-left="scrollLeft">
				<view class="tab-container">
					<view class="tab-item" v-for="tab in tabs" :key="tab.key" :class="{ active: activeTab === tab.key }"
						@click="setActiveTab(tab.key)" :id="'tab-' + tab.key">
						<text>{{ tab.label }}</text>
						<!-- <text class="count-badge">{{tab.count}}</text> -->
					</view>
				</view>
			</scroll-view>
			<view class="active-indicator" :style="indicatorStyle"></view>
		</view>

		<!-- 设备列表 -->
		<scroll-view class="device-list" scroll-y :refresher-enabled="true" :refresher-triggered="refreshing"
			@refresherrefresh="onRefresh" @scrolltolower="loadMore">
			<view v-if="filteredDevices.length === 0" class="empty-device">
				<image class="empty-image" src="/static/images/empty-state.png" mode="aspectFit"></image>
				<text class="empty-text">暂无设备</text>
				<!-- <view class="add-btn" @click="addDevice">添加设备</view> -->
			</view>

			<view v-for="(device, index) in filteredDevices" :key="device.id" class="device-item">
				<!-- @click="viewDeviceDetail(device)" -->
				<view class="device-icon">
					<image class="icon-image" :src="getDeviceIcon(device.type)"></image>
				</view>
				<view class="device-info">
					<view class="name-row">
						<text class="device-name">{{ device.name }}</text>
						<text class="device-status" :class="device.status">{{ getStatusText(device.status) }}</text>
					</view>
					<text class="device-id">设备ID: {{ device.id }}</text>
					<view class="device-meta">
						<text class="device-time" v-if="device.lastActive">
							<uni-icons type="calendar" size="14" color="#999"></uni-icons>
							{{ formatTime(device.lastActive) }}
						</text>
					</view>
				</view>
				<view class="device-actions" @click.stop v-if="device.status === 'online' && device.permission >= 2">
					<view class="used-btn"
						v-if="device.id === currentUseDevice && deviceStatus && connectionMethod === 'wifi'"
						@click.stop="disconnectDevice(device)">
						<text>断开</text>
						<!-- <uni-icons type="right" size="16" color="#fff"></uni-icons> -->
					</view>

					<view class="use-btn" @click.stop="useDevice(device)" v-else>
						<text>使用</text>
						<!-- <uni-icons type="right" size="16" color="#fff"></uni-icons> -->
					</view>
				</view>
				<view class="device-actions" @click.stop v-if="device.status === 'offline' && device.permission > 0">
					<view class="used-btn" @click.stop="unbind(device)">
						<text>解除绑定</text>
						<!-- <uni-icons type="right" size="16" color="#fff"></uni-icons> -->
					</view>
				</view>
			</view>

			<view class="load-more" v-if="loadingMore">
				<uni-icons type="spinner-cycle" size="20" color="#999" class="loading-icon"></uni-icons>
				<text>加载中...</text>
			</view>
			<view class="no-more" v-if="!hasMore && filteredDevices.length > 5">
				<text>没有更多设备了</text>
			</view>
		</scroll-view>

		<!-- 添加设备浮动按钮 -->
		<!-- <view class="fab" @click="addDevice" v-if="filteredDevices.length > 0">
      <uni-icons type="plus" size="26" color="#fff"></uni-icons>
    </view> -->
	</view>
</template>

<script>
	import {
		apiService,
		post
	} from '../../utils/request';
	import {
		formatTime
	} from '@/utils/time_util.js';
	import {
		startPollingWifi,
		stopPollingWifi,
		disconnectSSE
	} from '@/store/wifi.js'

	export default {
		data() {
			return {
				deviceStatus: false,
				connectionMethod: '',
				currentUseDevice: '', // 当前正在使用的设备
				isConnecting: false, // 是否正在连接中
				lastDisconnectTime: 0, // 最后断开连接的时间
				debounceDelay: 2000, // 防抖延迟时间（毫秒）
				tabs: [{
						key: 'offline',
						label: '离线',
						count: 1
					},
					{
						key: 'online',
						label: '在线',
						count: 4
					},
					{
						key: 'abnormal',
						label: '异常',
						count: 1
					}
				],
				activeTab: 'online',
				showBack: false,
				scrollLeft: 0,
				indicatorStyle: {
					width: '60px',
					transform: 'translateX(0px)'
				},
				refreshing: false,
				loadingMore: false,
				hasMore: true,
				pageSize: 10,
				currentPage: 1,
				devices: [
					// {
					//   id: 'IOT-KT-001',
					//   name: '客厅空调',
					//   status: 'online',
					//   lastActive: new Date().toISOString(),
					//   type: 'air_conditioner',
					//   // brand: '美的',
					//   // model: 'KFR-35GW',
					//   // temperature: 26,
					//   // mode: 'cool',
					//   // powerConsumption: '1.2kW/h'
					// },
					// {
					//   id: 'IOT-SD-002',
					//   name: '电动窗帘',
					//   status: 'online',
					//   lastActive: new Date(Date.now() - 3600000).toISOString(),
					//   type: 'curtain',
					//   brand: '小米',
					//   model: 'MJZNCL01LM',
					//   position: '80%',
					//   battery: 65
					// },
					// {
					//   id: 'IOT-LT-003',
					//   name: '餐厅吊灯',
					//   status: 'offline',
					//   lastActive: new Date(Date.now() - 86400000).toISOString(),
					//   type: 'light',
					//   brand: '飞利浦',
					//   model: 'Hue White',
					//   brightness: 0,
					//   colorTemp: 4000
					// },
					// {
					//   id: 'IOT-SF-004',
					//   name: '智能插座',
					//   status: 'abnormal',
					//   lastActive: new Date(Date.now() - 1800000).toISOString(),
					//   type: 'outlet',
					//   brand: '涂鸦',
					//   model: 'SR01',
					//   power: '220V',
					//   current: '0A',
					//   alert: '电流异常'
					// },
					// {
					//   id: 'IOT-CM-005',
					//   name: '门口摄像头',
					//   status: 'online',
					//   lastActive: new Date().toISOString(),
					//   type: 'camera',
					//   brand: '萤石',
					//   model: 'C6CN',
					//   resolution: '1080p',
					//   isRecording: true
					// },
					// {
					//   id: 'IOT-TH-006',
					//   name: '温湿度计',
					//   status: 'online',
					//   lastActive: new Date().toISOString(),
					//   type: 'sensor',
					//   brand: '米家',
					//   model: 'LYWSD03MMC',
					//   temperature: 24.5,
					//   humidity: 56,
					//   battery: 85
					// }
				],
				offlineCount: 0,
				onlineCount: 0,
				abnormalCount: 0,
				// 监听一下设备的连接状态等信息


			};
		},
		computed: {
			filteredDevices() {
				return this.devices.filter(device => device.status === this.activeTab);
			}
		},
		onLoad(options) {
			this.statusWatcher = setInterval(() => {
				const app = getApp();
				const deviceCode = app.globalData.connectedDeviceId // 连接的设备id
				const deviceStatus = app.globalData.deviceStatus // 连接的设备状态
				const connectionMethod = app.globalData.connectionMethod // 连接的方式

				if (deviceCode !== this.currentUseDevice ||
					deviceStatus != this.deviceStatus ||
					connectionMethod != this.connectionMethod
				) {
					this.currentUseDevice = deviceCode
					this.deviceStatus = deviceStatus
					this.connectionMethod = connectionMethod
					this.$forceUpdate();
				}

			}, 500);

			apiService.device.getDevicePermissionlist()
				.then(res => {
					this.devices = res.data
				})
			this.calculateCounts();
			this.loadDevices();
			this.showBack = !!options.from;

		},

		onShow() {
			// 页面显示时立即同步状态
			this.syncDeviceStatus();
		},

		onReady() {
			this.updateIndicatorPosition();
		},
		onUnload() {
			// 清理状态监听器
			if (this.statusWatcher) {
				clearInterval(this.statusWatcher);
				this.statusWatcher = null;
			}
		},
		// 下拉刷新
		onPullDownRefresh() {
			console.log('下拉刷新')
			this.loadDevice()
			// setTimeout(() => {
			//   uni.stopPullDownRefresh()
			// }, 1000)
		},
		methods: {
			async unbind(device) {
				console.log("需要解绑的设备", device);

				// 1.显示确认对话框
				const confirmResult = await new Promise((resolve) => {
					uni.showModal({
						title: '确认解除绑定码',
						content: `确定要解除绑定设备 ${device.name} 吗？`,
						success: (res) => {
							resolve(res.confirm);
						}
					});
				});

				if (!confirmResult) {
					return;
				}

				// 2.调用后端API解除绑定设备

				const response = await apiService.device.unbindDevice(device.id);

				if (response.code >= 200 && response.code <= 300) {
				    uni.hideLoading();
				    uni.showToast({
				        title: '设备已解除绑定',
				        icon: 'success',
				        duration: 2000
				    });
					this.loadDevice()
				} else {
				    uni.showToast({
				        title: '设备解除绑定失败,请重试',
				        icon: 'none',  // 注意：uni.showToast 没有 'fail' 图标，应该用 'none'
				        duration: 2000
				    });
				}



			},
			async disconnectDevice(device) {
				console.log("需要断开的设备", device);

				try {
					// 显示确认对话框
					const confirmResult = await new Promise((resolve) => {
						uni.showModal({
							title: '确认断开',
							content: `确定要断开设备 ${device.name} 吗？`,
							success: (res) => {
								resolve(res.confirm);
							}
						});
					});

					if (!confirmResult) {
						return;
					}

					// 显示加载提示
					uni.showLoading({
						title: '断开中...',
						mask: true
					});


					// 3. 调用后端API断开设备
					const response = await post(`/api/device/monitor/disconnect/${device.id}`);

					// 1. 停止WiFi轮询
					stopPollingWifi();

					// 2. 断开SSE连接
					disconnectSSE();

					// 4. 更新全局状态
					const app = getApp();
					app.globalData.connectedDeviceId = '';
					app.globalData.deviceStatus = false;
					app.globalData.connectionMethod = '';
					app.globalData.prepareToConnectDeviceCode = '';

					// 5. 停止设备服务
					if (app.stopDeviceServices) {
						app.stopDeviceServices();
					}

					// 6. 更新本地状态
					this.currentUseDevice = '';
					this.deviceStatus = false;
					this.connectionMethod = '';

					// 7. 记录断开时间（用于防抖）
					this.lastDisconnectTime = Date.now();

					uni.hideLoading();
					uni.showToast({
						title: '设备已断开',
						icon: 'success',
						duration: 2000
					});

					console.log('🔌 设备断开成功:', device.id);

				} catch (error) {
					console.error('❌ 断开设备失败:', error);
					uni.hideLoading();
					uni.showToast({
						title: '断开失败',
						icon: 'error',
						duration: 2000
					});
				}
			},
			getDeviceIcon(type) {
				const icons = {
					'air_conditioner': '/static/icons/air-conditioner.png',
					'curtain': '/static/icons/curtain.png',
					'light': '/static/icons/light.png',
					'outlet': '/static/icons/outlet.png',
					'camera': '/static/icons/camera.png',
					'sensor': '/static/icons/sensor.png'
				};
				return icons[type] || '/static/icons/device.png';
			},
			async useDevice(device) {
				const app = getApp();
				try {
					if (app.isBluetoothConnection() && app.globalData.deviceStatus) {
						uni.showToast({
							title: '设备已通过蓝牙连接,请先断开蓝牙连接',
							icon: 'none',
							duration: 2000
						})
						return;
					}

					// 防止重复点击
					if (this.isConnecting) {
						console.log('正在连接中，请勿重复点击');
						return;
					}

					// 防抖检查：如果刚刚断开连接，需要等待一段时间
					const timeSinceDisconnect = Date.now() - this.lastDisconnectTime;
					if (this.lastDisconnectTime > 0 && timeSinceDisconnect < this.debounceDelay) {
						const remainingTime = Math.ceil((this.debounceDelay - timeSinceDisconnect) / 1000);
						uni.showToast({
							title: `请等待 ${remainingTime} 秒后再试`,
							icon: 'none',
							duration: 2000
						});
						console.log(`防抖检查：距离上次断开连接仅 ${timeSinceDisconnect}ms，需要等待 ${remainingTime} 秒`);
						return;
					}

					this.isConnecting = true;

					// 显示加载提示
					uni.showLoading({
						title: '连接中...',
						mask: true
					});

					// 更新全局状态（先设置准备连接状态）
					app.globalData.connectedDeviceId = device.id;
					app.globalData.prepareToConnectDeviceCode = device.id;
					app.globalData.connectionMethod = 'wifi';
					// 注意：这里不立即设置 deviceStatus = true，等连接成功后再设置

					// 启动设备服务
					if (app.startDeviceServices) {
						app.startDeviceServices(device.id);
					}

					// 更新本地状态（准备连接状态）
					this.currentUseDevice = device.id;
					this.connectionMethod = 'wifi';
					// 注意：这里不立即设置 deviceStatus = true

					// 启动WiFi轮询连接（异步）
					startPollingWifi(device.id);

					// 等待连接建立，设置超时
					let connectionEstablished = false;
					const maxWaitTime = 15000; // 15秒超时
					const checkInterval = 500; // 每500ms检查一次
					let waitTime = 0;

					const checkConnection = () => {
						waitTime += checkInterval;

						// 根据连接方式使用不同的状态检测逻辑
						let isDeviceOnline = false;
						let isDeviceOffline = false;

						if (app.globalData.connectionMethod === 'wifi') {
							// WiFi连接：检查设备状态详情和全局状态
							const deviceStatusDetail = app.globalData.deviceStatusDetail;
							const globalDeviceStatus = app.globalData.deviceStatus;

							console.log('WiFi连接状态检查:', {
								deviceStatusDetail: deviceStatusDetail,
								globalDeviceStatus: globalDeviceStatus,
								isOnline: deviceStatusDetail?.isOnline
							});

							// WiFi连接认为在线的条件：设备状态详情显示在线 或 全局状态为true
							isDeviceOnline = (deviceStatusDetail?.isOnline === true) || (globalDeviceStatus ===
								true);
							// WiFi连接认为离线的条件：明确收到离线状态
							isDeviceOffline = (globalDeviceStatus === false) && (deviceStatusDetail?.isOnline ===
								false);
						} else {
							// 蓝牙连接：使用原有逻辑
							isDeviceOnline = app.globalData.deviceStatus === true;
							isDeviceOffline = app.globalData.deviceStatus === false;
						}

						if (isDeviceOnline) {
							// 连接成功
							connectionEstablished = true;
							this.deviceStatus = true;
							this.isConnecting = false;

							uni.hideLoading();
							uni.showToast({
								title: '连接成功',
								icon: 'success',
								duration: 2000
							});
							console.log('🔗 设备连接成功:', device.id);

						} else if (isDeviceOffline) {
							// 设备离线
							this.isConnecting = false;
							uni.hideLoading();
							uni.showToast({
								title: '设备离线，请检查设备状态',
								icon: 'none',
								duration: 3000
							});
							console.warn('设备离线:', device.id);

							// 重置状态
							this.resetConnectionState();

						} else if (waitTime >= maxWaitTime) {
							// 超时处理
							this.isConnecting = false;
							uni.hideLoading();
							uni.showToast({
								title: '连接超时，请检查设备状态',
								icon: 'none',
								duration: 3000
							});
							console.warn('连接超时:', device.id);

							// 重置状态
							this.resetConnectionState();

						} else {
							// 继续等待
							setTimeout(checkConnection, checkInterval);
						}
					};

					// 开始检查连接状态
					setTimeout(checkConnection, 1000);

				} catch (error) {
					console.error('❌ 连接设备失败:', error);
					this.isConnecting = false;
					uni.hideLoading();
					uni.showToast({
						title: '连接失败',
						icon: 'error',
						duration: 2000
					});

					// 重置状态
					this.resetConnectionState();
				}
			},

			// 重置连接状态的方法
			resetConnectionState() {
				this.currentUseDevice = '';
				this.deviceStatus = false;
				this.connectionMethod = '';
				this.isConnecting = false;

				const app = getApp();
				app.globalData.deviceStatus = false;
				app.globalData.connectedDeviceId = '';
				app.globalData.connectionMethod = '';
			},

			// 同步设备状态的方法
			syncDeviceStatus() {
				const app = getApp();
				const deviceCode = app.globalData.connectedDeviceId;
				const deviceStatus = app.globalData.deviceStatus;
				const connectionMethod = app.globalData.connectionMethod;

				console.log('同步设备状态:', {
					deviceCode,
					deviceStatus,
					connectionMethod,
					currentLocal: {
						currentUseDevice: this.currentUseDevice,
						deviceStatus: this.deviceStatus,
						connectionMethod: this.connectionMethod
					}
				});

				// 如果状态有变化，立即更新
				if (deviceCode !== this.currentUseDevice ||
					deviceStatus !== this.deviceStatus ||
					connectionMethod !== this.connectionMethod) {

					this.currentUseDevice = deviceCode;
					this.deviceStatus = deviceStatus;
					this.connectionMethod = connectionMethod;
					this.$forceUpdate();

					console.log('设备状态已同步更新');
				}
			},

			formatTime,
			goBack() {
				uni.navigateBack();
			},
			loadDevice() {
				apiService.device.getDevicePermissionlist()
					.then(res => {
						this.devices = res.data
						this.calculateCounts()
					})

				this.calculateCounts();
				this.loadDevices();
			},
			setActiveTab(tab) {
				this.activeTab = tab;
				this.currentPage = 1;
				this.hasMore = true;
				this.$nextTick(() => {
					this.updateIndicatorPosition();
				});
			},
			updateIndicatorPosition() {
				const query = uni.createSelectorQuery().in(this);
				query.select(`#tab-${this.activeTab}`).boundingClientRect();
				query.select('.tab-container').boundingClientRect();
				query.exec(res => {
					if (res[0] && res[1]) {
						const tab = res[0];
						const container = res[1];
						const left = tab.left - container.left;
						this.indicatorStyle = {
							width: `${tab.width}px`,
							transform: `translateX(${left}px)`,
							transition: 'transform 0.3s ease'
						};

						// 计算需要滚动的距离，确保当前tab可见
						const scrollLeft = left - (container.width - tab.width) / 2;
						this.scrollLeft = Math.max(0, scrollLeft);
					}
				});
			},
			async loadDevices() {
				try {
					this.refreshing = true;
					// 模拟API调用
					await new Promise(resolve => setTimeout(resolve, 800));

					// 实际项目中替换为真实API调用
					// const res = await uni.request({
					//   url: '/api/devices',
					//   method: 'GET',
					//   data: {
					//     page: this.currentPage,
					//     size: this.pageSize,
					//     status: this.activeTab
					//   }
					// });

					// this.devices = res.data.data || [];
					this.calculateCounts();
				} catch (e) {
					uni.showToast({
						title: '加载失败',
						icon: 'none'
					});
				} finally {
					this.refreshing = false;
					this.loadingMore = false;
					uni.stopPullDownRefresh(); // 停止下拉刷新动画
				}
			},
			onRefresh() {
				this.loadDevice()
				this.refreshing = true;
				this.currentPage = 1;
				this.hasMore = true;
				this.loadDevices();
			},
			loadMore() {
				if (this.loadingMore || !this.hasMore) return;

				this.loadingMore = true;
				this.currentPage += 1;
				this.loadDevices();
			},
			calculateCounts() {
				this.offlineCount = this.devices.filter(device => device.status === 'offline').length;
				this.onlineCount = this.devices.filter(device => device.status === 'online').length;
				this.abnormalCount = this.devices.filter(device => device.status === 'abnormal').length;
			},
			getStatusText(status) {
				const statusMap = {
					'offline': '离线',
					'online': '在线',
					'abnormal': '异常'
				};
				return statusMap[status] || status;
			},
			addDevice() {
				uni.navigateTo({
					url: '/pages/device/add'
				});
			},
			// viewDeviceDetail(device) {
			// 	uni.navigateTo({
			// 		url: `/pages/device/detail?id=${device.id}`
			// 	});
			// },
			updateIndicatorPosition() {
				const query = uni.createSelectorQuery().in(this);
				query.select(`#tab-${this.activeTab}`).boundingClientRect();
				query.select('.tab-container').boundingClientRect();
				query.exec(res => {
					if (res[0] && res[1]) {
						const tab = res[0];
						const container = res[1];
						const left = tab.left - container.left;

						this.indicatorStyle = {
							width: `${tab.width}px`,
							transform: `translateX(${left}px)`,
							transition: 'transform 0.3s ease'
						};

						// 计算需要滚动的距离，确保当前tab可见
						const scrollLeft = left - (container.width - tab.width) / 2;
						this.scrollLeft = Math.max(0, scrollLeft);
					}
				});
			},
			setActiveTab(tab) {
				this.activeTab = tab;
				this.$nextTick(() => {
					this.updateIndicatorPosition();
				});
			},
		}
	};
</script>

<style lang="scss">
	$primary-color: #4285f4;
	$success-color: #4caf50;
	$warning-color: #ff9800;
	$danger-color: #f44336;
	$text-color: #333;
	$light-text: #999;
	$border-color: #eee;
	$bg-color: #f8f8f8;

	.container {
		display: flex;
		flex-direction: column;
		height: 100vh;
		background-color: $bg-color;
	}

	.header {
		background-color: $primary-color;
		padding: 10px 0;
		position: sticky;
		top: 0;
		z-index: 100;
		box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

		.header-content {
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 0 15px;
			height: 44px;
		}

		.back-btn {
			width: 30px;
			display: flex;
			align-items: center;
			justify-content: flex-start;
		}

		.title {
			font-size: 18px;
			font-weight: 500;
			color: #fff;
			flex: 1;
			text-align: center;
			margin: 0 10px;
		}

		.header-icons {
			display: flex;
			align-items: center;
			gap: 15px;
			width: 30px;
			justify-content: flex-end;
		}
	}

	.status-tabs {
		background-color: $primary-color;
		position: sticky;
		z-index: 90;

		.tab-scroll {
			white-space: nowrap;
			width: 100%;
			height: 50px;

			.tab-container {
				display: flex;
				height: 100%;
				padding: 0 15px;
			}
		}

		.tab-item {
			flex: 1;
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			padding: 0 10px;
			color: rgba(255, 255, 255, 0.8);
			font-size: 15px;
			position: relative;
			height: 100%;
			min-width: 80px; // 设置最小宽度

			&.active {
				color: #fff;
				font-weight: bold;
			}

			.count-badge {
				font-size: 12px;
				background-color: rgba(255, 255, 255, 0.2);
				color: #fff;
				border-radius: 10px;
				padding: 0 6px;
				height: 16px;
				line-height: 16px;
				margin-left: 4px;
			}
		}

		.active-indicator {
			position: absolute;
			bottom: 0;
			left: 0;
			height: 3px;
			background-color: #fff;
			border-radius: 3px 3px 0 0;
			transition: all 0.3s ease;
		}
	}

	.device-list {
		flex: 1;
		height: calc(100vh - 114px);
		padding: 10px 15px;
		box-sizing: border-box;
	}

	.empty-device {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding: 60px 0;

		.empty-image {
			width: 150px;
			height: 100px;
			margin-bottom: 20px;
			opacity: 0.6;
		}

		.empty-text {
			font-size: 15px;
			color: $light-text;
			margin-bottom: 25px;
		}

		.add-btn {
			padding: 8px 25px;
			background-color: $primary-color;
			color: #fff;
			border-radius: 20px;
			font-size: 14px;
			box-shadow: 0 4px 12px rgba($primary-color, 0.2);
		}
	}

	.device-item {
		background-color: #fff;
		border-radius: 12px;
		padding: 15px;
		display: flex;
		align-items: center;
		margin-bottom: 12px;
		box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);
		transition: all 0.2s;
		position: relative;
		overflow: hidden;

		&:active {
			transform: scale(0.98);
			opacity: 0.9;
			background-color: #f9f9f9;
		}

		&::after {
			content: '';
			position: absolute;
			left: 0;
			top: 0;
			bottom: 0;
			width: 4px;
			background-color: $primary-color;
			border-radius: 4px 0 0 4px;
		}

		&.offline::after {
			background-color: $light-text;
		}

		&.abnormal::after {
			background-color: $danger-color;
		}
	}

	.device-icon {
		width: 50px;
		height: 50px;
		border-radius: 10px;
		overflow: hidden;
		margin-right: 12px;
		flex-shrink: 0;
		display: flex;
		align-items: center;
		justify-content: center;
		background-color: rgba($primary-color, 0.1);

		.icon-image {
			width: 30px;
			height: 30px;
		}
	}

	.device-info {
		flex: 1;
		overflow: hidden;

		.name-row {
			display: flex;
			align-items: center;
			margin-bottom: 4px;
		}

		.device-name {
			font-size: 16px;
			color: $text-color;
			font-weight: 500;
			white-space: nowrap;
			overflow: hidden;
			text-overflow: ellipsis;
			margin-right: 8px;
			flex: 1;
		}

		.device-id {
			font-size: 12px;
			color: $light-text;
			margin-bottom: 6px;
			display: block;
		}

		.device-meta {
			display: flex;
			align-items: center;
			gap: 10px;

			.device-time {
				font-size: 12px;
				color: $light-text;
				display: flex;
				align-items: center;
			}
		}
	}

	.device-status {
		font-size: 12px;
		padding: 2px 8px;
		border-radius: 10px;
		display: inline-block;
		white-space: nowrap;

		&.online {
			background-color: rgba($success-color, 0.1);
			color: $success-color;
		}

		&.offline {
			background-color: rgba($light-text, 0.1);
			color: $light-text;
		}

		&.abnormal {
			background-color: rgba($danger-color, 0.1);
			color: $danger-color;
		}
	}

	.device-actions {
		margin-left: 10px;

		.use-btn {
			display: flex;
			align-items: center;
			padding: 5px 12px;
			background-color: $primary-color;
			color: white;
			border-radius: 15px;
			font-size: 13px;
			white-space: nowrap;

			text {
				margin-right: 3px;
			}
		}

		.used-btn {
			display: flex;
			align-items: center;
			padding: 5px 12px;
			background-color: $danger-color;
			color: white;
			border-radius: 15px;
			font-size: 13px;
			white-space: nowrap;

			text {
				margin-right: 3px;
			}
		}
	}

	.load-more,
	.no-more {
		display: flex;
		justify-content: center;
		align-items: center;
		padding: 15px 0;
		font-size: 13px;
		color: $light-text;

		.loading-icon {
			animation: rotate 1s linear infinite;
			margin-right: 5px;
		}
	}

	.no-more {
		padding: 20px 0;
	}

	.fab {
		position: fixed;
		right: 20px;
		bottom: 80px;
		width: 56px;
		height: 56px;
		background-color: $primary-color;
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
		box-shadow: 0 4px 12px rgba($primary-color, 0.3);
		z-index: 100;
		transition: all 0.2s;

		&:active {
			transform: scale(0.95);
			opacity: 0.9;
		}
	}

	@keyframes rotate {
		from {
			transform: rotate(0deg);
		}

		to {
			transform: rotate(360deg);
		}
	}
</style>