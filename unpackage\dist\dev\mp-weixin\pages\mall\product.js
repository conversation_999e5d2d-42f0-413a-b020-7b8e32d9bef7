"use strict";
const common_vendor = require("../../common/vendor.js");
const ProductIndex = () => "./product/index2.js";
const _sfc_main = {
  components: {
    ProductIndex
  },
  data() {
    return {
      productId: null
    };
  },
  onLoad(options) {
    if (options.id) {
      this.productId = options.id;
    }
  }
};
if (!Array) {
  const _component_product_index = common_vendor.resolveComponent("product-index");
  _component_product_index();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.p({
      ["product-id"]: $data.productId
    })
  };
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/mall/product.js.map
