# 地理位置服务配置指南

## 概述

本项目集成了真实的地理位置服务，支持自动获取用户位置并转换为可读的地址信息。支持多个地图服务提供商，包括腾讯地图、高德地图等。

## 功能特性

- ✅ 自动获取用户GPS位置
- ✅ 逆地理编码（坐标转地址）
- ✅ 多地图服务支持（腾讯地图、高德地图）
- ✅ 位置缓存机制
- ✅ 权限管理
- ✅ 错误处理和降级方案

## 配置步骤

### 1. 申请地图API密钥

#### 腾讯地图
1. 访问 [腾讯位置服务](https://lbs.qq.com/)
2. 注册账号并创建应用
3. 获取API密钥
4. 开通"逆地理编码"服务

#### 高德地图
1. 访问 [高德开放平台](https://lbs.amap.com/)
2. 注册账号并创建应用
3. 获取API密钥
4. 开通"逆地理编码"服务

### 2. 配置API密钥

编辑 `config/map.js` 文件：

```javascript
export const mapConfig = {
  // 腾讯地图配置
  tencent: {
    key: 'YOUR_ACTUAL_TENCENT_KEY', // 替换为您的腾讯地图API key
    geocoderUrl: 'https://apis.map.qq.com/ws/geocoder/v1/',
    enabled: true // 启用腾讯地图服务
  },

  // 高德地图配置
  amap: {
    key: 'YOUR_ACTUAL_AMAP_KEY', // 替换为您的高德地图API key
    geocoderUrl: 'https://restapi.amap.com/v3/geocode/regeo',
    enabled: true // 启用高德地图服务
  }
};
```

### 3. 小程序权限配置

确保 `manifest.json` 中已配置位置权限：

```json
{
  "mp-weixin": {
    "permission": {
      "scope.userLocation": {
        "desc": "您的位置信息将用于提供更好的服务"
      }
    },
    "requiredPrivateInfos": ["getLocation"]
  }
}
```

## 使用方法

### 在页面中使用

```javascript
import locationService from '@/utils/location.js';

export default {
  data() {
    return {
      currentLocation: '正在获取位置...'
    };
  },

  async onLoad() {
    await this.getCurrentLocation();
  },

  methods: {
    async getCurrentLocation() {
      try {
        const locationData = await locationService.getCurrentLocation();
        this.currentLocation = locationData.address;
        console.log('位置信息:', locationData);
      } catch (error) {
        console.error('获取位置失败:', error);
        this.currentLocation = '定位失败，点击重试';
      }
    }
  }
};
```

### API 说明

#### locationService.getCurrentLocation()
获取完整的位置信息，包括坐标和地址。

**返回值：**
```javascript
{
  latitude: 39.9042,      // 纬度
  longitude: 116.4074,    // 经度
  address: "北京市朝阳区", // 格式化地址
  timestamp: 1640995200000 // 时间戳
}
```

#### locationService.reverseGeocode(lat, lng)
将坐标转换为地址。

**参数：**
- `lat`: 纬度
- `lng`: 经度

**返回值：** 格式化的地址字符串

## 注意事项

### 1. API配额限制
- 腾讯地图：免费版每日10万次调用
- 高德地图：免费版每日30万次调用

### 2. 坐标系统
- 小程序获取的坐标为GCJ-02坐标系
- 腾讯地图使用GCJ-02坐标系
- 高德地图使用GCJ-02坐标系

### 3. 缓存机制
- 相同位置的地址会被缓存30分钟
- 可以调用 `locationService.clearCache()` 清除缓存

### 4. 错误处理
- 如果地图服务不可用，会返回坐标信息
- 如果用户拒绝位置权限，会引导用户到设置页面

## 测试

### 1. 在开发工具中测试
- 微信开发者工具支持位置模拟
- 可以在"工具" -> "位置模拟"中设置测试位置

### 2. 真机测试
- 确保手机GPS已开启
- 确保微信已获得位置权限
- 在室外环境测试效果更佳

## 故障排除

### 1. 位置获取失败
- 检查手机GPS是否开启
- 检查微信位置权限
- 检查网络连接

### 2. 地址解析失败
- 检查API密钥是否正确
- 检查API服务是否开通
- 检查网络连接

### 3. 权限被拒绝
- 引导用户到设置页面开启权限
- 提供手动输入地址的备选方案

## 扩展功能

### 1. 添加新的地图服务
在 `utils/location.js` 中添加新的逆地理编码方法，并在配置文件中添加相应配置。

### 2. 自定义地址格式
修改 `formatAddress` 方法来自定义地址显示格式。

### 3. 位置历史记录
可以扩展服务来保存用户的位置历史记录。
