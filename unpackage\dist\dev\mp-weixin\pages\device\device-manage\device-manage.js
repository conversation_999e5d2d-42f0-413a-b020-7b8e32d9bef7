"use strict";
const common_vendor = require("../../../common/vendor.js");
const utils_request = require("../../../utils/request.js");
const ScaleCalibration = () => "../../../components/ScaleCalibration.js";
const _sfc_main = {
  components: {
    ScaleCalibration
  },
  data() {
    return {
      // 电子秤相关操作
      scaleWeight: 0,
      tabs: ["操作设备", "设备信息", "系统设置"],
      activeTab: 0,
      deviceCode: "",
      deviceStatus: {
        isOnline: false,
        weight: 0
      },
      deviceInfo: {
        deviceNo: "",
        deviceId: "",
        account: "<EMAIL>",
        model: "AISRD-X1",
        store: "芭佰邑旗舰店",
        expireDate: "2025-12-31",
        firmwareVersion: "v1.2.3"
      },
      colorList: [
        {
          name: "0/11灰色/Grey",
          color_code: "0x30",
          hex: "#8B8B8B",
          value: 0
        },
        {
          name: "0/22绿色/Green",
          color_code: "0x31",
          hex: "#2E7D32",
          value: 0
        },
        {
          name: "0/33黄色/yellow",
          color_code: "0x32",
          hex: "#FFD700",
          value: 0
        },
        {
          name: "0/43橙色/Orange",
          color_code: "0x33",
          hex: "#FFA726",
          value: 0
        },
        {
          name: "0/45红色/Red",
          color_code: "0x34",
          hex: "#D32F2F",
          value: 0
        },
        {
          name: "0/66紫色/Purple",
          color_code: "0x35",
          hex: "#8E24AA",
          value: 0
        },
        {
          name: "0/77棕色/Brown",
          color_code: "0x36",
          hex: "#6D4C41",
          value: 0
        },
        {
          name: "0/88蓝色/Blue",
          color_code: "0x37",
          hex: "#1565C0",
          value: 0
        },
        {
          name: "2/0黑色/Black",
          color_code: "0x38",
          hex: "#000000",
          value: 0
        },
        {
          name: "0/00淡化剂/Faded",
          color_code: "0x39",
          hex: "#FAFAFA",
          value: 0
        },
        {
          name: "12/11极浅灰金色/Bleached",
          color_code: "0x40",
          hex: "#E0E0E0",
          value: 0
        },
        {
          name: "12/16极浅紫金/Silver",
          color_code: "0x41",
          hex: "#E1BEE7",
          value: 0
        },
        {
          name: "浅棕色/Gold",
          color_code: "0x42",
          hex: "#6D4C41",
          value: 0
        },
        {
          name: "深灰亚麻色/Linen",
          color_code: "0x43",
          hex: "#B0BEC5",
          value: 0
        },
        {
          name: "双氧乳3%",
          color_code: "0x0A",
          hex: "#FAFAFA",
          value: 0
        },
        {
          name: "双氧乳12%",
          color_code: "0x0B",
          hex: "#FAFAFA",
          value: 0
        }
      ],
      selectedIndex: null,
      selectedColorName: "",
      selectedColorHex: "",
      selectedColorPosition: "",
      unit: "g",
      remainingQuantity: 0,
      popupType: "info",
      popupTitle: "",
      popupContent: ""
    };
  },
  onLoad() {
    const app = getApp();
    this.deviceCode = app.globalData.connectedDeviceId;
    this.deviceStatus.isOnline = app.globalData.deviceStatus;
    if (this.deviceCode && this.deviceStatus.isOnline) {
      this.initDeviceInfo();
    }
  },
  methods: {
    showCalibration() {
      this.$refs.scaleCalibration.open();
    },
    onCalibrationComplete(data) {
      common_vendor.index.__f__("log", "at pages/device/device-manage/device-manage.vue:350", "校准完成", data);
    },
    onCalibrationClose() {
      common_vendor.index.__f__("log", "at pages/device/device-manage/device-manage.vue:357", "校准弹窗关闭");
    },
    async initDeviceInfo() {
      try {
        const response = await utils_request.apiService.device.getDeviceInfo(this.deviceCode);
        if (response.code === 200) {
          this.deviceInfo = {
            ...this.deviceInfo,
            ...response.data
          };
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/device/device-manage/device-manage.vue:369", "获取设备信息失败:", error);
      }
    },
    getDyePercentage(item) {
      return item.value || 0;
    },
    async selectColor(color, index) {
      this.selectedIndex = index;
      this.selectedColorName = color.name;
      this.selectedColorHex = color.hex;
      this.selectedColorPosition = color.color_code;
      await this.checkColorRemaining();
    },
    async checkColorRemaining() {
      if (this.selectedIndex === null)
        return;
      try {
        common_vendor.index.showLoading({
          title: "检测中...",
          mask: true
        });
        const selectedColor = this.colorList[this.selectedIndex];
        const response = await utils_request.apiService.mall.dyeConsumption.checkRemaining({
          deviceCode: this.deviceCode,
          colorCode: selectedColor.color_code
        });
        if (response.code === 200) {
          this.remainingQuantity = response.data.remainingQuantity || 0;
          this.unit = response.data.unit || "g";
          this.popupType = this.remainingQuantity < 20 ? "error" : "success";
          this.popupTitle = "染膏余量检测";
          this.popupContent = `当前颜色余量: ${response.data.proportion}${this.unit}`;
          this.$refs.popup.open();
        } else {
          throw new Error(response.message || "查询余量失败");
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/device/device-manage/device-manage.vue:414", "检测余量失败:", error);
        common_vendor.index.showToast({
          title: error.message || "检测余量失败",
          icon: "none",
          duration: 2e3
        });
      } finally {
        common_vendor.index.hideLoading();
      }
    },
    switchTab(index) {
      this.activeTab = index;
    },
    clearWeight() {
      this.deviceStatus.weight = 0;
      common_vendor.index.showToast({
        title: "重量已清零",
        icon: "success",
        duration: 1500
      });
    },
    showSetting(config) {
      switch (config.type) {
        case "dischargeSpeed":
          this.showDischargeSpeedSetting(config);
          break;
        case "ambientLight":
          this.showAmbientLightSetting(config);
          break;
        case "deviceId":
          this.getDeviceId(config);
          break;
        case "scaleCalibration":
          this.$refs.scaleCalibration.open(config);
          break;
        case "motorTest":
          this.testMotor(config);
          break;
        case "clearCalibration":
          this.clearCalibrationData(config);
          break;
        case "wifi":
          this.showWifiSetting(config);
          break;
        case "apn":
          this.showApnSetting(config);
          break;
        default:
          common_vendor.index.__f__("warn", "at pages/device/device-manage/device-manage.vue:466", "未知的设置类型:", config.type);
      }
    },
    showScaleCalibration(config) {
      this.$refs.scaleCalibration.open(config);
    },
    // 示例方法 - 获取设备ID
    getDeviceId(config) {
      common_vendor.index.showLoading({ title: "获取中..." });
      getDeviceIdApi().then((res) => {
        common_vendor.index.hideLoading();
        common_vendor.index.showModal({
          title: config.title,
          content: `设备ID: ${res.deviceId}`,
          showCancel: false
        });
      }).catch((err) => {
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({ title: "获取失败", icon: "none" });
      });
    },
    disconnectDevice() {
      common_vendor.index.showModal({
        title: "确认断开",
        content: "确定要断开设备连接吗？",
        success: (res) => {
          if (res.confirm) {
            common_vendor.index.showToast({
              title: "设备已断开",
              icon: "success",
              duration: 1500
            });
          }
        }
      });
    },
    goTo(page) {
      common_vendor.index.showToast({
        title: `跳转到${page}`,
        icon: "none",
        duration: 1500
      });
    },
    popupClose() {
      common_vendor.index.__f__("log", "at pages/device/device-manage/device-manage.vue:517", "弹窗关闭");
    },
    popupConfirm() {
      this.$refs.popup.close();
    }
  }
};
if (!Array) {
  const _component_scale_calibration = common_vendor.resolveComponent("scale-calibration");
  const _easycom_uni_popup_dialog2 = common_vendor.resolveComponent("uni-popup-dialog");
  const _easycom_uni_popup2 = common_vendor.resolveComponent("uni-popup");
  (_component_scale_calibration + _easycom_uni_popup_dialog2 + _easycom_uni_popup2)();
}
const _easycom_uni_popup_dialog = () => "../../../uni_modules/uni-popup/components/uni-popup-dialog/uni-popup-dialog.js";
const _easycom_uni_popup = () => "../../../uni_modules/uni-popup/components/uni-popup/uni-popup.js";
if (!Math) {
  (_easycom_uni_popup_dialog + _easycom_uni_popup)();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.t($data.deviceStatus.isOnline ? "在线" : "离线"),
    b: common_vendor.t($data.deviceStatus.weight),
    c: common_vendor.o((...args) => $options.clearWeight && $options.clearWeight(...args)),
    d: common_vendor.f($data.tabs, (tab, index, i0) => {
      return {
        a: common_vendor.t(tab),
        b: index,
        c: $data.activeTab === index ? 1 : "",
        d: common_vendor.o(($event) => $options.switchTab(index), index)
      };
    }),
    e: $data.activeTab === 0
  }, $data.activeTab === 0 ? {
    f: common_vendor.f($data.colorList, (item, index, i0) => {
      return {
        a: common_vendor.t($options.getDyePercentage(item)),
        b: item.hex,
        c: common_vendor.t(item.name),
        d: index,
        e: common_vendor.o(($event) => $options.selectColor(item, index), index)
      };
    })
  } : {}, {
    g: $data.activeTab === 1
  }, $data.activeTab === 1 ? {
    h: common_vendor.t($data.deviceInfo.deviceNo || $data.deviceCode),
    i: common_vendor.t($data.deviceInfo.phone || _ctx.phone),
    j: common_vendor.t($data.deviceInfo.account),
    k: common_vendor.t($data.deviceInfo.model),
    l: common_vendor.t($data.deviceInfo.store),
    m: common_vendor.t($data.deviceInfo.expireDate),
    n: common_vendor.t($data.deviceInfo.firmwareVersion)
  } : {}, {
    o: common_vendor.sr("scaleCalibration", "b1070186-0"),
    p: common_vendor.o($options.onCalibrationComplete),
    q: common_vendor.o($options.onCalibrationClose),
    r: common_vendor.p({
      ["current-weight"]: $data.scaleWeight
    }),
    s: $data.activeTab === 2
  }, $data.activeTab === 2 ? {
    t: common_vendor.o(($event) => $options.showSetting({
      type: "dischargeSpeed",
      title: "出料速度"
    })),
    v: common_vendor.o(($event) => $options.showSetting({
      type: "ambientLight",
      title: "氛围灯颜色"
    })),
    w: common_vendor.o(($event) => $options.showSetting({
      type: "deviceId",
      title: "获取设备ID",
      action: "get"
    })),
    x: common_vendor.o(($event) => $options.showSetting({
      type: "scaleCalibration",
      title: "电子秤校准",
      steps: ["空载校准", "标准砝码校准"]
    })),
    y: common_vendor.o(($event) => $options.showSetting({
      type: "motorTest",
      title: "测试电机"
    })),
    z: common_vendor.o(($event) => $options.showSetting({
      type: "clearCalibration",
      title: "清除校准数据",
      confirmText: "确定要清除所有校准数据吗？此操作不可恢复！"
    })),
    A: common_vendor.o(($event) => $options.showSetting({
      type: "wifi",
      title: "连接WIFI",
      fields: [{
        name: "ssid",
        label: "网络名称"
      }, {
        name: "password",
        label: "密码",
        type: "password"
      }]
    })),
    B: common_vendor.o(($event) => $options.showSetting({
      type: "apn",
      title: "APN设置",
      fields: [{
        name: "apn",
        label: "APN"
      }, {
        name: "username",
        label: "用户名"
      }, {
        name: "password",
        label: "密码",
        type: "password"
      }]
    }))
  } : {}, {
    C: $data.selectedColorHex,
    D: common_vendor.t($data.selectedColorName),
    E: common_vendor.t($data.selectedColorPosition),
    F: common_vendor.t($data.remainingQuantity),
    G: common_vendor.t($data.unit),
    H: $data.remainingQuantity < 20 ? "#ff4d4f" : "#52c41a",
    I: $data.remainingQuantity,
    J: $data.remainingQuantity < 20 ? "#ff4d4f" : "#52c41a",
    K: common_vendor.t($data.remainingQuantity < 20 ? "余量不足，请及时补充" : "余量充足"),
    L: $data.remainingQuantity < 20 ? "#ff4d4f" : "#52c41a",
    M: common_vendor.o($options.popupClose),
    N: common_vendor.o($options.popupConfirm),
    O: common_vendor.p({
      type: $data.popupType,
      title: $data.popupTitle,
      content: $data.popupContent,
      duration: 2e3,
      ["before-close"]: true
    }),
    P: common_vendor.sr("popup", "b1070186-1"),
    Q: common_vendor.p({
      type: "dialog"
    })
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/pages/device/device-manage/device-manage.js.map
