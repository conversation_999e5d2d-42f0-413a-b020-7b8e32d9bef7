"use strict";
const common_vendor = require("../../../common/vendor.js");
const utils_request = require("../../../utils/request.js");
const common_assets = require("../../../common/assets.js");
const BindModal = () => "./bind-modal2.js";
const _sfc_main = {
  components: {
    BindModal
  },
  data() {
    return {
      currentTab: 0,
      tabs: ["全部设备", "已绑定", "未绑定"],
      deviceList: [],
      loadingStatus: "more",
      searchKeyword: "",
      filterStatus: 0,
      page: 1,
      pageSize: 10,
      total: 0,
      loading: false,
      scrollHeight: 0,
      contentText: {
        // contentdown: '上拉加载更多',
        // contentrefresh: '加载中',
        // contentnomore: '没有更多数据了'
      },
      userInfo: {
        // 实际应从缓存获取
      },
      // 分配权限相关数据
      assignForm: {
        deviceId: null,
        deviceName: "",
        currentManager: "",
        selectedStaff: {},
        permissionType: 1
      },
      staffList: [],
      // 员工列表从后端API获取
      permissionTypes: [
        {
          value: 0,
          text: "回收权限"
        },
        {
          value: 1,
          text: "普通权限"
        },
        {
          value: 2,
          text: "管理员权限"
        }
      ]
    };
  },
  computed: {
    filteredDevices() {
      return this.deviceList.filter((item) => {
        const keyword = this.searchKeyword.toLowerCase();
        const matchKeyword = !keyword || item.name && item.name.toLowerCase().includes(keyword) || item.sn && item.sn.toLowerCase().includes(keyword) || item.device_name && item.device_name.toLowerCase().includes(keyword) || item.device_code && item.device_code.toLowerCase().includes(keyword) || item.manager && typeof item.manager === "object" && item.manager.name && item.manager.name.toLowerCase().includes(keyword) || item.manager && typeof item.manager === "string" && item.manager.toLowerCase().includes(keyword);
        const matchStatus = this.filterStatus === 0 || this.filterStatus === 1 && item.status === 1 || this.filterStatus === 2 && item.status === 0;
        return matchKeyword && matchStatus;
      });
    }
  },
  onLoad() {
    this.calculateScrollHeight();
    this.checkLoginAndLoadData();
    this.getStaffList();
  },
  onShow() {
    this.calculateScrollHeight();
  },
  onPullDownRefresh() {
    this.page = 1;
    this.loadDeviceList(true);
  },
  methods: {
    openBindModal() {
      this.$refs.bindModal.open();
    },
    bindSuccess() {
      this.page = 1;
      this.loadDeviceList();
      common_vendor.index.showToast({
        title: "绑定成功",
        icon: "success"
      });
    },
    async getStaffList() {
      try {
        const response = await utils_request.apiService.mall.stores.getStaffList();
        this.staffList = response.data;
      } catch (e) {
        common_vendor.index.__f__("error", "at pages/device/dye-device-manager/dye-device-manager.vue:210", "获取员工列表失败:", e);
        common_vendor.index.showToast({
          title: "获取员工列表失败",
          icon: "none"
        });
      }
    },
    // 检查登录状态并加载数据
    checkLoginAndLoadData() {
      const isLoggedIn = common_vendor.index.getStorageSync("isLoggedIn");
      const token = common_vendor.index.getStorageSync("token");
      if (!isLoggedIn || !token) {
        common_vendor.index.showModal({
          title: "提示",
          content: "请先登录后再访问设备管理",
          showCancel: false,
          success: () => {
            common_vendor.index.navigateTo({
              url: "/pages/login/login"
            });
          }
        });
        return;
      }
      this.initUserInfo();
      this.loadDeviceList();
    },
    initUserInfo() {
      const token = common_vendor.index.getStorageSync("token");
      this.isLogin;
      this.isLogin = !!token;
      common_vendor.index.__f__("log", "at pages/device/dye-device-manager/dye-device-manager.vue:246", "登录状态", this.isLogin);
      if (this.isLogin) {
        this.userInfo = common_vendor.index.getStorageSync("userInfo") || this.userInfo;
      }
    },
    // 判断当前用户是否是设备管理员
    isCurrentUserManager(managers) {
      if (!managers || !Array.isArray(managers))
        return false;
      return managers.some((manager) => manager.id == this.userInfo.id);
    },
    calculateScrollHeight() {
      const systemInfo = common_vendor.index.getSystemInfoSync();
      this.scrollHeight = systemInfo.windowHeight - 44;
    },
    getStatusText(status) {
      return status === 1 ? "已绑定" : "未绑定";
    },
    getStatusColor(status) {
      return status === 1 ? "#4cd964" : "#dd524d";
    },
    async loadDeviceList(isRefresh = false) {
      if (this.loading)
        return;
      this.loading = true;
      this.loadingStatus = isRefresh ? "refresh" : "loading";
      try {
        await new Promise((resolve) => setTimeout(resolve, 800));
        const response = await utils_request.apiService.mall.stores.getStoreDeviceMapping();
        if (isRefresh || this.page === 1) {
          this.deviceList = response.data;
        } else {
          this.deviceList = [...this.deviceList, ...response.data];
        }
        this.loadingStatus = response.data.length > 0 ? "more" : "noMore";
        this.page++;
      } catch (e) {
        common_vendor.index.__f__("error", "at pages/device/dye-device-manager/dye-device-manager.vue:295", "加载失败:", e);
        this.loadingStatus = "more";
        common_vendor.index.showToast({
          title: "加载失败",
          icon: "none"
        });
      } finally {
        this.loading = false;
        if (isRefresh) {
          common_vendor.index.stopPullDownRefresh();
        }
      }
    },
    getMockData() {
      return [
        {
          id: 1,
          name: "染膏机-01",
          device_name: "染膏机-01",
          sn: "RG20230001",
          device_code: "RG20230001",
          type: "专业染膏机",
          status: 1,
          managers: [
            {
              id: 1,
              name: "张店长",
              permissionType: 2
            },
            // 管理员权限
            {
              id: 3,
              name: "王五",
              permissionType: 1
            }
          ],
          storeName: "朝阳门店",
          purchaseDate: "2023-01-15",
          lastMaintenance: "2023-06-20"
        },
        {
          id: 2,
          name: "染膏机-02",
          device_name: "染膏机-02",
          sn: "RG20230002",
          device_code: "RG20230002",
          type: "快速染膏机",
          status: 1,
          managers: [{
            id: 2,
            name: "李经理",
            permissionType: 2
          }],
          storeName: "海淀门店",
          purchaseDate: "2023-02-10",
          lastMaintenance: "2023-07-05"
        },
        {
          id: 3,
          name: "染膏调配仪-01",
          device_name: "染膏调配仪-01",
          sn: "RG20230003",
          device_code: "RG20230003",
          type: "智能调配仪",
          status: 0,
          managers: [],
          storeName: "",
          purchaseDate: "2023-03-20",
          lastMaintenance: ""
        }
      ];
    },
    loadMore() {
      if (this.loadingStatus !== "more")
        return;
      this.loadDeviceList();
    },
    onClickItem(e) {
      if (this.currentTab === e.currentIndex)
        return;
      this.currentTab = e.currentIndex;
      this.filterStatus = e.currentIndex;
      this.page = 1;
      this.loadDeviceList();
    },
    search() {
      this.page = 1;
      this.loadDeviceList();
    },
    clearSearch() {
      this.searchKeyword = "";
      this.page = 1;
      this.loadDeviceList();
    },
    bindManager(item) {
      this.$refs.bindModal.open(item);
    },
    async unbindManager(item) {
      common_vendor.index.__f__("log", "at pages/device/dye-device-manager/dye-device-manager.vue:396", "解除绑定信息", item);
      common_vendor.index.showModal({
        title: "确认解绑",
        content: `确定要解绑设备 ${item.name || item.device_name || item.deviceId} 吗？`,
        success: async (res) => {
          if (res.confirm) {
            common_vendor.index.showLoading({
              title: "处理中...",
              mask: true
            });
            try {
              const data = {
                deviceId: item.id,
                storeId: item.storeId
              };
              const response = await utils_request.apiService.mall.stores.storeUnbindDevice(data);
              if (response.code === 200) {
                common_vendor.index.showToast({
                  title: "解绑成功",
                  icon: "success"
                });
                this.page = 1;
                this.loadDeviceList();
              } else {
                common_vendor.index.showToast({
                  title: "解绑失败请重试",
                  icon: "fail",
                  duration: 2500
                });
              }
            } catch (e) {
              common_vendor.index.showToast({
                title: e.message || "解绑失败",
                icon: "none"
              });
            } finally {
              common_vendor.index.hideLoading();
            }
          }
        }
      });
    },
    // 打开分配权限弹窗
    assignPermission(item) {
      this.assignForm = {
        deviceId: item.id,
        deviceName: item.device_name || item.name,
        currentManager: item.managers && item.managers.length ? item.managers.map((m) => m.name).join("、") : "无",
        selectedStaff: {},
        permissionType: 1
      };
      this.$refs.assignPopup.open();
    },
    // 员工选择变化
    onStaffChange(e) {
      const index = e.detail.value;
      this.assignForm.selectedStaff = this.staffList[index];
    },
    // 确认分配
    confirmAssign() {
      if (!this.assignForm.selectedStaff.id) {
        common_vendor.index.showToast({
          title: "请选择员工",
          icon: "none"
        });
        return;
      }
      common_vendor.index.showLoading({
        title: "分配中...",
        mask: true
      });
      const data = {
        deviceId: this.assignForm.deviceId,
        permissionType: this.assignForm.permissionType,
        employeeId: this.assignForm.selectedStaff.employeeId
      };
      utils_request.apiService.mall.stores.assignPermission(data).then((res) => {
        if (res.code === 200) {
          common_vendor.index.hideLoading();
          this.$refs.assignPopup.close();
          common_vendor.index.showToast({
            title: "分配成功",
            icon: "success"
          });
          this.page = 1;
          this.loadDeviceList();
        }
      }).catch((err) => {
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({
          title: "分配失败,请重试",
          icon: "fail"
        });
        this.page = 1;
        this.loadDeviceList();
      });
    },
    // 关闭分配弹窗
    closeAssignPopup() {
      this.$refs.assignPopup.close();
    },
    showDetail(item) {
    },
    bindSuccess() {
      this.page = 1;
      this.loadDeviceList();
    },
    back() {
      common_vendor.index.navigateBack();
    }
  }
};
if (!Array) {
  const _easycom_uni_icons2 = common_vendor.resolveComponent("uni-icons");
  const _easycom_uni_list_item2 = common_vendor.resolveComponent("uni-list-item");
  const _easycom_uni_list2 = common_vendor.resolveComponent("uni-list");
  const _component_bind_modal = common_vendor.resolveComponent("bind-modal");
  const _easycom_uni_data_checkbox2 = common_vendor.resolveComponent("uni-data-checkbox");
  const _easycom_uni_popup2 = common_vendor.resolveComponent("uni-popup");
  (_easycom_uni_icons2 + _easycom_uni_list_item2 + _easycom_uni_list2 + _component_bind_modal + _easycom_uni_data_checkbox2 + _easycom_uni_popup2)();
}
const _easycom_uni_icons = () => "../../../uni_modules/uni-icons/components/uni-icons/uni-icons.js";
const _easycom_uni_list_item = () => "../../../uni_modules/uni-list/components/uni-list-item/uni-list-item.js";
const _easycom_uni_list = () => "../../../uni_modules/uni-list/components/uni-list/uni-list.js";
const _easycom_uni_data_checkbox = () => "../../../uni_modules/uni-data-checkbox/components/uni-data-checkbox/uni-data-checkbox.js";
const _easycom_uni_popup = () => "../../../uni_modules/uni-popup/components/uni-popup/uni-popup.js";
if (!Math) {
  (_easycom_uni_icons + _easycom_uni_list_item + _easycom_uni_list + _easycom_uni_data_checkbox + _easycom_uni_popup)();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.p({
      type: "plus",
      size: "16"
    }),
    b: common_vendor.o((...args) => $options.openBindModal && $options.openBindModal(...args)),
    c: common_vendor.f($options.filteredDevices, (item, index, i0) => {
      return common_vendor.e({
        a: item.status === 1 && $options.isCurrentUserManager(item.managers)
      }, item.status === 1 && $options.isCurrentUserManager(item.managers) ? {
        b: common_vendor.o(($event) => $options.unbindManager(item), item.id)
      } : {}, {
        c: item.status === 1 && $options.isCurrentUserManager(item.managers)
      }, item.status === 1 && $options.isCurrentUserManager(item.managers) ? {
        d: common_vendor.o(($event) => $options.assignPermission(item), item.id)
      } : {}, {
        e: item.id,
        f: common_vendor.o(($event) => $options.showDetail(item), item.id),
        g: "3d1857ba-2-" + i0 + ",3d1857ba-1",
        h: common_vendor.p({
          title: item.device_name || item.name || item.deviceCode,
          note: `编号: ${item.deviceCode || item.sn} | 类型: ${item.type}`,
          ["right-text"]: $options.getStatusText(item.status),
          ["right-text-style"]: {
            color: $options.getStatusColor(item.status)
          },
          clickable: true
        })
      });
    }),
    d: $options.filteredDevices.length === 0 && !$data.loading
  }, $options.filteredDevices.length === 0 && !$data.loading ? {
    e: common_assets._imports_0$14
  } : {}, {
    f: common_vendor.o((...args) => $options.loadMore && $options.loadMore(...args)),
    g: $data.scrollHeight + "px",
    h: common_vendor.sr("bindModal", "3d1857ba-3"),
    i: common_vendor.o($options.bindSuccess),
    j: common_vendor.o($options.closeAssignPopup),
    k: common_vendor.p({
      type: "closeempty",
      size: "24",
      color: "#999"
    }),
    l: common_vendor.t($data.assignForm.deviceName),
    m: common_vendor.t($data.assignForm.currentManager),
    n: common_vendor.t($data.assignForm.selectedStaff.name || "请选择员工"),
    o: $data.staffList,
    p: common_vendor.o((...args) => $options.onStaffChange && $options.onStaffChange(...args)),
    q: common_vendor.o(($event) => $data.assignForm.permissionType = $event),
    r: common_vendor.p({
      localdata: $data.permissionTypes,
      modelValue: $data.assignForm.permissionType
    }),
    s: common_vendor.o((...args) => $options.closeAssignPopup && $options.closeAssignPopup(...args)),
    t: common_vendor.o((...args) => $options.confirmAssign && $options.confirmAssign(...args)),
    v: !$data.assignForm.selectedStaff.id,
    w: common_vendor.sr("assignPopup", "3d1857ba-4"),
    x: common_vendor.p({
      type: "bottom",
      ["mask-click"]: false
    })
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/pages/device/dye-device-manager/dye-device-manager.js.map
