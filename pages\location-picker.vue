<template>
  <view class="location-picker">
    <!-- 头部 -->
    <view class="header">
	  <view class="header-btn" @click="confirmLocation">确定</view>
      <view class="header-title">{{selectedLocation.name || '广东省东莞市南城街道'}}</view>
      <view class="header-btn" @click="goBack">取消</view>
    </view>

    <!-- 搜索框 -->
    <view class="search-container">
      <view class="search-box">
        <view class="search-icon">🔍</view>
        <input 
          type="text" 
          class="search-input" 
          placeholder="搜索地点" 
          v-model="searchKeyword"
          @input="onSearchInput"
        />
      </view>
    </view>

    <!-- 当前位置 -->
    <view class="current-location">
      <view class="location-icon">📍</view>
      <view class="current-location-text">
        <view class="current-location-name">{{selectedLocation.name || '广东省东莞市南城街道'}}</view>
        <view class="current-location-detail">{{selectedLocation.address || '广东省东莞市南城街道'}}</view>
      </view>
      <view class="relocate-btn" @click="getCurrentLocation">重新定位</view>
    </view>

    <!-- 地图容器 -->
    <view class="map-container">
      <map
        class="map"
        :latitude="mapCenter.latitude"
        :longitude="mapCenter.longitude"
        :markers="markers"
        :scale="16"
        :show-location="false"
        :enable-scroll="true"
        :enable-zoom="true"
        :enable-rotate="false"
        @markertap="onMarkerTap"
        @tap="onMapTap"
        @regionchange="onRegionChange"
      >
      </map>
    </view>

    <!-- 位置列表 -->
    <scroll-view class="location-list" scroll-y>
      <view 
        v-for="(location, index) in locationList" 
        :key="index" 
        class="location-item"
        @click="selectLocation(location)"
      >
        <view class="location-name">{{location.name}}</view>
        <view class="location-address">{{location.address}}</view>
      </view>
    </scroll-view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      selectedLocation: {
        name: '广东省东莞市南城街道',
        address: '广东省东莞市南城街道'
      },
      searchKeyword: '',
      locationList: [
        { name: '广东省东莞市南城街道', address: '广东省东莞市南城街道' },
        { name: '广东省东莞市东城街道', address: '广东省东莞市东城街道' },
        { name: '广东省东莞市万江街道', address: '广东省东莞市万江街道' },
        { name: '广东省东莞市莞城街道', address: '广东省东莞市莞城街道' },
        { name: '广东省东莞市石碣镇', address: '广东省东莞市石碣镇' }
      ],
      defaultLocationList: [],
      mapCenter: {
        latitude: 23.0489,
        longitude: 113.7447
      },
      markers: [],
      isLocationInitialized: false
    };
  },

  onLoad() {
    this.defaultLocationList = [...this.locationList];
    this.getCurrentLocation();
  },

  methods: {
    // 获取当前位置
    getCurrentLocation() {
      uni.showLoading({
        title: '正在定位...'
      });

      // 使用微信原生定位
      uni.getLocation({
        type: 'gcj02',
        success: (res) => {
          // 使用默认地址而不是"当前位置"
          const defaultAddress = '广东省东莞市南城街道';

          this.selectedLocation = {
            name: defaultAddress,
            address: defaultAddress,
            latitude: res.latitude,
            longitude: res.longitude
          };

          // 更新地图中心和标记
          this.updateMapLocation(res.latitude, res.longitude, defaultAddress);

          console.log('位置获取成功:', res);
          uni.hideLoading();
        },
        fail: (err) => {
          console.error('定位失败:', err);
          uni.hideLoading();
          uni.showToast({
            title: '定位失败',
            icon: 'none'
          });
        }
      });
    },

    // 更新地图位置
    updateMapLocation(latitude, longitude, address) {
      // 只在第一次获取位置时更新地图中心，避免地图跳动
      if (!this.isLocationInitialized) {
        this.mapCenter = {
          latitude: latitude,
          longitude: longitude
        };
        this.isLocationInitialized = true;
      }

      // 始终更新标记位置
      this.markers = [
        {
          id: 1,
          latitude: latitude,
          longitude: longitude,
          width: 30,
          height: 30,
          callout: {
            content: address || '当前位置',
            color: '#333',
            fontSize: 14,
            borderRadius: 4,
            bgColor: '#fff',
            padding: 8,
            display: 'ALWAYS'
          }
        }
      ];
    },

    // 地图标记点击事件
    onMarkerTap(e) {
      console.log('标记点击:', e);
    },

    // 地图点击事件
    onMapTap(e) {
      console.log('地图点击:', e);
    },

    // 地图区域变化事件
    onRegionChange(e) {
      console.log('地图区域变化:', e);
    },

    // 搜索输入事件
    onSearchInput() {
      if (this.searchKeyword.trim()) {
        this.searchLocations(this.searchKeyword);
      } else {
        this.locationList = [...this.defaultLocationList];
      }
    },

    // 搜索位置
    searchLocations(query) {
      // 使用模拟搜索数据
      const mockResults = [
        { name: `${query}附近`, address: `广东省东莞市${query}` },
        { name: `${query}商圈`, address: `广东省东莞市南城街道${query}` },
        { name: `${query}地铁站`, address: `广东省东莞市${query}地铁站` },
        { name: `${query}购物中心`, address: `广东省东莞市南城街道${query}购物中心` },
        { name: `${query}公园`, address: `广东省东莞市${query}公园` }
      ];
      this.locationList = mockResults;
    },

    // 选择位置
    selectLocation(location) {
      this.selectedLocation = location;
      
      // 如果有坐标，更新地图
      if (location.latitude && location.longitude) {
        this.updateMapLocation(location.latitude, location.longitude, location.name);
      }
    },

    // 确认位置
    confirmLocation() {
      // 返回上一页并传递选中的位置
      const pages = getCurrentPages();
      const prevPage = pages[pages.length - 2];
      
      if (prevPage) {
        prevPage.$vm.currentLocation = this.selectedLocation;
      }
      
      uni.navigateBack();
    },

    // 返回
    goBack() {
      uni.navigateBack();
    }
  }
};
</script>

<style lang="scss" scoped>
.location-picker {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
}

.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 30rpx;
  background-color: #fff;
  border-bottom: 1rpx solid #eee;
}

.header-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  flex: 1;
  text-align: center;
}

.header-btn {
  font-size: 28rpx;
  color: #4285f4;
  padding: 10rpx 20rpx;
}

.search-container {
  padding: 20rpx 30rpx;
  background-color: #fff;
  border-bottom: 1rpx solid #eee;
}

.search-box {
  display: flex;
  align-items: center;
  background-color: #f5f5f5;
  border-radius: 20rpx;
  padding: 20rpx;
}

.search-icon {
  margin-right: 20rpx;
  font-size: 28rpx;
}

.search-input {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}

.current-location {
  display: flex;
  align-items: center;
  padding: 30rpx;
  background-color: #fff;
  border-bottom: 1rpx solid #eee;
}

.location-icon {
  font-size: 32rpx;
  margin-right: 20rpx;
}

.current-location-text {
  flex: 1;
}

.current-location-name {
  font-size: 30rpx;
  color: #333;
  font-weight: 500;
}

.current-location-detail {
  font-size: 24rpx;
  color: #999;
  margin-top: 10rpx;
}

.relocate-btn {
  font-size: 24rpx;
  color: #4285f4;
  padding: 10rpx 20rpx;
  border: 1rpx solid #4285f4;
  border-radius: 20rpx;
}

.map-container {
  height: 400rpx;
  background-color: #fff;
  border-bottom: 1rpx solid #eee;
}

.map {
  width: 100%;
  height: 100%;
}

.location-list {
  flex: 1;
  background-color: #fff;
}

.location-item {
  padding: 30rpx;
  border-bottom: 1rpx solid #eee;
}

.location-name {
  font-size: 30rpx;
  color: #333;
  font-weight: 500;
}

.location-address {
  font-size: 24rpx;
  color: #999;
  margin-top: 10rpx;
}
</style>
