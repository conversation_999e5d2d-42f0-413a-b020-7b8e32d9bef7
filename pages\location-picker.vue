<template>
  <view class="location-picker">
    <!-- 头部 -->
    <view class="header">
	  <view class="header-btn" @click="confirmLocation">确定</view>
      <view class="header-title">{{selectedLocation.name || '广东省东莞市南城街道'}}</view>
      <view class="header-btn" @click="goBack">取消</view>
    </view>

    <!-- 搜索框 -->
    <view class="search-container">
      <view class="search-box">
        <view class="search-icon">🔍</view>
        <input 
          type="text" 
          class="search-input" 
          placeholder="搜索地点" 
          v-model="searchKeyword"
          @input="onSearchInput"
        />
      </view>
    </view>

    <!-- 当前位置 -->
    <view class="current-location">
      <view class="location-icon">📍</view>
      <view class="current-location-text">
        <view class="current-location-name">{{selectedLocation.name || '广东省东莞市南城街道'}}</view>
        <view class="current-location-detail">{{selectedLocation.address || '广东省东莞市南城街道'}}</view>
      </view>
      <view class="relocate-btn" @click="getCurrentLocation">重新定位</view>
    </view>

    <!-- 地图容器 -->
    <view class="map-container">
      <map
        class="map"
        :latitude="mapCenter.latitude"
        :longitude="mapCenter.longitude"
        :markers="markers"
        :scale="16"
        :show-location="false"
        :enable-scroll="true"
        :enable-zoom="true"
        :enable-rotate="false"
        @markertap="onMarkerTap"
        @tap="onMapTap"
        @regionchange="onRegionChange"
      >
      </map>
    </view>

    <!-- 位置列表 -->
    <scroll-view class="location-list" scroll-y>
      <view 
        class="location-item" 
        v-for="(location, index) in locationList" 
        :key="index"
        :class="{ selected: selectedLocation.name === location.name }"
        @click="selectLocation(location)"
      >
        <view class="location-name">{{location.name}}</view>
        <view class="location-address">{{location.address}}</view>
      </view>
    </scroll-view>
  </view>
</template>

<script>
import locationService from '../utils/location.js';

export default {
  data() {
    return {
      selectedLocation: {
        name: '广东省东莞市南城街道',
        address: '广东省东莞市南城街道'
      },
      searchKeyword: '',
      locationList: [
        { name: '越秀区广州市人民政府(府前路北)', address: '广东省广州市越秀区府前路北' },
        { name: '广州市人民政府', address: '广东省广州市越秀区北京街道府前路1号' },
        { name: '珠江国际大厦', address: '广东省广州市越秀区越华路112号' },
        { name: '北京街道', address: '广东省广州市越秀区' },
        { name: '广州交易广场', address: '广东省广州市越秀区东风中路268号' }
      ],
      defaultLocationList: [],
      isLocationInitialized: false, // 防止重复定位
      // 地图相关数据
      mapCenter: {
        latitude: 23.1291, // 广州市中心纬度
        longitude: 113.2644 // 广州市中心经度
      },
      markers: [
        {
          id: 1,
          latitude: 23.1291,
          longitude: 113.2644,
          width: 30,
          height: 30,
          callout: {
            content: '当前位置',
            color: '#333',
            fontSize: 14,
            borderRadius: 4,
            bgColor: '#fff',
            padding: 8,
            display: 'ALWAYS'
          }
        }
      ]
    };
  },

  onLoad() {
    this.defaultLocationList = [...this.locationList];
    this.getCurrentLocation();
  },

  methods: {
    // 获取当前位置
    async getCurrentLocation() {
      try {
        uni.showLoading({
          title: '正在定位...'
        });

        const locationData = await locationService.getCurrentLocation();

        this.selectedLocation = {
          name: locationData.address,
          address: locationData.address,
          latitude: locationData.latitude,
          longitude: locationData.longitude
        };

        // 更新地图中心和标记
        this.updateMapLocation(locationData.latitude, locationData.longitude, locationData.address);

        console.log('位置获取成功:', locationData);
      } catch (error) {
        console.error('获取位置失败:', error);
        uni.showToast({
          title: '定位失败',
          icon: 'none'
        });
      } finally {
        uni.hideLoading();
      }
    },

    // 更新地图位置
    updateMapLocation(latitude, longitude, address) {
      // 只在第一次获取位置时更新地图中心，避免地图跳动
      if (!this.isLocationInitialized) {
        this.mapCenter = {
          latitude: latitude,
          longitude: longitude
        };
        this.isLocationInitialized = true;
      }

      // 始终更新标记位置
      this.markers = [
        {
          id: 1,
          latitude: latitude,
          longitude: longitude,
          width: 30,
          height: 30,
          callout: {
            content: address || '当前位置',
            color: '#333',
            fontSize: 14,
            borderRadius: 4,
            bgColor: '#fff',
            padding: 8,
            display: 'ALWAYS'
          }
        }
      ];
    },

    // 地图标记点击事件
    onMarkerTap(e) {
      console.log('标记点击:', e);
    },

    // 地图点击事件
    onMapTap(e) {
      console.log('地图点击:', e);
      // 可以在这里添加点击地图选择位置的功能
    },

    // 地图区域变化事件
    onRegionChange(e) {
      console.log('地图区域变化:', e);
      // 只在用户手动拖拽结束时更新位置
      if (e.type === 'end' && e.causedBy === 'drag') {
        // 可以在这里添加拖拽地图更新位置的功能
        // 暂时注释掉自动更新，避免地图跳动
        // this.updateLocationByCoordinates(e.detail.centerLocation.latitude, e.detail.centerLocation.longitude);
      }
    },

    // 搜索输入处理
    onSearchInput() {
      if (this.searchKeyword.trim()) {
        this.searchLocations(this.searchKeyword.trim());
      } else {
        this.showDefaultLocations();
      }
    },

    // 搜索位置
    async searchLocations(query) {
      try {
        uni.showLoading({
          title: '搜索中...'
        });

        // 尝试使用真实的高德地图POI搜索
        try {
          const searchResults = await locationService.searchPOI(query, '东莞');
          this.locationList = searchResults;
        } catch (apiError) {
          console.warn('API搜索失败，使用模拟数据:', apiError);
          // API调用失败时使用模拟数据
          const mockResults = [
            { name: `${query}附近`, address: `广东省东莞市${query}` },
            { name: `${query}商圈`, address: `广东省东莞市南城街道${query}` },
            { name: `${query}地铁站`, address: `广东省东莞市${query}地铁站` },
            { name: `${query}购物中心`, address: `广东省东莞市南城街道${query}购物中心` },
            { name: `${query}公园`, address: `广东省东莞市${query}公园` }
          ];
          this.locationList = mockResults;
        }

        uni.hideLoading();

      } catch (error) {
        console.error('搜索失败:', error);
        uni.hideLoading();
        uni.showToast({
          title: '搜索失败',
          icon: 'none'
        });
      }
    },

    // 显示默认位置列表
    showDefaultLocations() {
      this.locationList = [...this.defaultLocationList];
    },

    // 选择位置
    selectLocation(location) {
      this.selectedLocation = { ...location };
    },

    // 确认选择
    confirmLocation() {
      // 保存选择的位置到本地存储
      uni.setStorageSync('selectedLocation', this.selectedLocation);
      
      // 触发事件通知父页面
      uni.$emit('locationSelected', this.selectedLocation);
      
      uni.showToast({
        title: '位置已选择',
        icon: 'success'
      });

      // 返回上一页
      setTimeout(() => {
        uni.navigateBack();
      }, 500);
    },

    // 返回
    goBack() {
      uni.navigateBack();
    }
  }
};
</script>

<style lang="scss">
.location-picker {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f5f5;
}

.header {
  background: #4A90E2;
  color: white;
  padding: 20rpx 30rpx;
  padding-top: 150rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
  // height: 400rpx;
  // margin-top: 100rpx;
}

.header-title {
  font-size: 36rpx;
  font-weight: 500;
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  max-width: 400rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.header-btn {
  background: none;
  border: none;
  color: white;
  font-size: 32rpx;
  cursor: pointer;
  padding: 10rpx 20rpx;
}

.search-container {
  background: white;
  padding: 30rpx;
  border-bottom: 1rpx solid #eee;
}

.search-box {
  display: flex;
  align-items: center;
  background: #f5f5f5;
  border-radius: 16rpx;
  padding: 20rpx 30rpx;
}

.search-icon {
  font-size: 32rpx;
  margin-right: 20rpx;
  opacity: 0.6;
}

.search-input {
  flex: 1;
  border: none;
  background: none;
  font-size: 32rpx;
  outline: none;
}

.map-container {
  flex: 1;
  position: relative;
  background: #f5f5f5;
}

.map {
  width: 100%;
  height: 100%;
}

.map-placeholder {
  text-align: center;
  padding: 40rpx;
}

.map-logo {
  font-size: 80rpx;
  margin-bottom: 20rpx;
}

.map-text {
  font-size: 36rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 10rpx;
}

.map-desc {
  font-size: 28rpx;
  color: #666;
  opacity: 0.8;
}

.location-list {
  background: white;
  max-height: 600rpx;
}

.location-item {
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  transition: background-color 0.2s;
}

.location-item:active {
  background: #f8f8f8;
}

.location-item.selected {
  background: #f0f8ff;
}

.location-item:last-child {
  border-bottom: none;
}

.location-name {
  font-size: 32rpx;
  color: #333;
  margin-bottom: 10rpx;
}

.location-address {
  font-size: 28rpx;
  color: #666;
}

.current-location {
  background: white;
  padding: 30rpx;
  border-bottom: 1rpx solid #eee;
  display: flex;
  align-items: center;
}

.location-icon {
  font-size: 48rpx;
  margin-right: 30rpx;
  color: #4A90E2;
}

.current-location-text {
  flex: 1;
}

.current-location-name {
  font-size: 32rpx;
  color: #333;
  margin-bottom: 6rpx;
}

.current-location-detail {
  font-size: 28rpx;
  color: #666;
}

.relocate-btn {
  background: none;
  border: none;
  color: #4A90E2;
  font-size: 28rpx;
  cursor: pointer;
  padding: 10rpx;
}
</style>
