/* 水平间距 */
/* 水平间距 */
/* 全局样式修复 - 确保页面占满屏幕宽度 */
page {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
}

/* 通用容器样式 */
.container, .page-container {
  width: 100%;
  min-height: 100vh;
  box-sizing: border-box;
}

/* 修复小程序页面宽度问题 */
uni-page-body {
  width: 100% !important;
}

/* 确保所有view元素正确继承宽度 */
view {
  box-sizing: border-box;
}
.confirm-container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 120rpx;
  width: 100%;
  box-sizing: border-box;
}
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background-color: #f5f5f5;
}
.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f0f0f0;
  border-top: 4rpx solid #f44336;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}
@keyframes spin {
0% {
    transform: rotate(0deg);
}
100% {
    transform: rotate(360deg);
}
}
.loading-text {
  font-size: 28rpx;
  color: #666666;
}
.address-section {
  background-color: #ffffff;
  padding: 25rpx 30rpx;
  margin: 0 0 2rpx 0;
  display: flex;
  align-items: center;
  width: 100%;
  box-sizing: border-box;
}
.address-info {
  flex: 1;
}
.address-name-phone {
  margin-bottom: 10rpx;
}
.name {
  font-size: 30rpx;
  font-weight: bold;
  margin-right: 20rpx;
}
.phone {
  font-size: 28rpx;
  color: #666666;
}
.address-detail {
  font-size: 28rpx;
  color: #333333;
}
.no-address {
  flex: 1;
  font-size: 30rpx;
  color: #666666;
  text-align: center;
}
.address-arrow {
  font-size: 24rpx;
  color: #999999;
  margin-left: 20rpx;
}
.goods-list {
  background-color: #ffffff;
  margin: 0 0 2rpx 0;
  padding: 30rpx;
  width: 100%;
  box-sizing: border-box;
  overflow: hidden;
  /* 防止内容溢出 */
}
.shop-name {
  font-size: 28rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
}
.goods-item {
  display: flex;
  margin-bottom: 20rpx;
  width: 100%;
  align-items: flex-start;
  box-sizing: border-box;
  padding: 15rpx 0;
  gap: 15rpx;
  /* 增加间距控制 */
}
.goods-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 8rpx;
  flex-shrink: 0;
}
.goods-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  min-height: 120rpx;
  width: 0;
  /* 强制flex子元素重新计算宽度 */
  box-sizing: border-box;
  padding-right: 0;
  /* 移除右侧间距，最大化利用空间 */
}
.goods-main-info {
  flex: 1;
  margin-bottom: 15rpx;
}
.goods-name {
  font-size: 30rpx;
  margin-bottom: 8rpx;
  line-height: 1.4;
  word-break: break-all;
  overflow-wrap: break-word;
  width: 100%;
  font-weight: 500;
  color: #333333;
}
.goods-spec {
  font-size: 24rpx;
  color: #999999;
  line-height: 1.3;
  word-break: break-all;
  overflow-wrap: break-word;
  width: 100%;
}
.goods-price-qty {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  width: 100%;
  box-sizing: border-box;
  gap: 15rpx;
  /* 减少间距以充分利用空间 */
}
.price-section, .qty-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  padding: 10rpx 8rpx;
  background-color: #f8f9fa;
  border-radius: 6rpx;
  min-width: 0;
  /* 允许flex子元素收缩 */
}
.goods-price {
  font-size: 32rpx;
  color: #f44336;
  font-weight: bold;
  margin-bottom: 4rpx;
  white-space: nowrap;
}
.goods-qty {
  font-size: 28rpx;
  color: #333333;
  font-weight: 500;
  margin-bottom: 4rpx;
  white-space: nowrap;
}
.price-label, .qty-label {
  font-size: 20rpx;
  color: #999999;
  white-space: nowrap;
}
.delivery-section, .coupon-section, .remark-section {
  background-color: #ffffff;
  margin: 0 0 2rpx 0;
  width: 100%;
  box-sizing: border-box;
}
.section-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 25rpx 30rpx;
  font-size: 28rpx;
}
.item-label {
  color: #333333;
}
.item-value {
  color: #666666;
  display: flex;
  align-items: center;
}
.item-arrow {
  margin-left: 10rpx;
  color: #999999;
  font-size: 24rpx;
}
.coupon-value {
  color: #f44336;
}
.remark-input {
  text-align: right;
  width: 70%;
}
.amount-section {
  background-color: #ffffff;
  padding: 30rpx;
  margin: 0 0 2rpx 0;
  width: 100%;
  box-sizing: border-box;
}
.amount-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20rpx;
  font-size: 26rpx;
  color: #666666;
}
.amount-total {
  display: flex;
  justify-content: space-between;
  padding-top: 20rpx;
  border-top: 1rpx solid #f0f0f0;
}
.total-price {
  font-size: 32rpx;
  font-weight: bold;
  color: #f44336;
}
.bottom-bar {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  height: 100rpx;
  background-color: #ffffff;
  display: flex;
  align-items: center;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
}
.price-box {
  flex: 1;
  display: flex;
  align-items: center;
  padding-left: 30rpx;
  font-size: 28rpx;
}
.final-price {
  color: #f44336;
  font-size: 36rpx;
  font-weight: bold;
}
.submit-btn {
  width: 240rpx;
  height: 100%;
  background-color: #f44336;
  color: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 30rpx;
}

/* 响应式设计优化 */
@media screen and (max-width: 750rpx) {
.goods-image {
    width: 100rpx;
    height: 100rpx;
}
.goods-info {
    min-height: 100rpx;
    padding-right: 0;
}
.goods-name {
    font-size: 28rpx;
}
.goods-price {
    font-size: 28rpx;
}
.goods-qty {
    font-size: 24rpx;
}
.price-section, .qty-section {
    padding: 6rpx 8rpx;
    gap: 15rpx;
}
.price-label, .qty-label {
    font-size: 18rpx;
}
}
@media screen and (min-width: 751rpx) {
.goods-info {
    padding-right: 0;
    /* 在较大屏幕上也移除右侧内边距以充分利用空间 */
}
.goods-price-qty {
    gap: 18rpx;
}
.price-section, .qty-section {
    padding: 12rpx 10rpx;
}
}
/* 整体容器样式 */
.confirm-container {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 120rpx;
  box-sizing: border-box;
  width: 100%;
  display: flex;
  flex-direction: column;
}