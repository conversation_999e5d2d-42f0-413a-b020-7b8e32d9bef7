/* 水平间距 */
/* 水平间距 */
/* 全局样式修复 - 确保页面占满屏幕宽度 */
page.data-v-7ecc3ead {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
}

/* 通用容器样式 */
.container.data-v-7ecc3ead, .page-container.data-v-7ecc3ead {
  width: 100%;
  min-height: 100vh;
  box-sizing: border-box;
}

/* 修复小程序页面宽度问题 */
uni-page-body.data-v-7ecc3ead {
  width: 100% !important;
}

/* 确保所有view元素正确继承宽度 */
view.data-v-7ecc3ead {
  box-sizing: border-box;
}
.container.data-v-7ecc3ead {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}
.header.data-v-7ecc3ead {
  text-align: center;
  margin-bottom: 30rpx;
}
.title.data-v-7ecc3ead {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}
.status-section.data-v-7ecc3ead {
  background-color: #fff;
  border-radius: 10rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
}
.status-item.data-v-7ecc3ead {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  padding: 10rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}
.status-item.data-v-7ecc3ead:last-child {
  border-bottom: none;
  margin-bottom: 0;
}
.label.data-v-7ecc3ead {
  font-size: 28rpx;
  color: #666;
}
.value.data-v-7ecc3ead {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
}
.value.online.data-v-7ecc3ead {
  color: #4285f4;
}
.value.offline.data-v-7ecc3ead {
  color: #999;
}
.actions-section.data-v-7ecc3ead {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
  margin-bottom: 30rpx;
}
.action-btn.data-v-7ecc3ead {
  flex: 1;
  min-width: 200rpx;
  background-color: #4285f4;
  color: #fff;
  border: none;
  border-radius: 10rpx;
  padding: 20rpx;
  font-size: 28rpx;
}
.logs-section.data-v-7ecc3ead {
  background-color: #fff;
  border-radius: 10rpx;
  padding: 30rpx;
}
.section-title.data-v-7ecc3ead {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}
.logs-container.data-v-7ecc3ead {
  height: 400rpx;
  background-color: #f8f8f8;
  border-radius: 8rpx;
  padding: 20rpx;
}
.log-item.data-v-7ecc3ead {
  margin-bottom: 10rpx;
}
.log-text.data-v-7ecc3ead {
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
}