"use strict";
const common_vendor = require("../common/vendor.js");
const utils_bleCommandSet = require("./bleCommandSet.js");
const store_bluetooth = require("../store/bluetooth.js");
function arrayToArrayBuffer(arr) {
  const buffer = new ArrayBuffer(arr.length);
  const dataView = new Uint8Array(buffer);
  for (let i = 0; i < arr.length; i++) {
    dataView[i] = arr[i] || 0;
  }
  return buffer;
}
function encodeUTF8(str) {
  if (typeof TextEncoder !== "undefined") {
    return new TextEncoder().encode(str);
  }
  const bytes = [];
  for (let i = 0; i < str.length; i++) {
    let code = str.charCodeAt(i);
    if (code < 128) {
      bytes.push(code);
    } else if (code < 2048) {
      bytes.push(192 | code >> 6);
      bytes.push(128 | code & 63);
    } else if (code < 65536) {
      bytes.push(224 | code >> 12);
      bytes.push(128 | code >> 6 & 63);
      bytes.push(128 | code & 63);
    } else {
      bytes.push(240 | code >> 18);
      bytes.push(128 | code >> 12 & 63);
      bytes.push(128 | code >> 6 & 63);
      bytes.push(128 | code & 63);
    }
  }
  return new Uint8Array(bytes);
}
const TransferState = {
  IDLE: "idle",
  TRANSFERRING: "transferring",
  SUCCESS: "success",
  FAILED: "failed",
  TIMEOUT: "timeout"
};
const transferManager = {
  originalData: null,
  chunks: [],
  currentChunkIndex: 0,
  retryCount: 0,
  maxRetries: 3,
  isTransferring: false,
  lastSentChunk: null,
  lastSentIndex: -1,
  resolveCallback: null,
  rejectCallback: null,
  timeoutId: null,
  transferState: TransferState.IDLE,
  onProgress: null,
  onComplete: null,
  clearTransferState: function() {
    if (this.timeoutId) {
      clearTimeout(this.timeoutId);
      this.timeoutId = null;
    }
    this.originalData = null;
    this.chunks = [];
    this.currentChunkIndex = 0;
    this.retryCount = 0;
    this.isTransferring = false;
    this.lastSentChunk = null;
    this.lastSentIndex = -1;
    this.resolveCallback = null;
    this.rejectCallback = null;
    this.endCommandSent = false;
    this.endCommandConfirmed = false;
    this.onProgress = null;
    this.onComplete = null;
    setTimeout(() => {
      this.transferState = TransferState.IDLE;
    }, 820);
  },
  init: function(utf8Bytes, onProgress, onComplete) {
    this.originalData = utf8Bytes;
    this.chunks = this.splitIntoChunks(utf8Bytes, 13);
    this.currentChunkIndex = 0;
    this.retryCount = 0;
    this.isTransferring = true;
    this.transferState = TransferState.TRANSFERRING;
    this.onProgress = onProgress;
    this.onComplete = onComplete;
    this.startTransfer();
  },
  splitIntoChunks: function(data, chunkSize) {
    const chunks = [];
    const byteArray = Array.from(data);
    for (let i = 0; i < byteArray.length; i += chunkSize) {
      const chunkBytes = byteArray.slice(
        i,
        Math.min(i + chunkSize, byteArray.length)
      );
      const instruction = utils_bleCommandSet.getCommand(
        "generateInstructionFromParams",
        chunkBytes,
        chunkBytes.length
      );
      chunks.push(Array.from(instruction.value));
    }
    return chunks;
  },
  startTransfer: function() {
    if (!this.isTransferring || this.currentChunkIndex >= this.chunks.length) {
      return;
    }
    const currentChunk = this.chunks[this.currentChunkIndex];
    this.lastSentChunk = currentChunk;
    this.lastSentIndex = this.currentChunkIndex;
    if (this.onProgress) {
      this.onProgress({
        state: this.transferState,
        progress: this.currentChunkIndex,
        total: this.chunks.length,
        currentChunk
      });
    }
    common_vendor.index.__f__("log", "at utils/writeDataChunked.js:145", "\n=== 正在发送分片 ===");
    const hexString = currentChunk.map((b) => b.toString(16).padStart(2, "0")).join(" ");
    common_vendor.index.__f__(
      "log",
      "at utils/writeDataChunked.js:149",
      `发送分片 ${this.currentChunkIndex + 1}/${this.chunks.length}:`
    );
    common_vendor.index.__f__("log", "at utils/writeDataChunked.js:152", `  十进制: [${currentChunk.join(", ")}]`);
    common_vendor.index.__f__("log", "at utils/writeDataChunked.js:153", `  十六进制: ${hexString}`);
    this.timeoutId = setTimeout(() => {
      common_vendor.index.__f__("error", "at utils/writeDataChunked.js:157", "分片发送超时");
      this.handleError(new Error("分片发送超时"));
    }, 1e4);
    common_vendor.index.__f__("log", "at utils/writeDataChunked.js:162", "蓝牙数据发送:", arrayToArrayBuffer(currentChunk));
    try {
      setTimeout(() => {
        store_bluetooth.store.dispatch("writeData", currentChunk).then(() => {
          common_vendor.index.__f__("log", "at utils/writeDataChunked.js:170", "蓝牙数据发送成功:", arrayToArrayBuffer(currentChunk));
          clearTimeout(this.timeoutId);
        }).catch((error) => {
          common_vendor.index.__f__("error", "at utils/writeDataChunked.js:174", "蓝牙数据发送失败:", error);
          clearTimeout(this.timeoutId);
          this.handleError(error);
        });
      }, 200);
    } catch (error) {
      common_vendor.index.__f__("error", "at utils/writeDataChunked.js:180", "蓝牙数据发送失败:", error);
      clearTimeout(this.timeoutId);
      this.handleError(error);
    }
  },
  handleSuccess: function() {
    if (!this.isTransferring)
      return;
    clearTimeout(this.timeoutId);
    this.timeoutId = null;
    this.currentChunkIndex++;
    this.retryCount = 0;
    if (this.currentChunkIndex >= this.chunks.length) {
      this.sendEndCommand();
    } else {
      this.startTransfer();
    }
  },
  handleError: function(error) {
    if (!this.isTransferring)
      return;
    clearTimeout(this.timeoutId);
    this.timeoutId = null;
    this.retryCount++;
    common_vendor.index.__f__(
      "error",
      "at utils/writeDataChunked.js:209",
      `分片发送失败，重试次数: ${this.retryCount}/${this.maxRetries}`
    );
    if (this.retryCount >= this.maxRetries) {
      this.transferState = TransferState.FAILED;
      this.isTransferring = false;
      common_vendor.index.__f__("error", "at utils/writeDataChunked.js:216", "达到最大重试次数，停止传输");
      if (this.onComplete) {
        this.onComplete({
          state: TransferState.FAILED,
          error: error.message
        });
      }
      this.clearTransferState();
    } else {
      setTimeout(() => {
        this.startTransfer();
      }, 500);
    }
  },
  // 发送结束命令
  sendEndCommand: function() {
    if (this.endCommandSent)
      return;
    common_vendor.index.__f__("log", "at utils/writeDataChunked.js:236", "准备发送结束命令...");
    this.endCommandSent = true;
    const payloadCommand = utils_bleCommandSet.getCommand("bluetoothSendEnd");
    common_vendor.index.__f__("log", "at utils/writeDataChunked.js:240", "结束命令内容:", payloadCommand.value);
    common_vendor.index.__f__("log", "at utils/writeDataChunked.js:243", "所有分片发送完成");
    this.transferState = TransferState.SUCCESS;
    this.isTransferring = false;
    if (this.onComplete) {
      this.onComplete({ state: TransferState.SUCCESS });
    }
    this.clearTransferState();
    common_vendor.index.__f__("log", "at utils/writeDataChunked.js:250", "结束命令发送成功");
    this.endCommandConfirmed = true;
    setTimeout(() => {
      store_bluetooth.store.dispatch("writeData", payloadCommand.value).then(() => {
        common_vendor.index.__f__("log", "at utils/writeDataChunked.js:258", "所有分片发送完成");
        this.transferState = TransferState.SUCCESS;
        this.isTransferring = false;
        if (this.onComplete) {
          this.onComplete({ state: TransferState.SUCCESS });
        }
        this.clearTransferState();
        common_vendor.index.__f__("log", "at utils/writeDataChunked.js:265", "结束命令发送成功");
        this.endCommandConfirmed = true;
      }).catch((err) => {
        common_vendor.index.__f__("error", "at utils/writeDataChunked.js:269", "结束命令发送失败:", err);
        if (this.retryCount < this.maxRetries) {
          this.retryCount++;
          common_vendor.index.__f__("log", "at utils/writeDataChunked.js:272", `重试结束命令 (${this.retryCount}/${this.maxRetries})`);
          setTimeout(() => this.sendEndCommand(), 500);
        } else {
          this.handleError(new Error("结束命令发送失败"));
        }
      });
    }, 500);
  }
};
async function writeDataChunked(data, onProgress, onComplete) {
  try {
    if (transferManager.isTransferring) {
      throw new Error("已有传输在进行中，请等待完成");
    }
    const payloadString = `ssid:${data.wifiInfo.SSID}password:${data.wifiInfo.password}`;
    common_vendor.index.__f__("log", "at utils/writeDataChunked.js:289", `原始字符串: ${payloadString}`);
    const utf8Bytes = encodeUTF8(payloadString);
    common_vendor.index.__f__("log", "at utils/writeDataChunked.js:291", "发送字符串长度为", payloadString.length);
    transferManager.init(utf8Bytes, onProgress, onComplete);
    return { success: true };
  } catch (err) {
    common_vendor.index.__f__("error", "at utils/writeDataChunked.js:298", "分片发送失败:", err);
    if (onComplete) {
      onComplete({
        state: TransferState.FAILED,
        error: err.message
      });
    }
    throw err;
  }
}
exports.TransferState = TransferState;
exports.transferManager = transferManager;
exports.writeDataChunked = writeDataChunked;
//# sourceMappingURL=../../.sourcemap/mp-weixin/utils/writeDataChunked.js.map
