<view class="demo-container"><view class="header"><text class="title">防抖功能演示</text></view><view class="content"><view class="demo-section"><text class="section-title">方法1：状态变量防抖</text><button class="demo-btn" disabled="{{b}}" bindtap="{{c}}">{{a}}</button><text class="demo-desc">使用 saving 状态变量防止重复点击</text></view><view class="demo-section"><text class="section-title">方法2：防抖工具类</text><button class="demo-btn" disabled="{{e}}" bindtap="{{f}}">{{d}}</button><text class="demo-desc">使用 DebounceHelper 类管理多个操作状态</text></view><view class="demo-section"><text class="section-title">方法3：按钮防抖混入</text><button class="demo-btn" disabled="{{h}}" bindtap="{{i}}">{{g}}</button><text class="demo-desc">使用 buttonDebounce 混入</text></view><view class="demo-section"><text class="section-title">方法4：函数防抖</text><button class="demo-btn" bindtap="{{j}}"> 快速点击测试 </button><text class="demo-desc">使用 debounce 函数包装，300ms内只执行一次</text></view><view class="log-section"><text class="section-title">操作日志</text><scroll-view class="log-list" scroll-y><view wx:for="{{k}}" wx:for-item="log" wx:key="b" class="log-item"><text>{{log.a}}</text></view></scroll-view><button class="clear-btn" bindtap="{{l}}">清除日志</button></view></view></view>