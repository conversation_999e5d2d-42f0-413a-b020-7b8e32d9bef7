{"appid": "wxff6c37bedff665b9", "compileType": "miniprogram", "libVersion": "3.8.1", "packOptions": {"ignore": [], "include": []}, "setting": {"coverView": true, "es6": true, "postcss": true, "minified": true, "enhance": true, "showShadowRootInWxmlPanel": true, "packNpmRelationList": [], "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "minifyWXML": true}, "condition": {}, "editorSetting": {"tabIndent": "insertSpaces", "tabSize": 2}}