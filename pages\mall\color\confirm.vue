<template>
	<view class="app-container">
		<view class="content">
			<view class="content fixed-container">
				<!-- 设备信息 -->
				<view class="device-info fixed-content">
					<text class="brand-text">芭佰邑-{{ categoryName }}</text>
					<!-- <text class="offline" v-if="!$store.state.connectedDevice">设备离线</text>
				<text class="online" v-else>设备在线</text> -->
				</view>

				<!-- 相机预览区域 -->
				<view class="camera-preview" @click="takePhoto">
					<view class="camera-icon">
						<text class="emoji-icon">📷</text>
					</view>
					<view class="camera-text">请拍摄实际发色</view>
					<view class="camera-subtext">> 完作品必拍</view>
				</view>

				<!-- 模式选择 -->
				<view class="mode-tabs fixed-content">
					<view class="mode-tab" :class="{ active: mode === 'smart' }" @click="switchMode('smart')">智能模式
					</view>
					<view class="mode-tab" :class="{ active: mode === 'master' }" @click="switchMode('master')">大师模式
					</view>
				</view>

				<!-- 色彩信息与设置 -->

				<view class="color-settings fixed-content">
					<view class="setting-item"
						style="flex-direction: row; justify-content: space-between; align-items: center;">
						<text class="setting-label"
							style="width: 45%; padding-right: 10rpx; box-sizing: border-box;">目标色:
							{{
						colorName }}</text>
						<view v-if="mode === 'master'" class="mode-buttons"
							style="width: 45%; display: flex; border: 1px solid #4285f4; border-radius: 30rpx; overflow: hidden;">
							<view class="mode-button single" :style="{
							flex: 1,
							background: isSingleMode ? '#4285f4' : '#fff',
							color: isSingleMode ? '#fff' : '#4285f4',
							transition: 'all 0.3s ease'
						}" @click="switchToSingleMode">单仓</view>
							<view class="mode-button combo" :style="{
							flex: 1,
							background: !isSingleMode ? '#4285f4' : '#fff',
							color: !isSingleMode ? '#fff' : '#4285f4',
							transition: 'all 0.3s ease'
						}" @click="switchToComboMode">组合</view>
						</view>
					</view>

					<!-- 智能模式设置 -->
					<view v-if="mode === 'smart'" class="mode">
						<!-- 提示窗示例 -->
						<!-- <uni-popup ref="alertDialog" type="dialog">
						<uni-popup-dialog :type="msgType" cancelText="关闭" confirmText="同意" title="通知"
							content="当前调配量超过250g请注意!" @confirm="dialogConfirm" @close="dialogClose"></uni-popup-dialog>
						</uni-popup> -->

						<!-- 输入框 -->
						<uni-easyinput type="digit" v-model="adjustAmount" :min="10" :max="250"
							placeholder="请输入调配量 (10-250g)" @blur="onBlurChange" class="input-slider">
						</uni-easyinput>

						<view class="setting-item">
							<text class="setting-label">设置染膏/双氧比例:</text>
							<!-- 修改比例选项部分 -->
							<view class="ratio-options">
								<view v-for="(ratio, index) in hydrogenPeroxideRatios" :key="index" class="ratio-option"
									:class="{ active: ratioSettings[ratio.key].proportion }"
									@click="selectHydrogenPeroxide(ratio)">
									{{ ratioSettings[ratio.key].proportion ? `${ratio.label} ${ratioSettings[ratio.key].value}g` : ratio.label }}
								</view>

								<MasterColorinput ref="colorModal" :initial-color="hydrogenPeroxideRatios"
									:initial-color-index="index" @confirm="handleHydrogenConfirm" />

							</view>


							<!-- 比例选择弹窗 -->
							<view class="modal-mask" v-if="showRatioModal" @click="hideRatioInput">
								<view class="modal-container" @click.stop>
									<view class="modal-header">请选择 {{ tempSelectedRatio }}双氧比例</view>
									<view class="modal-content">
										<view class="container">
											<CustomPicker :ratioOptions="ratioOptions" :value="currentRatioIndex"
												@change="onRatioChange" />
										</view>
									</view>
									<view class="modal-footer">
										<view class="modal-button confirm-button" @click="confirmRatio">确认</view>
									</view>
								</view>
							</view>
						</view>
					</view>

					<!-- 大师模式设置 -->
					<view v-if="mode === 'master'" class="mode">
						<!-- 弹窗组件 -->
						<MasterColorinput :initial-color="selectedColor" :initial-color-index="selectedColorIndex"
							@confirm="handleColorConfirm" ref="colorModal" />
						<view class="master">
							<scroll-view scroll-y="true" class="master-scroll" style="height: 600rpx;">
								<view class="color-grid">
									<view class="color-item" v-for="(item, index) in colorList" :key="index"
										@click="selectColor(index)">
										<view v-if="item.value" class="color-circle" style="backgroundColor: #4285f4">
											<text class="percentage">{{ item.value }}g</text>
										</view>

										<view v-else class="color-circle" :style="{ backgroundColor: item.color }">
											<text class="percentage">{{ item.value }}%</text>
										</view>
										<text class="color-name">{{ item.name }}</text>
									</view>
								</view>
							</scroll-view>
						</view>
					</view>

					<!-- 染膏碗提示 -->
					<view class="bowl-container fixed-content" v-if="mode === 'smart'">
						<view class="bowl-icon">
							<text class="emoji-icon">🧪</text>
						</view>
						<view class="bowl-text">请放置空染膏碗</view>
					</view>

					<!-- 开始调色按钮 -->
					<view class="start-button fixed-content" @click="startColorMixing">
						开始调色
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import CustomPicker from '@/components/custom-picker.vue'
	import MasterColorinput from '@/components/master-colorinput.vue'
	export default {
		components: {
			CustomPicker,
			MasterColorinput
		},
		data() {
			return {
				// 新增双氧乳比例数据
				hydrogenPeroxideRatios: [{
						key: '3%',
						label: '3%',
						name: '双氧乳3%',
						color: '#FFFFFF',
						fieldname: 'hydrogenPeroxide3Percent',
						isOxidant: true
					},
					{
						key: '6%',
						label: '6%',
						name: '双氧乳6%',
						color: '#FFFFFF',
						fieldname: 'hydrogenPeroxide6Percent',
						isOxidant: true
					},
					{
						key: '9%',
						label: '9%',
						name: '双氧乳9%',
						color: '#FFFFFF',
						fieldname: 'hydrogenPeroxide9Percent',
						isOxidant: true
					},
					{
						key: '12%',
						label: '12%',
						name: '双氧乳12%',
						color: '#FFFFFF',
						fieldname: 'hydrogenPeroxide12Percent',
						isOxidant: true
					}
				],
				msgType: 'info', // 可以是 success/warning/error/info
				isSingleMode: true,
				colorName: '',
				categoryName: '',
				colorHex: '',
				adjustAmount: null, // 滚轮调配量
				selectedRatio: '3%',
				isLoading: false,
				mode: 'smart', // 默认为智能模式
				imageList: [], // 上传的图片列表
				// 以下是智能模式设置
				showPicker: false,
				showRatioModal: false,
				tempSelectedRatio: '', // 就是将中间的3% 6% 9% 12%放在里面
				selectedMixRatio: '1:1', // 默认选择1:1比例
				ratioOptions: ['1:1', '1:1.5', '1:2', '1:2.5', '1:3'], // 比例选项
				sliderValue: 0, // 默认选中第一个
				oxygenAmount: '',
				currentRatioIndex: 0, // 当前选中的索引
				ratioSettings: { // 存储每个百分比对应的设置
					'3%': {
						value: 0,
						proportion: ''
					},
					'6%': {
						value: 0,
						proportion: ''
					},
					'9%': {
						value: 0,
						proportion: ''
					},
					'12%': {
						value: 0,
						proportion: ''
					}
				},
				colorComponents: null,
				developerComponents: {},
				// 以下是大师模式设置
				colorList: [{
						name: '双氧乳3%',
						color: '#FFFFFF',
						value: 0,
						fieldname: 'hydrogenPeroxide3Percent',
						isOxidant: true
					},
					{
						name: '双氧乳6%',
						color: '#FFFFFF',
						value: 0,
						fieldname: 'hydrogenPeroxide6Percent',
						isOxidant: true
					},
					{
						name: '双氧乳9%',
						color: '#FFFFFF',
						value: 0,
						fieldname: 'hydrogenPeroxide9Percent',
						isOxidant: true
					},
					{
						name: '双氧乳12%',
						color: '#FFFFFF',
						value: 0,
						fieldname: 'hydrogenPeroxide12Percent',
						isOxidant: true
					},
					{
						name: '灰色',
						color: '#CCCCCC',
						value: 0,
						fieldname: 'gray',
						isOxidant: false
					},
					{
						name: '绿色',
						color: '#2E7D32',
						value: 0,
						fieldname: 'green',
						isOxidant: false
					},
					{
						name: '黄色',
						color: '#FFD600',
						value: 0,
						fieldname: 'yellow',
						isOxidant: false
					},
					{
						name: '橙色',
						color: '#FFA726',
						value: 0,
						fieldname: 'orange',
						isOxidant: false
					},
					{
						name: '红色',
						color: '#D32F2F',
						value: 0,
						fieldname: 'red',
						isOxidant: false
					},
					{
						name: '紫色',
						color: '#8E24AA',
						fieldname: 'purple',
						value: 0,
						isOxidant: false
					},
					{
						name: '棕色',
						color: '#6D4C41',
						value: 0,
						fieldname: 'brown',
						isOxidant: false
					},
					{
						name: '蓝色',
						color: '#1565C0',
						value: 0,
						fieldname: 'blue',
						isOxidant: false
					},
					{
						name: '自然黑',
						color: '#000000',
						value: 0,
						fieldname: 'black',
						isOxidant: false
					},
					{
						name: '淡化剂',
						color: '#FFFFFF',
						value: 0,
						fieldname: 'faded_bleached',
						isOxidant: false
					},
					{
						name: '灰金色',
						color: '#E0E0E0',
						value: 0,
						fieldname: 'bleached',
						isOxidant: false
					},
					{
						name: '梦幻紫',
						color: '#E1BEE7',
						value: 0,
						fieldname: 'silver',
						isOxidant: false
					},
					{
						name: '浅棕色',
						color: '#6D4C41',
						value: 0,
						fieldname: 'gold',
						isOxidant: false
					},
					{
						name: '深灰亚麻色',
						color: '#B0BEC5',
						value: 0,
						fieldname: 'linen',
						isOxidant: false
					},

					// bleached: '#e8e3d9', // 极浅灰金色 (Very light gray-gold) - 12.11
					// silver: '#d6cadd',   // 梦幻紫 (Dreamy purple) - V10.21
					// gold: '#d1b8a0',     // 浅棕色 (Light brown) - 5.0
					// linen: '#8c837b',      // 深灰亚麻色 (Dark gray linen) - 7.11
				],
				showColorModal: false,
				currentColor: [], // 大师模式现在选择的颜色以及克重
				// colorAmount : '',
				selectedColor: null, // 用于存储选中的颜色
				selectedColorIndex: -1,
				// colorData
			}
		},
		onShow() {
			// 页面显示时检查参数
			if (!this.colorName) {
				this.loadColorInfo();
			}
			// 清掉大师模式的配色
			this.handleReset()

		},
		onLoad(options) {
			// console.log('确认调色页面加载，接收参数:', options);
			this.loadColorInfo(options);
			console.log("配方", this.developerComponents)
			console.log("配方", this.colorComponents)
		},
		mounted() {
			this.ctx = uni.createCameraContext(this); // 创建相机上下文
		},
		methods: {
			// 双氧比例
			handleHydrogenConfirm(res) {
				this.ratioSettings = {
					...this.ratioSettings, // 保留其他属性不变
					[res.color.ratioKey]: {
						// ...this.ratioSettings[res.color.ratioKey], // 保留该属性的其他字段
						proportion: res.color.ratioKey,
						value: res.color.value // 仅更新 value
					}
				};
				this.developerComponents[res.color.fieldname] = res.color.value
				console.log("this.ratioSettings", this.ratioSettings)
				console.log("智能模式选择的双氧乳", res)
			},
			// 新增方法：选择双氧乳比例
			selectHydrogenPeroxide(ratio) {
				// 确保正确构造颜色对象
				const colorData = {
					name: ratio.name,
					color: ratio.color,
					value: this.ratioSettings[ratio.key].value || 0,
					fieldname: ratio.fieldname,
					isOxidant: ratio.isOxidant,
					isHydrogenPeroxide: true,
					ratioKey: ratio.key
				};

				// 打开弹窗
				this.$refs.colorModal.showColorModal(colorData);
			},

			// 修改确认回调方法
			handleColorConfirm(res) {
				if (this.isSingleMode) {
					this.handleReset();
				}

				// 判断是否是双氧乳
				if (res.color.isHydrogenPeroxide) {
					// 更新双氧乳比例设置
					this.ratioSettings[res.color.ratioKey] = {
						value: res.color.value,
						proportion: this.selectedMixRatio
					};

					// 更新developerComponents
					this.developerComponents[res.color.fieldname] = res.color.value;
				} else {
					// 原有颜色处理逻辑
					this.$set(this.colorList, res.colorIndex, {
						...this.colorList[res.colorIndex],
						value: res.color.value
					});
				}
			},
			dialogConfirm() {
				console.log("用户点击了“同意”");
			},
			dialogClose() {
				console.log("用户点击了“关闭”或遮罩层");
			},
			onInputChange(value) {
				// 转换为数字（type="digit" 输入的是字符串）
				const numValue = Number(value);

				// 检查是否为有效数字
				if (isNaN(numValue)) {
					this.adjustAmount = ''; // 非数字则清空（或设为默认值）
					return;
				}

				// 修正超出范围的值
				if (numValue > 250) {
					this.adjustAmount = 250;
				} else if (numValue < 10) {
					this.adjustAmount = 10;
				} else {
					this.adjustAmount = numValue; // 在范围内则正常赋值
				}
				console.log("当前的值为", this.adjustAmount)
			},
			onBlurChange(e) {
				let value = Number(e.detail.value);
				if (value < 10) value = 10;
				if (value > 250) value = 250;
				this.adjustAmount = value;
			},
			onInputBlur() {
				// 确保最终值符合范围
				this.onInputChange(this.adjustAmount);
			},
			// 大师模式页面加载等都把这个值置为0
			handleReset() {
				this.colorList = (this.colorList || []).map(color => ({
					...color,
					value: 0
				}));
			},

			// 单仓模式只有一个
			switchToSingleMode() {
				this.isSingleMode = true
				// 清除大师模式的颜色
				this.handleReset()
			},
			switchToComboMode() {
				this.isSingleMode = false
				// 清除大师模式的颜色
				this.handleReset()
			},
			// 上传拍照
			takePhoto() {
				uni.chooseImage({
					count: 3, // 最多选择3张图片
					sizeType: ['compressed'],
					sourceType: ['album', 'camera'],
					success: (res) => {
						this.imageList = this.imageList.concat(res.tempFilePaths);
						// 这里可以上传图片到服务器
						this.uploadImages(res.tempFilePaths);
					},
					fail: (err) => {
						console.error('选择图片失败：', err);
						uni.showToast({
							title: '选择图片失败',
							icon: 'none'
						});
					}
				});
			},
			//大师模式设置
			selectColor(index) { //点击的是哪一个按钮
				console.log("大师模式点击颜色索引", index)
				const color = this.colorList[index]
				this.selectedColor = color // 弹窗输入颜色克重的颜色值
				this.selectedColorIndex = index // 弹窗输入颜色克重的索引
				console.log("this.selectedColorIndex", this.selectedColorIndex)
				this.$refs.colorModal.showColorModal(color);
				this.$refs.colorModal.updateColorIndex(index);
				this.currentColor = color;
				// this.colorAmount = '';
				// this.showColorModal = true;
				// 将颜色代码清除

			},

			// 染膏输入模式
			inputHydrogenPeroxide() {

			},

			//处理输入的值 单仓模式直接清掉之前输入的配色
			// @parm res.color 颜色类{name: "橙色", color: "#FFA726", value: 0, isOxidant: false}
			// @parm res.amount 输入的克重
			handleColorConfirm(res) {
				// console.log("当前是否是单仓模式",)
				if (this.isSingleMode) {
					console.log("当时是单仓模式")
					this.handleReset()
				}
				// console.log("颜色值",res.color.value)
				// console.log("颜色值索引",res.colorIndex)
				this.$set(this.colorList, res.colorIndex, {
					...this.colorList[res.colorIndex],
					value: res.color.value
				});
			},

			// 以上是大师模式的设置

			// 以下是智能模式设置
			confirmRatio() {
				this.selectedMixRatio = this.ratioOptions[this.sliderValue];

				// 根据选择的currentRatioIndex比例索引到ratioOptions中获取比例
				const selectedMixRatio = this.ratioOptions[this.currentRatioIndex]


				this.ratioSettings[this.tempSelectedRatio].proportion = this.selectedMixRatio;
				// console.log("this.ratioSettings[this.tempSelectedRatio]",selectedMixRatio)


				// 解析混合比例（例如 "1:1" 分解为 [1, 1]）
				const ratioParts = selectedMixRatio.split(':').map(Number);
				const dyeRatio = ratioParts[0]; // 染膏比例
				const oxygenRatio = ratioParts[1]; // 双氧乳比例

				// 计算总比例
				// const totalRatio = dyeRatio + oxygenRatio;
				console.log("染膏重量", this.adjustAmount)
				console.log("染膏比例dyeRatio", dyeRatio)
				console.log("双氧乳比例", oxygenRatio)


				// 计算实际用量
				// 计算出双氧乳使用量
				const actualOxygenAmount = ((this.adjustAmount / dyeRatio) * oxygenRatio)
				console.log("双氧乳比例克重", actualOxygenAmount)

				// 将值放到ratioSettings中
				this.ratioSettings[this.tempSelectedRatio] = {
					value: actualOxygenAmount,
					proportion: selectedMixRatio,
				}

				switch (this.tempSelectedRatio) {
					case '3%':
						this.developerComponents.hydrogenPeroxide3Percent = actualOxygenAmount
						break;
					case '6%':
						this.developerComponents.hydrogenPeroxide6Percent = actualOxygenAmount
						break;
					case '9%':
						this.developerComponents.hydrogenPeroxide9Percent = actualOxygenAmount
						break;
					case '12%':
						this.developerComponents.hydrogenPeroxide12Percent = actualOxygenAmount
						break;
				}

				// 判断是百分之多少的染膏

				// console.log("当前染膏选择", JSON.stringify(this.ratioSettings))

				this.showRatioModal = false;
			},

			hideColorModal() {
				this.showColorModal = false;
			},

			confirmColorAmount() {
				if (!this.colorAmount || isNaN(this.colorAmount) || this.colorAmount <= 0) {
					uni.showToast({
						title: '请输入有效的克数',
						icon: 'none'
					});
					return;
				}

				// 更新颜色列表中的对应颜色
				const index = this.colorList.findIndex(item => item.name === this.currentColor.name);
				if (index !== -1) {
					this.colorList[index].amount = parseFloat(this.colorAmount);
				}

				this.showColorModal = false;
			},

			startColorMixing() {
				let data = []

				// 定义颜色名称和对应的 colorClass 映射
				const colorMap = {
					gray: '灰色',
					green: '绿色',
					yellow: '黄色',
					orange: '橙色',
					red: '红色',
					purple: '紫色',
					brown: '棕色',
					blue: '蓝色',
					black: '黑色',
					faded_bleached: '淡化剂',

					bleached: '极浅灰金色', // 12.11 (用bleached字段对应) 11号仓位
					silver: '梦幻紫', // V10.21 (用silver字段对应) 12号仓位
					gold: '浅棕色', // 5.0 (用gold字段对应) 13号仓位
					linen: '深灰亚麻色' // 7.11 (原字段) 14号仓位

				};

				// 定义颜色代码映射
				const colorCodeMap = {
					gray: '#808080',
					green: '#008000',
					yellow: '#FFFF00',
					orange: '#FFA500',
					red: '#FF0000',
					purple: '#800080',
					brown: '#A52A2A',
					blue: '#0000FF',
					black: '#000000',
					faded_bleached: '#F5F5F5', // 浅灰色模拟淡化剂
					white: '#F5F5F5', // 双氧乳颜色

					bleached: '#e8e3d9', // 极浅灰金色 (Very light gray-gold) - 12.11
					silver: '#d6cadd', // 梦幻紫 (Dreamy purple) - V10.21
					gold: '#d1b8a0', // 浅棕色 (Light brown) - 5.0
					linen: '#8c837b', // 深灰亚麻色 (Dark gray linen) - 7.11

					hydrogenPeroxide3Percent: '#F5F5F5',
					hydrogenPeroxide6Percent: '#F5F5F5',
					hydrogenPeroxide9Percent: '#F5F5F5',
					hydrogenPeroxide12Percent: '#F5F5F5'
				};

				let encodedData;
				let developerComponents;
				let encodedColorComponents;
				let encodedDeveloperComponents;
				let model;

				if (this.mode === 'smart') {
					// 或使用函数式编程
					const proportion = Object
						.values(this.colorComponents || {})
						.reduce((sum, val) => sum + (typeof val === 'number' ? val : 0), 0);
					console.log("当前配方颜色总数是", proportion)

					const multiple = (this.adjustAmount || 0) / proportion

					// 转换 转换视图对象
					const colorComponentsArray = [];
					for (const [key, value] of Object.entries(this.colorComponents)) {
						if (value !== null && value !== undefined && value !== 0) {
							colorComponentsArray.push({
								name: colorMap[key] || key,
								value: multiple ? Number((value * multiple).toFixed(2)) : value,
								color: colorCodeMap[key] || '#000000',
								used: 0,
							});
						}
					}

					const adjustedColorComponents = {};

					// 先复制原始对象的所有属性（保留未修改的值）
					if (this.colorComponents) {
						Object.keys(this.colorComponents).forEach(key => {
							adjustedColorComponents[key] = this.colorComponents[key];
						});
					}

					// if(multiple && this.colorComponents) {
					// 处理 this.colorComponents
					if (this.colorComponents && typeof this.colorComponents === 'object') {
						for (const key in this.colorComponents) {
							if (typeof this.colorComponents[key] === 'number' && multiple) {
								adjustedColorComponents[key] = multiple ? parseFloat((this.colorComponents[key] * multiple)
									.toFixed(2)) : this.colorComponents[key]; // 直接修改原对象的数值
							}
						}
					}
					// }

					const multipleDeveloper = (this.adjustAmount / 4) || 0

					// 转换 developerComponents（双氧乳）
					const developerComponentsArray = [];

					const adjustedDeveloperComponents = {};
					console.log("准备拷贝对象",this.developerComponents)
					if (this.developerComponents && typeof this.developerComponents === 'object') {
						for (const key in this.developerComponents) {
							if (typeof this.developerComponents[key] === 'number') {
								adjustedDeveloperComponents[key] =  
								  this.developerComponents[key] ? 
								  parseFloat(this.developerComponents[key]) : 0
							}
						}
					}
					
					console.log("当前adjustedDeveloperComponents",adjustedDeveloperComponents)

					if (adjustedDeveloperComponents && typeof adjustedDeveloperComponents === 'object') {
						// 情况1：存在 developerComponents，且是非空对象
						const hasValidData = Object.entries(adjustedDeveloperComponents).some(
							([_, value]) => value !== null && value !== undefined && value !== 0
						);

						if (hasValidData) {
							console.log("当前双氧占比", adjustedDeveloperComponents)
							// if (!adjustedDeveloperComponents.hydrogenPeroxide6Percent) {
							// 	adjustedDeveloperComponents.hydrogenPeroxide6Percent =
							// 		typeof this.adjustAmount === 'number' ?
							// 		Number(this.adjustAmount.toFixed(2)) :
							// 		0;
							// } else {
							// 	adjustedDeveloperComponents.hydrogenPeroxide6Percent =
							// 		Number(adjustedDeveloperComponents.hydrogenPeroxide6Percent.toFixed(2));
							// }
							
							
							console.log("当前染膏是一个非空对象", hasValidData)
							// 遍历有效数据
							for (const [key, value] of Object.entries(adjustedDeveloperComponents)) {
								if (value !== null && value !== undefined && value !== 0) {
									const percent = key.replace('hydrogenPeroxide', '').replace('Percent', '');
									console.log("percent",percent)
									// if (percent !== 6) {
										developerComponentsArray.push({
											name: `双氧乳${percent}%VOL`,
											value: parseFloat(value.toFixed(2)), // 保留2位小数
											color: colorCodeMap.white,
											used: 0,
										});
									// }
								}
							}

						} else {
							[6].forEach(percent => {
								if (this.adjustAmount) {
									developerComponentsArray.push({
										name: `双氧乳${percent}%VOL`,
										value: multipleDeveloper ? parseFloat(this.adjustAmount) :
										0, // 处理可能的 undefined
										color: colorCodeMap.white,
										used: 0,
									});
								}

							});
							adjustedDeveloperComponents.hydrogenPeroxide6Percent = parseFloat(this.adjustAmount) || 0;
						}
					} else {
						// 情况3：this.developerComponents 不存在或不是对象，添加默认配置
						[6].forEach(percent => {
							if (this.adjustAmount) {
								developerComponentsArray.push({
									name: `双氧乳${percent}%VOL`,
									value: multipleDeveloper ? parseFloat(this.adjustAmount) :
									0, // 处理可能的 undefined
									color: colorCodeMap.white,
									used: 0,
								});
							}

						});

						// 将双氧乳重新赋值
						adjustedDeveloperComponents.hydrogenPeroxide6Percent = parseFloat(this.adjustAmount) || 0;
					}

					// 在 developerComponentsArray 创建完成后，添加排序逻辑：
					developerComponentsArray.sort((a, b) => {
						// 从名称中提取百分比数字（如 "双氧乳6%VOL" → 6）
						const percentA = parseInt(a.name.match(/\d+/)[0]);
						const percentB = parseInt(b.name.match(/\d+/)[0]);
						return percentA - percentB; // 从小到大排序
					});

					// 合并颜色数据
					const data = [...colorComponentsArray, ...developerComponentsArray];
					// console.log("colorComponentsArray",colorComponentsArray)
					// console.log('转换后的 colorData:', data);


					// colorComponents: null,
					// developerComponents: null,
					// console.log("准备跳转 encodedData:", encodedData);
					const dataString = JSON.stringify(data);
					encodedData = encodeURIComponent(dataString);

					// console.log("数据",dataString)


					// 2. 再处理双氧乳

					// 处理可能为 null 的情况
					// const colorComponents = this.colorComponents ? JSON.stringify(this.colorComponents) : 'null';
					// const developerComponents = this.developerComponents ? JSON.stringify(this.developerComponents) :
					// 	'null';
					const colorComponents = adjustedColorComponents ? JSON.stringify(adjustedColorComponents) : 'null';
					const developerComponents = adjustedDeveloperComponents ? JSON.stringify(adjustedDeveloperComponents) :
						'null';

					// if (colorComponents && typeof colorComponents === 'object') {
					// 	for (const key in colorComponents) {
					// 		if (typeof colorComponents[key] === 'number') {
					// 			if(multiple) {
					// 				colorComponents[key] *= multiple;
					// 			}
					// 			 // 直接修改原对象的值
					// 		}
					// 	}
					// }

					console.log("生成指令前置对象染膏", colorComponents)
					console.log("生成指令前置对象双氧乳", developerComponents)

					encodedColorComponents = encodeURIComponent(colorComponents);
					encodedDeveloperComponents = encodeURIComponent(developerComponents);
					model = encodeURIComponent("smart")
				} else if (this.mode === 'master') {

					data = (this.colorList || [])
						.filter(color => color && color.value > 0)
						.map(color => ({
							name: color.name,
							color: colorCodeMap[color.fieldname] || '#F5F5F5',
							value: color.value,
							used: 0
						}))

					console.log("页面显示的data", data)
					// console.log("大师模式需要发送", this.colorList)

					const result = this.colorList
						.filter(item => item.value !== 0)
						.reduce((acc, item) => {
							acc[item.fieldname] = item.value;
							return acc;
						}, {});

					const colorComponentsArray = [];
					for (const [key, value] of Object.entries(result)) {
						if (value !== null && value !== undefined && value !== 0) {
							colorComponentsArray.push({
								name: colorMap[key] || key,
								value: value,
								color: colorCodeMap[key] || '#000000',
								used: 0,
							});
						}
					}

					// console.log("数据", colorComponentsArray)

					JSON.stringify(colorComponentsArray)

					const dataString = JSON.stringify(data);
					encodedData = encodeURIComponent(dataString);

					const colorComponents = result ? JSON.stringify(result) : 'null';
					encodedColorComponents = encodeURIComponent(colorComponents);
					model = encodeURIComponent("master")
				}

				// console.log("encodeData",encodedData)
				// console.log("准备发送的数据", encodedDeveloperComponents);
				// console.log("this.developerComponents", this.developerComponents);


				// 跳转到配方制作页面
				uni.navigateTo({
					url: `/pages/mall/formula-making/formula-making?data=${encodedData}` +
						`&colorComponents=${encodedColorComponents}` +
						`&developerComponents=${encodedDeveloperComponents}` +
						`&model=${model}`,
					success: () => {
						console.log('跳转到配方制作页面成功');
					},
					fail: (err) => {
						console.error('跳转失败:', err);
						uni.showToast({
							title: '跳转失败',
							icon: 'none'
						});
					}
				});
			},

			onRatioChange(e) {
				console.log(e)
				this.currentRatioIndex = e.detail.value
				// 其他处理逻辑

			},
			loadColorInfo(options) {
				// 从URL参数获取颜色信息
				if (options) {
					// 处理模式参数
					if (options.mode === 'master') {
						this.mode = 'master';
						console.log('设置为大师模式');
					} else {
						this.mode = 'smart';
					}

					if (options.name || options.category || options.color) {
						try {
							this.colorName = decodeURIComponent(options.name || '未知颜色');
							this.categoryName = decodeURIComponent(options.category || '生活色');
							this.colorHex = decodeURIComponent(options.color || '#000000');
							console.log('从URL参数获取数据成功:', this.colorName, this.categoryName, this.colorHex);
						} catch (e) {
							console.error('URL参数解析错误:', e);
							this.loadFromStorage();
						}
						return;
					}
				}

				this.loadFromStorage();
			},

			loadFromStorage() {
				// 从缓存中读取颜色信息
				try {
					const dyeColorInfo = uni.getStorageSync('dyeColorInfo');
					console.log('从缓存读取到的数据:', dyeColorInfo);

					if (dyeColorInfo) {
						this.colorName = dyeColorInfo.name || '未知颜色';
						this.categoryName = dyeColorInfo.category || '生活色';
						this.colorHex = dyeColorInfo.hex || '#000000';
						this.colorComponents = dyeColorInfo.colorComponents
						// this.developerComponents = dyeColorInfo.developerComponents
						this.developerComponents = {};
						// 处理模式
						if (dyeColorInfo.mode === 'master') {
							this.mode = 'master';
							console.log('从缓存设置为大师模式');
						} else {
							this.mode = 'smart';
						}

						console.log('从缓存读取数据成功:', this.colorName, this.categoryName, this.colorHex, this.mode);
					} else {
						console.error('缓存中无法获取颜色信息');
						// 尝试用默认值
						this.colorName = 'X深紫色';
						this.categoryName = '生活色';
						this.colorHex = '#5733FF';
						console.log('使用默认颜色数据');

						uni.showToast({
							title: '加载失败，使用默认色',
							icon: 'none'
						});
					}
				} catch (e) {
					console.error('读取缓存失败:', e);

					// 出错时使用默认值
					this.colorName = 'X深紫色';
					this.categoryName = '生活色';
					this.colorHex = '#5733FF';
					console.log('异常情况，使用默认颜色数据');
				}
			},

			goBack() {
				uni.navigateBack();
			},

			onSliderChange(e) {
				this.adjustAmount = e.detail.value;
				console.log('调整量设置为:', this.adjustAmount);
			},

			onRatioSliderChange(e) {
				const index = e.detail.value;
				this.sliderValue = index;
				this.selectedMixRatio = this.ratioOptions[index];
				console.log("比例选择:", this.selectedMixRatio);
			},

			onRatioSliderChanging(e) {
				// 滑动过程中实时更新
				const index = e.detail.value;
				this.sliderValue = index;
			},

			showRatioInput(ratio) {
				this.tempSelectedRatio = ratio;
				this.sliderValue = this.ratioOptions.indexOf(this.selectedMixRatio);
				if (this.sliderValue === -1) this.sliderValue = 0;
				this.showRatioModal = true;
			},

			hideRatioInput() {
				this.showRatioModal = false;
			},

			// confirmRatio() {
			//   this.selectedRatio = this.tempSelectedRatio;
			//   this.selectedMixRatio = this.ratioOptions[this.sliderValue];

			//   // 保存设置到对应的百分比
			//   this.ratioSettings[this.tempSelectedRatio] = this.selectedMixRatio;

			//   // 计算实际用量
			//   const ratioValue = parseFloat(this.selectedRatio); // 获取百分比数值（去掉%符号）
			//   const actualAmount = (this.adjustAmount * ratioValue / 100).toFixed(1); // 计算实际用量并保留一位小数

			//   this.showRatioModal = false;

			//   console.log("当前填入的染膏",JSON.stringify(this.ratioSettings))

			//   // console.log("双氧占比", this.selectedMixRatio);
			//   // console.log("当前所有比例设置:", this.ratioSettings);
			//   // console.log("实际用量:", actualAmount);

			//   uni.showToast({
			//     title: `已设置 ${this.selectedRatio} 双氧，混合比例 ${this.selectedMixRatio}\n实际用量: ${actualAmount}g`,
			//     icon: 'success',
			//     duration: 3000 // 延长显示时间以便查看完整信息
			//   });
			// },

			// startColorMixing() {
			// 	if (this.isLoading) return;

			// 	this.isLoading = true;
			// 	// 模拟调色过程
			// 	uni.showLoading({
			// 		title: '调色中...'
			// 	});

			// 	setTimeout(() => {
			// 		uni.hideLoading();
			// 		this.isLoading = false;

			// 		uni.showModal({
			// 			title: '调色完成',
			// 			content: `已成功为您调配${this.colorName}，请取走染膏碗进行使用`,
			// 			showCancel: false,
			// 			success: () => {
			// 				// 返回颜色详情页
			// 				uni.navigateBack();
			// 			}
			// 		});
			// 	}, 2000);
			// },

			switchMode(mode) {
				this.mode = mode;
				console.log('切换到模式:', mode);
			},

			// 上传图片到服务器
			uploadImages(tempFilePaths) {
				console.log('准备上传图片:', tempFilePaths);

				// 这里可以实现图片上传逻辑
				// 例如上传到服务器或保存到本地存储
				tempFilePaths.forEach((filePath, index) => {
					console.log(`图片${index + 1}:`, filePath);

					// 示例：可以在这里调用上传API
					// uni.uploadFile({
					//     url: 'https://your-server.com/upload',
					//     filePath: filePath,
					//     name: 'file',
					//     success: (uploadFileRes) => {
					//         console.log('上传成功:', uploadFileRes);
					//     },
					//     fail: (err) => {
					//         console.error('上传失败:', err);
					//     }
					// });
				});

				uni.showToast({
					title: `已选择${tempFilePaths.length}张图片`,
					icon: 'success'
				});
			}
		}
	}
</script>

<style lang="scss" scoped>
	/* 添加页面级别样式，修复右侧空白问题 */
	page {
		width: 100vw;
		height: 100vh;
		overflow: hidden;
		padding: 0;
		margin: 0;
		box-sizing: border-box;
		background-color: #f5f5f5;
		overscroll-behavior: none;
		position: fixed;
		left: 0;
		right: 0;
		top: 0;
		bottom: 0;
	}

	/* 补充uni-app全局样式，确保内容不超出屏幕 */
	:root,
	uni-page,
	uni-page-head,
	uni-page-wrapper,
	uni-page-body,
	uni-app,
	uni-window {
		width: 100vw;
		max-width: 100vw;
		min-width: 100vw;
		box-sizing: border-box;
		overflow-x: hidden;
		margin: 0;
		padding: 0;
	}

	/* 固定宽度防止溢出 */
	view {
		max-width: 100vw;
		box-sizing: border-box;
	}

	.app-container {
		width: 100vw;
		max-width: 100vw;
		margin: 0;
		padding: 0;
		overflow: hidden;
		position: relative;
		height: 100vh;
		box-sizing: border-box;
		left: 0;
		right: 0;
	}

	.fixed-container {
		width: 100vw;
		max-width: 100vw;
		left: 0;
		position: relative;
		/* 改为相对定位 */
		right: 0;
		position: absolute;
	}

	.fixed-content {
		width: 100%;
		max-width: 100vw;
		box-sizing: border-box;
		left: 0;
		right: 0;
		padding-left: 0;
		padding-right: 0;
		margin-left: 0;
		margin-right: 0;
	}

	.content {
		display: flex;
		flex-direction: column;
		min-height: 100vh;
		width: 100vw;
		background-color: #f5f5f5;
		padding-bottom: 30rpx;
		box-sizing: border-box;
		overflow-x: hidden;
		overflow-y: auto;
		/* 添加垂直滚动 */
		position: relative;
		left: 0;
		right: 0;
	}


	/* 确保设备信息和模式标签固定在顶部 */
	.device-info,
	.mode-tabs {
		position: sticky;
		top: 0;
		z-index: 100;
		background-color: #fff;
	}

	.master-scroll {
		max-height: 480rpx;
		margin-top: 20rpx;
	}


	.header {
		display: flex;
		align-items: center;
		justify-content: space-between;
		background-color: #4285f4;
		color: #fff;
		padding: 20rpx 30rpx;
		position: sticky;
		top: 0;
		z-index: 100;
		width: 100%;
		box-sizing: border-box;
	}

	.back-btn {
		width: 60rpx;
		height: 60rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.back-icon {
		font-size: 40rpx;
	}

	.title {
		font-size: 36rpx;
		font-weight: 500;
	}

	.header-icons {
		display: flex;
		align-items: center;
	}

	.header-icon {
		margin-left: 20rpx;
		font-size: 30rpx;
	}

	.device-info {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 20rpx 30rpx;
		background-color: #fff;
		border-bottom: 1rpx solid #f0f0f0;
		width: 100%;
		box-sizing: border-box;
	}

	.brand-text {
		font-size: 28rpx;
		color: #333;
	}

	.camera-preview {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		margin: 40rpx auto;
		width: 270rpx;
		height: 270rpx;
		border-radius: 50%;
		background-color: #fff;
		flex-shrink: 0;
		/* 防止被压缩 */
		box-shadow: 0 0 30rpx rgba(0, 0, 0, 0.1);
	}

	.camera-icon {
		margin-bottom: 10rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.emoji-icon {
		font-size: 80rpx;
		color: #4285f4;
	}

	.camera-text {
		font-size: 28rpx;
		color: #333;
		margin-bottom: 5rpx;
	}

	.camera-subtext {
		font-size: 24rpx;
		color: #666;
	}

	.mode-tabs {
		display: flex;
		width: 100%;
		background-color: #fff;
		padding: 10rpx 30rpx;
		margin-bottom: 20rpx;
		box-sizing: border-box;
		border-radius: 10rpx;
	}

	.mode-tab {
		flex: 1;
		text-align: center;
		padding: 20rpx 0;
		font-size: 30rpx;
		color: #666;
		border-radius: 10rpx;
		transition: all 0.3s ease;
		position: relative;
		margin: 0 10rpx;
	}

	.mode-tab.active {
		background-color: #4285f4;
		color: #fff;
		font-weight: 500;
	}

	.mode-tab:not(.active) {
		background-color: #f5f5f5;
	}

	.color-settings {
		flex: 1;
		/* 占据剩余空间 */
		overflow-y: auto;
		/* 允许内部滚动 */
		min-height: 300rpx;
		/* 确保有足够高度 */

		background-color: #fff;
		padding: 30rpx;
		margin-bottom: 20rpx;
		width: 100%;
		box-sizing: border-box;
	}

	.setting-item {
		margin-bottom: 30rpx;
		display: flex;
		flex-direction: column;
		width: 100%;
	}

	.setting-label {
		font-size: 30rpx;
		color: #333;
		margin-bottom: 20rpx;
		display: flex;
		align-items: center;
	}

	.color-preview {
		width: 40rpx;
		height: 40rpx;
		border-radius: 50%;
		margin-left: 10rpx;
		border: 1px solid #ddd;
	}

	.color-circle {
		margin-left: 10rpx;
		border: 1px solid #ddd;
	}

	.slider-with-value {
		display: flex;
		align-items: center;
		width: 100%;
	}

	.slider-value {
		margin-left: 10rpx;
		font-size: 28rpx;
		color: #4285f4;
		font-weight: bold;
		min-width: 40rpx;
		text-align: center;
	}

	.online {
		color: blue;
		font-size: 28rpx;
		margin-left: 20rpx;
	}

	.offline {
		color: #625f5f;
		font-size: 28rpx;
		margin-left: 20rpx;
		margin-top: 10rpx;
	}

	.slider {
		width: 100%;
		margin: 10rpx 0;
	}

	.ratio-options {
		display: flex;
		justify-content: space-between;
		margin-top: 20rpx;
		width: 100%;
	}

	.ratio-option {
		flex: 1;
		text-align: center;
		padding: 15rpx 0;
		margin: 0 10rpx;
		background-color: #f5f5f5;
		border-radius: 10rpx;
		font-size: 28rpx;
		color: #333;
	}

	.ratio-option.active {
		background-color: #4285f4;
		color: #fff;
	}

	.bowl-container {
		display: flex;
		flex-direction: column;
		align-items: center;
		margin: 60rpx 0;
		width: 100%;
		height: 100rpx;
		box-sizing: border-box;
	}

	.bowl-icon {
		margin-bottom: 10rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.bowl-text {
		font-size: 30rpx;
		color: #666;
	}

	.start-button {
		background-color: #4285f4;
		color: #fff;
		text-align: center;
		padding: 25rpx 0;
		// margin: 20rpx 30rpx;
		border-radius: 40rpx;
		font-size: 32rpx;
		font-weight: 500;
		width: calc(100% - 60rpx);
		box-sizing: border-box;
	}

	/* 确保所有内容区块占满整个宽度 */
	.device-info,
	.mode-tabs,
	.color-settings,
	.bowl-container,
	.start-button {
		width: 100%;
		box-sizing: border-box;
		max-width: 100vw;
		left: 0;
		right: 0;
	}

	/* 调整底部按钮容器 */
	.start-button-container {
		position: sticky;
		bottom: 0;
		background-color: #f5f5f5;
		padding: 20rpx 0;
		z-index: 100;
	}

	/* 改进圆形相机预览区域的居中显示 */
	.camera-preview {
		margin: 40rpx auto;
		width: 320rpx;
		height: 320rpx;
	}

	/* 添加弹窗样式 */
	.modal-mask {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: rgba(0, 0, 0, 0.5);
		display: flex;
		align-items: center;
		/* 垂直居中 */
		justify-content: center;
		/* 水平居中 */
		z-index: 1000;
		height: 100rpx
			/* 不要设置 ，否则遮罩会失效 */
	}

	.modal-container {
		width: 80%;
		background-color: #fff;
		border-radius: 16rpx;
		overflow: hidden;
	}

	.modal-header {
		padding: 30rpx;
		font-size: 32rpx;
		font-weight: bold;
		text-align: center;
		background-color: #f5f5f5;
		border-bottom: 1rpx solid #eee;
	}

	.modal-content {
		padding: 30rpx;
	}

	.modal-text {
		font-size: 28rpx;
		margin-bottom: 20rpx;
		text-align: center;
		display: block;
	}

	.modal-footer {
		display: flex;
		border-top: 1rpx solid #eee;
	}

	.modal-button {
		flex: 1;
		padding: 25rpx;
		text-align: center;
		font-size: 30rpx;
	}

	.confirm-button {
		color: #4285f4;
		font-weight: bold;
	}

	/* 比例选择器样式 */
	.slider-container {
		margin: 30rpx 0;
		padding: 0 20rpx;
	}

	.ratio-slider {
		width: 100%;
	}

	.ratio-labels {
		display: flex;
		justify-content: space-between;
		margin-top: 10rpx;
	}

	.ratio-label {
		font-size: 24rpx;
		color: #666;
	}

	.ratio-desc {
		margin-top: 30rpx;
		padding: 20rpx;
		background-color: #f9f9f9;
		border-radius: 8rpx;
	}

	.desc-title {
		font-size: 26rpx;
		color: #333;
		font-weight: bold;
		display: block;
		margin-bottom: 10rpx;
	}

	.desc-text {
		font-size: 24rpx;
		color: #666;
		display: block;
		margin: 5rpx 0;
	}

	.mode {
		margin-top: 20rpx;
	}

	// 大师模式样式
	.master {
		padding: 20rpx;
	}

	.color-grid {
		display: flex;
		flex-wrap: wrap;
		justify-content: flex-start
	}

	.color-item {
		width: 25%;
		display: flex;
		flex-direction: column;
		align-items: center;
		margin-bottom: 20rpx;
	}

	.color-circle {
		width: 100rpx;
		height: 100rpx;
		border-radius: 50%;
		display: flex;
		justify-content: center;
		align-items: center;
		position: relative;
		border: 2rpx solid #f0f0f0;
	}

	.percentage {
		color: #333;
		font-size: 24rpx;
	}

	.color-name {
		margin-top: 10rpx;
		font-size: 24rpx;
		color: #333;
	}

	// 大师模式选择克重弹窗
	.amount-input {
		width: 100%;
		padding: 20rpx;
		border: 1rpx solid #ddd;
		border-radius: 8rpx;
		margin-top: 20rpx;
		box-sizing: border-box;
	}

	.selected-color {
		color: #1565C0 !important;
		/* 明显的蓝色 */
		font-weight: bold;
	}

	.mode-buttons {
		display: flex;
		justify-content: space-between;
		width: 38%;
	}

	.mode-button {
		padding: 8rpx 12rpx;
		border-radius: 8rpx;
		font-size: 24rpx;
		text-align: center;
		border: 1rpx solid #ddd;

		&.single {
			background-color: #f5f5f5;
			color: #333;

			&.active {
				background-color: #4285f4;
				color: white;
				border-color: #4285f4;
			}
		}

		&.combo {
			background-color: #f5f5f5;
			color: #333;

			&.active {
				background-color: #34a853;
				color: white;
				border-color: #34a853;
			}
		}
	}

	/* 新增的样式代码（添加到原有样式表的最后） */
	.setting-item {
		.setting-label {
			width: 65% !important;
			/* 覆盖内联样式 */
			padding-right: 20rpx;
			box-sizing: border-box;
		}

		.mode-buttons {
			width: 30% !important;
			/* 覆盖内联样式 */
			display: flex !important;
			border: 1rpx solid #4285f4 !important;
			border-radius: 30rpx !important;
			overflow: hidden !important;
			height: 60rpx;

			.mode-button {
				flex: 1;
				display: flex;
				align-items: center;
				justify-content: center;
				font-size: 26rpx;
				transition: all 0.3s ease;

				&.single {
					background-color: v-bind('isSingleMode ? "#4285f4" : "#fff"');
					color: v-bind('isSingleMode ? "#fff" : "#4285f4"');
				}

				&.combo {
					background-color: v-bind('!isSingleMode ? "#4285f4" : "#fff"');
					color: v-bind('!isSingleMode ? "#fff" : "#4285f4"');
				}
			}
		}
	}


	/* 优化动画 */
	@keyframes slide-up {
		from {
			transform: translateY(100%);
			opacity: 0;
		}

		to {
			transform: translateY(0);
			opacity: 1;
		}
	}
</style>