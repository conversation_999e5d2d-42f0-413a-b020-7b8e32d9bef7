"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_request = require("../../utils/request.js");
const _sfc_main = {
  data() {
    return {
      // 基本信息
      colorType: "",
      deviceStatus: false,
      // 颜色信息
      colorDisplayName: "",
      colorHex: "",
      colorCode: "",
      // 图片
      beforeImage: "",
      afterImage: "",
      // 颜色构造
      colorComponents: [],
      // 交互数据
      isLiked: false,
      likeCount: 0,
      isStarred: false,
      starCount: 0,
      shareCount: 1,
      // 原始记录数据
      recordData: null
    };
  },
  onLoad(options) {
    common_vendor.index.__f__("log", "at pages/my/actual-effect.vue:113", "实际效果页面加载，参数:", options);
    if (options.recordId) {
      this.loadRecordData(options.recordId);
    } else {
      try {
        const cachedData = common_vendor.index.getStorageSync("shareRecordData");
        if (cachedData) {
          common_vendor.index.__f__("log", "at pages/my/actual-effect.vue:123", "当前调色记录", cachedData);
          this.initializeData(cachedData);
          common_vendor.index.__f__("log", "at pages/my/actual-effect.vue:125", "从缓存读取分享数据成功");
        } else {
          common_vendor.index.__f__("error", "at pages/my/actual-effect.vue:127", "无法获取记录数据");
          common_vendor.index.showToast({
            title: "数据加载失败",
            icon: "none"
          });
        }
      } catch (e) {
        common_vendor.index.__f__("error", "at pages/my/actual-effect.vue:134", "读取缓存失败:", e);
      }
    }
    this.loadDeviceStatus();
  },
  methods: {
    loadDeviceStatus() {
      const app = getApp();
      this.deviceStatus = app.globalData.deviceStatus;
    },
    // 初始化数据
    initializeData(record) {
      this.recordData = record;
      this.colorDisplayName = record.colorName || "未知颜色";
      this.colorHex = record.colorHex || "#000000";
      this.colorCode = record.colorCode || "";
      this.colorType = record.colorType || "未知分类";
      if (record.beforeImageUrl) {
        this.beforeImage = record.beforeImageUrl;
      } else if (record.beforeImage) {
        this.beforeImage = record.beforeImage;
      } else if (record.photoUrl) {
        this.beforeImage = record.photoUrl;
      }
      if (record.afterImageUrl) {
        this.afterImage = record.afterImageUrl;
      } else if (record.afterImage) {
        this.afterImage = record.afterImage;
      }
      this.parseColorComponents(record);
      if (record.deviceCode) {
        this.deviceName = `芭佰邑-${record.deviceCode}`;
        this.deviceStatus = true;
      }
    },
    // 解析颜色构造
    parseColorComponents(record) {
      try {
        const colorFieldOrder = [
          "gray",
          "green",
          "yellow",
          "orange",
          "red",
          "purple",
          "brown",
          "blue",
          "black",
          "faded_bleached",
          "bleached",
          "silver",
          "gold",
          "linen",
          "custom1",
          "custom2",
          "custom3",
          "custom4"
        ];
        const colorMap = {
          red: {
            name: "红色",
            color: "#E74C3C"
          },
          blue: {
            name: "蓝色",
            color: "#3498DB"
          },
          gold: {
            name: "浅棕色",
            color: "#F1C40F"
          },
          gray: {
            name: "灰色",
            color: "#95A5A6"
          },
          black: {
            name: "黑色",
            color: "#2C3E50"
          },
          brown: {
            name: "棕色",
            color: "#A04000"
          },
          green: {
            name: "绿色",
            color: "#2ECC71"
          },
          linen: {
            name: "深灰亚麻色",
            color: "#D7DBDD"
          },
          orange: {
            name: "橙色",
            color: "#E67E22"
          },
          purple: {
            name: "紫色",
            color: "#9B59B6"
          },
          silver: {
            name: "梦幻紫",
            color: "#AEB6BF"
          },
          yellow: {
            name: "黄色",
            color: "#F4D03F"
          },
          custom1: {
            name: "自定义1",
            color: "#EC7063"
          },
          custom2: {
            name: "自定义2",
            color: "#5DADE2"
          },
          custom3: {
            name: "自定义3",
            color: "#58D68D"
          },
          custom4: {
            name: "自定义4",
            color: "#F7DC6F"
          },
          bleached: {
            name: "极浅灰金色",
            color: "#FDFEFE"
          },
          faded_bleached: {
            name: "褪色剂",
            color: "#EAEDED"
          }
        };
        const developerMap = {
          hydrogenPeroxide3Percent: {
            name: "3%双氧水",
            color: "#EAEDED"
          },
          hydrogenPeroxide6Percent: {
            name: "6%双氧水",
            color: "#EAEDED"
          },
          hydrogenPeroxide9Percent: {
            name: "9%双氧水",
            color: "#EAEDED"
          },
          hydrogenPeroxide12Percent: {
            name: "12%双氧水",
            color: "#EAEDED"
          }
        };
        const components = [];
        const colorData = record.colorComponents ? JSON.parse(record.colorComponents) : {};
        colorFieldOrder.forEach((field) => {
          if (colorData[field] && colorData[field] > 0 && colorMap[field]) {
            components.push({
              name: colorMap[field].name,
              amount: `${colorData[field]}g`,
              color: colorMap[field].color,
              isDeveloper: false
            });
          }
        });
        if (record.developerComponents) {
          const developerData = JSON.parse(record.developerComponents);
          for (const [key, value] of Object.entries(developerData)) {
            if (value && value > 0 && developerMap[key]) {
              components.push({
                name: developerMap[key].name,
                amount: `${value}ml`,
                color: developerMap[key].color,
                isDeveloper: true
                // 标记为双氧水成分
              });
            }
          }
        }
        const sortedComponents = [
          ...components.filter((item) => !item.isDeveloper),
          ...components.filter((item) => item.isDeveloper)
        ];
        this.colorComponents = sortedComponents;
        common_vendor.index.__f__("log", "at pages/my/actual-effect.vue:331", "颜色构造", sortedComponents);
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/my/actual-effect.vue:334", "解析颜色构造失败:", error);
        this.colorComponents = this.generateColorComponents(this.colorCode, this.getColorInfo(this.colorCode));
      }
    },
    // 获取颜色信息
    getColorInfo(colorCode) {
      const colorMap = {
        "0x30": {
          name: "灰色",
          hex: "#808080"
        },
        "0x31": {
          name: "绿色",
          hex: "#00FF00"
        },
        "0x32": {
          name: "黄色",
          hex: "#FFFF00"
        },
        "0x33": {
          name: "橙色",
          hex: "#FFA500"
        },
        "0x34": {
          name: "红色",
          hex: "#FF0000"
        },
        "0x35": {
          name: "紫色",
          hex: "#800080"
        },
        "0x36": {
          name: "棕色",
          hex: "#A52A2A"
        },
        "0x37": {
          name: "蓝色",
          hex: "#0000FF"
        },
        "0x38": {
          name: "黑色",
          hex: "#000000"
        },
        "0x39": {
          name: "漂白剂",
          hex: "#F5F5DC"
        },
        "0x40": {
          name: "3%双氧水",
          hex: "#E0E0E0"
        },
        "0x41": {
          name: "12%双氧水",
          hex: "#D0D0D0"
        }
      };
      return colorMap[colorCode] || {
        name: "未知颜色",
        hex: "#CCCCCC"
      };
    },
    // 生成颜色构造
    generateColorComponents(colorCode, baseColorInfo) {
      const componentMap = {
        "0x31": [
          // 绿色
          {
            name: "绿色",
            amount: "20",
            color: "#00FF00"
          },
          {
            name: "红色",
            amount: "5",
            color: "#FF0000"
          },
          {
            name: "紫色",
            amount: "1",
            color: "#800080"
          }
        ],
        "0x33": [
          // 橙色
          {
            name: "橙色",
            amount: "16",
            color: "#FFA500"
          },
          {
            name: "黄色",
            amount: "8",
            color: "#FFFF00"
          },
          {
            name: "红色",
            amount: "2",
            color: "#FF0000"
          }
        ],
        "0x35": [
          // 紫色
          {
            name: "紫色",
            amount: "15",
            color: "#800080"
          },
          {
            name: "蓝色",
            amount: "10",
            color: "#0000FF"
          },
          {
            name: "红色",
            amount: "3",
            color: "#FF0000"
          }
        ]
      };
      return componentMap[colorCode] || [{
        name: baseColorInfo.name,
        amount: "20",
        color: baseColorInfo.hex
      }];
    },
    // 加载记录数据
    async loadRecordData(recordId) {
      try {
        const response = await utils_request.apiService.dyeConsumption.getUsageByCommand(recordId);
        if (response.code === 200) {
          this.initializeData(response.data);
        } else {
          common_vendor.index.__f__("error", "at pages/my/actual-effect.vue:472", "获取记录详情失败:", response.message);
          common_vendor.index.showToast({
            title: "数据加载失败",
            icon: "none"
          });
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/my/actual-effect.vue:479", "加载记录数据失败:", error);
        common_vendor.index.showToast({
          title: "网络错误",
          icon: "none"
        });
      }
    },
    // 上传染发前照片
    uploadBeforeImage() {
      common_vendor.index.chooseImage({
        count: 1,
        sizeType: ["compressed"],
        sourceType: ["camera", "album"],
        success: (res) => {
          this.beforeImage = res.tempFilePaths[0];
          this.saveImageToRecord("before", res.tempFilePaths[0]);
        },
        fail: (err) => {
          common_vendor.index.__f__("error", "at pages/my/actual-effect.vue:498", "选择照片失败:", err);
        }
      });
    },
    // 上传染发后照片
    uploadAfterImage() {
      common_vendor.index.chooseImage({
        count: 1,
        sizeType: ["compressed"],
        sourceType: ["camera", "album"],
        success: (res) => {
          this.afterImage = res.tempFilePaths[0];
          this.saveImageToRecord("after", res.tempFilePaths[0]);
        },
        fail: (err) => {
          common_vendor.index.__f__("error", "at pages/my/actual-effect.vue:514", "选择照片失败:", err);
        }
      });
    },
    // 预览图片
    previewImage(imageUrl) {
      common_vendor.index.previewImage({
        urls: [imageUrl],
        current: imageUrl
      });
    },
    // 保存照片到记录
    async saveImageToRecord(type, imagePath) {
      try {
        if (!this.recordData || !this.recordData.id) {
          common_vendor.index.showToast({
            title: "记录数据不完整",
            icon: "none"
          });
          return;
        }
        const beforeImageUrl = type === "before" ? imagePath : void 0;
        const afterImageUrl = type === "after" ? imagePath : void 0;
        const response = await utils_request.apiService.mall.dyeConsumption.updateRecordPhotos(
          this.recordData.id,
          beforeImageUrl,
          afterImageUrl
        );
        if (response.code === 200) {
          if (type === "before") {
            this.recordData.beforeImageUrl = imagePath;
          } else {
            this.recordData.afterImageUrl = imagePath;
          }
          common_vendor.index.showToast({
            title: "照片已保存",
            icon: "success"
          });
        } else {
          throw new Error(response.message || "保存失败");
        }
        common_vendor.index.__f__("log", "at pages/my/actual-effect.vue:565", `保存${type}照片成功:`, imagePath);
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/my/actual-effect.vue:567", "保存照片失败:", error);
        common_vendor.index.showToast({
          title: "保存失败",
          icon: "none"
        });
      }
    },
    // 点赞
    toggleLike() {
      this.isLiked = !this.isLiked;
      this.likeCount += this.isLiked ? 1 : -1;
    },
    // 收藏
    toggleStar() {
      this.isStarred = !this.isStarred;
      this.starCount += this.isStarred ? 1 : -1;
    },
    // 分享效果
    shareEffect() {
      common_vendor.index.showActionSheet({
        itemList: ["分享到微信", "分享到朋友圈", "保存图片"],
        success: (res) => {
          switch (res.tapIndex) {
            case 0:
              this.shareToWeChat();
              break;
            case 1:
              this.shareToMoments();
              break;
            case 2:
              this.saveImage();
              break;
          }
        }
      });
    },
    shareToWeChat() {
      this.shareCount++;
      common_vendor.index.showToast({
        title: "分享到微信成功",
        icon: "success"
      });
    },
    shareToMoments() {
      this.shareCount++;
      common_vendor.index.showToast({
        title: "分享到朋友圈成功",
        icon: "success"
      });
    },
    saveImage() {
      common_vendor.index.showToast({
        title: "图片已保存",
        icon: "success"
      });
    },
    // 生成新配方
    generateNewFormula() {
      if (!this.recordData) {
        common_vendor.index.showToast({
          title: "数据不完整",
          icon: "none"
        });
        return;
      }
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.t($data.colorType),
    b: common_vendor.t($data.deviceStatus ? "设备在线" : "设备离线"),
    c: common_vendor.n($data.deviceStatus ? "status-online" : "status-offline"),
    d: $data.beforeImage
  }, $data.beforeImage ? {
    e: $data.beforeImage,
    f: common_vendor.o(($event) => $options.previewImage($data.beforeImage))
  } : {
    g: common_vendor.o((...args) => $options.uploadBeforeImage && $options.uploadBeforeImage(...args))
  }, {
    h: $data.afterImage
  }, $data.afterImage ? {
    i: $data.afterImage,
    j: common_vendor.o(($event) => $options.previewImage($data.afterImage))
  } : {
    k: common_vendor.o((...args) => $options.uploadAfterImage && $options.uploadAfterImage(...args))
  }, {
    l: common_vendor.t($data.colorDisplayName),
    m: common_vendor.f($data.colorComponents, (item, index, i0) => {
      return {
        a: item.color,
        b: common_vendor.t(item.name),
        c: common_vendor.t(item.amount),
        d: index
      };
    }),
    n: common_vendor.t($data.isLiked ? "♥" : "♡"),
    o: common_vendor.t($data.likeCount),
    p: common_vendor.o((...args) => $options.toggleLike && $options.toggleLike(...args)),
    q: common_vendor.t($data.isStarred ? "★" : "☆"),
    r: common_vendor.t($data.starCount),
    s: common_vendor.o((...args) => $options.toggleStar && $options.toggleStar(...args)),
    t: common_vendor.t($data.shareCount),
    v: common_vendor.o((...args) => $options.shareEffect && $options.shareEffect(...args)),
    w: common_vendor.o((...args) => $options.generateNewFormula && $options.generateNewFormula(...args))
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/my/actual-effect.js.map
