/* 水平间距 */
/* 水平间距 */
/* 全局样式修复 - 确保页面占满屏幕宽度 */
page {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
}

/* 通用容器样式 */
.container, .page-container {
  width: 100%;
  min-height: 100vh;
  box-sizing: border-box;
}

/* 修复小程序页面宽度问题 */
uni-page-body {
  width: 100% !important;
}

/* 确保所有view元素正确继承宽度 */
view {
  box-sizing: border-box;
}
.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f5f5;
}
.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx;
  background-color: #4285f4;
  color: #fff;
}
.back-btn {
  font-size: 40rpx;
  width: 60rpx;
  display: flex;
  align-items: center;
}
.title {
  font-size: 36rpx;
  font-weight: 500;
}
.header-icons {
  display: flex;
  align-items: center;
}
.header-icon {
  margin-left: 15rpx;
  font-size: 24rpx;
}
.data-overview {
  background-color: #fff;
  border-radius: 10rpx;
  margin: 20rpx;
  padding: 30rpx;
  display: flex;
  flex-wrap: wrap;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}
.overview-item {
  width: 33.33%;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 20rpx;
}
.time-range {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 10rpx;
}
.data-value {
  font-size: 40rpx;
  font-weight: bold;
  color: #333;
}
.unit {
  font-size: 24rpx;
  font-weight: normal;
  margin-left: 4rpx;
}
.chart-section {
  background-color: #fff;
  border-radius: 10rpx;
  margin: 20rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}
.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}
.time-filter {
  display: flex;
  border-radius: 45rpx;
  overflow: hidden;
  background-color: #f5f5f5;
  margin: 20rpx 0;
}
.filter-btn {
  flex: 1;
  height: 70rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 26rpx;
  color: #666;
}
.filter-btn.active {
  background-color: #4285f4;
  color: #fff;
}
.chart-area {
  height: 400rpx;
  position: relative;
}
.chart-placeholder {
  width: 100%;
  height: 100%;
  border-bottom: 1px solid #eee;
  position: relative;
}
.chart-line {
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
}
.chart-point {
  position: absolute;
  width: 10rpx;
  height: 10rpx;
  background-color: #4285f4;
  border-radius: 50%;
}
.chart-point::after {
  content: "";
  position: absolute;
  width: 100%;
  height: 2px;
  background-color: #4285f4;
  right: -50rpx;
  top: 50%;
  transform: translateY(-50%);
}
.chart-x-axis {
  display: flex;
  justify-content: space-between;
  padding: 10rpx 0;
}
.chart-x-axis text {
  font-size: 24rpx;
  color: #999;
}
.completion-section {
  background-color: #fff;
  border-radius: 10rpx;
  margin: 20rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}
.completion-data {
  margin-top: 30rpx;
}
.no-data {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 0;
}
.no-data-icon {
  width: 120rpx;
  height: 120rpx;
  opacity: 0.3;
  margin-bottom: 20rpx;
}
.no-data-text {
  font-size: 28rpx;
  color: #999;
}
.no-chart-data {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 染膏消耗详情样式 */
.dye-consumption-section {
  background-color: #fff;
  border-radius: 10rpx;
  margin: 20rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}
.refresh-btn {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  background-color: #f0f9ff;
  color: #1890ff;
  font-size: 24rpx;
}
.inventory-status {
  margin-bottom: 30rpx;
  padding: 20rpx;
  background-color: #f8f9fa;
  border-radius: 8rpx;
}
.inventory-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15rpx;
}
.inventory-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}
.inventory-summary {
  font-size: 24rpx;
  color: #666;
}
.alert-section {
  margin-top: 15rpx;
}
.alert-title {
  font-size: 26rpx;
  font-weight: bold;
  color: #ff6b35;
  margin-bottom: 12rpx;
}
.alert-list {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}
.alert-item {
  display: flex;
  align-items: center;
}
.alert-text {
  font-size: 24rpx;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
}
.alert-text.warning {
  background-color: #fff3cd;
  color: #856404;
}
.alert-text.critical {
  background-color: #f8d7da;
  color: #721c24;
}
.more-alerts {
  margin-top: 8rpx;
}
.more-alerts text {
  font-size: 22rpx;
  color: #999;
}
.color-breakdown {
  margin-top: 20rpx;
}
.breakdown-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}
.color-list {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}
.color-item {
  display: flex;
  align-items: center;
  gap: 15rpx;
}
.color-rank {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  background-color: #4285f4;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 22rpx;
  font-weight: bold;
}
.color-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4rpx;
}
.color-name {
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
}
.color-usage {
  font-size: 22rpx;
  color: #666;
}
.color-bar {
  width: 120rpx;
  height: 8rpx;
  background-color: #f0f0f0;
  border-radius: 4rpx;
  overflow: hidden;
}
.color-fill {
  height: 100%;
  background-color: #4285f4;
  transition: width 0.3s ease;
}