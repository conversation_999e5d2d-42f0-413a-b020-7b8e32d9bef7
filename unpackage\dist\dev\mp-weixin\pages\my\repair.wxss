/* 水平间距 */
/* 水平间距 */
/* 全局样式修复 - 确保页面占满屏幕宽度 */
page {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
}

/* 通用容器样式 */
.container, .page-container {
  width: 100%;
  min-height: 100vh;
  box-sizing: border-box;
}

/* 修复小程序页面宽度问题 */
uni-page-body {
  width: 100% !important;
}

/* 确保所有view元素正确继承宽度 */
view {
  box-sizing: border-box;
}
.container {
  min-height: 100vh;
  background-color: #f5f5f5;
}
.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx;
  background-color: #4285f4;
  color: #fff;
}
.back-btn {
  font-size: 40rpx;
  width: 60rpx;
  display: flex;
  align-items: center;
}
.title {
  font-size: 36rpx;
  font-weight: 500;
}
.header-icons {
  display: flex;
  align-items: center;
}
.header-icon {
  margin-left: 15rpx;
  font-size: 24rpx;
}
.form-container {
  padding: 30rpx;
}
.form-section {
  margin-bottom: 40rpx;
}
.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}
.feedback-textarea {
  width: 100%;
  height: 220rpx;
  background-color: #fff;
  border-radius: 10rpx;
  padding: 20rpx;
  font-size: 28rpx;
  box-sizing: border-box;
}
.upload-box {
  width: 100%;
  background-color: #fff;
  border-radius: 10rpx;
  padding: 30rpx 0;
  display: flex;
  flex-wrap: wrap;
}
.upload-placeholder {
  width: 180rpx;
  height: 180rpx;
  border: 1px dashed #ddd;
  border-radius: 8rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  margin: 0 20rpx;
}
.upload-icon {
  font-size: 60rpx;
  color: #ccc;
  margin-bottom: 10rpx;
}
.upload-text {
  font-size: 24rpx;
  color: #999;
}
.image-list {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
  margin-bottom: 30rpx;
  padding: 0 20rpx;
}
.image-item {
  position: relative;
  width: 160rpx;
  height: 160rpx;
  border-radius: 16rpx;
  overflow: hidden;
}
.preview-image {
  width: 100%;
  height: 100%;
}
.remove-btn {
  position: absolute;
  top: -10rpx;
  right: -10rpx;
  width: 40rpx;
  height: 40rpx;
  background-color: #ff4757;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: bold;
}
.contact-input {
  width: 100%;
  height: 90rpx;
  background-color: #fff;
  border-radius: 10rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  box-sizing: border-box;
}
.submit-btn {
  width: 100%;
  height: 90rpx;
  background-color: #4285f4;
  color: #fff;
  border-radius: 45rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 32rpx;
  margin-top: 60rpx;
}