/* 水平间距 */
/* 水平间距 */
/* 全局样式修复 - 确保页面占满屏幕宽度 */
page {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
}

/* 通用容器样式 */
.container, .page-container {
  width: 100%;
  min-height: 100vh;
  box-sizing: border-box;
}

/* 修复小程序页面宽度问题 */
uni-page-body {
  width: 100% !important;
}

/* 确保所有view元素正确继承宽度 */
view {
  box-sizing: border-box;
}
.search-page {
  min-height: 100vh;
  background-color: #f8f8f8;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
  width: 100%;
  box-sizing: border-box;
}
.search-bar {
  background: linear-gradient(180deg, #4285f4 0%, #f8f8f8 100%);
  padding: 20rpx;
  border-bottom: 1rpx solid #e0e0e0;
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  z-index: 100;
  width: 100%;
  box-sizing: border-box;
}
.search-input-box {
  background-color: #fff;
  border-radius: 30rpx;
  display: flex;
  align-items: center;
  padding: 0 20rpx;
  height: 70rpx;
  position: relative;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  border: 1rpx solid #e0e0e0;
  width: 100%;
  box-sizing: border-box;
}
.search-input {
  flex: 1;
  height: 70rpx;
  font-size: 28rpx;
  border: none;
  outline: none;
  background: transparent;
  min-width: 0;
}
.clear-btn {
  font-size: 40rpx;
  color: #ccc;
  padding: 0 10rpx;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.search-btn-inline {
  color: #4285f4;
  font-size: 28rpx;
  padding: 0 15rpx;
  height: 50rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f0f8ff;
  border-radius: 25rpx;
  margin-left: 10rpx;
  white-space: nowrap;
  flex-shrink: 0;
}
.search-container {
  padding: 20rpx;
  min-height: calc(100vh - 200rpx);
  width: 100%;
  box-sizing: border-box;
}
.search-section {
  margin-bottom: 40rpx;
}
.section-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20rpx;
}
.section-title {
  font-size: 30rpx;
  color: #333;
  font-weight: bold;
}
.clear-history {
  font-size: 28rpx;
  color: #4285f4;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 40rpx;
  text-align: center;
  flex: 1;
}
.empty-text {
  font-size: 32rpx;
  color: #666;
  margin-bottom: 20rpx;
}
.empty-tip {
  font-size: 26rpx;
  color: #999;
}
.recommend-list {
  display: flex;
  flex-wrap: wrap;
  width: 100%;
}
.recommend-item {
  font-size: 26rpx;
  color: #666;
  background-color: #fff;
  padding: 10rpx 30rpx;
  margin-right: 20rpx;
  margin-bottom: 20rpx;
  border-radius: 30rpx;
  flex: 0 0 auto;
  max-width: calc(50% - 10rpx);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.history-list {
  background-color: #fff;
  border-radius: 10rpx;
  padding: 10rpx 0;
  width: 100%;
  box-sizing: border-box;
}
.history-item {
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}
.history-item:last-child {
  border-bottom: none;
}
.history-icon {
  font-size: 30rpx;
  color: #999;
  margin-right: 10rpx;
}
.history-text {
  flex: 1;
  font-size: 28rpx;
  color: #333;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.history-time {
  font-size: 24rpx;
  color: #999;
}
.search-results {
  padding: 20rpx;
  width: 100%;
  box-sizing: border-box;
}
.category-filter {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #f0f8ff;
  padding: 20rpx 30rpx;
  margin-bottom: 20rpx;
  border-radius: 10rpx;
  border-left: 6rpx solid #4285f4;
}
.filter-text {
  font-size: 28rpx;
  color: #4285f4;
  font-weight: bold;
}
.clear-filter {
  font-size: 26rpx;
  color: #666;
  padding: 10rpx 20rpx;
  background-color: #fff;
  border-radius: 20rpx;
  border: 1rpx solid #ddd;
}
.result-count {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 20rpx;
}
.result-list {
  background-color: #fff;
  border-radius: 10rpx;
  padding: 20rpx;
  width: 100%;
  box-sizing: border-box;
}
.result-item {
  display: flex;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}
.result-item:last-child {
  border-bottom: none;
}
.result-image {
  width: 160rpx;
  height: 160rpx;
  border-radius: 10rpx;
  margin-right: 20rpx;
  flex-shrink: 0;
}
.result-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  min-width: 0;
  /* 确保弹性项可以缩小到其最小内容尺寸以下 */
}
.result-name {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}
.result-price {
  font-size: 32rpx;
  color: #ff0036;
  font-weight: bold;
}
.result-sold {
  font-size: 24rpx;
  color: #999;
}
.no-more, .no-result {
  text-align: center;
  padding: 40rpx 0;
  color: #999;
  font-size: 28rpx;
}
.no-result {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.no-result-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 20rpx;
}
.no-result-text {
  font-size: 28rpx;
  color: #999;
}