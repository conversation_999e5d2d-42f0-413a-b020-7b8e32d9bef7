<view class="container"><view class="device-status"><text class="categoryName">Ai芭佰邑-{{a}}</text><text class="{{['device-state', c]}}">{{b}}</text></view><view class="card"><text style="{{'color:' + e}}" class="{{['card-title', 'grading', 'status-text', f && 'command-active']}}">{{d}}</text><image src="{{g}}" class="{{['bowl-icon', h && 'active', i && 'inactive']}}"/><text class="bowl-color">{{j}}</text><view class="button-group"><button wx:if="{{k}}" class="start-button" bindtap="{{l}}"> 开始 </button><button wx:elif="{{m}}" class="stop-button" bindtap="{{n}}"> 暂停 </button><button wx:elif="{{o}}" class="resume-button" bindtap="{{p}}"> 恢复 </button><button wx:if="{{q}}" class="cancel-button" bindtap="{{r}}"> 取消 </button><button wx:if="{{s}}" class="stop-master-button" bindtap="{{t}}"> 停止 </button></view></view><view class="card"><text class="card-title">出膏量</text><view wx:for="{{v}}" wx:for-item="item" wx:key="f" class="color-item"><view class="{{['color-indicator', item.a]}}" style="{{'background-color:' + item.b}}"></view><text>{{item.c}}</text><text class="amount">{{item.d}}g ({{item.e}}g)</text></view></view></view>