"use strict";
const common_vendor = require("../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      selectedLocation: {
        name: "广东省东莞市南城街道",
        address: "广东省东莞市南城街道"
      },
      searchKeyword: "",
      locationList: [
        { name: "广东省东莞市南城街道", address: "广东省东莞市南城街道" },
        { name: "广东省东莞市东城街道", address: "广东省东莞市东城街道" },
        { name: "广东省东莞市万江街道", address: "广东省东莞市万江街道" },
        { name: "广东省东莞市莞城街道", address: "广东省东莞市莞城街道" },
        { name: "广东省东莞市石碣镇", address: "广东省东莞市石碣镇" }
      ],
      defaultLocationList: [],
      mapCenter: {
        latitude: 23.0489,
        longitude: 113.7447
      },
      markers: [],
      isLocationInitialized: false
    };
  },
  onLoad() {
    this.defaultLocationList = [...this.locationList];
    this.getCurrentLocation();
  },
  methods: {
    // 获取当前位置
    getCurrentLocation() {
      common_vendor.index.showLoading({
        title: "正在定位..."
      });
      common_vendor.index.getLocation({
        type: "gcj02",
        success: (res) => {
          this.selectedLocation = {
            name: "当前位置",
            address: "当前位置",
            latitude: res.latitude,
            longitude: res.longitude
          };
          this.updateMapLocation(res.latitude, res.longitude, "当前位置");
          common_vendor.index.__f__("log", "at pages/location-picker.vue:120", "位置获取成功:", res);
          common_vendor.index.hideLoading();
        },
        fail: (err) => {
          common_vendor.index.__f__("error", "at pages/location-picker.vue:124", "定位失败:", err);
          common_vendor.index.hideLoading();
          common_vendor.index.showToast({
            title: "定位失败",
            icon: "none"
          });
        }
      });
    },
    // 更新地图位置
    updateMapLocation(latitude, longitude, address) {
      if (!this.isLocationInitialized) {
        this.mapCenter = {
          latitude,
          longitude
        };
        this.isLocationInitialized = true;
      }
      this.markers = [
        {
          id: 1,
          latitude,
          longitude,
          width: 30,
          height: 30,
          callout: {
            content: address || "当前位置",
            color: "#333",
            fontSize: 14,
            borderRadius: 4,
            bgColor: "#fff",
            padding: 8,
            display: "ALWAYS"
          }
        }
      ];
    },
    // 地图标记点击事件
    onMarkerTap(e) {
      common_vendor.index.__f__("log", "at pages/location-picker.vue:168", "标记点击:", e);
    },
    // 地图点击事件
    onMapTap(e) {
      common_vendor.index.__f__("log", "at pages/location-picker.vue:173", "地图点击:", e);
    },
    // 地图区域变化事件
    onRegionChange(e) {
      common_vendor.index.__f__("log", "at pages/location-picker.vue:178", "地图区域变化:", e);
    },
    // 搜索输入事件
    onSearchInput() {
      if (this.searchKeyword.trim()) {
        this.searchLocations(this.searchKeyword);
      } else {
        this.locationList = [...this.defaultLocationList];
      }
    },
    // 搜索位置
    searchLocations(query) {
      const mockResults = [
        { name: `${query}附近`, address: `广东省东莞市${query}` },
        { name: `${query}商圈`, address: `广东省东莞市南城街道${query}` },
        { name: `${query}地铁站`, address: `广东省东莞市${query}地铁站` },
        { name: `${query}购物中心`, address: `广东省东莞市南城街道${query}购物中心` },
        { name: `${query}公园`, address: `广东省东莞市${query}公园` }
      ];
      this.locationList = mockResults;
    },
    // 选择位置
    selectLocation(location) {
      this.selectedLocation = location;
      if (location.latitude && location.longitude) {
        this.updateMapLocation(location.latitude, location.longitude, location.name);
      }
    },
    // 确认位置
    confirmLocation() {
      const pages = getCurrentPages();
      const prevPage = pages[pages.length - 2];
      if (prevPage) {
        prevPage.$vm.currentLocation = this.selectedLocation;
      }
      common_vendor.index.navigateBack();
    },
    // 返回
    goBack() {
      common_vendor.index.navigateBack();
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.o((...args) => $options.confirmLocation && $options.confirmLocation(...args)),
    b: common_vendor.t($data.selectedLocation.name || "广东省东莞市南城街道"),
    c: common_vendor.o((...args) => $options.goBack && $options.goBack(...args)),
    d: common_vendor.o([($event) => $data.searchKeyword = $event.detail.value, (...args) => $options.onSearchInput && $options.onSearchInput(...args)]),
    e: $data.searchKeyword,
    f: common_vendor.t($data.selectedLocation.name || "广东省东莞市南城街道"),
    g: common_vendor.t($data.selectedLocation.address || "广东省东莞市南城街道"),
    h: common_vendor.o((...args) => $options.getCurrentLocation && $options.getCurrentLocation(...args)),
    i: $data.mapCenter.latitude,
    j: $data.mapCenter.longitude,
    k: $data.markers,
    l: common_vendor.o((...args) => $options.onMarkerTap && $options.onMarkerTap(...args)),
    m: common_vendor.o((...args) => $options.onMapTap && $options.onMapTap(...args)),
    n: common_vendor.o((...args) => $options.onRegionChange && $options.onRegionChange(...args)),
    o: common_vendor.f($data.locationList, (location, index, i0) => {
      return {
        a: common_vendor.t(location.name),
        b: common_vendor.t(location.address),
        c: index,
        d: common_vendor.o(($event) => $options.selectLocation(location), index)
      };
    })
  };
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-4925d87e"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../.sourcemap/mp-weixin/pages/location-picker.js.map
