"use strict";
const common_vendor = require("../common/vendor.js");
const utils_location = require("../utils/location.js");
const _sfc_main = {
  data() {
    return {
      selectedLocation: {
        name: "广东省东莞市南城街道",
        address: "广东省东莞市南城街道"
      },
      searchKeyword: "",
      locationList: [
        { name: "越秀区广州市人民政府(府前路北)", address: "广东省广州市越秀区府前路北" },
        { name: "广州市人民政府", address: "广东省广州市越秀区北京街道府前路1号" },
        { name: "珠江国际大厦", address: "广东省广州市越秀区越华路112号" },
        { name: "北京街道", address: "广东省广州市越秀区" },
        { name: "广州交易广场", address: "广东省广州市越秀区东风中路268号" }
      ],
      defaultLocationList: [],
      isLocationInitialized: false,
      // 防止重复定位
      // 地图相关数据
      mapCenter: {
        latitude: 23.1291,
        // 广州市中心纬度
        longitude: 113.2644
        // 广州市中心经度
      },
      markers: [
        {
          id: 1,
          latitude: 23.1291,
          longitude: 113.2644,
          width: 30,
          height: 30,
          callout: {
            content: "当前位置",
            color: "#333",
            fontSize: 14,
            borderRadius: 4,
            bgColor: "#fff",
            padding: 8,
            display: "ALWAYS"
          }
        }
      ]
    };
  },
  onLoad() {
    this.defaultLocationList = [...this.locationList];
    this.getCurrentLocation();
  },
  methods: {
    // 获取当前位置
    async getCurrentLocation() {
      try {
        common_vendor.index.showLoading({
          title: "正在定位..."
        });
        const locationData = await utils_location.locationService.getCurrentLocation();
        this.selectedLocation = {
          name: locationData.address,
          address: locationData.address,
          latitude: locationData.latitude,
          longitude: locationData.longitude
        };
        this.updateMapLocation(locationData.latitude, locationData.longitude, locationData.address);
        common_vendor.index.__f__("log", "at pages/location-picker.vue:140", "位置获取成功:", locationData);
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/location-picker.vue:142", "获取位置失败:", error);
        common_vendor.index.showToast({
          title: "定位失败",
          icon: "none"
        });
      } finally {
        common_vendor.index.hideLoading();
      }
    },
    // 更新地图位置
    updateMapLocation(latitude, longitude, address) {
      if (!this.isLocationInitialized) {
        this.mapCenter = {
          latitude,
          longitude
        };
        this.isLocationInitialized = true;
      }
      this.markers = [
        {
          id: 1,
          latitude,
          longitude,
          width: 30,
          height: 30,
          callout: {
            content: address || "当前位置",
            color: "#333",
            fontSize: 14,
            borderRadius: 4,
            bgColor: "#fff",
            padding: 8,
            display: "ALWAYS"
          }
        }
      ];
    },
    // 地图标记点击事件
    onMarkerTap(e) {
      common_vendor.index.__f__("log", "at pages/location-picker.vue:186", "标记点击:", e);
    },
    // 地图点击事件
    onMapTap(e) {
      common_vendor.index.__f__("log", "at pages/location-picker.vue:191", "地图点击:", e);
    },
    // 地图区域变化事件
    onRegionChange(e) {
      common_vendor.index.__f__("log", "at pages/location-picker.vue:197", "地图区域变化:", e);
      if (e.type === "end" && e.causedBy === "drag")
        ;
    },
    // 搜索输入处理
    onSearchInput() {
      if (this.searchKeyword.trim()) {
        this.searchLocations(this.searchKeyword.trim());
      } else {
        this.showDefaultLocations();
      }
    },
    // 搜索位置
    async searchLocations(query) {
      try {
        common_vendor.index.showLoading({
          title: "搜索中..."
        });
        try {
          const searchResults = await utils_location.locationService.searchPOI(query, "东莞");
          this.locationList = searchResults;
        } catch (apiError) {
          common_vendor.index.__f__("warn", "at pages/location-picker.vue:227", "API搜索失败，使用模拟数据:", apiError);
          const mockResults = [
            { name: `${query}附近`, address: `广东省东莞市${query}` },
            { name: `${query}商圈`, address: `广东省东莞市南城街道${query}` },
            { name: `${query}地铁站`, address: `广东省东莞市${query}地铁站` },
            { name: `${query}购物中心`, address: `广东省东莞市南城街道${query}购物中心` },
            { name: `${query}公园`, address: `广东省东莞市${query}公园` }
          ];
          this.locationList = mockResults;
        }
        common_vendor.index.hideLoading();
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/location-picker.vue:242", "搜索失败:", error);
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({
          title: "搜索失败",
          icon: "none"
        });
      }
    },
    // 显示默认位置列表
    showDefaultLocations() {
      this.locationList = [...this.defaultLocationList];
    },
    // 选择位置
    selectLocation(location) {
      this.selectedLocation = { ...location };
    },
    // 确认选择
    confirmLocation() {
      common_vendor.index.setStorageSync("selectedLocation", this.selectedLocation);
      common_vendor.index.$emit("locationSelected", this.selectedLocation);
      common_vendor.index.showToast({
        title: "位置已选择",
        icon: "success"
      });
      setTimeout(() => {
        common_vendor.index.navigateBack();
      }, 500);
    },
    // 返回
    goBack() {
      common_vendor.index.navigateBack();
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.o((...args) => $options.confirmLocation && $options.confirmLocation(...args)),
    b: common_vendor.t($data.selectedLocation.name || "广东省东莞市南城街道"),
    c: common_vendor.o((...args) => $options.goBack && $options.goBack(...args)),
    d: common_vendor.o([($event) => $data.searchKeyword = $event.detail.value, (...args) => $options.onSearchInput && $options.onSearchInput(...args)]),
    e: $data.searchKeyword,
    f: common_vendor.t($data.selectedLocation.name || "广东省东莞市南城街道"),
    g: common_vendor.t($data.selectedLocation.address || "广东省东莞市南城街道"),
    h: common_vendor.o((...args) => $options.getCurrentLocation && $options.getCurrentLocation(...args)),
    i: $data.mapCenter.latitude,
    j: $data.mapCenter.longitude,
    k: $data.markers,
    l: common_vendor.o((...args) => $options.onMarkerTap && $options.onMarkerTap(...args)),
    m: common_vendor.o((...args) => $options.onMapTap && $options.onMapTap(...args)),
    n: common_vendor.o((...args) => $options.onRegionChange && $options.onRegionChange(...args)),
    o: common_vendor.f($data.locationList, (location, index, i0) => {
      return {
        a: common_vendor.t(location.name),
        b: common_vendor.t(location.address),
        c: index,
        d: $data.selectedLocation.name === location.name ? 1 : "",
        e: common_vendor.o(($event) => $options.selectLocation(location), index)
      };
    })
  };
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../.sourcemap/mp-weixin/pages/location-picker.js.map
