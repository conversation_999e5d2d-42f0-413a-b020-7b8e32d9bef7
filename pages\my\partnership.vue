<template>
  <view class="partnership-container">
    <!-- <view class="header">
      <view class="back-btn" @click="goBack">
        <text class="iconfont icon-arrow-left"></text>
      </view>
      <view class="title">合作加盟</view>
    </view> -->
    
    <view class="content">
      <!-- 加载状态 -->
      <view v-if="loading" class="loading-container">
        <text>加载中...</text>
      </view>
      
      <!-- 错误状态 -->
      <view v-else-if="error" class="error-container">
        <text>{{ error }}</text>
        <button @click="loadPartnershipInfo" class="retry-btn">重试</button>
      </view>
      
      <!-- 合作加盟信息 -->
      <view v-else-if="partnershipInfo" class="partnership-detail">
        <!-- 头部信息 -->
        <view class="partnership-header">
          <image v-if="partnershipInfo.imageUrl" :src="partnershipInfo.imageUrl" class="partnership-image" mode="aspectFill"></image>
          <view class="partnership-title">{{ partnershipInfo.title }}</view>
          <view class="partnership-desc">{{ partnershipInfo.description }}</view>
        </view>
        
        <!-- 详细内容 -->
        <view v-if="partnershipInfo.content" class="partnership-content">
          <view class="section-title">项目介绍</view>
          <rich-text :nodes="formatContent(partnershipInfo.content)"></rich-text>
        </view>
        
        <!-- 加盟优势 -->
        <view v-if="advantages.length > 0" class="partnership-section">
          <view class="section-title">加盟优势</view>
          <view class="advantage-list">
            <view v-for="(item, index) in advantages" :key="index" class="advantage-item">
              <view class="advantage-icon">✓</view>
              <text class="advantage-text">{{ item }}</text>
            </view>
          </view>
        </view>
        
        <!-- 加盟要求 -->
        <view v-if="requirements.length > 0" class="partnership-section">
          <view class="section-title">加盟要求</view>
          <view class="requirement-list">
            <view v-for="(item, index) in requirements" :key="index" class="requirement-item">
              <view class="requirement-number">{{ index + 1 }}</view>
              <text class="requirement-text">{{ item }}</text>
            </view>
          </view>
        </view>
        
        <!-- 加盟流程 -->
        <view v-if="process.length > 0" class="partnership-section">
          <view class="section-title">加盟流程</view>
          <view class="process-list">
            <view v-for="(item, index) in process" :key="index" class="process-item">
              <view class="process-step">
                <view class="step-number">{{ index + 1 }}</view>
                <view v-if="index < process.length - 1" class="step-line"></view>
              </view>
              <text class="process-text">{{ item }}</text>
            </view>
          </view>
        </view>
        
        <!-- 联系方式 -->
        <view class="contact-section">
          <view class="section-title">联系我们</view>
          <view class="contact-list">
            <view v-if="partnershipInfo.contactPhone" class="contact-item" @click="makeCall">
              <view class="contact-icon">📞</view>
              <text class="contact-text">{{ partnershipInfo.contactPhone }}</text>
            </view>
            <view v-if="partnershipInfo.contactEmail" class="contact-item" @click="sendEmail">
              <view class="contact-icon">📧</view>
              <text class="contact-text">{{ partnershipInfo.contactEmail }}</text>
            </view>
            <view v-if="partnershipInfo.contactWechat" class="contact-item">
              <view class="contact-icon">💬</view>
              <text class="contact-text">微信：{{ partnershipInfo.contactWechat }}</text>
            </view>
            <view v-if="partnershipInfo.address" class="contact-item">
              <view class="contact-icon">📍</view>
              <text class="contact-text">{{ partnershipInfo.address }}</text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { get } from '@/utils/request.js'

export default {
  data() {
    return {
      partnershipInfo: null,
      loading: false,
      error: '',
      advantages: [],
      requirements: [],
      process: []
    }
  },
  onLoad() {
    this.loadPartnershipInfo()
  },
  methods: {
    goBack() {
      uni.navigateBack({
        delta: 1
      });
    },
    
    async loadPartnershipInfo() {
      try {
        this.loading = true
        this.error = ''
        
        const response = await get('/api/partnership/info')
        
        if (response && response.code === 200) {
          this.partnershipInfo = response.data
          this.parseJsonFields()
        } else {
          this.error = response?.message || '获取合作加盟信息失败'
        }
      } catch (error) {
        console.error('获取合作加盟信息失败:', error)
        this.error = '网络错误，请稍后重试'
      } finally {
        this.loading = false
      }
    },
    
    parseJsonFields() {
      if (!this.partnershipInfo) return
      
      // 解析优势
      try {
        this.advantages = this.partnershipInfo.advantages ? JSON.parse(this.partnershipInfo.advantages) : []
      } catch (e) {
        this.advantages = []
      }
      
      // 解析要求
      try {
        this.requirements = this.partnershipInfo.requirements ? JSON.parse(this.partnershipInfo.requirements) : []
      } catch (e) {
        this.requirements = []
      }
      
      // 解析流程
      try {
        this.process = this.partnershipInfo.process ? JSON.parse(this.partnershipInfo.process) : []
      } catch (e) {
        this.process = []
      }
    },
    
    formatContent(content) {
      if (!content) return ''
      return content.replace(/\n/g, '<br/>')
    },
    
    makeCall() {
      if (this.partnershipInfo.contactPhone) {
        uni.makePhoneCall({
          phoneNumber: this.partnershipInfo.contactPhone
        })
      }
    },
    
    sendEmail() {
      if (this.partnershipInfo.contactEmail) {
        uni.showToast({
          title: '请使用邮件客户端发送',
          icon: 'none'
        })
      }
    }
  }
}
</script>

<style lang="scss">
.partnership-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100%;
  background-color: #f7f8fa;
  box-sizing: border-box;
}

.header {
  display: flex;
  align-items: center;
  height: 88rpx;
  background-color: #ffffff;
  padding: 0 30rpx;
  position: relative;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.back-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.title {
  flex: 1;
  text-align: center;
  font-size: 36rpx;
  font-weight: 500;
  margin-right: 60rpx;
}

.content {
  flex: 1;
  padding: 20rpx;
  overflow-y: auto;
}

.loading-container, .error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400rpx;
  color: #999;
}

.retry-btn {
  margin-top: 20rpx;
  padding: 10rpx 30rpx;
  background-color: #007AFF;
  color: white;
  border-radius: 10rpx;
  border: none;
}

.partnership-detail {
  display: flex;
  flex-direction: column;
}

.partnership-header {
  background-color: #ffffff;
  border-radius: 10rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  text-align: center;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.partnership-image {
  width: 200rpx;
  height: 200rpx;
  border-radius: 10rpx;
  margin-bottom: 20rpx;
}

.partnership-title {
  font-size: 40rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 15rpx;
}

.partnership-desc {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
}

.partnership-content, .partnership-section, .contact-section {
  background-color: #ffffff;
  border-radius: 10rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  padding-bottom: 10rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.advantage-list, .requirement-list {
  display: flex;
  flex-direction: column;
}

.advantage-item, .requirement-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 15rpx;
}

.advantage-icon {
  width: 40rpx;
  height: 40rpx;
  background-color: #52c41a;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  margin-right: 15rpx;
  flex-shrink: 0;
}

.requirement-number {
  width: 40rpx;
  height: 40rpx;
  background-color: #007AFF;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  margin-right: 15rpx;
  flex-shrink: 0;
}

.advantage-text, .requirement-text {
  font-size: 28rpx;
  color: #333;
  line-height: 1.6;
  flex: 1;
}

.contact-list {
  display: flex;
  flex-direction: column;
}

.contact-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.contact-item:last-child {
  border-bottom: none;
}

.contact-icon {
  font-size: 32rpx;
  margin-right: 20rpx;
  width: 50rpx;
  text-align: center;
}

.contact-text {
  font-size: 28rpx;
  color: #333;
  flex: 1;
}
</style>

<style lang="scss">
.partnership-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f7f8fa;
}

.header {
  display: flex;
  align-items: center;
  height: 88rpx;
  background-color: #ffffff;
  padding: 0 30rpx;
  position: relative;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.back-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.title {
  flex: 1;
  text-align: center;
  font-size: 36rpx;
  font-weight: 500;
  margin-right: 60rpx;
}

.content {
  flex: 1;
  padding: 20rpx;
  overflow-y: auto;
}

.loading-container, .error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400rpx;
  color: #999;
}

.retry-btn {
  margin-top: 20rpx;
  padding: 10rpx 30rpx;
  background-color: #007AFF;
  color: white;
  border-radius: 10rpx;
  border: none;
}

.partnership-detail {
  display: flex;
  flex-direction: column;
}

.partnership-header {
  background-color: #ffffff;
  border-radius: 10rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  text-align: center;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.partnership-image {
  width: 200rpx;
  height: 200rpx;
  border-radius: 10rpx;
  margin-bottom: 20rpx;
}

.partnership-title {
  font-size: 40rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 15rpx;
}

.partnership-desc {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
}

.partnership-content, .partnership-section, .contact-section {
  background-color: #ffffff;
  border-radius: 10rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  padding-bottom: 10rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.advantage-list, .requirement-list {
  display: flex;
  flex-direction: column;
}

.advantage-item, .requirement-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 15rpx;
}

.advantage-icon {
  width: 40rpx;
  height: 40rpx;
  background-color: #52c41a;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  margin-right: 15rpx;
  flex-shrink: 0;
}

.requirement-number {
  width: 40rpx;
  height: 40rpx;
  background-color: #007AFF;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  margin-right: 15rpx;
  flex-shrink: 0;
}

.advantage-text, .requirement-text {
  font-size: 28rpx;
  color: #333;
  line-height: 1.6;
  flex: 1;
}

.process-list {
  display: flex;
  flex-direction: column;
}

.process-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 30rpx;
}

.process-step {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-right: 20rpx;
}

.step-number {
  width: 50rpx;
  height: 50rpx;
  background-color: #007AFF;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 26rpx;
  font-weight: bold;
}

.step-line {
  width: 2rpx;
  height: 40rpx;
  background-color: #ddd;
  margin-top: 10rpx;
}

.process-text {
  font-size: 28rpx;
  color: #333;
  line-height: 1.6;
  flex: 1;
  padding-top: 10rpx;
}

.contact-list {
  display: flex;
  flex-direction: column;
}

.contact-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.contact-item:last-child {
  border-bottom: none;
}

.contact-icon {
  font-size: 32rpx;
  margin-right: 20rpx;
  width: 50rpx;
  text-align: center;
}

.contact-text {
  font-size: 28rpx;
  color: #333;
  flex: 1;
}
</style>
