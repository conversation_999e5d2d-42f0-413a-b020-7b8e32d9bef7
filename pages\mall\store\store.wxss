.store-container {
	background-color: #f5f5f5;
	min-height: 100vh;
}

.top-bar {
	display: flex;
	align-items: center;
	justify-content: space-between;
	height: 88rpx;
	background-color: #c52137;
	color: #ffffff;
	padding: 0 20rpx;
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	z-index: 100;
}

.back-btn, .more-btn {
	font-size: 40rpx;
	width: 80rpx;
	text-align: center;
}

.title {
	font-size: 32rpx;
	font-weight: bold;
}

.store-header {
	margin-top: 88rpx;
	position: relative;
}

.store-bg {
	height: 300rpx;
	overflow: hidden;
}

.store-bg image {
	width: 100%;
	height: 100%;
}

.store-info {
	display: flex;
	align-items: flex-start;
	justify-content: space-between;
	padding: 30rpx;
	background-color: #ffffff;
	border-bottom: 1rpx solid #f0f0f0;
}

.store-main {
	flex: 1;
}

.store-name-row {
	display: flex;
	align-items: center;
	margin-bottom: 15rpx;
}

.store-name {
	font-size: 40rpx;
	font-weight: bold;
	color: #333333;
	margin-right: 20rpx;
}

.rating-badge {
	background-color: #ff6b35;
	color: #ffffff;
	padding: 8rpx 16rpx;
	border-radius: 20rpx;
	display: flex;
	align-items: center;
	font-size: 24rpx;
}

.rating-score {
	font-weight: bold;
	margin-right: 4rpx;
}

.rating-text {
	font-size: 20rpx;
}

.business-license {
	font-size: 24rpx;
	color: #666666;
	line-height: 1.4;
}

.follow-btn {
	background-color: #007aff;
	color: #ffffff;
	padding: 15rpx 30rpx;
	border-radius: 30rpx;
	font-size: 28rpx;
	margin-top: 10rpx;
}

.category-nav {
	display: flex;
	background-color: #ffffff;
	border-bottom: 1rpx solid #f0f0f0;
}

.nav-item {
	flex: 1;
	text-align: center;
	padding: 20rpx 0;
	font-size: 28rpx;
	position: relative;
}

.nav-item.active {
	color: #c52137;
}

.nav-item.active:after {
	content: '';
	position: absolute;
	bottom: 0;
	left: 25%;
	width: 50%;
	height: 4rpx;
	background-color: #c52137;
}

.filter-bar {
	display: flex;
	background-color: #ffffff;
	padding: 20rpx 0;
	margin-bottom: 20rpx;
}

.filter-item {
	flex: 1;
	display: flex;
	justify-content: center;
	align-items: center;
	font-size: 28rpx;
}

.arrow {
	font-size: 20rpx;
	margin-left: 10rpx;
	color: #999999;
}

.product-list {
	padding: 20rpx;
	display: flex;
	flex-wrap: wrap;
	justify-content: space-between;
	background-color: #f5f5f5;
}

.product-item {
	width: 48%;
	background-color: #ffffff;
	margin-bottom: 20rpx;
	border-radius: 10rpx;
	overflow: hidden;
	box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

.product-image {
	width: 100%;
	height: 300rpx;
	background-color: #f8f8f8;
}

.product-info {
	padding: 20rpx 15rpx;
}

.product-name {
	font-size: 28rpx;
	color: #333333;
	line-height: 1.4;
	height: 80rpx;
	overflow: hidden;
	text-overflow: ellipsis;
	display: -webkit-box;
	-webkit-line-clamp: 2;
	-webkit-box-orient: vertical;
	margin-bottom: 10rpx;
}

.product-price-row {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.product-price {
	font-size: 32rpx;
	color: #c52137;
	font-weight: bold;
}

.product-sold {
	font-size: 24rpx;
	color: #999999;
}