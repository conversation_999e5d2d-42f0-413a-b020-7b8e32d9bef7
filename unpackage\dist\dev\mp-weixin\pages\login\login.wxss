/* 水平间距 */
/* 水平间距 */
/* 全局样式修复 - 确保页面占满屏幕宽度 */
page {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
}

/* 通用容器样式 */
.container, .page-container {
  width: 100%;
  min-height: 100vh;
  box-sizing: border-box;
}

/* 修复小程序页面宽度问题 */
uni-page-body {
  width: 100% !important;
}

/* 确保所有view元素正确继承宽度 */
view {
  box-sizing: border-box;
}
.login-container {
  width: 100%;
  /* 默认占父容器90%宽度 */
  margin: 0 auto;
  /* 水平居中 */
  padding: 40rpx;
  background-color: #fff;
  min-height: 100vh;
}

/* 微信登录按钮样式 */
.wechat-login-btn {
  background: none;
  border: none;
  padding: 0;
  margin: 0;
  line-height: 1;
}
.wechat-login-btn::after {
  border: none;
  /* 移除默认边框 */
}
.wechat-icon {
  width: 100rpx;
  height: 100rpx;
}
.login-header {
  display: flex;
  justify-content: center;
  margin-bottom: 60rpx;
}
.login-header .login-logo {
  width: 120rpx;
  height: 120rpx;
}
.login-header .login-logo image {
  width: 100%;
  height: 100%;
}
.login-tabs {
  display: flex;
  border-bottom: 1px solid #eee;
  margin-bottom: 50rpx;
}
.login-tabs .tab-item {
  flex: 1;
  text-align: center;
  padding: 20rpx 0;
  font-size: 30rpx;
  color: #999;
  position: relative;
}
.login-tabs .tab-item.active {
  color: #4285f4;
  font-weight: bold;
}
.login-tabs .tab-item.active::after {
  content: "";
  position: absolute;
  bottom: -2rpx;
  left: 30%;
  width: 40%;
  height: 4rpx;
  background-color: #4285f4;
}
.login-form .input-group {
  border: 1px solid #eee;
  border-radius: 8rpx;
  padding: 20rpx;
  margin-bottom: 30rpx;
}
.login-form .input-group input {
  width: 100%;
  font-size: 30rpx;
}
.login-form .input-group.verify-code-group {
  display: flex;
  align-items: center;
}
.login-form .input-group.verify-code-group input {
  flex: 1;
  margin-right: 20rpx;
}
.login-form .input-group.verify-code-group .verify-btn {
  background-color: #4285f4;
  color: #fff;
  border: none;
  border-radius: 6rpx;
  padding: 15rpx 20rpx;
  font-size: 26rpx;
  white-space: nowrap;
}
.login-form .input-group.verify-code-group .verify-btn:disabled {
  background-color: #ccc;
  color: #999;
}
.login-form .agreement-row {
  display: flex;
  align-items: center;
  margin-bottom: 50rpx;
}
.login-form .agreement-row .agreement-text {
  font-size: 26rpx;
  color: #666;
  margin-left: 10rpx;
}
.login-form .login-btn {
  background-color: #4285f4;
  color: #fff;
  height: 90rpx;
  line-height: 90rpx;
  font-size: 32rpx;
  border-radius: 45rpx;
  margin-bottom: 30rpx;
}
.login-form .divider {
  position: relative;
  display: flex;
  justify-content: center;
  margin: 60rpx 0;
}
.login-form .divider::before {
  content: "";
  position: absolute;
  left: 0;
  top: 50%;
  width: 40%;
  height: 1px;
  background-color: #eee;
}
.login-form .divider::after {
  content: "";
  position: absolute;
  right: 0;
  top: 50%;
  width: 40%;
  height: 1px;
  background-color: #eee;
}
.login-form .divider .divider-text {
  padding: 0 20rpx;
  background-color: #fff;
  color: #999;
  font-size: 26rpx;
  z-index: 1;
}
.login-form .quick-login {
  display: flex;
  justify-content: center;
}
.login-form .quick-login .wechat-login {
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.login-form .quick-login .wechat-login .wechat-icon {
  width: 80rpx;
  height: 80rpx;
}