/* 水平间距 */
/* 水平间距 */
/* 全局样式修复 - 确保页面占满屏幕宽度 */
page.data-v-14fa498a {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
}

/* 通用容器样式 */
.container.data-v-14fa498a, .page-container.data-v-14fa498a {
  width: 100%;
  min-height: 100vh;
  box-sizing: border-box;
}

/* 修复小程序页面宽度问题 */
uni-page-body.data-v-14fa498a {
  width: 100% !important;
}

/* 确保所有view元素正确继承宽度 */
view.data-v-14fa498a {
  box-sizing: border-box;
}

/* CSS Variables for better maintainability */
.data-v-14fa498a:root {
  --color-bar-gap: 4px;
  --color-block-min-width: 25px;
  --color-block-max-width: 40px;
  --composition-padding: 8px 12px;
  --composition-min-height: 120rpx;
  --color-bar-height: 140rpx;
}

/* 添加页面级别样式，修复右侧空白问题 */
page.data-v-14fa498a {
  width: 100vw;
  height: 100vh;
  overflow-x: hidden;
  padding: 0;
  margin: 0;
  box-sizing: border-box;
  background-color: #ffffff;
  overscroll-behavior: none;
}

/* 补充uni-app全局样式，确保内容不超出屏幕 */
.data-v-14fa498a:root,
uni-page.data-v-14fa498a,
uni-page-head.data-v-14fa498a,
uni-page-wrapper.data-v-14fa498a,
uni-page-body.data-v-14fa498a,
uni-app.data-v-14fa498a,
uni-window.data-v-14fa498a {
  width: 100vw;
  max-width: 100vw;
  min-width: 100vw;
  box-sizing: border-box;
  overflow-x: hidden;
  margin: 0;
  padding: 0;
}

/* 固定宽度防止溢出 */
view.data-v-14fa498a {
  max-width: 100vw;
  box-sizing: border-box;
}
.app-container.data-v-14fa498a {
  width: 100vw;
  max-width: 100vw;
  margin: 0;
  padding: 0;
  position: relative;
  height: 100vh;
  box-sizing: border-box;
  left: 0;
  right: 0;
}
.scrollable-content.data-v-14fa498a {
  height: calc(100vh - 120rpx);
  width: 100%;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  scroll-behavior: smooth;
  overscroll-behavior: contain;
  box-sizing: border-box;
}
.bottom-spacer.data-v-14fa498a {
  height: 120rpx;
  width: 100%;
}
.header.data-v-14fa498a {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #4285f4;
  color: #fff;
  padding: 20rpx 30rpx;
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  z-index: 100;
  width: 100%;
  box-sizing: border-box;
}
.back-btn.data-v-14fa498a {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.back-icon.data-v-14fa498a {
  font-size: 40rpx;
}
.title.data-v-14fa498a {
  font-size: 36rpx;
  font-weight: 500;
}
.more-btn.data-v-14fa498a {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.more-icon.data-v-14fa498a {
  font-size: 30rpx;
}
.color-info.data-v-14fa498a {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
  width: 100%;
  box-sizing: border-box;
}
.category-line.data-v-14fa498a {
  display: flex;
  align-items: center;
  width: 100%;
  font-size: 26rpx;
  color: #333;
  margin-bottom: 15rpx;
}
.offline-tag.data-v-14fa498a {
  margin-left: 20rpx;
  font-size: 24rpx;
  color: #999;
}
.online-tag.data-v-14fa498a {
  margin-left: 20rpx;
  font-size: 24rpx;
  color: #4CAF50;
}
.color-preview.data-v-14fa498a {
  width: 200rpx;
  height: 200rpx;
  border-radius: 50%;
  overflow: hidden;
  transition: all 0.3s ease;
  position: relative;
  z-index: 1;
  display: block;
  border: none;
  outline: none;
  background: transparent;
}
.color-circle.data-v-14fa498a {
  width: 100%;
  height: 100%;
  display: block;
  border: none;
  outline: none;
  object-fit: cover;
}
.color-name.data-v-14fa498a {
  font-size: 30rpx;
  font-weight: bold;
  margin-bottom: 5rpx;
}
.color-category.data-v-14fa498a {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 5rpx;
}
.dye-count.data-v-14fa498a {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 15rpx;
}
.mode-tabs.data-v-14fa498a {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}
.mode-tab.data-v-14fa498a {
  padding: 10rpx 20rpx;
  background-color: #f0f0f0;
  border-radius: 40rpx;
  margin-right: 10rpx;
  font-size: 28rpx;
  color: #333;
  cursor: pointer;
}
.active.data-v-14fa498a {
  background-color: #4285f4;
  color: #fff;
}
.divider.data-v-14fa498a {
  width: 100%;
  height: 1rpx;
  background-color: #f0f0f0;
  margin-bottom: 10rpx;
}
.color-structure.data-v-14fa498a {
  font-size: 26rpx;
  font-weight: bold;
  align-self: flex-start;
  margin-bottom: 10rpx;
}
.dye-instruction.data-v-14fa498a {
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  width: 100%;
  box-sizing: border-box;
  margin-bottom: 20rpx;
}
.instruction-title.data-v-14fa498a {
  font-size: 30rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
}
.step.data-v-14fa498a {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
}
.step-num.data-v-14fa498a {
  width: 40rpx;
  height: 40rpx;
  background-color: #4285f4;
  color: #fff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  margin-right: 10rpx;
}
.step-desc.data-v-14fa498a {
  font-size: 28rpx;
  color: #333;
}
.step-detail.data-v-14fa498a {
  font-size: 28rpx;
  color: #666;
  margin-left: 20rpx;
  margin-bottom: 20rpx;
  white-space: pre-wrap;
  word-wrap: break-word;
  line-height: 1.8;
}
.base-color-info.data-v-14fa498a {
  margin-top: 20rpx;
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}
.base-color-detail.data-v-14fa498a {
  font-size: 26rpx;
  color: #666;
  margin-top: 10rpx;
  line-height: 1.5;
}
.bottom-actions.data-v-14fa498a {
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx;
  background-color: #fff;
  border-top: 1rpx solid #f0f0f0;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.1);
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 100;
  width: 100%;
  box-sizing: border-box;
}
.action-icon.data-v-14fa498a {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-right: 30rpx;
}
.icon.data-v-14fa498a {
  font-size: 40rpx;
  color: #666;
}
.count.data-v-14fa498a {
  font-size: 24rpx;
  color: #999;
  margin-top: 4rpx;
}
.dye-btn.data-v-14fa498a {
  flex: 1;
  background-color: #4285f4;
  color: #fff;
  height: 80rpx;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 30rpx;
  margin-left: 20rpx;
}

/* 确保所有内容区块占满整个宽度 */
.color-info.data-v-14fa498a,
.dye-instruction.data-v-14fa498a {
  width: 100%;
  box-sizing: border-box;
  max-width: 100vw;
}

/* Color composition container with adaptive height */
.color-composition.data-v-14fa498a {
  min-height: var(--composition-min-height);
  padding: var(--composition-padding);
}
.title.data-v-14fa498a {
  text-align: center;
  font-size: 20px;
  margin-bottom: 20px;
  color: #666;
}

/* Horizontal scrollable container for color bars */
.color-bar-container.data-v-14fa498a {
  height: var(--color-bar-height);
  display: flex;
  justify-content: flex-start;
  align-items: flex-end;
  overflow-x: auto;
  white-space: nowrap;
  gap: var(--color-bar-gap);
  scroll-behavior: smooth;
}
.color-bar.data-v-14fa498a {
  display: flex;
  flex-direction: column;
  align-items: center;
}

/* Responsive color block with flexible width */
.color-block.data-v-14fa498a {
  width: 40px;
  min-width: var(--color-block-min-width);
  max-width: var(--color-block-max-width);
  border-radius: 5rpx;
}
.gray.data-v-14fa498a {
  background-color: gray;
}
.green.data-v-14fa498a {
  background-color: green;
}
.yellow.data-v-14fa498a {
  background-color: yellow;
}
.orange.data-v-14fa498a {
  background-color: orange;
}
.red.data-v-14fa498a {
  background-color: red;
}
.purple.data-v-14fa498a {
  background-color: purple;
}
.brown.data-v-14fa498a {
  background-color: brown;
}
.blue.data-v-14fa498a {
  background-color: blue;
}
.black.data-v-14fa498a {
  background-color: black;
}
.bleached.data-v-14fa498a {
  background-color: #f0f0f0;
  /* Light gray to simulate bleached/white */
}
.white.data-v-14fa498a {
  background-color: #f0f0f0;
  /* Light gray to simulate white on white background */
}
.color-label.data-v-14fa498a {
  margin-top: 5px;
  text-align: center;
  display: flex;
  flex-direction: column;
}