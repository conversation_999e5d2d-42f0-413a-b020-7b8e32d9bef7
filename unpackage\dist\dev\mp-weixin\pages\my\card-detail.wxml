<view class="card-detail-container"><view class="operator-info card"><view class="operator-header"><image class="operator-logo" src="{{a}}"></image><text class="operator-name">中国联通</text><view class="operator-status"><text class="status-badge">已激活</text><text class="status-refresh" bindtap="{{b}}">⟳</text></view></view><view class="info-list"><view class="info-row"><text class="info-label">充值号：</text><text class="info-value">76033840</text><button class="mini-btn" size="mini" bindtap="{{c}}">切换</button></view><view class="info-row"><text class="info-label">WIFI名称：</text><text class="info-value">Asimon</text></view><view class="info-row"><text class="info-label">WIFI密码：</text><text class="info-value">Simon..@Simon</text><button class="mini-btn" size="mini" bindtap="{{d}}">修改</button></view></view></view><view class="package-info card"><view class="package-header"><text class="section-title">我的套餐</text><text class="package-expire">到期时间: 2029-01-12</text></view><view class="package-content"><view class="package-progress"><text class="package-name">每月99996高速流量套餐</text><progress percent="{{100}}" activeColor="#1890ff" backgroundColor="#e6f7ff" stroke-width="10" class="progress-bar"/><view class="progress-text"><text>已用0%</text><text>剩余100%</text></view></view><button class="renew-btn" bindtap="{{e}}">立即续费</button></view></view><view class="official-account card"><image class="qrcode" src="{{f}}"></image><view class="official-content"><text class="official-title">关注公众号</text><text class="official-desc">获取最新优惠活动、查询流量使用情况</text><button class="follow-btn" open-type="contact">关注公众号</button></view></view><view class="grid-container"><view wx:for="{{g}}" wx:for-item="item" wx:key="c" bindtap="{{item.d}}" bindtouchstart="{{item.e}}" bindtouchend="{{item.f}}" class="{{['grid-item', item.g && 'active']}}"><image class="grid-icon" src="{{item.a}}"></image><text class="grid-text">{{item.b}}</text></view></view><view class="bottom-nav"><view class="nav-item" bindtap="{{i}}"><image class="nav-icon" src="{{h}}"></image><text>激活</text></view><view class="nav-item" bindtap="{{k}}"><image class="nav-icon" src="{{j}}"></image><text>客服</text></view><view class="nav-item" bindtap="{{m}}"><image class="nav-icon" src="{{l}}"></image><text>我的</text></view></view><uni-popup wx:if="{{p}}" class="r" u-s="{{['d']}}" u-r="popup" u-i="6772c398-0" bind:__l="__l" u-p="{{p}}"><uni-popup-message wx:if="{{n}}" u-i="6772c398-1,6772c398-0" bind:__l="__l" u-p="{{n}}"></uni-popup-message></uni-popup></view>