"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_request = require("../../utils/request.js");
const common_assets = require("../../common/assets.js");
const _sfc_main = {
  data() {
    return {
      // baseUrl : "http://127.0.0.1:6549/api",
      baseUrl: "https://www.narenqiqige.com/api",
      currentTab: 0,
      tabs: ["全部", "待付款", "待发货", "待收货", "已完成"],
      orderList: [],
      loading: false,
      page: 1,
      pageSize: 10,
      hasMore: true
    };
  },
  onLoad() {
    this.loadOrders();
  },
  onShow() {
    this.loadOrders();
  },
  methods: {
    async switchTab(index) {
      this.currentTab = index;
      this.page = 1;
      this.orderList = [];
      this.hasMore = true;
      await this.loadOrders();
    },
    goShopping() {
      common_vendor.index.switchTab({
        url: "/pages/mall/mall"
      });
    },
    // 获取订单列表
    async loadOrders() {
      if (this.loading || !this.hasMore)
        return;
      this.loading = true;
      try {
        let status = null;
        if (this.currentTab === 1)
          status = 1;
        else if (this.currentTab === 2)
          status = 2;
        else if (this.currentTab === 3)
          status = 3;
        else if (this.currentTab === 4)
          status = 4;
        const params = {
          page: this.page,
          pageSize: this.pageSize
        };
        if (status !== null) {
          params.status = status;
        }
        const response = await utils_request.apiService.mall.orders.list(params);
        if (response.code === 200) {
          const orders = response.data.records || [];
          for (let order of orders) {
            try {
              const itemsResponse = await utils_request.apiService.mall.orders.items(order.id);
              if (itemsResponse.code === 200) {
                order.items = itemsResponse.data || [];
                order.totalQuantity = order.items.reduce((sum, item) => sum + item.quantity, 0);
              } else {
                order.items = [];
                order.totalQuantity = 0;
              }
            } catch (error) {
              common_vendor.index.__f__("error", "at pages/my/order.vue:154", "获取订单项失败:", error);
              order.items = [];
              order.totalQuantity = 0;
            }
          }
          if (this.page === 1) {
            this.orderList = orders;
          } else {
            this.orderList = [...this.orderList, ...orders];
          }
          this.hasMore = orders.length === this.pageSize;
          this.page++;
        } else {
          common_vendor.index.showToast({
            title: response.message || "获取订单列表失败",
            icon: "none"
          });
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/my/order.vue:175", "获取订单列表失败:", error);
        common_vendor.index.showToast({
          title: "网络错误，请重试",
          icon: "none"
        });
      } finally {
        this.loading = false;
      }
    },
    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        1: "待付款",
        2: "待发货",
        3: "待收货",
        4: "已完成",
        5: "已取消"
      };
      return statusMap[status] || "未知状态";
    },
    // 跳转到订单详情
    goToOrderDetail(orderId) {
      common_vendor.index.navigateTo({
        url: `/pages/my/order-detail?id=${orderId}`
      });
    },
    // 支付订单
    async payOrder(orderNo) {
      common_vendor.index.__f__("log", "at pages/my/order.vue:206", "支付的单号", orderNo);
      try {
        common_vendor.index.__f__("log", "at pages/my/order.vue:208", "付款中....");
        common_vendor.index.showToast({
          title: "跳转支付页面",
          icon: "none"
        });
        common_vendor.index.__f__("log", "at pages/my/order.vue:215", "订单编号");
        utils_request.apiService.mall.orders.pay(orderNo, {
          paymentMethod: 1
        }).then((res) => {
          common_vendor.wx$1.requestPayment({
            timeStamp: res.data.timeStamp,
            nonceStr: res.data.nonceStr,
            package: res.data.package,
            signType: "MD5",
            paySign: res.data.paySign,
            success: function(e) {
              common_vendor.index.__f__("log", "at pages/my/order.vue:229", e);
              common_vendor.index.showToast({
                title: "支付成功",
                icon: "success"
              });
              setTimeout(() => {
                common_vendor.index.redirectTo({
                  url: `/pages/my/order-detail`
                });
              }, 1500);
            },
            fail: function(e) {
              common_vendor.index.__f__("log", "at pages/my/order.vue:246", "支付失败", e);
            }
          });
        }).catch((err) => {
          common_vendor.index.__f__("error", "at pages/my/order.vue:262", "支付失败:", err);
          common_vendor.index.showToast({
            title: "支付失败",
            icon: "none"
          });
        });
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/my/order.vue:269", "支付失败:", error);
        common_vendor.index.showToast({
          title: "支付失败",
          icon: "none"
        });
      }
    },
    // 取消订单
    async cancelOrder(orderNo) {
      try {
        common_vendor.index.showModal({
          title: "确认取消",
          content: "确定要取消这个订单吗？",
          success: async (res) => {
            if (res.confirm) {
              const response = await utils_request.apiService.mall.orders.cancel(orderNo);
              if (response.code === 200) {
                common_vendor.index.showToast({
                  title: "订单已取消",
                  icon: "success"
                });
                this.page = 1;
                this.orderList = [];
                this.hasMore = true;
                await this.loadOrders();
              } else {
                common_vendor.index.showToast({
                  title: response.message || "取消失败",
                  icon: "none"
                });
              }
            }
          }
        });
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/my/order.vue:306", "取消订单失败:", error);
        common_vendor.index.showToast({
          title: "取消失败",
          icon: "none"
        });
      }
    },
    // 确认收货
    async confirmReceive(orderNo) {
      try {
        common_vendor.index.showModal({
          title: "确认收货",
          content: "确定已收到商品吗？",
          success: async (res) => {
            if (res.confirm) {
              const response = await utils_request.apiService.mall.orders.confirm(orderNo);
              if (response.code === 200) {
                common_vendor.index.showToast({
                  title: "确认收货成功",
                  icon: "success"
                });
                this.page = 1;
                this.orderList = [];
                this.hasMore = true;
                await this.loadOrders();
              } else {
                common_vendor.index.showToast({
                  title: response.message || "确认收货失败",
                  icon: "none"
                });
              }
            }
          }
        });
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/my/order.vue:343", "确认收货失败:", error);
        common_vendor.index.showToast({
          title: "确认收货失败",
          icon: "none"
        });
      }
    }
  },
  // 下拉刷新
  onPullDownRefresh() {
    this.page = 1;
    this.orderList = [];
    this.hasMore = true;
    this.loadOrders().then(() => {
      common_vendor.index.stopPullDownRefresh();
    });
  },
  // 上拉加载更多
  onReachBottom() {
    this.loadOrders();
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.f($data.tabs, (tab, index, i0) => {
      return common_vendor.e({
        a: common_vendor.t(tab),
        b: $data.currentTab === index
      }, $data.currentTab === index ? {} : {}, {
        c: index,
        d: $data.currentTab === index ? 1 : "",
        e: common_vendor.o(($event) => $options.switchTab(index), index)
      });
    }),
    b: $data.loading
  }, $data.loading ? {} : $data.orderList.length > 0 ? {
    d: common_vendor.f($data.orderList, (order, k0, i0) => {
      return common_vendor.e({
        a: common_vendor.t(order.orderNo),
        b: common_vendor.t($options.getStatusText(order.status)),
        c: common_vendor.f(order.items, (item, k1, i1) => {
          return {
            a: $data.baseUrl + item.productImage,
            b: common_vendor.t(item.productName),
            c: common_vendor.t(item.productSpec),
            d: common_vendor.t(item.price),
            e: common_vendor.t(item.quantity),
            f: item.id
          };
        }),
        d: common_vendor.t(order.totalQuantity),
        e: common_vendor.t(order.totalAmount),
        f: order.status === 1
      }, order.status === 1 ? {
        g: common_vendor.o(($event) => $options.payOrder(order.orderNo), order.id)
      } : {}, {
        h: order.status === 1
      }, order.status === 1 ? {
        i: common_vendor.o(($event) => $options.cancelOrder(order.orderNo), order.id)
      } : {}, {
        j: order.status === 3
      }, order.status === 3 ? {
        k: common_vendor.o(($event) => $options.confirmReceive(order.orderNo), order.id)
      } : {}, {
        l: order.id,
        m: common_vendor.o(($event) => $options.goToOrderDetail(order.id), order.id)
      });
    })
  } : {
    e: common_assets._imports_0$5,
    f: common_vendor.o((...args) => $options.goShopping && $options.goShopping(...args))
  }, {
    c: $data.orderList.length > 0
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/my/order.js.map
