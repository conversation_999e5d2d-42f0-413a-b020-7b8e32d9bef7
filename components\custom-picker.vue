<template>
  <!-- 自定义 Picker 容器 -->
  <view class="custom-picker-container">
    <view class="picker-display" @click="togglePicker">
      {{ ratioOptions[currentIndex] }}
      <view class="arrow-icon" :class="{ 'arrow-up': showPicker }"></view>
    </view>
    
    <!-- 弹出层 @touchmove.stop.prevent-->
    <view class="picker-popup" v-if="showPicker" >
      <view class="picker-mask" @click="togglePicker"></view>
      <view class="picker-content" :style="{ height: pickerHeight + 'px' }">
        <view class="picker-header">
          <text class="picker-btn picker-cancel" @click="togglePicker">取消</text>
          <text class="picker-title">选择比例</text>
          <text class="picker-btn picker-confirm" @click="confirmSelection">确定</text>
        </view>
        <picker-view class="picker-view" :value="[currentIndex]" @change="handlePickerChange">
          <picker-view-column>
            <view class="picker-item" v-for="(item, index) in ratioOptions" :key="index">
              <text :class="['item-text', currentIndex === index ? 'active' : '']">{{ item }}</text>
            </view>
          </picker-view-column>
        </picker-view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  props: {
    ratioOptions: {
      type: Array,
      default: () => []
    },
    value: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      showPicker: true,
      currentIndex: this.value,
      tempIndex: this.value,
      pickerHeight: 300 // 默认高度，可根据需要调整
    }
  },
  methods: {
    togglePicker() {
      this.showPicker = !this.showPicker
      if (!this.showPicker) {
        // 取消时恢复原值
        this.tempIndex = this.currentIndex
      }
    },
    handlePickerChange:function(e) {
        this.tempIndex = e.detail.value[0]
      },
    confirmSelection() {
      this.currentIndex = this.tempIndex
      this.$emit('change', { detail: { value: this.currentIndex } })
      this.$emit('input', this.currentIndex)
      this.togglePicker()
    }
  },
  watch: {
    value(newVal) {
      this.currentIndex = newVal
      this.tempIndex = newVal
    }
  },
  created() {
    // 获取系统信息，调整picker高度
    uni.getSystemInfo({
      success: (res) => {
        this.pickerHeight = res.windowHeight * 0.6
      }
    })
  }
}
</script>

<style lang="scss" scoped>
/* 容器样式 */
.custom-picker-container {
  position: relative;
  width: 100%;
}

/* 显示区域样式 */
.picker-display {
  height: 30rpx;
  line-height: 80rpx;
  padding: 0 30rpx;
  border: 1rpx solid #ebedf0;
  border-radius: 16rpx;
  background-color: #fff;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 28rpx;
  color: #333;
}

.arrow-icon {
  width: 0;
  height: 0;
  border-left: 10rpx solid transparent;
  border-right: 10rpx solid transparent;
  border-top: 16rpx solid #999;
  transition: transform 0.3s;
}

.arrow-icon.arrow-up {
  transform: rotate(180deg);
}

/* 弹出层样式 */
.picker-popup {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999;
}

.picker-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
}

.picker-content {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  border-top-left-radius: 32rpx;
  border-top-right-radius: 32rpx;
  overflow: hidden;
  transform: translateY(0);
  animation: slide-up 0.3s;
}

@keyframes slide-up {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

.picker-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 32rpx;
  border-bottom: 1rpx solid #f5f5f5;
}

.picker-btn {
  font-size: 28rpx;
  color: #5B8EFF;
}

.picker-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

/* Picker 内容样式 */
.picker-view {
  height: 100%;
}

.picker-item {
  height: 88rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.item-text {
  font-size: 32rpx;
  color: #666;
}

.item-text.active {
  color: #5B8EFF;
  font-weight: bold;
}
</style>