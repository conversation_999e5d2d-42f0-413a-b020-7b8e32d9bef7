"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_request = require("../../utils/request.js");
const common_assets = require("../../common/assets.js");
const _sfc_main = {
  data() {
    return {
      // baseUrl : "http://127.0.0.1:6549/api",
      baseUrl: "https://www.narenqiqige.com/api",
      orderId: "",
      order: {}
      // {
      //      id: '1001',
      //      orderNo: 'DD20230515143022',
      //      shopName: '专业染发店',
      //      status: 'shipped',
      //      productName: '专业染发剂套装',
      //      productSpec: '棕色 200ml',
      //      productImage: '/static/images/product1.jpg',
      //      price: 128.00,
      //      count: 1,
      //      shippingFee: 12.00,
      //      receiverName: '张三',
      //      receiverPhone: '138****5678',
      //      address: '广东省广州市天河区体育西路123号',
      //      createTime: '2023-05-15 14:30:22',
      //      payTime: '2023-05-15 14:35:46',
      //      shippingTime: '2023-05-16 09:20:33',
      //      completeTime: ''
      //    }
    };
  },
  computed: {
    statusInfo() {
      const statusMap = {
        "pending": {
          text: "等待付款",
          desc: "请在24小时内完成支付，超时订单将自动取消"
        },
        "paid": {
          text: "等待发货",
          desc: "商家已接单，正在准备发货"
        },
        "shipped": {
          text: "等待收货",
          desc: "商品已发货，请耐心等待"
        },
        "completed": {
          text: "交易完成",
          desc: "交易已完成，感谢您的购买"
        },
        "cancelled": {
          text: "交易关闭",
          desc: "订单已取消"
        }
      };
      return statusMap[this.order.status] || { text: "未知状态", desc: "" };
    },
    progressItems() {
      var _a;
      const items = [
        {
          title: "提交订单",
          time: this.order.createTime,
          active: false,
          done: true
        },
        {
          title: "付款成功",
          time: this.order.payTime,
          active: false,
          done: this.order.status !== "pending"
        },
        {
          title: "商家发货",
          time: this.order.shippingTime,
          active: false,
          done: this.order.status === "shipped" || this.order.status === "completed"
        },
        {
          title: "交易完成",
          time: this.order.completeTime,
          active: false,
          done: this.order.status === "completed"
        }
      ];
      for (let i = items.length - 1; i >= 0; i--) {
        if (items[i].done && !((_a = items[i + 1]) == null ? void 0 : _a.done)) {
          items[i].active = true;
          break;
        }
      }
      return items;
    }
  },
  onLoad(options) {
    if (options.id) {
      this.orderId = options.id;
      this.fetchOrderDetail(this.orderId);
    }
  },
  methods: {
    goBack() {
      common_vendor.index.navigateBack();
    },
    copyOrderNo() {
      common_vendor.index.setClipboardData({
        data: this.order.orderNo,
        success: () => {
          common_vendor.index.showToast({
            title: "复制成功",
            icon: "success"
          });
        }
      });
    },
    fetchOrderDetail(orderId) {
      utils_request.apiService.mall.orders.detail(orderId).then((res) => {
        this.orderId = res.data.id;
        this.order = res.data;
        common_vendor.index.__f__("log", "at pages/my/order-detail.vue:246", "订单详细", res);
      }).catch((err) => {
      });
      common_vendor.index.__f__("log", "at pages/my/order-detail.vue:250", "获取订单详情，ID:", orderId);
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  var _a, _b, _c, _d;
  return common_vendor.e({
    a: common_vendor.t($options.statusInfo.text),
    b: common_vendor.t($options.statusInfo.desc),
    c: common_vendor.f($options.progressItems, (item, index, i0) => {
      return common_vendor.e({
        a: item.active ? 1 : "",
        b: item.done ? 1 : "",
        c: index < $options.progressItems.length - 1
      }, index < $options.progressItems.length - 1 ? {
        d: $options.progressItems[index + 1].active || $options.progressItems[index + 1].done ? 1 : ""
      } : {}, {
        e: common_vendor.t(item.title),
        f: common_vendor.t(item.time || ""),
        g: item.active ? 1 : "",
        h: item.done ? 1 : "",
        i: index
      });
    }),
    d: common_assets._imports_0$11,
    e: common_vendor.t($data.order.receiverName),
    f: common_vendor.t($data.order.receiverPhone),
    g: common_vendor.t($data.order.address),
    h: common_assets._imports_1$4,
    i: common_vendor.t($data.order.shopName),
    j: $data.baseUrl + $data.order.productImage,
    k: common_vendor.t($data.order.productName),
    l: common_vendor.t($data.order.productSpec),
    m: common_vendor.t((_a = $data.order.price) == null ? void 0 : _a.toFixed(2)),
    n: common_vendor.t($data.order.count),
    o: common_vendor.t((_b = $data.order.price * $data.order.count) == null ? void 0 : _b.toFixed(2)),
    p: common_vendor.t((_c = $data.order.shippingFee) == null ? void 0 : _c.toFixed(2)),
    q: common_vendor.t((_d = $data.order.price * $data.order.count + $data.order.shippingFee) == null ? void 0 : _d.toFixed(2)),
    r: common_assets._imports_2$2,
    s: common_vendor.t($data.order.orderNo),
    t: common_vendor.o((...args) => $options.copyOrderNo && $options.copyOrderNo(...args)),
    v: common_vendor.t($data.order.createTime),
    w: $data.order.payTime
  }, $data.order.payTime ? {
    x: common_vendor.t($data.order.payTime)
  } : {}, {
    y: $data.order.shippingTime
  }, $data.order.shippingTime ? {
    z: common_vendor.t($data.order.shippingTime)
  } : {}, {
    A: $data.order.completeTime
  }, $data.order.completeTime ? {
    B: common_vendor.t($data.order.completeTime)
  } : {}, {
    C: $data.order.status === "pending"
  }, $data.order.status === "pending" ? {} : {}, {
    D: $data.order.status === "completed"
  }, $data.order.status === "completed" ? {} : {}, {
    E: $data.order.status === "pending"
  }, $data.order.status === "pending" ? {} : {}, {
    F: $data.order.status === "shipped"
  }, $data.order.status === "shipped" ? {} : {}, {
    G: $data.order.status === "completed"
  }, $data.order.status === "completed" ? {} : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-433b65bc"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/my/order-detail.js.map
