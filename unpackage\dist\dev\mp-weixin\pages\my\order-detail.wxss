/* 水平间距 */
/* 水平间距 */
/* 全局样式修复 - 确保页面占满屏幕宽度 */
page.data-v-433b65bc {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
}

/* 通用容器样式 */
.container.data-v-433b65bc, .page-container.data-v-433b65bc {
  width: 100%;
  min-height: 100vh;
  box-sizing: border-box;
}

/* 修复小程序页面宽度问题 */
uni-page-body.data-v-433b65bc {
  width: 100% !important;
}

/* 确保所有view元素正确继承宽度 */
view.data-v-433b65bc {
  box-sizing: border-box;
}
.container.data-v-433b65bc {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 120rpx;
}
.header.data-v-433b65bc {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 88rpx;
  background-color: #ffffff;
  padding: 0 30rpx;
}
.header .back-btn.data-v-433b65bc {
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
}
.header .back-btn .back-icon.data-v-433b65bc {
  width: 36rpx;
  height: 36rpx;
}
.header .title.data-v-433b65bc {
  font-size: 34rpx;
  font-weight: bold;
  color: #333;
}
.header .placeholder.data-v-433b65bc {
  width: 40rpx;
}
.status-section.data-v-433b65bc {
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 40rpx 30rpx;
  background-color: #ff6b81;
  color: #fff;
}
.status-section .status-text.data-v-433b65bc {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}
.status-section .status-desc.data-v-433b65bc {
  font-size: 26rpx;
  opacity: 0.8;
}
.progress-section.data-v-433b65bc {
  background-color: #ffffff;
  padding: 30rpx;
  margin-bottom: 20rpx;
}
.progress-section .progress-item.data-v-433b65bc {
  display: flex;
  position: relative;
}
.progress-section .progress-item .dot.data-v-433b65bc {
  width: 20rpx;
  height: 20rpx;
  border-radius: 50%;
  background-color: #ddd;
  margin-top: 8rpx;
}
.progress-section .progress-item .dot.active.data-v-433b65bc {
  background-color: #ff6b81;
}
.progress-section .progress-item .dot.done.data-v-433b65bc {
  background-color: #ff6b81;
}
.progress-section .progress-item .progress-line.data-v-433b65bc {
  position: absolute;
  left: 10rpx;
  top: 28rpx;
  width: 2rpx;
  height: 60rpx;
  background-color: #ddd;
}
.progress-section .progress-item .progress-line.active.data-v-433b65bc {
  background-color: #ff6b81;
}
.progress-section .progress-item .progress-content.data-v-433b65bc {
  margin-left: 30rpx;
  margin-bottom: 30rpx;
}
.progress-section .progress-item .progress-content .progress-title.data-v-433b65bc {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 6rpx;
}
.progress-section .progress-item .progress-content .progress-time.data-v-433b65bc {
  font-size: 24rpx;
  color: #999;
}
.progress-section .progress-item .progress-content.active .progress-title.data-v-433b65bc {
  color: #ff6b81;
  font-weight: 500;
}
.progress-section .progress-item .progress-content.done .progress-title.data-v-433b65bc {
  color: #333;
}
.info-card.data-v-433b65bc {
  background-color: #ffffff;
  margin-bottom: 20rpx;
  padding: 30rpx;
}
.info-card .card-title.data-v-433b65bc {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}
.info-card .card-title .title-icon.data-v-433b65bc {
  width: 32rpx;
  height: 32rpx;
  margin-right: 10rpx;
}
.info-card .card-title text.data-v-433b65bc {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
}
.info-card .address-info.data-v-433b65bc {
  padding-left: 10rpx;
}
.info-card .address-info .contact.data-v-433b65bc {
  font-size: 30rpx;
  color: #333;
  margin-bottom: 10rpx;
}
.info-card .address-info .address.data-v-433b65bc {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
}
.info-card .product-item.data-v-433b65bc {
  display: flex;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f2f2f2;
}
.info-card .product-item .product-image.data-v-433b65bc {
  width: 160rpx;
  height: 160rpx;
  border-radius: 8rpx;
  background-color: #f5f5f5;
}
.info-card .product-item .product-info.data-v-433b65bc {
  flex: 1;
  margin-left: 20rpx;
  position: relative;
}
.info-card .product-item .product-info .product-name.data-v-433b65bc {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
}
.info-card .product-item .product-info .product-spec.data-v-433b65bc {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 40rpx;
}
.info-card .product-item .product-info .price-count.data-v-433b65bc {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  justify-content: space-between;
}
.info-card .product-item .product-info .price-count .price.data-v-433b65bc {
  font-size: 30rpx;
  color: #ff6b81;
  font-weight: 500;
}
.info-card .product-item .product-info .price-count .count.data-v-433b65bc {
  font-size: 26rpx;
  color: #999;
}
.info-card .order-amount.data-v-433b65bc {
  padding: 30rpx 0 10rpx;
}
.info-card .order-amount .amount-item.data-v-433b65bc {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20rpx;
  font-size: 28rpx;
  color: #666;
}
.info-card .order-amount .amount-divider.data-v-433b65bc {
  height: 1rpx;
  background-color: #f2f2f2;
  margin: 20rpx 0;
}
.info-card .order-amount .total.data-v-433b65bc {
  font-size: 30rpx;
  color: #333;
}
.info-card .order-amount .total .total-price.data-v-433b65bc {
  color: #ff6b81;
  font-weight: 500;
}
.info-card .order-info .info-item.data-v-433b65bc {
  display: flex;
  margin-bottom: 20rpx;
}
.info-card .order-info .info-item .info-label.data-v-433b65bc {
  width: 160rpx;
  font-size: 28rpx;
  color: #999;
}
.info-card .order-info .info-item .info-value.data-v-433b65bc {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}
.info-card .order-info .info-item .info-value-copy.data-v-433b65bc {
  flex: 1;
  display: flex;
  align-items: center;
}
.info-card .order-info .info-item .info-value-copy text.data-v-433b65bc {
  font-size: 28rpx;
  color: #333;
}
.info-card .order-info .info-item .info-value-copy .copy-btn.data-v-433b65bc {
  margin-left: 20rpx;
  font-size: 24rpx;
  color: #2196f3;
}
.bottom-btns.data-v-433b65bc {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ffffff;
  padding: 20rpx 30rpx;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
}
.bottom-btns .btn-group.data-v-433b65bc {
  display: flex;
  justify-content: flex-end;
}
.bottom-btns .btn-group .btn.data-v-433b65bc {
  margin-left: 20rpx;
  padding: 16rpx 30rpx;
  font-size: 28rpx;
  border-radius: 40rpx;
}
.bottom-btns .btn-group .btn.btn-default.data-v-433b65bc {
  color: #666;
  background-color: #f5f5f5;
  border: 1rpx solid #ddd;
}
.bottom-btns .btn-group .btn.btn-primary.data-v-433b65bc {
  color: #fff;
  background-color: #ff6b81;
}