"use strict";
const common_vendor = require("../../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      deviceId: "",
      filterDate: "",
      typeIndex: 0,
      actionTypes: [
        { id: 0, name: "全部类型" },
        { id: 1, name: "绑定" },
        { id: 2, name: "解绑" },
        { id: 3, name: "分配权限" }
      ],
      logList: [],
      loadingStatus: "more"
    };
  },
  onLoad(options) {
    this.deviceId = options.id || "";
    this.loadLogs();
  },
  methods: {
    async loadLogs() {
      var _a;
      this.loadingStatus = "loading";
      try {
        const res = await this.$api.dyeDevice.getLogs({
          deviceId: this.deviceId,
          date: this.filterDate,
          actionType: ((_a = this.actionTypes[this.typeIndex]) == null ? void 0 : _a.id) || 0
        });
        this.logList = res.data;
        this.loadingStatus = this.logList.length >= res.total ? "noMore" : "more";
      } catch (e) {
        this.loadingStatus = "more";
        common_vendor.index.showToast({ title: "加载失败", icon: "none" });
      }
    },
    dateChange(e) {
      this.filterDate = e.detail.value;
      this.loadLogs();
    },
    typeChange(e) {
      this.typeIndex = e.detail.value;
      this.loadLogs();
    },
    getActionText(action) {
      const map = {
        "bind": "绑定负责人",
        "unbind": "解绑负责人",
        "assign": "分配使用权限",
        "revoke": "收回使用权限"
      };
      return map[action] || action;
    },
    back() {
      common_vendor.index.navigateBack();
    }
  }
};
if (!Array) {
  const _easycom_uni_nav_bar2 = common_vendor.resolveComponent("uni-nav-bar");
  const _easycom_uni_icons2 = common_vendor.resolveComponent("uni-icons");
  const _easycom_uni_list_item2 = common_vendor.resolveComponent("uni-list-item");
  const _easycom_uni_list2 = common_vendor.resolveComponent("uni-list");
  const _easycom_uni_load_more2 = common_vendor.resolveComponent("uni-load-more");
  (_easycom_uni_nav_bar2 + _easycom_uni_icons2 + _easycom_uni_list_item2 + _easycom_uni_list2 + _easycom_uni_load_more2)();
}
const _easycom_uni_nav_bar = () => "../../../uni_modules/uni-nav-bar/components/uni-nav-bar/uni-nav-bar.js";
const _easycom_uni_icons = () => "../../../uni_modules/uni-icons/components/uni-icons/uni-icons.js";
const _easycom_uni_list_item = () => "../../../uni_modules/uni-list/components/uni-list-item/uni-list-item.js";
const _easycom_uni_list = () => "../../../uni_modules/uni-list/components/uni-list/uni-list.js";
const _easycom_uni_load_more = () => "../../../uni_modules/uni-load-more/components/uni-load-more/uni-load-more.js";
if (!Math) {
  (_easycom_uni_nav_bar + _easycom_uni_icons + _easycom_uni_list_item + _easycom_uni_list + _easycom_uni_load_more)();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  var _a;
  return {
    a: common_vendor.o($options.back),
    b: common_vendor.p({
      title: "染膏设备操作日志",
      ["left-icon"]: "back"
    }),
    c: common_vendor.t($data.filterDate || "选择日期"),
    d: common_vendor.p({
      type: "arrowdown",
      size: "14"
    }),
    e: common_vendor.o((...args) => $options.dateChange && $options.dateChange(...args)),
    f: common_vendor.t(((_a = $data.actionTypes[$data.typeIndex]) == null ? void 0 : _a.name) || "全部类型"),
    g: common_vendor.p({
      type: "arrowdown",
      size: "14"
    }),
    h: common_vendor.o((...args) => $options.typeChange && $options.typeChange(...args)),
    i: $data.typeIndex,
    j: $data.actionTypes,
    k: common_vendor.f($data.logList, (item, index, i0) => {
      return common_vendor.e({
        a: common_vendor.t(item.deviceName),
        b: common_vendor.t(item.deviceSn),
        c: item.detail
      }, item.detail ? {
        d: common_vendor.t(item.detail)
      } : {}, {
        e: index,
        f: "92f796fa-4-" + i0 + ",92f796fa-3",
        g: common_vendor.p({
          title: $options.getActionText(item.action),
          note: item.createTime,
          rightText: item.operator
        })
      });
    }),
    l: common_vendor.p({
      status: $data.loadingStatus
    })
  };
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-92f796fa"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/pages/device/dye-device-manager/log.js.map
