"use strict";
const common_vendor = require("../common/vendor.js");
class SwatchesCache {
  constructor() {
    this.cache = /* @__PURE__ */ new Map();
    this.cacheExpiry = 10 * 60 * 1e3;
    this.maxCacheSize = 50;
  }
  /**
   * 生成缓存键
   * @param {Object} params - 请求参数
   * @returns {string}
   */
  generateCacheKey(params) {
    const { colorType, colorName } = params;
    return `swatches_${colorType || "all"}_${colorName || "all"}`;
  }
  /**
   * 获取缓存数据
   * @param {Object} params - 请求参数
   * @returns {Object|null}
   */
  get(params) {
    const key = this.generateCacheKey(params);
    if (!this.cache.has(key)) {
      return null;
    }
    const cached = this.cache.get(key);
    const isExpired = Date.now() - cached.timestamp > this.cacheExpiry;
    if (isExpired) {
      this.cache.delete(key);
      return null;
    }
    common_vendor.index.__f__("log", "at utils/swatchesCache.js:43", "从缓存获取色板数据:", key);
    return cached.data;
  }
  /**
   * 设置缓存数据
   * @param {Object} params - 请求参数
   * @param {Object} data - 响应数据
   */
  set(params, data) {
    const key = this.generateCacheKey(params);
    if (this.cache.size >= this.maxCacheSize) {
      const firstKey = this.cache.keys().next().value;
      this.cache.delete(firstKey);
    }
    this.cache.set(key, {
      data,
      timestamp: Date.now()
    });
    common_vendor.index.__f__("log", "at utils/swatchesCache.js:66", "缓存色板数据:", key);
  }
  /**
   * 检查是否有缓存
   * @param {Object} params - 请求参数
   * @returns {boolean}
   */
  has(params) {
    const key = this.generateCacheKey(params);
    if (!this.cache.has(key)) {
      return false;
    }
    const cached = this.cache.get(key);
    const isExpired = Date.now() - cached.timestamp > this.cacheExpiry;
    if (isExpired) {
      this.cache.delete(key);
      return false;
    }
    return true;
  }
  /**
   * 清理过期缓存
   */
  cleanExpired() {
    const now = Date.now();
    for (const [key, cached] of this.cache.entries()) {
      if (now - cached.timestamp > this.cacheExpiry) {
        this.cache.delete(key);
      }
    }
  }
  /**
   * 清空所有缓存
   */
  clear() {
    this.cache.clear();
    common_vendor.index.__f__("log", "at utils/swatchesCache.js:109", "清空色板缓存");
  }
  /**
   * 获取缓存统计信息
   * @returns {Object}
   */
  getStats() {
    return {
      size: this.cache.size,
      maxSize: this.maxCacheSize,
      keys: Array.from(this.cache.keys())
    };
  }
  /**
   * 预缓存常用数据
   * @param {Function} apiCall - API调用函数
   */
  async preCache(apiCall) {
    const commonParams = [
      { colorType: "潮色", colorName: null },
      { colorType: "生活色", colorName: null },
      { colorType: "浅染深", colorName: null }
    ];
    for (const params of commonParams) {
      if (!this.has(params)) {
        try {
          const result = await apiCall(params);
          if (result.code === 200 && result.data) {
            this.set(params, result.data);
          }
        } catch (error) {
          common_vendor.index.__f__("warn", "at utils/swatchesCache.js:143", "预缓存失败:", params, error);
        }
      }
    }
  }
}
const swatchesCache = new SwatchesCache();
setInterval(() => {
  swatchesCache.cleanExpired();
}, 2 * 60 * 1e3);
exports.swatchesCache = swatchesCache;
//# sourceMappingURL=../../.sourcemap/mp-weixin/utils/swatchesCache.js.map
