"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      logs: []
    };
  },
  methods: {
    addLog(message) {
      const timestamp = (/* @__PURE__ */ new Date()).toLocaleTimeString();
      this.logs.unshift(`${timestamp}: ${message}`);
      common_vendor.index.__f__("log", "at pages/mall/test-navigation.vue:33", message);
    },
    navigateToProduct(productId) {
      this.addLog(`尝试跳转到商�?${productId} (使用/pages/mall/product路径)`);
      common_vendor.index.navigateTo({
        url: `/pages/mall/product/detail?id=${productId}`,
        success: (res) => {
          this.addLog(`跳转到商�?${productId} 成功`);
        },
        fail: (err) => {
          this.addLog(`跳转到商�?${productId} 失败: ${err.errMsg}`);
        }
      });
    },
    navigateToProductIndex(productId) {
      this.addLog(`尝试跳转到商品索�?${productId} (使用/pages/mall/product/index路径)`);
      common_vendor.index.navigateTo({
        url: `/pages/mall/product/index?id=${productId}`,
        success: (res) => {
          this.addLog(`跳转到商品索�?${productId} 成功`);
        },
        fail: (err) => {
          this.addLog(`跳转到商品索�?${productId} 失败: ${err.errMsg}`);
        }
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.o(($event) => $options.navigateToProduct(1)),
    b: common_vendor.o(($event) => $options.navigateToProduct(2)),
    c: common_vendor.o(($event) => $options.navigateToProductIndex(1)),
    d: common_vendor.o(($event) => $options.navigateToProductIndex(2)),
    e: common_vendor.f($data.logs, (log, index, i0) => {
      return {
        a: common_vendor.t(log),
        b: index
      };
    })
  };
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/mall/test-navigation.js.map
