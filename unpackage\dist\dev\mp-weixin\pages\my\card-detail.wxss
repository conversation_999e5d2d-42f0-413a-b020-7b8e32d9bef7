/* 水平间距 */
/* 水平间距 */
/* 全局样式修复 - 确保页面占满屏幕宽度 */
page {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
}

/* 通用容器样式 */
.container, .page-container {
  width: 100%;
  min-height: 100vh;
  box-sizing: border-box;
}

/* 修复小程序页面宽度问题 */
uni-page-body {
  width: 100% !important;
}

/* 确保所有view元素正确继承宽度 */
view {
  box-sizing: border-box;
}
.card-detail-container {
  padding: 20rpx 20rpx 120rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
  box-sizing: border-box;
}

/* 顶部标题栏 */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
  padding: 0 10rpx;
}
.header .title-container .title {
  font-size: 38rpx;
  font-weight: bold;
  color: #333;
  line-height: 1.4;
}
.header .title-container .subtitle {
  font-size: 24rpx;
  color: #666;
}
.header .user-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  overflow: hidden;
}
.header .user-avatar image {
  width: 100%;
  height: 100%;
}

/* 卡片通用样式 */
.card {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 24rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

/* 运营商信息 */
.operator-info .operator-header {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #eee;
}
.operator-info .operator-header .operator-logo {
  width: 60rpx;
  height: 60rpx;
  margin-right: 16rpx;
}
.operator-info .operator-header .operator-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #1890ff;
  flex: 1;
}
.operator-info .operator-header .operator-status {
  display: flex;
  align-items: center;
}
.operator-info .operator-header .operator-status .status-badge {
  background-color: #52c41a;
  color: #fff;
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  margin-right: 16rpx;
}
.operator-info .operator-header .operator-status .status-refresh {
  font-size: 32rpx;
  color: #666;
  padding: 8rpx;
}
.operator-info .info-list .info-row {
  display: flex;
  align-items: center;
  padding: 16rpx 0;
  font-size: 28rpx;
}
.operator-info .info-list .info-row:not(:last-child) {
  border-bottom: 1rpx dashed #eee;
}
.operator-info .info-list .info-row .info-label {
  color: #666;
  width: 180rpx;
}
.operator-info .info-list .info-row .info-value {
  flex: 1;
  color: #333;
}
.operator-info .mini-btn {
  margin: 0;
  padding: 0 20rpx;
  height: 50rpx;
  line-height: 50rpx;
  font-size: 24rpx;
  background-color: #1890ff;
  color: #fff;
  border-radius: 25rpx;
}
.operator-info .mini-btn::after {
  border: none;
}

/* 套餐信息 */
.package-info .package-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}
.package-info .package-header .section-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
}
.package-info .package-header .package-expire {
  font-size: 24rpx;
  color: #666;
}
.package-info .package-content .package-progress {
  margin-bottom: 20rpx;
}
.package-info .package-content .package-progress .package-name {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 16rpx;
}
.package-info .package-content .package-progress .progress-bar {
  width: 100%;
  margin-bottom: 8rpx;
}
.package-info .package-content .package-progress .progress-text {
  display: flex;
  justify-content: space-between;
  font-size: 24rpx;
  color: #666;
}
.package-info .package-content .renew-btn {
  background-color: #1890ff;
  color: #fff;
  height: 80rpx;
  line-height: 80rpx;
  border-radius: 40rpx;
  font-size: 30rpx;
}
.package-info .package-content .renew-btn::after {
  border: none;
}

/* 公众号提示 */
.official-account {
  display: flex;
  align-items: center;
  padding: 30rpx;
}
.official-account .qrcode {
  width: 160rpx;
  height: 160rpx;
  margin-right: 30rpx;
  border: 1rpx solid #eee;
}
.official-account .official-content {
  flex: 1;
}
.official-account .official-content .official-title {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 12rpx;
}
.official-account .official-content .official-desc {
  display: block;
  font-size: 24rpx;
  color: #666;
  margin-bottom: 20rpx;
}
.official-account .follow-btn {
  background-color: #1890ff;
  color: #fff;
  height: 60rpx;
  line-height: 60rpx;
  border-radius: 30rpx;
  font-size: 26rpx;
  padding: 0 30rpx;
}
.official-account .follow-btn::after {
  border: none;
}

/* 功能按钮网格 */
.grid-container {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 1px;
  background-color: #eee;
  margin-bottom: 24rpx;
  border-radius: 12rpx;
  overflow: hidden;
}
.grid-container .grid-item {
  background-color: #fff;
  height: 180rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  transition: all 0.2s;
}
.grid-container .grid-item.active {
  background-color: #f0f7ff;
  transform: scale(0.98);
}
.grid-container .grid-item .grid-icon {
  width: 60rpx;
  height: 60rpx;
  margin-bottom: 16rpx;
}
.grid-container .grid-item .grid-text {
  font-size: 26rpx;
  color: #333;
  text-align: center;
}

/* 底部导航 */
.bottom-nav {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  display: flex;
  justify-content: space-around;
  padding: 20rpx 0;
  box-shadow: 0 -4rpx 12rpx rgba(0, 0, 0, 0.08);
  z-index: 100;
}
.bottom-nav .nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.bottom-nav .nav-item .nav-icon {
  width: 48rpx;
  height: 48rpx;
  margin-bottom: 8rpx;
}
.bottom-nav .nav-item text {
  font-size: 24rpx;
  color: #666;
}
.bottom-nav .nav-item.active text {
  color: #1890ff;
}

/* 响应式调整 */
@media (max-width: 320px) {
.grid-container .grid-text {
    font-size: 22rpx;
}
.package-info .package-name {
    font-size: 24rpx;
}
}