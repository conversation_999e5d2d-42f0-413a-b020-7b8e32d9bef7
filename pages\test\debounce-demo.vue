<template>
	<view class="demo-container">
		<view class="header">
			<text class="title">防抖功能演示</text>
		</view>
		
		<view class="content">
			<!-- 方法1：使用状态变量防抖 -->
			<view class="demo-section">
				<text class="section-title">方法1：状态变量防抖</text>
				<button 
					class="demo-btn" 
					:disabled="saving1" 
					@click="saveMethod1"
				>
					{{ saving1 ? '保存中...' : '保存数据' }}
				</button>
				<text class="demo-desc">使用 saving 状态变量防止重复点击</text>
			</view>
			
			<!-- 方法2：使用防抖工具类 -->
			<view class="demo-section">
				<text class="section-title">方法2：防抖工具类</text>
				<button 
					class="demo-btn" 
					:disabled="debounceHelper.isProcessing('save')" 
					@click="saveMethod2"
				>
					{{ debounceHelper.isProcessing('save') ? '处理中...' : '提交表单' }}
				</button>
				<text class="demo-desc">使用 DebounceHelper 类管理多个操作状态</text>
			</view>
			
			<!-- 方法3：使用混入 -->
			<view class="demo-section">
				<text class="section-title">方法3：按钮防抖混入</text>
				<button 
					class="demo-btn" 
					:disabled="isButtonDisabled('submit')" 
					@click="saveMethod3"
				>
					{{ isButtonDisabled('submit') ? '提交中...' : '提交订单' }}
				</button>
				<text class="demo-desc">使用 buttonDebounce 混入</text>
			</view>
			
			<!-- 方法4：函数防抖 -->
			<view class="demo-section">
				<text class="section-title">方法4：函数防抖</text>
				<button class="demo-btn" @click="debouncedSave">
					快速点击测试
				</button>
				<text class="demo-desc">使用 debounce 函数包装，300ms内只执行一次</text>
			</view>
			
			<!-- 操作日志 -->
			<view class="log-section">
				<text class="section-title">操作日志</text>
				<scroll-view class="log-list" scroll-y>
					<view v-for="(log, index) in logs" :key="index" class="log-item">
						<text>{{ log }}</text>
					</view>
				</scroll-view>
				<button class="clear-btn" @click="clearLogs">清除日志</button>
			</view>
		</view>
	</view>
</template>

<script>
import { DebounceHelper, buttonDebounce, debounce } from '@/utils/debounce.js';

export default {
	mixins: [buttonDebounce],
	data() {
		return {
			saving1: false,
			debounceHelper: new DebounceHelper(),
			logs: []
		};
	},
	created() {
		// 创建防抖函数
		this.debouncedSave = debounce(this.normalSave, 300);
	},
	methods: {
		// 方法1：使用状态变量
		async saveMethod1() {
			if (this.saving1) return;
			
			this.saving1 = true;
			this.addLog('方法1：开始保存...');
			
			try {
				// 模拟异步操作
				await this.simulateAsync(2000);
				this.addLog('方法1：保存成功！');
			} catch (error) {
				this.addLog('方法1：保存失败 - ' + error.message);
			} finally {
				this.saving1 = false;
			}
		},
		
		// 方法2：使用防抖工具类
		async saveMethod2() {
			if (this.debounceHelper.isProcessing('save')) return;
			
			this.debounceHelper.start('save');
			this.addLog('方法2：开始提交...');
			
			try {
				await this.simulateAsync(1500);
				this.addLog('方法2：提交成功！');
			} catch (error) {
				this.addLog('方法2：提交失败 - ' + error.message);
			} finally {
				this.debounceHelper.end('save');
			}
		},
		
		// 方法3：使用混入
		async saveMethod3() {
			if (this.isButtonDisabled('submit')) return;
			
			this.disableButton('submit');
			this.addLog('方法3：开始提交订单...');
			
			try {
				await this.simulateAsync(2500);
				this.addLog('方法3：订单提交成功！');
			} catch (error) {
				this.addLog('方法3：订单提交失败 - ' + error.message);
			} finally {
				this.enableButton('submit');
			}
		},
		
		// 方法4：普通保存函数（会被防抖包装）
		normalSave() {
			this.addLog('方法4：执行保存操作（防抖后）');
		},
		
		// 模拟异步操作
		simulateAsync(delay) {
			return new Promise((resolve, reject) => {
				setTimeout(() => {
					// 90% 成功率
					if (Math.random() > 0.1) {
						resolve();
					} else {
						reject(new Error('模拟网络错误'));
					}
				}, delay);
			});
		},
		
		// 添加日志
		addLog(message) {
			const timestamp = new Date().toLocaleTimeString();
			this.logs.unshift(`[${timestamp}] ${message}`);
			
			// 限制日志数量
			if (this.logs.length > 20) {
				this.logs = this.logs.slice(0, 20);
			}
		},
		
		// 清除日志
		clearLogs() {
			this.logs = [];
		}
	}
};
</script>

<style lang="scss">
.demo-container {
	min-height: 100vh;
	background-color: #f5f5f5;
}

.header {
	background-color: #4285f4;
	color: #fff;
	padding: 40rpx 30rpx;
	text-align: center;
}

.title {
	font-size: 36rpx;
	font-weight: bold;
}

.content {
	padding: 30rpx;
}

.demo-section {
	background-color: #fff;
	padding: 30rpx;
	margin-bottom: 30rpx;
	border-radius: 12rpx;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.section-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 20rpx;
}

.demo-btn {
	width: 100%;
	height: 80rpx;
	line-height: 80rpx;
	background-color: #4285f4;
	color: #fff;
	border-radius: 8rpx;
	font-size: 28rpx;
	margin-bottom: 15rpx;
	
	&[disabled] {
		background-color: #ccc;
		color: #999;
	}
}

.demo-desc {
	font-size: 24rpx;
	color: #666;
	line-height: 1.4;
}

.log-section {
	background-color: #fff;
	padding: 30rpx;
	border-radius: 12rpx;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.log-list {
	height: 400rpx;
	border: 1rpx solid #eee;
	border-radius: 8rpx;
	padding: 15rpx;
	margin: 20rpx 0;
}

.log-item {
	font-size: 24rpx;
	color: #333;
	line-height: 1.5;
	padding: 5rpx 0;
	border-bottom: 1rpx solid #f0f0f0;
}

.clear-btn {
	width: 100%;
	height: 60rpx;
	line-height: 60rpx;
	background-color: #ff5252;
	color: #fff;
	border-radius: 8rpx;
	font-size: 26rpx;
}
</style>
