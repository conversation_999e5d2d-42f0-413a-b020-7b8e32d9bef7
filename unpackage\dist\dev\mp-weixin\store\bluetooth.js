"use strict";
const common_vendor = require("../common/vendor.js");
const utils_bleCommandSet = require("../utils/bleCommandSet.js");
require("./wifi.js");
const utils_dyeConsumptionManager = require("../utils/dyeConsumptionManager.js");
const utils_writeDataChunked = require("../utils/writeDataChunked.js");
const utils_connectionStateValidator = require("../utils/connectionStateValidator.js");
const BLUETOOTH_SERVICE_UUID = "55535343-fe7d-4ae5-8fa9-9fafd205e455";
const WRITE_CHARACTERISTIC_UUID = "*************-43f4-a8d4-ecbe34729bb3";
const NOTIFY_CHARACTERISTIC_UUID = "49535343-1e4d-4bd9-ba61-23c647249616";
const arrayBufferToHex = (buffer) => {
  if (!buffer)
    return "";
  const hexArr = Array.prototype.map.call(
    new Uint8Array(buffer),
    (bit) => ("00" + bit.toString(16)).slice(-2)
  );
  return hexArr.join("");
};
function arrayToArrayBuffer(arr) {
  const buffer = new ArrayBuffer(arr.length);
  const dataView = new Uint8Array(buffer);
  for (let i = 0; i < arr.length; i++) {
    dataView[i] = arr[i] || 0;
  }
  return buffer;
}
const STORE_PERSISTENCE_KEY = "vuex_store_state";
function restorePersistedState() {
  try {
    const persistedState = common_vendor.index.getStorageSync(STORE_PERSISTENCE_KEY);
    if (persistedState) {
      common_vendor.index.__f__("log", "at store/bluetooth.js:84", "恢复持久化状态:", persistedState);
      return {
        connectedDevice: persistedState.connectedDevice || null
        // 其他需要持久化的状态可以在这里添加
      };
    }
  } catch (error) {
    common_vendor.index.__f__("error", "at store/bluetooth.js:91", "恢复持久化状态失败:", error);
  }
  return {};
}
function saveStateToStorage(state) {
  try {
    const stateToSave = {
      connectedDevice: state.connectedDevice
      // 其他需要持久化的状态可以在这里添加
    };
    common_vendor.index.setStorageSync(STORE_PERSISTENCE_KEY, stateToSave);
    common_vendor.index.__f__("log", "at store/bluetooth.js:104", "状态已保存到本地存储:", stateToSave);
  } catch (error) {
    common_vendor.index.__f__("error", "at store/bluetooth.js:106", "保存状态到本地存储失败:", error);
  }
}
function getInitialState() {
  restorePersistedState();
  return {
    isScanning: false,
    deviceList: [],
    // connectedDevice: persistedState.connectedDevice || null,
    connectedDevice: null,
    logs: [],
    discoveredDevices: /* @__PURE__ */ new Map(),
    discoveredNameDevices: /* @__PURE__ */ new Map(),
    bluetoothAdapterReady: false,
    error: null,
    discharging: false
  };
}
const store = common_vendor.createStore({
  state: getInitialState(),
  mutations: {
    SET_SCANNING(state, isScanning) {
      state.isScanning = isScanning;
    },
    //映射蓝牙id和设备参数
    ADD_DEVICE(state, device) {
      if (!state.discoveredDevices.has(device.deviceId)) {
        state.discoveredDevices.set(device.deviceId, device);
        state.deviceList = Array.from(state.discoveredDevices.values());
      }
    },
    // 映射蓝牙名和设备参数
    ADD_MAP_DEVICENAME(state, device) {
      if (!state.discoveredNameDevices.has(device.name)) {
        state.discoveredNameDevices.set(device.name, device);
        state.deviceList = Array.from(state.discoveredNameDevices.values());
      }
    },
    CLEAR_DEVICES(state) {
      state.deviceList = [];
      state.discoveredDevices.clear();
      state.discoveredNameDevices.clear();
    },
    SET_CONNECTED_DEVICE(state, device) {
      state.connectedDevice = device;
      saveStateToStorage(state);
    },
    SET_MOCK_CONNECTED_DEVICE(state, device) {
      state.connectedDevice = device;
    },
    CLEAR_CONNECTED_DEVICE(state) {
      state.connectedDevice = null;
      saveStateToStorage(state);
    },
    ADD_LOG(state, message) {
      common_vendor.index.__f__("log", "at store/bluetooth.js:181", "日志", message);
      const timestamp = (/* @__PURE__ */ new Date()).toLocaleTimeString();
      state.logs.unshift(`[${timestamp}] ${message}`);
      if (state.logs.length > 50)
        state.logs.pop();
    },
    SET_BLUETOOTH_ADAPTER_READY(state, ready) {
      state.bluetoothAdapterReady = ready;
    },
    SET_ERROR(state, error) {
      state.error = error;
    }
  },
  actions: {
    /**
     * 模拟连接设备
     */
    async mockConnect({
      commit,
      state
    }) {
      try {
        common_vendor.index.__f__("log", "at store/bluetooth.js:208", "mockConnect action 被调用");
        const mockDevice = {
          deviceId: "mock_connected_device",
          name: "芭佰邑-智能染发机",
          serviceId: "mock_service_id",
          writeCharId: "mock_write_char",
          notifyCharId: "mock_notify_char",
          RSSI: -45
        };
        common_vendor.index.__f__("log", "at store/bluetooth.js:218", "准备设置模拟设备:", mockDevice);
        commit("SET_MOCK_CONNECTED_DEVICE", mockDevice);
        commit("ADD_LOG", "模拟设备连接成功");
        common_vendor.index.__f__("log", "at store/bluetooth.js:221", "模拟设备设置完成，当前状态:", state.connectedDevice);
        return true;
      } catch (error) {
        common_vendor.index.__f__("error", "at store/bluetooth.js:225", "mockConnect 错误:", error);
        commit("ADD_LOG", `模拟连接失败: ${error.message}`);
        throw error;
      }
    },
    /**
     * 添加指令到队列
     * @param {Object} context - Vuex上下文
     * @param {Object} payload - 参数对象
     * @param {Object} payload.command - 指令对象
     * @param {boolean} [payload.immediate=false] - 是否立即执行
     */
    async addToCommandQueue(context, {
      command: command2,
      immediate = false
    }) {
      const {
        commit,
        dispatch
      } = context;
      if (!command2) {
        throw new Error("无效的指令对象");
      }
      const app = getApp();
      app.addToCommandQueue(command2);
      commit("ADD_LOG", `指令已添加到队列: ${command2.description || "无描述指令"}`);
      if (immediate && !app.globalData.isExecutingQueue) {
        await dispatch("executeCommandQueue");
      }
      common_vendor.index.__f__("log", "at store/bluetooth.js:258", "指令以添加到队列");
    },
    /**
     * 清空指令队列
     */
    clearCommandQueue({
      commit
    }) {
      common_vendor.index.__f__("log", "at store/bluetooth.js:267", "清空队列中");
      const app = getApp();
      app.clearCommandQueue();
      commit("ADD_LOG", "指令队列已清空");
      common_vendor.index.__f__("log", "at store/bluetooth.js:271", "队列已经清空");
    },
    /**
     * 暂停执行
     */
    async pauseExecution({
      commit,
      dispatch
    }) {
      try {
        const app = getApp();
        if (!app.globalData.isExecutingQueue) {
          commit("ADD_LOG", "当前没有正在执行的队列");
          return false;
        }
        if (app.globalData.isPaused) {
          commit("ADD_LOG", "已经处于暂停状态");
          return true;
        }
        app.setPausedQueueState({
          commandQueue: [...app.globalData.commandQueue],
          currentCommand: app.globalData.currentCommand,
          isExecutingQueue: app.globalData.isExecutingQueue
        });
        const pauseCommand = utils_bleCommandSet.getCommand("pauseDispense");
        if (pauseCommand) {
          commit("ADD_LOG", "正在发送暂停指令...");
          await dispatch("writeData", pauseCommand.value);
          commit("ADD_LOG", "暂停指令已发送，等待设备确认");
        }
        return true;
      } catch (error) {
        commit("ADD_LOG", `暂停执行失败: ${error.message}`);
        throw error;
      }
    },
    /**
     * 恢复执行
     */
    async resumeExecution({
      commit,
      dispatch
    }) {
      try {
        const app = getApp();
        if (!app.globalData.isPaused) {
          commit("ADD_LOG", "当前不在暂停状态");
          return false;
        }
        if (!app.globalData.pausedQueueState) {
          commit("ADD_LOG", "没有保存的队列状态，无法恢复");
          return false;
        }
        commit("ADD_LOG", "正在发送继续指令...");
        const continuedischarging = utils_bleCommandSet.getCommand("continue_discharging");
        common_vendor.index.__f__("log", "at store/bluetooth.js:343", "继续指令", continuedischarging);
        await dispatch("writeData", continuedischarging.value);
        commit("ADD_LOG", "继续指令已发送，等待设备确认");
        return true;
      } catch (error) {
        commit("ADD_LOG", `恢复执行失败: ${error.message}`);
        throw error;
      }
    },
    /**
     * 执行指令队列
     */
    async executeCommandQueue({
      state,
      commit,
      dispatch
    }) {
      try {
        const app = getApp();
        if (!state.connectedDevice) {
          return;
        }
        if (app.globalData.isPaused) {
          commit("ADD_LOG", "当前处于暂停状态，无法执行队列");
          return;
        }
        if (app.globalData.commandQueue.length === 0) {
          app.allExecutionCompleted();
          common_vendor.index.__f__("log", "at store/bluetooth.js:385", "全部出料完成");
          const allclear = utils_bleCommandSet.getCommand("dischargingCompleted");
          dispatch("writeData", allclear.value);
          return;
        }
        app.setExecutingQueue(true);
        try {
          const command2 = app.globalData.commandQueue[0];
          common_vendor.index.__f__("log", "at store/bluetooth.js:395", "当前执行的指令");
          app.setCurrentCommand(command2);
          common_vendor.index.__f__("log", "at store/bluetooth.js:405", "指令", command2.value);
          commit("ADD_LOG", `开始执行指令: ${command2.description}`);
          try {
            await dispatch("writeData", command2.value);
            app.globalData.currentCommand = command2;
          } catch (error) {
            common_vendor.index.__f__("error", "at store/bluetooth.js:413", "执行命令队列失败:", error);
            return;
          }
          commit("ADD_LOG", `指令发送成功: ${command2.description}`);
          app.removeFromCommandQueue();
        } catch (error) {
          commit("ADD_LOG", `指令执行失败: ${command.description} - ${error.message}`);
        }
      } finally {
        commit("ADD_LOG", "指令队列执行完成");
      }
    },
    // 检查运行环境
    checkEnvironment() {
      const systemInfo = common_vendor.index.getSystemInfoSync();
      const isDevTools = systemInfo.platform === "devtools";
      return {
        isDevTools,
        platform: systemInfo.platform,
        system: systemInfo.system
      };
    },
    // 检查蓝牙权限
    async checkBluetoothPermission({
      commit,
      dispatch
    }) {
      try {
        const env = dispatch("checkEnvironment");
        if (env.isDevTools) {
          commit("ADD_LOG", "检测到开发者工具环境，蓝牙功能需要在真机上测试");
          return await new Promise((resolve) => {
            common_vendor.index.showModal({
              title: "开发者工具提示",
              content: "蓝牙功能在开发者工具中无法正常使用，请在真机上测试。是否继续尝试初始化？",
              confirmText: "继续",
              cancelText: "取消",
              success: (res) => {
                resolve(res.confirm);
              }
            });
          });
        }
        const setting = await new Promise((resolve, reject) => {
          common_vendor.index.getSetting({
            success: resolve,
            fail: reject
          });
        });
        commit("ADD_LOG", `当前权限状态: ${JSON.stringify(setting.authSetting)}`);
        if (setting.authSetting["scope.bluetooth"] === false) {
          return await new Promise((resolve) => {
            common_vendor.index.showModal({
              title: "需要蓝牙权限",
              content: "请在设置中开启蓝牙权限，以便扫描和连接蓝牙设备",
              confirmText: "去设置",
              cancelText: "取消",
              success: (res) => {
                if (res.confirm) {
                  common_vendor.index.openSetting({
                    success: (settingRes) => {
                      if (settingRes.authSetting["scope.bluetooth"]) {
                        commit(
                          "ADD_LOG",
                          "蓝牙权限已开启"
                        );
                        resolve(
                          true
                        );
                      } else {
                        commit(
                          "ADD_LOG",
                          "蓝牙权限仍未开启"
                        );
                        resolve(
                          false
                        );
                      }
                    },
                    fail: () => {
                      commit(
                        "ADD_LOG",
                        "打开设置页面失败"
                      );
                      resolve(
                        false
                      );
                    }
                  });
                } else {
                  commit("ADD_LOG", "用户取消开启蓝牙权限");
                  resolve(false);
                }
              }
            });
          });
        } else if (setting.authSetting["scope.bluetooth"] === void 0) {
          try {
            await common_vendor.index.authorize({
              scope: "scope.bluetooth"
            });
            commit("ADD_LOG", "蓝牙权限请求成功");
            return true;
          } catch (authErr) {
            commit("ADD_LOG", `蓝牙权限请求失败: ${authErr.errMsg}`);
            return false;
          }
        }
        return true;
      } catch (err) {
        common_vendor.index.__f__("error", "at store/bluetooth.js:551", "检查蓝牙权限失败:", err);
        commit("ADD_LOG", `权限检查异常: ${err.errMsg || err.message}`);
        return false;
      }
    },
    // 初始化蓝牙适配器
    async initBluetooth({
      commit,
      dispatch
    }) {
      var _a, _b;
      try {
        const env = dispatch("checkEnvironment");
        commit("ADD_LOG", `运行环境: ${env.platform}, 系统: ${env.system}`);
        const hasPermission = await dispatch("checkBluetoothPermission");
        if (!hasPermission) {
          throw new Error("用户拒绝蓝牙权限");
        }
        if (env.isDevTools) {
          commit("ADD_LOG", "开发者工具环境，启用模拟模式");
          commit("SET_BLUETOOTH_ADAPTER_READY", true);
          commit("ADD_LOG", "模拟蓝牙环境初始化完成，请在真机上测试实际功能");
          return true;
        }
        const res = await new Promise((resolve, reject) => {
          common_vendor.index.openBluetoothAdapter({
            success: resolve,
            fail: reject
          });
        });
        common_vendor.index.__f__("log", "at store/bluetooth.js:611", "初始化蓝牙结果", res);
        commit("ADD_LOG", "蓝牙适配器初始化成功");
        commit("SET_BLUETOOTH_ADAPTER_READY", true);
        common_vendor.index.onBluetoothAdapterStateChange((res2) => {
          commit("ADD_LOG", `蓝牙状态变化: ${JSON.stringify(res2)}`);
          if (!res2.available) {
            commit("SET_SCANNING", false);
            commit("SET_BLUETOOTH_ADAPTER_READY", false);
            dispatch("resetBluetoothState");
          }
        });
        return true;
      } catch (err) {
        common_vendor.index.__f__("error", "at store/bluetooth.js:626", "蓝牙初始化失败:", err);
        let title = "蓝牙初始化失败";
        let content = "请检查蓝牙设置";
        let showRetry = true;
        const env = dispatch("checkEnvironment");
        if (env.isDevTools || ((_a = err.errMsg) == null ? void 0 : _a.includes("Mac")) || ((_b = err.errMsg) == null ? void 0 : _b.includes("仅支持"))) {
          title = "开发者工具环境";
          content = "蓝牙功能在开发者工具中无法正常使用，已启用模拟模式。请在真机上测试完整功能。";
          showRetry = false;
          commit("SET_BLUETOOTH_ADAPTER_READY", true);
          commit("ADD_LOG", "开发者工具模拟模式已启用");
          const mockDevices = [
            {
              deviceId: "mock_device_1",
              name: "模拟蓝牙设备1",
              RSSI: -50
            },
            {
              deviceId: "mock_device_2",
              name: "模拟蓝牙设备2",
              RSSI: -60
            }
          ];
          setTimeout(() => {
            mockDevices.forEach((device) => {
              commit("ADD_DEVICE", device);
              commit(
                "ADD_LOG",
                `发现模拟设备: ${device.name}`
              );
            });
          }, 1e3);
          common_vendor.index.showModal({
            title,
            content,
            showCancel: false,
            confirmText: "确定"
          });
          return true;
        }
        if (err.errCode === 10001) {
          title = "蓝牙未开启";
          content = "请先开启手机蓝牙功能，然后重试";
        } else if (err.errCode === 10004) {
          title = "蓝牙权限被拒绝";
          content = "请在设置中开启蓝牙权限";
          showRetry = false;
        } else if (err.errCode === 10005) {
          title = "系统版本过低";
          content = "当前系统版本不支持蓝牙功能";
          showRetry = false;
        } else if (err.errCode === 10009) {
          title = "蓝牙适配器不可用";
          content = "请检查设备是否支持蓝牙功能";
          showRetry = false;
        } else {
          content = err.errMsg || err.message || "未知错误，请重试";
        }
        commit("ADD_LOG", `蓝牙初始化失败: ${title} - ${content}`);
        return await new Promise((resolve) => {
          common_vendor.index.showModal({
            title,
            content,
            confirmText: showRetry ? "重试" : "确定",
            cancelText: showRetry ? "取消" : "",
            showCancel: showRetry,
            success: async (res) => {
              if (res.confirm && showRetry) {
                try {
                  const result = await dispatch(
                    "initBluetooth"
                  );
                  resolve(result);
                } catch (retryErr) {
                  resolve(false);
                }
              } else {
                resolve(false);
              }
            }
          });
        });
      }
    },
    // 重置蓝牙状态
    resetBluetoothState({
      commit
    }) {
      commit("CLEAR_CONNECTED_DEVICE");
      commit("CLEAR_DEVICES");
      commit("SET_BLUETOOTH_ADAPTER_READY", false);
    },
    // 开始扫描设备
    async startScan({
      state,
      commit,
      dispatch
    }, bluetoothName) {
      if (state.isScanning) {
        commit("ADD_LOG", "扫描已在进行中");
        return;
      }
      if (!state.bluetoothAdapterReady) {
        try {
          const initResult = await dispatch("initBluetooth");
          if (!initResult) {
            commit("ADD_LOG", "蓝牙初始化失败，无法开始扫描");
            return false;
          }
        } catch (err) {
          commit("ADD_LOG", "蓝牙初始化异常，无法开始扫描");
          return false;
        }
      }
      commit("CLEAR_DEVICES");
      commit("SET_SCANNING", true);
      commit("ADD_LOG", "开始扫描蓝牙设备...");
      const env = dispatch("checkEnvironment");
      if (env.isDevTools) {
        commit("ADD_LOG", "开发者工具环境，使用模拟扫描");
        const mockDevices = [
          {
            deviceId: "mock_device_1",
            name: "模拟蓝牙设备1",
            RSSI: -50
          },
          {
            deviceId: "mock_device_2",
            name: "模拟蓝牙设备2",
            RSSI: -60
          },
          {
            deviceId: "mock_device_3",
            name: "模拟蓝牙设备3",
            RSSI: -70
          }
        ];
        mockDevices.forEach((device, index) => {
          setTimeout(() => {
            commit("ADD_DEVICE", device);
            commit(
              "ADD_LOG",
              `发现模拟设备: ${device.name} (${device.deviceId})`
            );
          }, (index + 1) * 1e3);
        });
        setTimeout(() => {
          commit("SET_SCANNING", false);
          commit("ADD_LOG", "模拟扫描完成");
        }, 5e3);
        return true;
      }
      try {
        await new Promise((resolve, reject) => {
          common_vendor.index.startBluetoothDevicesDiscovery({
            allowDuplicatesKey: true,
            interval: 0,
            success: resolve,
            fail: reject
          });
        });
        commit("ADD_LOG", "扫描启动成功，正在搜索设备...");
        common_vendor.index.onBluetoothDeviceFound((res) => {
          if (res.devices && res.devices.length > 0) {
            res.devices.forEach((device) => {
              const app = getApp();
              if (app.hasDeviceList(device.name)) {
                const deviceInfo = {
                  name: device.name || device.localName || "未知设备",
                  deviceId: device.deviceId,
                  RSSI: device.RSSI || 0
                };
                commit("ADD_DEVICE", deviceInfo);
                commit("ADD_MAP_DEVICENAME", deviceInfo);
                commit(
                  "ADD_LOG",
                  `发现设备: ${deviceInfo.name} (${deviceInfo.deviceId})`
                );
              }
            });
          }
        });
        setTimeout(async () => {
          if (state.isScanning) {
            await dispatch("stopScan");
            commit("ADD_LOG", "扫描超时，已自动停止");
            if (state.deviceList.length === 0) {
              common_vendor.index.showToast({
                title: "未发现设备",
                icon: "none",
                duration: 2e3
              });
            }
          }
        }, 3e4);
        return true;
      } catch (err) {
        commit("SET_SCANNING", false);
        commit("ADD_LOG", `扫描失败: ${err.errMsg || err.message}`);
        let errorMsg = "扫描失败";
        if (err.errCode === 10001) {
          errorMsg = "蓝牙未开启，请开启蓝牙后重试";
        } else if (err.errCode === 10004) {
          errorMsg = "没有蓝牙权限，请授权后重试";
        } else if (err.errCode === 10012) {
          errorMsg = "连接超时，请重试";
        }
        common_vendor.index.showToast({
          title: errorMsg,
          icon: "none",
          duration: 3e3
        });
        throw err;
      }
    },
    // 停止扫描
    async stopScan({
      state,
      commit
    }) {
      if (!this.state.isScanning)
        return;
      try {
        await new Promise((resolve, reject) => {
          common_vendor.index.stopBluetoothDevicesDiscovery({
            success: resolve,
            fail: reject
          });
        });
        commit("SET_SCANNING", false);
        commit("ADD_LOG", "扫描已停止");
      } catch (err) {
        commit("ADD_LOG", `停止扫描失败: ${err.errMsg}`);
        throw err;
      }
    },
    // 连接设备
    async connectDevice({
      state,
      commit,
      dispatch
    }, device) {
      var _a;
      if (((_a = state.connectedDevice) == null ? void 0 : _a.deviceId) === device.deviceId) {
        throw new Error("设备已连接");
      }
      common_vendor.index.showLoading({
        title: "连接中...",
        mask: true
      });
      try {
        await dispatch("stopScan");
        commit("ADD_LOG", "已停止扫描");
        const connectionResult = await new Promise((resolve, reject) => {
          common_vendor.index.createBLEConnection({
            deviceId: device.deviceId,
            success: (res) => {
              const app = getApp();
              app.globalData.deviceStatus = true;
              app.setConnectionMethod("bluetooth");
              app.globalData.connectedDeviceId = device.name;
              common_vendor.index.__f__("log", "at store/bluetooth.js:940", "当前设备编号为", app.globalData.connectedDeviceId);
              utils_connectionStateValidator.connectionStateValidator.logConnectionChange("BLUETOOTH_CONNECT_SUCCESS", "bluetooth", {
                deviceId: device.deviceId,
                deviceName: device.name
              });
              commit("ADD_LOG", `连接成功: ${device.deviceId}`);
              resolve(res);
            },
            fail: (err) => {
              commit("ADD_LOG", `连接失败: ${err.errMsg}`);
              reject(new Error(`连接失败: ${err.errMsg}`));
            }
          });
        });
        common_vendor.index.onBLEConnectionStateChange((res) => {
          if (!res.connected) {
            const app = getApp();
            if (app.globalData.taskCompletionBuffer) {
              common_vendor.index.__f__("log", "at store/bluetooth.js:966", "当前在任务完成缓冲期内，忽略蓝牙断开连接");
              commit("ADD_LOG", `设备在任务完成缓冲期内断开，这是正常现象`);
              return;
            }
            utils_connectionStateValidator.connectionStateValidator.logConnectionChange("BLUETOOTH_DISCONNECT", "bluetooth", {
              deviceId: res.deviceId,
              reason: "CONNECTION_LOST"
            });
            app.globalData.deviceStatus = false;
            app.globalData.connectedDeviceId = "";
            app.setConnectionMethod("");
            commit("ADD_LOG", `设备断开: ${res.deviceId}`);
            commit("CLEAR_CONNECTED_DEVICE");
            dispatch("resetBluetoothState");
          } else {
            commit("ADD_LOG", `设备保持连接: ${res.deviceId}`);
          }
        });
        const services = await new Promise((resolve, reject) => {
          common_vendor.index.getBLEDeviceServices({
            deviceId: device.deviceId,
            success: (res) => {
              common_vendor.index.__f__("log", "at store/bluetooth.js:995", "所有服务", res.services);
              commit("ADD_LOG", `获取到${res.services.length}个服务`);
              resolve(res);
            },
            fail: (err) => {
              commit("ADD_LOG", `获取服务失败: ${err.errMsg}`);
              reject(new Error(`获取服务失败: ${err.errMsg}`));
            }
          });
        });
        const service = services.services.find(
          (s) => s.uuid.toLowerCase() === BLUETOOTH_SERVICE_UUID.toLowerCase()
        );
        if (!service) {
          throw new Error(`未找到服务: ${BLUETOOTH_SERVICE_UUID}`);
        }
        commit("ADD_LOG", `找到目标服务: ${service.uuid}`);
        const characteristics = await new Promise((resolve, reject) => {
          common_vendor.index.getBLEDeviceCharacteristics({
            deviceId: device.deviceId,
            serviceId: service.uuid,
            success: (res) => {
              common_vendor.index.__f__("log", "at store/bluetooth.js:1021", "所有特征", res.characteristics);
              commit(
                "ADD_LOG",
                `获取到${res.characteristics.length}个特征值`
              );
              resolve(res);
            },
            fail: (err) => {
              commit("ADD_LOG", `获取特征值失败: ${err.errMsg}`);
              reject(new Error(`获取特征值失败: ${err.errMsg}`));
            }
          });
        });
        common_vendor.index.__f__("log", "at store/bluetooth.js:1033", "获取到的特征", characteristics);
        const writeChar = characteristics.characteristics.find(
          (c) => c.uuid.toLowerCase() === WRITE_CHARACTERISTIC_UUID.toLowerCase() && c.properties.write
        );
        const notifyChar = characteristics.characteristics.find(
          (c) => c.uuid.toLowerCase() === NOTIFY_CHARACTERISTIC_UUID.toLowerCase() && (c.properties.notify || c.properties.indicate)
        );
        if (!writeChar) {
          throw new Error(`未找到可写特征: ${WRITE_CHARACTERISTIC_UUID}`);
        }
        if (!notifyChar) {
          throw new Error(`未找到可通知特征: ${NOTIFY_CHARACTERISTIC_UUID}`);
        }
        commit("ADD_LOG", `找到写特征: ${writeChar.uuid}`);
        commit("ADD_LOG", `找到通知特征: ${notifyChar.uuid}`);
        await new Promise((resolve, reject) => {
          common_vendor.index.notifyBLECharacteristicValueChange({
            deviceId: device.deviceId,
            serviceId: service.uuid,
            characteristicId: notifyChar.uuid,
            state: true,
            success: () => {
              commit("ADD_LOG", "通知特征启用成功");
              resolve();
            },
            fail: (err) => {
              commit("ADD_LOG", `启用通知失败: ${err.errMsg}`);
              reject(new Error(`启用通知失败: ${err.errMsg}`));
            }
          });
        });
        common_vendor.index.onBLECharacteristicValueChange(async (res) => {
          var _a2;
          try {
            common_vendor.index.__f__("log", "at store/bluetooth.js:1076", "原始数据", res.value);
            const value = arrayBufferToHex(res.value);
            commit("ADD_LOG", `收到数据: ${value}`);
            const parsedData = utils_bleCommandSet.BLUETOOTH_COMMANDS.utils.parseResponse(res.value);
            common_vendor.index.__f__("log", "at store/bluetooth.js:1084", "收到设备操作码", (_a2 = parsedData.opcode) == null ? void 0 : _a2.toString(16));
            const app = getApp();
            app.updateDeviceStatusDetail({
              isOnline: true,
              lastHeartbeat: (/* @__PURE__ */ new Date()).toISOString(),
              connectionQuality: 100
            });
            switch (parsedData.opcode) {
              case utils_bleCommandSet.BLUETOOTH_COMMANDS.OPCODES.PRE_END_DISPENSE:
                common_vendor.index.__f__("log", "at store/bluetooth.js:1097", "继续出料指令返回出料情况", parsedData);
                try {
                  const currentCommand = app.globalData.currentCommand;
                  await dispatch(
                    "recordDyeConsumption",
                    currentCommand
                  );
                } catch (error) {
                  common_vendor.index.__f__("error", "at store/bluetooth.js:1105", "记录染膏消耗失败:", error);
                  commit("ADD_LOG", `记录染膏消耗失败: ${error.message}`);
                }
                dispatch("executeCommandQueue");
                break;
              case utils_bleCommandSet.BLUETOOTH_COMMANDS.OPCODES.END_DISPENSE:
                common_vendor.index.__f__("log", "at store/bluetooth.js:1113", "出料完成", parsedData);
                app.updateCommandExecutionDetail({
                  progress: 100,
                  currentStep: "出料完成",
                  estimatedEndTime: (/* @__PURE__ */ new Date()).toISOString()
                });
                app.setCurrentCommand(null);
                app.setExecutingQueue(false);
                break;
              case utils_bleCommandSet.BLUETOOTH_COMMANDS.OPCODES.BOWL_ALREADY_PLACED:
                app.updateCommandStatus(true);
                app.updateWanColor(true);
                app.updateCommandExecutionDetail({
                  startTime: (/* @__PURE__ */ new Date()).toISOString(),
                  progress: 0,
                  currentStep: "执行中"
                });
                break;
              case utils_bleCommandSet.BLUETOOTH_COMMANDS.OPCODES.BLUETOOTH_SENDS_COMMANDS:
                break;
              case utils_bleCommandSet.BLUETOOTH_COMMANDS.OPCODES.PAUSE_DISPENSE:
                common_vendor.index.__f__("log", "at store/bluetooth.js:1143", "设备暂停确认", parsedData);
                app.setPaused(true);
                app.setExecutingQueue(false);
                common_vendor.index.showToast({
                  title: "设备已暂停",
                  icon: "success"
                });
                commit("ADD_LOG", "设备暂停确认");
                break;
              case utils_bleCommandSet.BLUETOOTH_COMMANDS.OPCODES.CONTINUE_DISCHARGING:
                common_vendor.index.__f__("log", "at store/bluetooth.js:1155", "设备继续确认", parsedData);
                app.setPaused(false);
                app.setExecutingQueue(true);
                app.setPausedQueueState(null);
                common_vendor.index.showToast({
                  title: "设备已恢复",
                  icon: "success"
                });
                commit("ADD_LOG", "设备继续确认");
                break;
              case utils_bleCommandSet.BLUETOOTH_COMMANDS.OPCODES.CANCEL_DISCHARGE:
                common_vendor.index.__f__("log", "at store/bluetooth.js:1169", "设备取消确认", parsedData);
                app.setExecutingQueue(false);
                app.setPaused(false);
                app.globalData.commandQueue = [];
                common_vendor.index.showToast({
                  title: "操作已取消",
                  icon: "success"
                });
                commit("ADD_LOG", "设备取消确认");
                break;
              case utils_bleCommandSet.BLUETOOTH_COMMANDS.OPCODES.CHUNK_SUCCESS:
                setTimeout(() => {
                  utils_writeDataChunked.transferManager.handleSuccess();
                }, 200);
                break;
              case utils_bleCommandSet.BLUETOOTH_COMMANDS.OPCODES.CHUNK_ERROR:
                commit("ADD_LOG", "设备报告分片数据错误");
                setTimeout(() => {
                  utils_writeDataChunked.transferManager.handleError();
                }, 200);
                break;
              case utils_bleCommandSet.BLUETOOTH_COMMANDS.OPCODES.ALREADY_FLAT:
                common_vendor.index.__f__("log", "at store/bluetooth.js:1199", "收到电子秤校准响应 (0x15):", parsedData);
                commit("ADD_LOG", `电子秤校准响应: ${JSON.stringify(parsedData)}`);
                if (app.handleBluetoothMessage) {
                  app.handleBluetoothMessage({
                    type: "CALIBRATION_RESPONSE",
                    opcode: parsedData.opcode,
                    data: parsedData.data,
                    timestamp: (/* @__PURE__ */ new Date()).toISOString()
                  });
                }
                common_vendor.index.$emit("calibration-response", {
                  success: true,
                  opcode: parsedData.opcode,
                  data: parsedData.data
                });
                break;
              case utils_bleCommandSet.BLUETOOTH_COMMANDS.OPCODES.CALIBRATION_RESPONSE:
                common_vendor.index.__f__("log", "at store/bluetooth.js:1224", "收到电子秤校准响应 (0x14):", parsedData);
                commit("ADD_LOG", `电子秤校准响应: ${JSON.stringify(parsedData)}`);
                if (app.handleBluetoothMessage) {
                  app.handleBluetoothMessage({
                    type: "CALIBRATION_RESPONSE",
                    opcode: parsedData.opcode,
                    data: parsedData.data,
                    timestamp: (/* @__PURE__ */ new Date()).toISOString()
                  });
                }
                common_vendor.index.$emit("calibration-response", {
                  success: true,
                  opcode: parsedData.opcode,
                  data: parsedData.data
                });
                break;
              default:
                common_vendor.index.__f__("warn", "at store/bluetooth.js:1246", "未知指令:", parsedData.opcode);
                app.setErrorState({
                  errorType: "UNKNOWN_COMMAND",
                  errorMessage: `未知指令: ${parsedData.opcode}`,
                  errorCode: parsedData.opcode
                });
            }
          } catch (err) {
            commit("ADD_LOG", `数据处理错误: ${err.message}`);
          }
        });
        commit("SET_CONNECTED_DEVICE", {
          ...device,
          serviceId: service.uuid,
          writeCharId: writeChar.uuid,
          notifyCharId: notifyChar.uuid,
          connectionType: "bluetooth"
          // ✅ 添加连接类型标识，防止与WiFi连接混淆
        });
        utils_connectionStateValidator.connectionStateValidator.logConnectionChange("BLUETOOTH_DEVICE_SAVED", "bluetooth", {
          deviceId: device.deviceId,
          deviceName: device.name,
          hasConnectionType: true
        });
        common_vendor.index.showToast({
          title: "连接成功",
          icon: "success"
        });
        try {
          const command2 = utils_bleCommandSet.getCommand("bluetoothConnectSuccess");
          await dispatch("writeData", command2.value);
        } catch (err) {
          common_vendor.index.__f__("log", "at store/bluetooth.js:1284", "发送语音指令失败", err);
        }
      } catch (err) {
        commit("ADD_LOG", `连接过程出错: ${err.message}`);
        await dispatch("resetBluetoothState");
        throw err;
      } finally {
        common_vendor.index.hideLoading();
      }
    },
    // 断开连接
    async disconnectDevice({
      state,
      commit
    }) {
      if (!state.connectedDevice)
        return;
      common_vendor.index.showLoading({
        title: "断开中...",
        mask: true
      });
      try {
        await new Promise((resolve, reject) => {
          common_vendor.index.closeBLEConnection({
            deviceId: state.connectedDevice.deviceId,
            success: resolve,
            fail: reject
          });
        });
        const app = getApp();
        app.globalData.deviceStatus = false;
        app.setConnectionMethod("");
        app.globalData.connectedDeviceId = "";
        commit("ADD_LOG", "设备已断开");
        commit("CLEAR_CONNECTED_DEVICE");
        common_vendor.index.showToast({
          title: "已断开",
          icon: "success"
        });
      } catch (err) {
        commit("ADD_LOG", `断开失败: ${err.errMsg}`);
        throw err;
      } finally {
        common_vendor.index.hideLoading();
      }
    },
    // 写入数据
    async writeData({
      state,
      commit
    }, data) {
      if (!state.connectedDevice) {
        common_vendor.index.__f__("log", "at store/bluetooth.js:1349", "设备未连接");
        throw new Error("未连接设备");
      }
      try {
        const res = await new Promise((resolve, reject) => {
          common_vendor.index.writeBLECharacteristicValue({
            deviceId: state.connectedDevice.deviceId,
            serviceId: state.connectedDevice.serviceId,
            characteristicId: state.connectedDevice.writeCharId,
            value: arrayToArrayBuffer(data),
            success: resolve,
            fail: reject
          });
        });
        common_vendor.index.__f__("log", "at store/bluetooth.js:1377", "发送数据返回的结果", res);
        commit("ADD_LOG", `数据发送成功: ${arrayToArrayBuffer(data)}`);
        common_vendor.index.__f__("log", "at store/bluetooth.js:1379", "发送成功", arrayToArrayBuffer(data));
        return res;
      } catch (err) {
        common_vendor.index.__f__("log", "at store/bluetooth.js:1382", "指令发送失败", err);
        commit("ADD_LOG", `发送失败: ${err.errMsg}`);
        throw err;
      }
    },
    async writeDataChunked({
      state,
      commit,
      dispatch
    }, data) {
      function delay(ms) {
        return new Promise((resolve) => setTimeout(resolve, ms));
      }
      await delay(500);
      utils_writeDataChunked.writeDataChunked(data);
    },
    // async writeDataChunked({
    // 	state,
    // 	commit,
    // 	dispatch
    // }, data) {
    // 	try {
    // 		// 延迟函数
    // 		function delay(ms) {
    // 			return new Promise(resolve => setTimeout(resolve, ms));
    // 		}
    // 		// 1. 首先发送命令头
    // 		await dispatch('writeData', data.command);
    // 		commit('ADD_LOG', `命令头发送成功: ${JSON.stringify(data.command)}`);
    // 		// 等待设备准备接收数据
    // 		await delay(300);
    // 		// 2. 构造要发送的字符串
    // 		const payloadString =
    // 			`ssid:${data.wifiInfo.SSID}password:${data.wifiInfo.password}`;
    // 		commit('ADD_LOG', `准备发送的WiFi配置字符串: ${payloadString}`);
    // 		// 3.将字符串转换为十六进制
    // 		function stringToHex(str) {
    // 			let hex = '';
    // 			for (let i = 0; i < str.length; i++) {
    // 				// 获取字符的Unicode编码，然后转换为16进制
    // 				const code = str.charCodeAt(i).toString(16);
    // 				// 确保是两位表示，不足补0
    // 				hex += code.padStart(2, '0');
    // 			}
    // 			return hex;
    // 		}
    // 		// 十六进制字符串
    // 		const payloadStringHex =stringToHex(payloadString)
    // 		// 4.将十六进制字符串转换为十进制数组
    // 		function hexToIntArray(hex) {
    // 			const intArray = [];
    // 			// 每两位处理一次
    // 			for (let i = 0; i < hex.length; i += 2) {
    // 			  const byte = hex.substr(i, 2);
    // 			  // 将16进制字符串转换为10进制整数
    // 			  intArray.push(parseInt(byte, 16));
    // 			}
    // 			return intArray;
    // 		}
    // 		const uint8Array = hexToIntArray(payloadStringHex);
    // 		commit('ADD_LOG', `转换为字节数组: ${Array.from(uint8Array).join(',')}`);
    // 		// 4. 分片发送（调整为20字节/片）
    // 		const CHUNK_SIZE = 20;
    // 		const totalChunks = Math.ceil(uint8Array.length / CHUNK_SIZE);
    // 		for (let i = 0; i < totalChunks; i++) {
    // 			const start = i * CHUNK_SIZE;
    // 			const end = Math.min(start + CHUNK_SIZE, uint8Array.length);
    // 			const chunk = uint8Array.slice(start, end);
    // 			// 转换为普通数组
    // 			const chunkArray = Array.from(chunk);
    // 			commit('ADD_LOG', `发送分片 ${i+1}/${totalChunks}: ${chunkArray.join(',')}`);
    // 			await dispatch('writeData', chunkArray);
    // 			// 如果不是最后一片，等待更长时间（500ms）
    // 			if (i < totalChunks - 1) {
    // 				await delay(500);
    // 			}
    // 		}
    // 		commit('ADD_LOG', "全部分片发送完成");
    // 		return {
    // 			success: true
    // 		};
    // 	} catch (err) {
    // 		commit('ADD_LOG', `分片发送失败: ${err.message}`);
    // 		uni.__f__('error','at store/bluetooth.js:1523',"分片发送失败:", err);
    // 		throw err;
    // 	}
    // },
    // async writeDataChunked({
    // 	state,
    // 	commit,
    // 	dispatch
    // }, data) {
    // 	try {
    // 		// 先发送 0x06 作为分片开始（假设 data.command 是 0x06 的 int 数组形式）
    // 		await dispatch('writeData', data.command);
    // 		// 等待500ms再继续
    // 		await delay(500);
    // 		// 构造要发送的字符串
    // 		const payloadString =
    // 		`ssid:${data.wifiInfo.SSID}password:${data.wifiInfo.password}`;
    // 		// 将字符串转换为 Uint8Array（即 int 数组）
    // 		function stringToUint8Array(str) {
    // 			const arr = new Uint8Array(str.length);
    // 			for (let i = 0; i < str.length; i++) {
    // 				arr[i] = str.charCodeAt(i); // 获取字符的 ASCII 码（0-255）
    // 			}
    // 			return arr;
    // 		}
    // 		// 定义 delay 函数（方法1）
    // 		function delay(ms) {
    // 		    return new Promise(resolve => setTimeout(resolve, ms));
    // 		}
    // 		const uint8Array = stringToUint8Array(payloadString);
    // 		// 分片发送（假设 CHUNK_SIZE = 20 字节）
    // 		const CHUNK_SIZE = 13; // 每个分片 20 字节（可调整）
    // 		const totalChunks = Math.ceil(uint8Array.length / CHUNK_SIZE);
    // 		for (let i = 0; i < totalChunks; i++) {
    // 			const start = i * CHUNK_SIZE;
    // 			const end = Math.min(start + CHUNK_SIZE, uint8Array.length);
    // 			const chunk = uint8Array.slice(start, end);
    // 			// 转换为普通数组（因为 arrayToArrayBuffer 接收的是 number[]）
    // 			const chunkArray = Array.from(chunk);
    // 			uni.__f__('log','at store/bluetooth.js:1573','Sending data:', Array.from(new Uint8Array(arrayToArrayBuffer(data))))
    // 			await dispatch("writeData", chunkArray);
    // 			commit("ADD_LOG", `分片 ${i+1}/${totalChunks} 发送成功 (字节 ${start}-${end-1})`);
    // 			if (i < totalChunks - 1) {
    // 				await delay(500);
    // 			}
    // 		}
    // 		commit("ADD_LOG", "全部分片发送完成");
    // 		return {
    // 			success: true
    // 		};
    // 	} catch (err) {
    // 		uni.__f__('error','at store/bluetooth.js:1587',"分片发送失败:", err);
    // 		throw err;
    // 	}
    // },
    // 记录染膏消耗
    async recordDyeConsumption({
      state,
      commit
    }, responseData) {
      try {
        const app = getApp();
        const deviceCode = app.globalData.connectedDeviceId;
        if (!deviceCode) {
          common_vendor.index.__f__("warn", "at store/bluetooth.js:1603", "设备编码为空，无法记录染膏消耗");
          return;
        }
        common_vendor.index.__f__("log", "at store/bluetooth.js:1607", "记录染膏耗量数据", responseData);
        if (responseData.colorCode && responseData.weight) {
          const colorCode = responseData.colorCode;
          const weight = responseData.weight;
          const currentCommand = app.globalData.currentCommand;
          const commandId = currentCommand ? currentCommand.backendId : null;
          const consumptionData = {
            deviceCode,
            colorCode: `0x${colorCode.toString(16).padStart(2, "0").toUpperCase()}`,
            usageAmount: weight,
            timestamp: (/* @__PURE__ */ new Date()).toISOString()
          };
          const success = await utils_dyeConsumptionManager.dyeConsumptionManager.recordConsumption(
            deviceCode,
            {
              consumption: {
                [colorCode]: {
                  amount: weight,
                  colorCode: consumptionData.colorCode
                }
              }
            },
            commandId
          );
          if (success) {
            app.updateDyeConsumption({
              currentUsage: {
                [consumptionData.colorCode]: weight
              },
              totalConsumption: app.globalData.dyeConsumption.totalConsumption + weight
            });
            commit(
              "ADD_LOG",
              `✅ 蓝牙染膏消耗记录成功: 颜色${consumptionData.colorCode}, 用量${weight}g, 后端指令ID: ${commandId}`
            );
          } else {
            commit("ADD_LOG", `蓝牙染膏消耗记录失败`);
          }
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at store/bluetooth.js:1658", "记录染膏消耗失败:", error);
        commit("ADD_LOG", `记录染膏消耗失败: ${error.message}`);
      }
    }
  },
  getters: {
    // Vue 3 的 computed 写法（在组件中使用 computed）
    // 这里保持与 Vue 2 相同的 getters 以兼容两种模式
    isConnected: (state) => !!state.connectedDevice,
    deviceCount: (state) => state.deviceList.length,
    recentLogs: (state) => state.logs.slice(0, 10),
    deviceNameMap: (state) => state.discoveredNameDevices,
    deviceList: (state) => state.deviceList
  }
});
exports.store = store;
//# sourceMappingURL=../../.sourcemap/mp-weixin/store/bluetooth.js.map
