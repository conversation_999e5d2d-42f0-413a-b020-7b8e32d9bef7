<template>
	<view class="content">
		<!-- 顶部导航 -->
		<view class="top-bar">
			<view class="back-btn" @click="goBack">
				<text class="iconfont icon-back">←</text>
			</view>
			<view class="title">设备详情</view>
			<view class="action-btns">
				<view class="share-btn">↗</view>
				<view class="more-btn">•••</view>
			</view>
		</view>
		
		<!-- 设备图片 -->
		<view class="device-images">
			<swiper class="swiper-box" circular indicator-dots autoplay>
				<swiper-item v-for="(item, index) in deviceImages" :key="index">
					<image :src="item" mode="aspectFill"></image>
				</swiper-item>
			</swiper>
			<view class="image-counter">{{currentImageIndex}}/{{deviceImages.length}}</view>
		</view>
		
		<!-- 设备信息 -->
		<view class="device-info-section">
			<view class="device-title">{{deviceInfo.name}}</view>
			<view class="device-id">设备ID: {{deviceInfo.id}}</view>
			<view class="device-status" :class="deviceInfo.status === '在线' ? 'online' : 'offline'">
				状态: {{deviceInfo.status}}
			</view>
		</view>
		
		<!-- 设备数据 -->
		<view class="data-section">
			<view class="data-title">设备数据</view>
			<view class="data-list">
				<view class="data-item" v-for="(item, index) in deviceInfo.data" :key="index">
					<view class="data-label">{{item.label}}</view>
					<view class="data-value">{{item.value}}</view>
				</view>
			</view>
		</view>
		
		<!-- 设备控制 -->
		<view class="control-section">
			<view class="control-title">设备控制</view>
			<view class="control-list">
				<view class="control-item" v-for="(item, index) in deviceInfo.controls" :key="index" @click="controlDevice(item)">
					<view class="control-icon">{{item.icon}}</view>
					<view class="control-label">{{item.label}}</view>
				</view>
			</view>
		</view>
		
		<!-- 历史记录 -->
		<view class="history-section">
			<view class="history-title">历史记录</view>
			<view class="history-list">
				<view class="history-item" v-for="(item, index) in deviceInfo.history" :key="index">
					<view class="history-time">{{item.time}}</view>
					<view class="history-event">{{item.event}}</view>
				</view>
			</view>
		</view>
		
		<!-- 底部操作栏 -->
		<view class="bottom-actions">
			<view class="action-btn diagnose" @click="diagnosisDevice">
				<view class="action-icon">🔍</view>
				<text class="action-text">诊断</text>
			</view>
			<view class="action-btn restart" @click="restartDevice">
				<view class="action-icon">🔄</view>
				<text class="action-text">重启</text>
			</view>
			<view class="action-btn settings" @click="openSettings">
				<view class="action-icon">⚙️</view>
				<text class="action-text">设置</text>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			deviceId: null,
			currentImageIndex: 1,
			deviceImages: [
				'/static/images/device1.jpg',
				'/static/images/device2.jpg'
			],
			deviceInfo: {
				id: 'DV20240501',
				name: '智能美发护理仪',
				status: '在线',
				data: [
					{
						label: '使用次数',
						value: '38次'
					},
					{
						label: '使用时长',
						value: '12小时'
					},
					{
						label: '电池电量',
						value: '85%'
					},
					{
						label: '温度',
						value: '38℃'
					}
				],
				controls: [
					{
						icon: '🔥',
						label: '加热'
					},
					{
						icon: '💨',
						label: '风速'
					},
					{
						icon: '🔆',
						label: '强度'
					},
					{
						icon: '⏱️',
						label: '定时'
					}
				],
				history: [
					{
						time: '2024-05-01 15:30',
						event: '设备开机'
					},
					{
						time: '2024-05-01 15:45',
						event: '模式切换：护理模式'
					},
					{
						time: '2024-05-01 16:30',
						event: '设备关机'
					}
				]
			}
		};
	},
	onLoad(options) {
		if (options.id) {
			this.deviceId = options.id;
			// 根据ID加载实际设备数据
			this.loadDeviceInfo(this.deviceId);
		}
	},
	methods: {
		loadDeviceInfo(id) {
			// 实际应用中这里应该调用API获取设备信息
			console.log('加载设备ID:', id);
			// 模拟API调用
			// this.deviceInfo = ...从API获取的数据
		},
		goBack() {
			uni.navigateBack();
		},
		controlDevice(control) {
			uni.showToast({
				title: `${control.label}已调整`,
				icon: 'none'
			});
		},
		diagnosisDevice() {
			uni.showLoading({
				title: '正在诊断设备'
			});
			
			setTimeout(() => {
				uni.hideLoading();
				uni.showModal({
					title: '诊断结果',
					content: '设备运行正常，无异常情况',
					showCancel: false
				});
			}, 2000);
		},
		restartDevice() {
			uni.showModal({
				title: '重启设备',
				content: '确定要重启设备吗？',
				success: (res) => {
					if (res.confirm) {
						uni.showLoading({
							title: '重启中'
						});
						
						setTimeout(() => {
							uni.hideLoading();
							uni.showToast({
								title: '设备已重启',
								icon: 'success'
							});
						}, 3000);
					}
				}
			});
		},
		openSettings() {
			uni.navigateTo({
				url: '/pages/device/settings?id=' + this.deviceInfo.id
			});
		}
	}
};
</script>

<style lang="scss">
.content {
	display: flex;
	flex-direction: column;
	background-color: #f5f5f5;
	min-height: 100vh;
}

.top-bar {
	display: flex;
	align-items: center;
	background-color: #4285f4;
	padding: 20rpx;
	color: #fff;
	position: sticky;
	top: 0;
	z-index: 100;
}

.back-btn {
	padding: 10rpx 20rpx;
	font-size: 40rpx;
}

.title {
	flex: 1;
	text-align: center;
	font-size: 32rpx;
	font-weight: bold;
}

.action-btns {
	display: flex;
	align-items: center;
}

.share-btn, .more-btn {
	margin-left: 20rpx;
	font-size: 30rpx;
}

.device-images {
	width: 100%;
	height: 500rpx;
	position: relative;
	background-color: #fff;
}

.swiper-box {
	width: 100%;
	height: 100%;
}

.swiper-box image {
	width: 100%;
	height: 100%;
}

.image-counter {
	position: absolute;
	bottom: 20rpx;
	right: 20rpx;
	background-color: rgba(0, 0, 0, 0.5);
	color: #fff;
	font-size: 24rpx;
	padding: 4rpx 20rpx;
	border-radius: 20rpx;
}

.device-info-section {
	background-color: #fff;
	padding: 30rpx 20rpx;
	margin-bottom: 20rpx;
}

.device-title {
	font-size: 36rpx;
	font-weight: bold;
	margin-bottom: 10rpx;
}

.device-id {
	font-size: 24rpx;
	color: #999;
	margin-bottom: 10rpx;
}

.device-status {
	display: inline-block;
	font-size: 24rpx;
	padding: 4rpx 20rpx;
	border-radius: 20rpx;
	
	&.online {
		background-color: #e8f5e9;
		color: #4caf50;
	}
	
	&.offline {
		background-color: #ffebee;
		color: #f44336;
	}
}

.data-section, .control-section, .history-section {
	background-color: #fff;
	padding: 30rpx 20rpx;
	margin-bottom: 20rpx;
}

.data-title, .control-title, .history-title {
	font-size: 32rpx;
	font-weight: bold;
	margin-bottom: 20rpx;
}

.data-list {
	display: flex;
	flex-wrap: wrap;
}

.data-item {
	width: 50%;
	padding: 20rpx 0;
}

.data-label {
	font-size: 24rpx;
	color: #666;
	margin-bottom: 10rpx;
}

.data-value {
	font-size: 32rpx;
	font-weight: bold;
}

.control-list {
	display: flex;
	justify-content: space-around;
}

.control-item {
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 20rpx;
	width: 120rpx;
}

.control-icon {
	font-size: 48rpx;
	margin-bottom: 10rpx;
}

.control-label {
	font-size: 24rpx;
	color: #666;
}

.history-list {
	
}

.history-item {
	padding: 20rpx 0;
	border-bottom: 1rpx solid #f0f0f0;
	
	&:last-child {
		border-bottom: none;
	}
}

.history-time {
	font-size: 24rpx;
	color: #999;
	margin-bottom: 10rpx;
}

.history-event {
	font-size: 28rpx;
}

.bottom-actions {
	position: fixed;
	left: 0;
	right: 0;
	bottom: 0;
	display: flex;
	height: 100rpx;
	background-color: #fff;
	box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.action-btn {
	flex: 1;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
}

.action-icon {
	font-size: 40rpx;
	margin-bottom: 4rpx;
}

.action-text {
	font-size: 24rpx;
	color: #666;
}

.diagnose {
	color: #4285f4;
}

.restart {
	color: #f44336;
}

.settings {
	color: #4caf50;
}
</style> 