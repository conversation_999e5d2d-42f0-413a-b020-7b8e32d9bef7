"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_request = require("../../utils/request.js");
const utils_imageUtils = require("../../utils/imageUtils.js");
const _sfc_main = {
  data() {
    return {
      categories: [],
      products: [],
      loading: false,
      refreshing: false,
      hasMore: true,
      currentPage: 1,
      pageSize: 10,
      searchKeyword: "",
      imageErrors: {},
      currentLocation: {
        address: "定位中...",
        latitude: null,
        longitude: null
      }
    };
  },
  onLoad() {
    this.loadInitialData();
    this.getCurrentLocation();
  },
  methods: {
    // 获取当前位置
    getCurrentLocation() {
      common_vendor.index.getLocation({
        type: "wgs84",
        success: (res) => {
          common_vendor.index.__f__("log", "at pages/mall/mall.vue:152", "定位成功:", res);
          this.currentLocation.latitude = res.latitude;
          this.currentLocation.longitude = res.longitude;
          this.reverseGeocode(res.latitude, res.longitude);
        },
        fail: (err) => {
          common_vendor.index.__f__("error", "at pages/mall/mall.vue:160", "定位失败:", err);
          this.currentLocation.address = "定位失败，点击重试";
          common_vendor.index.showToast({
            title: "定位失败",
            icon: "none"
          });
        }
      });
    },
    // 逆地理编码获取地址
    async reverseGeocode(latitude, longitude) {
      try {
        const key = "48f35f240d10902e83fedb71a918e4f7";
        const url = `https://restapi.amap.com/v3/geocode/regeo?key=${key}&location=${longitude},${latitude}&poitype=&radius=1000&extensions=base&batch=false&roadlevel=0`;
        const response = await common_vendor.index.request({
          url,
          method: "GET"
        });
        if (response.data && response.data.status === "1" && response.data.regeocode) {
          const address = response.data.regeocode.formatted_address;
          this.currentLocation.address = address || "广东省东莞市南城街道";
        } else {
          this.currentLocation.address = "广东省东莞市南城街道";
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/mall/mall.vue:189", "逆地理编码失败:", error);
        this.currentLocation.address = "广东省东莞市南城街道";
      }
    },
    // 扫码功能
    scanCode() {
    },
    async loadInitialData() {
      await Promise.all([
        this.loadCategories(),
        this.loadProducts()
      ]);
    },
    async loadCategories() {
      try {
        const response = await utils_request.apiService.mall.categories.list();
        if (response.code === 200) {
          this.categories = (response.data || []).slice(0, 15);
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/mall/mall.vue:227", "获取分类失败:", error);
        common_vendor.index.showToast({
          title: "获取分类失败",
          icon: "none"
        });
      }
    },
    async loadProducts(isLoadMore = false) {
      if (this.loading)
        return;
      this.loading = true;
      try {
        const params = {
          page: isLoadMore ? this.currentPage + 1 : 1,
          pageSize: this.pageSize
        };
        const response = await utils_request.apiService.mall.products.list(params);
        if (response.code === 200) {
          const newProducts = response.data.products || response.data.records || response.data || [];
          if (isLoadMore) {
            this.products = [...this.products, ...newProducts];
            this.currentPage++;
          } else {
            this.products = newProducts;
            this.currentPage = 1;
          }
          this.hasMore = newProducts.length === this.pageSize;
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/mall/mall.vue:261", "获取商品失败:", error);
      } finally {
        this.loading = false;
        this.refreshing = false;
      }
    },
    onRefresh() {
      if (this.refreshing)
        return;
      this.refreshing = true;
      this.loadProducts();
      this.loadCategories();
    },
    loadMoreProducts() {
      if (!this.hasMore || this.loading)
        return;
      this.loadProducts(true);
    },
    // 刷新数据
    refreshData() {
      this.products = [];
      this.categories = [];
      this.currentPage = 1;
      this.hasMore = true;
      Promise.all([
        this.loadCategories(),
        this.loadProducts()
      ]).finally(() => {
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({
          title: "刷新完成",
          icon: "success"
        });
      });
    },
    navigateToCategory(category) {
      common_vendor.index.navigateTo({
        url: `/pages/mall/category/category?categoryId=${category.id}&categoryName=${encodeURIComponent(category.name)}`
      });
    },
    navigateToProduct(productId) {
      common_vendor.index.navigateTo({
        url: `/pages/mall/product/detail?id=${productId}`
      });
    },
    doSearch() {
      const keyword = this.searchKeyword.trim();
      if (!keyword) {
        common_vendor.index.showToast({
          title: "请输入搜索内容",
          icon: "none"
        });
        return;
      }
      common_vendor.index.navigateTo({
        url: `/pages/mall/search/search?keyword=${encodeURIComponent(keyword)}`
      });
    },
    goToSearch() {
      common_vendor.index.navigateTo({
        url: "/pages/mall/search/search"
      });
    },
    // 图片相关方法
    getFirstImage(images) {
      return utils_imageUtils.getFirstImage(images);
    },
    getFullImageUrl(url) {
      return utils_imageUtils.getFullImageUrl(url);
    },
    isImageUrl(url) {
      if (!url)
        return false;
      return url.startsWith("http") || url.startsWith("/static") || url.startsWith("data:image");
    },
    onImageError(categoryId) {
      this.$set(this.imageErrors, categoryId, true);
    },
    getDefaultIcon(name) {
      const iconMap = {
        "数码": "📱",
        "服装": "👕",
        "家居": "🏠",
        "美妆": "💄",
        "食品": "🍎",
        "运动": "⚽",
        "图书": "📚",
        "母婴": "👶"
      };
      return iconMap[name] || "🛍️";
    }
  }
};
if (!Array) {
  const _easycom_uni_icons2 = common_vendor.resolveComponent("uni-icons");
  const _easycom_uni_load_more2 = common_vendor.resolveComponent("uni-load-more");
  (_easycom_uni_icons2 + _easycom_uni_load_more2)();
}
const _easycom_uni_icons = () => "../../uni_modules/uni-icons/components/uni-icons/uni-icons.js";
const _easycom_uni_load_more = () => "../../uni_modules/uni-load-more/components/uni-load-more/uni-load-more.js";
if (!Math) {
  (_easycom_uni_icons + _easycom_uni_load_more)();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.p({
      type: "location",
      size: "16",
      color: "#4285f4"
    }),
    b: common_vendor.t($data.currentLocation.address),
    c: common_vendor.p({
      type: "arrowright",
      size: "14",
      color: "#999"
    }),
    d: common_vendor.o((...args) => $options.getCurrentLocation && $options.getCurrentLocation(...args)),
    e: common_vendor.p({
      type: "search",
      size: "18",
      color: "#999"
    }),
    f: common_vendor.o((...args) => $options.doSearch && $options.doSearch(...args)),
    g: $data.searchKeyword,
    h: common_vendor.o(($event) => $data.searchKeyword = $event.detail.value),
    i: common_vendor.o((...args) => $options.goToSearch && $options.goToSearch(...args)),
    j: common_vendor.o((...args) => $options.goToSearch && $options.goToSearch(...args)),
    k: common_vendor.p({
      type: "scan",
      size: "24",
      color: "#4285f4"
    }),
    l: common_vendor.o((...args) => $options.scanCode && $options.scanCode(...args)),
    m: common_vendor.f($data.categories, (category, index, i0) => {
      return common_vendor.e({
        a: category.icon && $options.isImageUrl(category.icon)
      }, category.icon && $options.isImageUrl(category.icon) ? {
        b: $options.getFullImageUrl(category.icon),
        c: common_vendor.o(($event) => $options.onImageError(category.id), category.id)
      } : {
        d: common_vendor.t($options.getDefaultIcon(category.name))
      }, {
        e: common_vendor.t(category.name),
        f: category.id,
        g: common_vendor.o(($event) => $options.navigateToCategory(category), category.id)
      });
    }),
    n: common_vendor.f($data.products, (product, k0, i0) => {
      return common_vendor.e({
        a: $options.getFirstImage(product.images),
        b: product.tag
      }, product.tag ? {
        c: common_vendor.t(product.tag)
      } : {}, {
        d: common_vendor.t(product.productName),
        e: common_vendor.t(product.price),
        f: product.originalPrice
      }, product.originalPrice ? {
        g: common_vendor.t(product.originalPrice)
      } : {}, {
        h: common_vendor.t(product.sales || 0),
        i: "2d381b1c-4-" + i0,
        j: common_vendor.t(product.rating || "5.0"),
        k: product.id,
        l: common_vendor.o(($event) => $options.navigateToProduct(product.id), product.id)
      });
    }),
    o: common_vendor.p({
      type: "star-filled",
      size: "17",
      color: "#4285f4"
    }),
    p: $data.loading
  }, $data.loading ? {
    q: common_vendor.p({
      status: "loading"
    })
  } : {}, {
    r: !$data.hasMore && $data.products.length > 0
  }, !$data.hasMore && $data.products.length > 0 ? {} : {}, {
    s: $data.products.length === 0 && !$data.loading
  }, $data.products.length === 0 && !$data.loading ? {
    t: common_vendor.o((...args) => $options.refreshData && $options.refreshData(...args))
  } : {}, {
    v: $data.refreshing,
    w: common_vendor.o((...args) => $options.onRefresh && $options.onRefresh(...args)),
    x: common_vendor.o((...args) => $options.loadMoreProducts && $options.loadMoreProducts(...args))
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/mall/mall.js.map
