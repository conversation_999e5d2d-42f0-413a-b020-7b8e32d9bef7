/* 水平间距 */
/* 水平间距 */
/* 全局样式修复 - 确保页面占满屏幕宽度 */
page {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
}

/* 通用容器样式 */
.container, .page-container {
  width: 100%;
  min-height: 100vh;
  box-sizing: border-box;
}

/* 修复小程序页面宽度问题 */
uni-page-body {
  width: 100% !important;
}

/* 确保所有view元素正确继承宽度 */
view {
  box-sizing: border-box;
}
.guide-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100%;
  background-color: #f7f8fa;
  box-sizing: border-box;
}
.header {
  display: flex;
  align-items: center;
  height: 88rpx;
  background-color: #ffffff;
  padding: 0 30rpx;
  position: relative;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}
.back-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.title {
  flex: 1;
  text-align: center;
  font-size: 36rpx;
  font-weight: 500;
  margin-right: 60rpx;
}
.content {
  flex: 1;
  padding: 20rpx;
}
.loading-container, .error-container, .empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400rpx;
  color: #999;
}
.retry-btn {
  margin-top: 20rpx;
  padding: 10rpx 30rpx;
  background-color: #007AFF;
  color: white;
  border-radius: 10rpx;
  border: none;
}
.guide-list {
  display: flex;
  flex-direction: column;
}
.guide-item {
  display: flex;
  align-items: center;
  background-color: #ffffff;
  padding: 30rpx;
  margin-bottom: 20rpx;
  border-radius: 10rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}
.guide-icon {
  width: 80rpx;
  height: 80rpx;
  background: linear-gradient(135deg, #007AFF, #5AC8FA);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
}
.step-number {
  font-size: 32rpx;
  font-weight: bold;
  color: white;
}
.guide-info {
  flex: 1;
}
.guide-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 10rpx;
  display: block;
}
.guide-desc {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 10rpx;
  display: block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.guide-meta {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.guide-type {
  font-size: 22rpx;
  color: #007AFF;
  background-color: #f0f5ff;
  padding: 4rpx 12rpx;
  border-radius: 8rpx;
}
.view-count {
  font-size: 22rpx;
  color: #999;
}
.guide-arrow {
  display: flex;
  align-items: center;
  color: #ccc;
  font-size: 24rpx;
}