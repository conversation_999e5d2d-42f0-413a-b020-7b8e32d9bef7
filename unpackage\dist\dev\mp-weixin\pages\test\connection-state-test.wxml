<view class="container data-v-7ecc3ead"><view class="header data-v-7ecc3ead"><text class="title data-v-7ecc3ead">连接状态测试</text></view><view class="status-section data-v-7ecc3ead"><view class="status-item data-v-7ecc3ead"><text class="label data-v-7ecc3ead">Vuex Store 连接状态:</text><text class="{{['value', 'data-v-7ecc3ead', b && 'online', c && 'offline']}}">{{a}}</text></view><view class="status-item data-v-7ecc3ead"><text class="label data-v-7ecc3ead">设备ID:</text><text class="value data-v-7ecc3ead">{{d}}</text></view><view class="status-item data-v-7ecc3ead"><text class="label data-v-7ecc3ead">连接类型:</text><text class="value data-v-7ecc3ead">{{e}}</text></view><view class="status-item data-v-7ecc3ead"><text class="label data-v-7ecc3ead">GlobalData 设备状态:</text><text class="{{['value', 'data-v-7ecc3ead', g && 'online', h && 'offline']}}">{{f}}</text></view><view class="status-item data-v-7ecc3ead"><text class="label data-v-7ecc3ead">GlobalData 设备ID:</text><text class="value data-v-7ecc3ead">{{i}}</text></view></view><view class="actions-section data-v-7ecc3ead"><button class="action-btn data-v-7ecc3ead" bindtap="{{j}}">刷新状态</button><button class="action-btn data-v-7ecc3ead" bindtap="{{k}}">模拟连接</button><button class="action-btn data-v-7ecc3ead" bindtap="{{l}}">清除连接</button><button class="action-btn data-v-7ecc3ead" bindtap="{{m}}">测试持久化</button><button class="action-btn data-v-7ecc3ead" bindtap="{{n}}">测试续传</button><button class="action-btn data-v-7ecc3ead" bindtap="{{o}}">模拟刷新</button></view><view class="logs-section data-v-7ecc3ead"><text class="section-title data-v-7ecc3ead">操作日志:</text><scroll-view scroll-y class="logs-container data-v-7ecc3ead"><view wx:for="{{p}}" wx:for-item="log" wx:key="b" class="log-item data-v-7ecc3ead"><text class="log-text data-v-7ecc3ead">{{log.a}}</text></view></scroll-view></view></view>