"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_request = require("../../utils/request.js");
const _sfc_main = {
  data() {
    return {
      aboutInfo: {
        companyName: "",
        companyIntro: "",
        phone: "",
        email: "",
        address: "",
        version: "",
        logoUrl: "",
        website: "",
        qq: "",
        wechat: "",
        businessHours: ""
      },
      loading: true,
      error: ""
    };
  },
  onLoad() {
    this.loadAboutInfo();
  },
  methods: {
    async loadAboutInfo() {
      try {
        this.loading = true;
        this.error = "";
        const response = await utils_request.get("/api/about/info");
        if (response && response.data) {
          this.aboutInfo = {
            companyName: response.data.companyName || "芭佰邑智能染发机",
            companyIntro: response.data.companyIntro || "我们是一家专注于提供高质量服务的企业，致力于为用户创造价值，提供便捷、高效的解决方案。",
            phone: response.data.phone || "***********",
            email: response.data.email || "<EMAIL>",
            address: response.data.address || "广州白云石井街芭百邑化妆品商行广州市白云区石槎路979号239室",
            version: response.data.version || "v1.0.0",
            logoUrl: response.data.logoUrl || "",
            website: response.data.website || "",
            qq: response.data.qq || "",
            wechat: response.data.wechat || "",
            businessHours: response.data.businessHours || ""
          };
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/my/about.vue:115", "获取关于我们信息失败:", error);
        this.error = "获取信息失败，请检查网络连接";
        this.aboutInfo = {
          companyName: "芭佰邑智能染发机",
          companyIntro: "我们是一家专注于提供高质量服务的企业，致力于为用户创造价值，提供便捷、高效的解决方案。",
          phone: "***********",
          email: "<EMAIL>",
          address: "广州白云石井街芭百邑化妆品商行广州市白云区石槎路979号239室",
          version: "v1.0.0",
          logoUrl: "",
          website: "",
          qq: "",
          wechat: "",
          businessHours: ""
        };
      } finally {
        this.loading = false;
      }
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.aboutInfo.logoUrl || "/static/logo.png",
    b: common_vendor.t($data.aboutInfo.companyName || "关于我们"),
    c: !$data.loading
  }, !$data.loading ? common_vendor.e({
    d: $data.aboutInfo.companyIntro
  }, $data.aboutInfo.companyIntro ? {
    e: common_vendor.t($data.aboutInfo.companyIntro)
  } : {}, {
    f: $data.aboutInfo.phone
  }, $data.aboutInfo.phone ? {
    g: common_vendor.t($data.aboutInfo.phone)
  } : {}, {
    h: $data.aboutInfo.email
  }, $data.aboutInfo.email ? {
    i: common_vendor.t($data.aboutInfo.email)
  } : {}, {
    j: $data.aboutInfo.address
  }, $data.aboutInfo.address ? {
    k: common_vendor.t($data.aboutInfo.address)
  } : {}, {
    l: $data.aboutInfo.website
  }, $data.aboutInfo.website ? {
    m: common_vendor.t($data.aboutInfo.website)
  } : {}, {
    n: $data.aboutInfo.qq
  }, $data.aboutInfo.qq ? {
    o: common_vendor.t($data.aboutInfo.qq)
  } : {}, {
    p: $data.aboutInfo.wechat
  }, $data.aboutInfo.wechat ? {
    q: common_vendor.t($data.aboutInfo.wechat)
  } : {}, {
    r: $data.aboutInfo.businessHours
  }, $data.aboutInfo.businessHours ? {
    s: common_vendor.t($data.aboutInfo.businessHours)
  } : {}, {
    t: $data.aboutInfo.version
  }, $data.aboutInfo.version ? {
    v: common_vendor.t($data.aboutInfo.version)
  } : {}) : {}, {
    w: $data.loading
  }, $data.loading ? {} : {}, {
    x: $data.error
  }, $data.error ? {
    y: common_vendor.t($data.error),
    z: common_vendor.o((...args) => $options.loadAboutInfo && $options.loadAboutInfo(...args))
  } : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/my/about.js.map
