"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_request = require("../../utils/request.js");
const _sfc_main = {
  data() {
    return {
      baseUrl: "https://www.narenqiqige.com",
      contentId: "",
      contentInfo: {
        images: [],
        content: "",
        userName: "",
        createTime: "",
        commentCount: 0,
        likeCount: 0,
        viewCount: 0
      },
      comments: [],
      commentContent: ""
    };
  },
  onLoad(options) {
    common_vendor.index.__f__("log", "at pages/my/content-detail.vue:89", "传入的id", options.id);
    this.contentId = options.id;
    this.loadContentDetail(options.id);
  },
  methods: {
    async loadContentDetail(contentId) {
      const response = await utils_request.apiService.content.getDetail(contentId);
      this.contentInfo = response.data;
      this.contentId = response.data.id;
      const date = new Date(this.contentInfo.createTime);
      this.contentInfo.createTime = `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, "0")}-${date.getDate().toString().padStart(2, "0")} ${date.getHours().toString().padStart(2, "0")}:${date.getMinutes().toString().padStart(2, "0")}:${date.getSeconds().toString().padStart(2, "0")}`;
    },
    loadComments() {
    },
    sendComment() {
      if (!this.commentContent.trim())
        return;
      const newComment = {
        id: Date.now(),
        userName: "当前用户",
        content: this.commentContent,
        createTime: (/* @__PURE__ */ new Date()).toLocaleString()
      };
      this.comments.push(newComment);
      this.commentContent = "";
      this.contentInfo.commentCount += 1;
      common_vendor.index.pageScrollTo({
        scrollTop: 99999,
        duration: 300
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.f($data.contentInfo.images, (image, index, i0) => {
      return {
        a: $data.baseUrl + image,
        b: index
      };
    }),
    b: common_vendor.t($data.contentInfo.content),
    c: $data.contentInfo.avatar || "/static/logo.png",
    d: common_vendor.t($data.contentInfo.userName),
    e: common_vendor.t($data.contentInfo.createTime),
    f: common_vendor.t($data.contentInfo.commentCount || 0),
    g: common_vendor.t($data.contentInfo.likeCount || 0),
    h: common_vendor.t($data.contentInfo.viewCount || 0),
    i: $data.comments.length === 0
  }, $data.comments.length === 0 ? {} : {
    j: common_vendor.f($data.comments, (comment, k0, i0) => {
      return {
        a: common_vendor.t(comment.userName),
        b: common_vendor.t(comment.createTime),
        c: common_vendor.t(comment.content),
        d: comment.id
      };
    })
  }, {
    k: $data.commentContent,
    l: common_vendor.o(($event) => $data.commentContent = $event.detail.value),
    m: common_vendor.o((...args) => $options.sendComment && $options.sendComment(...args))
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/my/content-detail.js.map
