/* 水平间距 */
/* 水平间距 */
/* 全局样式修复 - 确保页面占满屏幕宽度 */
page {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
}

/* 通用容器样式 */
.container, .page-container {
  width: 100%;
  min-height: 100vh;
  box-sizing: border-box;
}

/* 修复小程序页面宽度问题 */
uni-page-body {
  width: 100% !important;
}

/* 确保所有view元素正确继承宽度 */
view {
  box-sizing: border-box;
}
.about-container {
  width: 100%;
  box-sizing: border-box;
  padding: 30rpx;
}
.header {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 50rpx;
}
.header .logo {
  width: 150rpx;
  height: 150rpx;
  margin-bottom: 20rpx;
}
.header .title {
  font-size: 36rpx;
  font-weight: bold;
}
.content .section {
  margin-bottom: 40rpx;
}
.content .section .section-title {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
  color: #333;
}
.content .section .section-text {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
}
.content .section .contact-item {
  display: flex;
  margin-bottom: 10rpx;
}
.content .section .contact-item .label {
  font-size: 28rpx;
  color: #333;
  width: 100rpx;
}
.content .section .contact-item .value {
  font-size: 28rpx;
  color: #666;
  flex: 1;
}
.content .section .version {
  font-size: 28rpx;
  color: #999;
}
.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400rpx;
}
.loading text {
  font-size: 28rpx;
  color: #999;
}
.error {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 400rpx;
}
.error text {
  font-size: 28rpx;
  color: #999;
  margin-bottom: 20rpx;
}
.error .retry-btn {
  background-color: #4285f4;
  color: white;
  border: none;
  border-radius: 10rpx;
  font-size: 28rpx;
}