<template>
  <view class="content">
    <!-- 分类标签栏 -->
    <view class="category-tabs">
      <view class="category-tab"
            v-for="category in categories"
            :key="category.id"
            :class="{active: currentCategoryId === category.id}"
            @click="switchCategory(category)">
        <view class="icon-placeholder">
          <!-- 如果是图片URL，显示图片 -->
          <image v-if="category.icon && isImageUrl(category.icon)" :src="baseUrl + category.icon" mode="aspectFit" @error="onImageError(category.id)"></image>
          <!-- 如果是Font Awesome类名，显示图标文字 -->
          <view v-else-if="category.icon && !isImageUrl(category.icon)" class="font-icon">
            <text class="icon-text">{{getFontAwesomeIcon(category.icon)}}</text>
          </view>
          <!-- 默认图标 -->
          <view v-else class="default-icon">
            <text class="icon-text">{{getDefaultIcon(category.name)}}</text>
          </view>
        </view>
        <text>{{category.name}}</text>
      </view>
    </view>
    
    <!-- 商品列表 -->
    <view class="product-list">
      <!-- 商品项 -->
      <view class="product-item" v-for="product in products" :key="product.id" @click="navigateToProduct(product.id)">
        <view class="product-image-placeholder">
          <image v-if="product.images" :src="baseUrl + getFirstImage(product.images)" mode="aspectFill"></image>
        </view>
        <view class="product-details">
          <view class="product-name">{{product.productName || product.name}}</view>
          <view class="product-brand">{{product.brand || '官方'}}</view>
          <view class="product-desc">{{product.description || '优质商品，值得信赖'}}</view>
          <view class="delivery-info">最快2天送达</view>
          <view class="product-price">
            <text class="price-value">{{product.price}}</text>
            <text class="price-unit">元</text>
          </view>
          <view class="store-info">
            <text class="store-name">{{product.storeName || '官方直营'}}</text>
            <text class="rating">已售{{product.sales || 0}} 好评{{product.rating || 99}}%</text>
          </view>
        </view>
      </view>

      <!-- 加载更多 -->
      <view class="load-more" v-if="hasMore && !loading" @click="loadMoreProducts">
        <text>加载更多</text>
      </view>

      <!-- 加载中 -->
      <view class="loading" v-if="loading">
        <text>加载中...</text>
      </view>

      <!-- 没有更多数据 -->
      <view class="no-more" v-if="!hasMore && products.length > 0">
        <text>没有更多商品了</text>
      </view>

      <!-- 空状态 -->
      <view class="empty-state" v-if="products.length === 0 && !loading">
        <view class="empty-icon">
          <text class="icon">🛍️</text>
        </view>
        <view class="empty-title">该分类暂无商品</view>
        <view class="empty-desc">{{categoryName}}分类正在上新中，请稍后再来看看</view>
        <view class="empty-actions">
          <button class="empty-back-btn" @click="goBack">返回商城</button>
          <button class="refresh-btn" @click="refreshData">刷新</button>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { apiService } from '@/utils/request.js';

export default {
  data() {
    return {
      baseUrl : "https://www.narenqiqige.com/api",
      categoryName: '',
      currentCategoryId: null,
      categories: [],
      products: [],
      loading: false,
      hasMore: true,
      currentPage: 1,
      pageSize: 10,
      imageErrors: {} // 记录图片加载失败的分类
    };
  },
  onLoad(options) {
    // 接收页面参数
    if (options.categoryId) {
      this.currentCategoryId = parseInt(options.categoryId);
    }
    if (options.categoryName) {
      this.categoryName = decodeURIComponent(options.categoryName);
    }

    this.loadCategories();
    this.loadProducts();
  },
  methods: {
    // 加载分类列表
    async loadCategories() {
      try {
        const response = await apiService.mall.categories.list();
        if (response.code === 200) {
          this.categories = response.data || [];

          // 如果没有指定分类，默认选择第一个
          if (!this.currentCategoryId && this.categories.length > 0) {
            this.currentCategoryId = this.categories[0].id;
            this.categoryName = this.categories[0].name;
          }
        } else {
          console.error('获取分类失败:', response.message);
        }
      } catch (error) {
        console.error('获取分类异常:', error);
      }
    },

    // 加载商品列表
    async loadProducts(isLoadMore = false) {
      if (this.loading) return;

      this.loading = true;

      try {
        const params = {
          categoryId: this.currentCategoryId,
          page: isLoadMore ? this.currentPage + 1 : 1,
          pageSize: this.pageSize
        };

        const response = await apiService.mall.products.list(params);

        if (response.code === 200) {
          const newProducts = response.data.products || response.data.records || response.data || [];

          if (isLoadMore) {
            this.products = [...this.products, ...newProducts];
            this.currentPage++;
          } else {
            this.products = newProducts;
            this.currentPage = 1;
          }

          // 判断是否还有更多数据
          this.hasMore = newProducts.length === this.pageSize;
        } else {
          console.error('获取商品失败:', response.message);
          uni.showToast({
            title: '获取商品失败',
            icon: 'none'
          });
        }
      } catch (error) {
        console.error('获取商品异常:', error);
        uni.showToast({
          title: '网络异常',
          icon: 'none'
        });
      } finally {
        this.loading = false;
      }
    },

    // 加载更多商品
    loadMoreProducts() {
      this.loadProducts(true);
    },

    // 获取商品第一张图片
    getFirstImage(images) {
      if (!images) return '';
      if (typeof images === 'string') {
        const imageArray = images.split(',');
        return imageArray[0] || '';
      }
      return images[0] || '';
    },

    goBack() {
      uni.navigateBack();
    },

    switchCategory(category) {
      this.currentCategoryId = category.id;
      this.categoryName = category.name;
      this.loadProducts(); // 重新加载商品
    },

    navigateToProduct(productId) {
      uni.navigateTo({
        url: "/pages/mall/product/detail?id=" + productId
      });
    },

    // 刷新数据
    refreshData() {
      this.products = [];
      this.currentPage = 1;
      this.hasMore = true;

      uni.showLoading({
        title: '刷新中...'
      });

      this.loadProducts().finally(() => {
        uni.hideLoading();
        uni.showToast({
          title: '刷新完成',
          icon: 'success'
        });
      });
    },

    // 判断是否为图片URL
    isImageUrl(url) {
      if (!url) return false;
      return url.startsWith('http') || url.startsWith('/static') || url.startsWith('data:image') || url.startsWith('https');
    },

    // 图片加载失败处理
    onImageError(categoryId) {
      this.$set(this.imageErrors, categoryId, true);
    },

    // 获取Font Awesome图标对应的Unicode字符
    getFontAwesomeIcon(iconClass) {
      // Font Awesome图标映射表（部分常用图标）
      const iconMap = {
        'fas fa-mobile-alt': '📱',
        'fas fa-tshirt': '👕',
        'fas fa-home': '🏠',
        'fas fa-heart': '💄',
        'fas fa-utensils': '🍽️',
        'fas fa-running': '🏃',
        'fas fa-laptop': '💻',
        'fas fa-car': '🚗',
        'fas fa-book': '📚',
        'fas fa-music': '🎵'
      };

      return iconMap[iconClass] || '📦';
    },

    // 获取默认图标
    getDefaultIcon(categoryName) {
      // 根据分类名称返回默认图标
      const nameIconMap = {
        '数码电子': '📱',
        '时尚服装': '👕',
        '家居生活': '🏠',
        'Beauty Care': '💄',
        'Food Drinks': '🍽️',
        'Sports Outdoor': '🏃'
      };

      return nameIconMap[categoryName] || '📦';
    }
  }
};
</script>

<style lang="scss">
.content {
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
  min-height: 100vh;
}



.category-tabs {
  display: flex;
  background-color: #fff;
  padding: 20rpx 0;
  overflow-x: auto;
  white-space: nowrap;
  border-bottom: 1rpx solid #eee;
}

.category-tab {
  display: inline-flex;
  flex-direction: column;
  align-items: center;
  margin: 0 30rpx;
}

.icon-placeholder {
  width: 80rpx;
  height: 80rpx;
  background-color: transparent;
  border-radius: 50%;
  margin-bottom: 10rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.font-icon, .default-icon {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.icon-text {
  font-size: 48rpx;
}

.category-tab text {
  font-size: 24rpx;
  color: #333;
}

.product-list {
  padding: 20rpx;
}

.product-item {
  display: flex;
  background-color: #fff;
  padding: 20rpx;
  margin-bottom: 20rpx;
  border-radius: 12rpx;
}

.product-image-placeholder {
  width: 200rpx;
  height: 200rpx;
  background-color: #f0f0f0;
  border-radius: 8rpx;
}

.product-details {
  flex: 1;
  margin-left: 20rpx;
  display: flex;
  flex-direction: column;
}

.product-name {
  font-size: 28rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.product-brand {
  font-size: 26rpx;
  color: #333;
  margin-bottom: 10rpx;
}

.product-desc {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 10rpx;
}

.delivery-info {
  font-size: 24rpx;
  color: #4285f4;
  margin-bottom: 15rpx;
}

.product-price {
  margin-bottom: 10rpx;
}

.price-value {
  font-size: 36rpx;
  color: #f44336;
  font-weight: bold;
}

.price-unit {
  font-size: 24rpx;
  color: #666;
  margin-left: 5rpx;
}

.store-info {
  display: flex;
  justify-content: space-between;
  font-size: 24rpx;
  color: #999;
}

.load-more, .loading, .no-more {
  text-align: center;
  padding: 40rpx 20rpx;
  color: #999;
  font-size: 28rpx;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 40rpx;
  background-color: #fff;
  margin: 20rpx;
  border-radius: 16rpx;
}

.empty-icon {
  margin-bottom: 30rpx;
}

.empty-icon .icon {
  font-size: 120rpx;
  opacity: 0.6;
}

.empty-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 16rpx;
}

.empty-desc {
  font-size: 28rpx;
  color: #666;
  text-align: center;
  line-height: 1.5;
  margin-bottom: 40rpx;
}

.empty-actions {
  display: flex;
  gap: 20rpx;
}

.empty-back-btn, .refresh-btn {
  border: none;
  border-radius: 15rpx;
  padding: 12rpx ;
  font-size: 25rpx;
  min-width: 200rpx; // 最小宽度
  font-weight: bold;
}

.empty-back-btn {
  background-color: #f5f5f5;
  color: #666;
}

.empty-back-btn:active {
  background-color: #e0e0e0;
}

.refresh-btn {
  background-color: #4285f4;
  color: #fff;
}

.refresh-btn:active {
  background-color: #3367d6;
}

.load-more {
  background-color: #f5f5f5;
  margin: 20rpx;
  border-radius: 8rpx;
  cursor: pointer;
}

.load-more:active {
  background-color: #e0e0e0;
}

.icon-placeholder image {
  width: 100%;
  height: 100%;
  border-radius: 50%;
}

.product-image-placeholder image {
  width: 100%;
  height: 100%;
  border-radius: 8rpx;
}

.category-tab.active {
  color: #4285f4;
}

.category-tab.active .icon-placeholder {
  border: 2rpx solid #4285f4;
}
</style> 