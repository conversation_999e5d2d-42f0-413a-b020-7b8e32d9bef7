<template>
	<view class="modal-mask" v-if="showModal" @click="hideModal">
		<view class="modal-container" @click.stop>
			<view class="modal-header">
				<text>{{ modalTitle }}</text>
				<view class="close-icon" @click="hideModal">×</view>
			</view>

			<view class="modal-content">
				<text class="modal-text">{{ inputPrompt }}</text>
				<input type="number" v-model="inputValue" :placeholder="inputPlaceholder" class="amount-input"
					:class="{'empty-input': !inputValue}" />
			</view>

			<view class="modal-footer">
				<view class="confirm-button" @click="confirmInput">确认</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		props: {
			// 定义父组件可以传递的属性
			initialColor: {
				type: Object,
				default: null
			},
			initialColorIndex: {
				type: Number,
				default: 0
			}
		},
		data() {
			return {
				showModal: false, // 控制弹窗显示
				inputValue: '', // 输入的值
				modalTitle: '', // 弹窗标题
				inputPrompt: '', // 输入提示文本
				inputPlaceholder: '', // 输入框占位符
				// 获取一下当前输入的值
				currentColor: this.initialColor,
				// 当前颜色的索引
				currentColorIndex: this.initialColorIndex
			};
		},
		onLoad() {


		},
		methods: {
			// 更新当前颜色的索引
			updateColorIndex(index) {
				this.currentColorIndex = index;
			},
			// 显示弹窗
			showColorModal(color) {
				this.currentColor = color;
				this.inputValue = ''; // 重置输入值

				if (color.isOxidant) {
					// 如果是双氧乳
					this.modalTitle = `配置${color.name}`;
					this.inputPrompt = '请输入双氧克数';
					this.inputPlaceholder = `请输入${color.name}克数`;
				} else {
					// 如果是其他颜色
					this.modalTitle = `配置${color.name}克重`;
					this.inputPrompt = '请输入克重';
					this.inputPlaceholder = `请输入${color.name}克重`;
				}

				this.showModal = true;
			},

			// 隐藏弹窗
			hideModal() {
				this.showModal = false;
			},

			// 确认输入
			confirmInput() {
				if (!this.inputValue || isNaN(this.inputValue) || this.inputValue <= 0) {
					uni.showToast({
						title: '请输入有效的数值',
						icon: 'none'
					});
					return;
				}

				// 这里可以处理确认后的逻辑
				// 例如：更新颜色列表中的对应颜色
				// console.log('当前颜色:', this.currentColor.name);
				// console.log('输入值:', this.inputValue);

				// 可以在这里触发事件或调用父组件方法
				// this.$emit('color-configured', {
				//   color: this.currentColor,
				//   amount: parseFloat(this.inputValue)
				// });、
				console.log("现在的颜色类", this.currentColor)

				let value = parseFloat(this.inputValue)

				// Handle values greater than 500
				if (value > 500) {
					value = 500;
					uni.showToast({
						title: '超过最大限制500，已自动设置为500',
						icon: 'none'
					});
				}

				// Handle values less than 0.1
				if (value < 0.1) {
					value = 0.1;
					uni.showToast({
						title: '不能小于0.1，已自动设置为0.1',
						icon: 'none'
					});
				}

				this.currentColor.value = value;
				// this.currentColor.value = parseFloat(this.inputValue)


				this.$emit('confirm', {
					color: this.currentColor,
					colorIndex: this.currentColorIndex
					// color: this.currentColor,
					// amount: parseFloat(this.inputValue)
				});

				this.hideModal();
			}
		}
	};
</script>

<style lang="scss" scoped>
	.modal-mask {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: rgba(0, 0, 0, 0.5);
		display: flex;
		align-items: center;
		justify-content: center;
		z-index: 1000;
	}

	.modal-container {
		width: 80%;
		background-color: #fff;
		border-radius: 16rpx;
		overflow: hidden;
		box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
	}

	.modal-header {
		padding: 30rpx;
		font-size: 32rpx;
		font-weight: 500;
		text-align: center;
		background-color: #4285f4;
		color: #fff;
		position: relative;
		display: flex;
		justify-content: center;
		align-items: center;
	}

	.close-icon {
		position: absolute;
		right: 30rpx;
		font-size: 36rpx;
		font-weight: bold;
		color: #fff;
	}

	.modal-content {
		padding: 40rpx 30rpx;
		/* 微信小程序建议使用 min-height 替代固定 height */
		min-height: 120rpx;
		display: flex;
		flex-direction: column;
		justify-content: center;
	}

	.modal-text {
		display: block;
		font-size: 28rpx;
		margin-bottom: 20rpx;
		color: #333;
	}

	.amount-input {
		width: 100%;
		padding: 25rpx 20rpx;
		border: 2rpx solid #ddd;
		/* 微信小程序中稍微加粗边框更清晰 */
		border-radius: 12rpx;
		font-size: 32rpx;
		/* 适当调大字体 */
		box-sizing: border-box;
		background-color: #f9f9f9;
		height: 100rpx;
		/* 显著增加高度 */
		line-height: 1.5;

		/* 微信小程序专用样式 */
		::v-deep input {
			height: 100%;
			vertical-align: middle;
		}

		&:focus {
			border-color: #4285f4;
			background-color: #fff;
		}

		&.empty-input {
			border: 2rpx dashed #ccc;
			background-color: rgba(245, 245, 245, 0.8);
		}
	}

	/* 调整底部按钮间距 */
	.modal-footer {
		padding: 30rpx;
		margin-top: 0;
	}

	.confirm-button {
		padding: 20rpx;
		text-align: center;
		background-color: #f5f5f5;
		color: #4285f4;
		border-radius: 8rpx;
		font-size: 30rpx;
		font-weight: 500;
		margin-top: 50rpx;
	}
</style>