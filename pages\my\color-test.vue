<template>
	<view class="container">
		<!-- 设备信息区域 -->
		<view class="device-info">
			<view class="device-icon">
				<image src="/static/icons/equipment-icon.png" class="device-image"></image>
			</view>
			<view class="device-details">
				<view class="device-name">芭佰邑-智能染发机</view>
				<view class="device-id">编号: {{deviceCode}}</view>
			</view>
			<!-- <view class="device-status">
        <text class="status-text">连接</text>
      </view> -->
		</view>


		<!-- 颜色网格区域 -->
		<view class="color-grid" v-if="deviceCode && deviceStatus">
			<view class="color-item" v-for="(color, index) in colorList" :key="index"
				@click="selectColor(color, index)">
				<view class="color-circle-container">
					<view class="color-circle" :style="{ backgroundColor: color.hex }"
						:class="{ 'selected': selectedIndex === index }"></view>
					<!-- 比例条 -->
					<view class="color-progress" :style="{ width: getProgressWidth(color.name) }"></view>
				</view>
				<view class="color-name">{{ color.name }}</view>
			</view>
		</view>

		<!-- 底部按钮区域 -->
		<view class="bottom-buttons" v-if="deviceCode">
			<view class="btn-secondary" @click="checkColorRemaining">
				<text>检测 {{ selectedColorName || '灰色(1-0/1)' }} 色</text>
			</view>
			<view class="btn-primary" @click="startDetection">
				<image src="/static/icons/saoyisao.png" class="btn-icon"></image>
				<text style="font: 10rpx;">添加染膏</text>
			</view>
		</view>

		<!-- 自定义弹窗 -->
		<uni-popup ref="popup" type="dialog">
			<uni-popup-dialog :type="popupType" :title="popupTitle" :content="popupContent" :duration="2000"
				:before-close="true" @close="popupClose" @confirm="popupConfirm">
				<view class="popup-content">
					<view class="color-display" :style="{ backgroundColor: selectedColorHex }"></view>
					<view class="color-details">
						<view class="detail-row">
							<text class="detail-label">颜色名称:</text>
							<text class="detail-value">{{ selectedColorName }}</text>
						</view>
						<view class="detail-row">
							<text class="detail-label">仓位编号:</text>
							<text class="detail-value">{{ selectedColorPosition }}</text>
						</view>
						<view class="detail-row">
							<text class="detail-label">剩余量:</text>
							<text class="detail-value"
								:style="{ color: remainingQuantity < 20 ? '#ff4d4f' : '#52c41a' }">
								{{ remainingQuantity }} {{ unit }}
							</text>
						</view>
						<view class="progress-container">
							<progress :percent="remainingQuantity" stroke-width="10"
								:activeColor="remainingQuantity < 20 ? '#ff4d4f' : '#52c41a'"
								backgroundColor="#f0f0f0" />
						</view>
						<view class="status-message" :style="{ color: remainingQuantity < 20 ? '#ff4d4f' : '#52c41a' }">
							{{ remainingQuantity < 20 ? '余量不足，请及时补充' : '余量充足' }}
						</view>
					</view>
				</view>
			</uni-popup-dialog>
		</uni-popup>
	</view>
</template>

<script>
	import {
		apiService
	} from '../../utils/request.js';
	export default {
		data() {
			return {
				deviceCode: '',
				deviceStatus: false, // 设备是否在线
				selectedColorName: '', // 新增属性存储选中的颜色名称
				selectedIndex: null,
				colorList: [{
						name: '0/11灰色/Grey',
						color_code: '0x30',
						hex: '#8B8B8B',
						value: 20
					},
					{
						name: '0/22绿色/Green',
						color_code: '0x31',
						hex: '#2E7D32',
						value: 20
					},
					{
						name: '0/33黄色/yellow',
						color_code: '0x32',
						hex: '#FFD700',
						value: 35
					},
					{
						name: '0/43橙色/Orange',
						color_code: '0x33',
						hex: '#FFA726',
						value: 32
					},
					{
						name: '0/45红色/Red',
						color_code: '0x34',
						hex: '#D32F2F',
						value: 15
					},
					{
						name: '0/66紫色/Purple',
						color_code: '0x35',
						hex: '#8E24AA',
						value: 15
					},
					{
						name: '0/77棕色/Brown',
						color_code: '0x36',
						hex: '#6D4C41',
						value: 10
					},
					{
						name: '0/88蓝色/Blue',
						color_code: '0x37',
						hex: '#1565C0',
						value: 5
					},
					{
						name: '2/0黑色/Black',
						color_code: '0x38',
						hex: '#000000',
						value: 30
					},
					{
						name: '0/00淡化剂/Faded',
						color_code: '0x39',
						hex: '#FFFFFF',
						value: 25
					},
					{
						name: '12/11极浅灰金色/Bleached',
						color_code: '0x40',
						hex: '#E0E0E0',
						value: 25
					},
					{
						name: '12/16极浅紫金/Silver',
						color_code: '0x41',
						hex: '#E1BEE7',
						value: 25
					},
					{
						name: '浅棕色/Gold',
						color_code: '0x42',
						hex: '#6D4C41',
						value: 25
					},
					{
						name: '深灰亚麻色/Linen',
						color_code: '0x43',
						hex: '#B0BEC5',
						value: 25
					},
					{
						name: '双氧乳3%',
						color_code: '0x0A',
						hex: '#FFFFFF',
						value: 25
					},
					{
						name: '双氧乳12%',
						color_code: '0x0B',
						hex: '#FFFFFF',
						value: 25
					},
				],
				unit: 'g',
				remainingQuantity: 0
			};
		},
		onLoad() {
			const app = getApp()
			this.deviceCode = app.globalData.connectedDeviceId;
			this.deviceStatus = app.globalData.deviceStatus;
		},
		methods: {
			selectColor(color, index) {
				console.log('选择颜色:', color);
				this.selectedIndex = index;
				this.selectedColorName = color.name;
				this.selectedColorHex = color.hex;
				this.selectedColorPosition = color.color_code;
			},
			async checkColorRemaining() {
				if (this.selectedIndex === null) {
					uni.showToast({
						title: '请先选择要检测的颜色',
						icon: 'none',
						duration: 2000
					});
					return;
				}

				const selectedColor = this.colorList[this.selectedIndex];

				try {
					uni.showLoading({
						title: '检测中...',
						mask: true
					});

					// 调用API查询余量
					// const response = await apiService.mall.dyeConsumption.checkRemaining({
					//   deviceCode: this.deviceCode,
					//   colorCode: selectedColor.color_code
					// });

					// if (response.code === 200) {
					//   this.remainingQuantity = 50 || 0;
					//   this.popupType = 50 < 20 ? 'error' : 'success';
					//   this.popupTitle = '染膏余量检测';
					//   this.popupContent = `当前颜色余量: ${50}%`;

					//   // 显示弹窗
					//   this.$refs.popup.open();
					// } else {
					//   throw new Error(response.message || '查询余量失败');
					// }
					const response = await apiService.mall.dyeConsumption.checkRemaining({
						deviceCode: this.deviceCode,
						colorCode: selectedColor.color_code
					});

					if (response.code === 200) {
						this.remainingQuantity = response.data.remainingQuantity || 0;
						this.popupType = this.remainingQuantity <= 50 ? 'error' : 'success';
						this.popupTitle = '染膏余量检测';
						this.popupContent = `当前颜色余量: ${response.data.proportion}${response.data.unit}`;
						this.unit = response.data.unit

						// 显示弹窗
						this.$refs.popup.open();
					} else {
						throw new Error(response.message || '查询余量失败');
					}
				} catch (error) {
					console.error('检测余量失败:', error);
					uni.showToast({
						title: error.message || '检测余量失败',
						icon: 'none',
						duration: 2000
					});
				} finally {
					uni.hideLoading();
				}
			},
			popupClose() {
				console.log('弹窗关闭');
			},

			popupConfirm() {
				console.log('弹窗确认');
				this.$refs.popup.close();
			},
			showAllColors() {
				console.log('显示所有颜色');
				// 处理显示所有颜色逻辑
			},
			startDetection() {
				const app = getApp()
				console.log('开始检测');
				// 处理开始检测逻辑
				// 调用微信小程序扫码接口
				uni.scanCode({
					onlyFromCamera: false, // 是否只能从相机扫码（不允许从相册选择）
					scanType: ['qrCode', 'barCode'], // 扫码类型：二维码、条形码
					success: async (res) => {
						console.log('扫码成功:', res);

						// 扫码结果处理
						const result = res.result; // 获取扫码内容
						const scanType = res.scanType; // 获取扫码类型

						// uni.showToast({
						//   title: `扫码成功: ${result}`,
						//   icon: 'success',
						//   duration: 2000
						// });

						// 将扫码结果转换为json
						try {
							const data = JSON.parse(result.replace(/[ \n]+/g, ''));

							// 这里调用后端接口传入数据标记染膏
							const deviceCode = app.globalData.connectedDeviceId ? app.globalData
								.connectedDeviceId : undefined
							const response = await apiService.mall.dyeConsumption.scanCodeBinding(deviceCode,
								data)
							if (response.code === 200) {
								uni.showToast({
									title: `添加${data.colorname ? data.colorname:''}染膏成功`,
									icon: 'success',
									duration: 3000
								});
							} else {
								throw new Error("扫码识别,不是染膏类型的二维码");
							}
						} catch (err) {
							uni.showToast({
								title: `扫码识别,不是染膏类型的二维码`,
								icon: 'none',
								duration: 3000
							});
						}


						// 这里可以处理扫码结果，比如跳转页面或发送请求
						// this.handleScanResult(result);
					},
					fail: (err) => {
						console.error('扫码失败:', err);
						uni.showToast({
							title: '扫码失败，请重试',
							icon: 'none',
							duration: 2000
						});
					}
				});
			},
			getProgressWidth(name) {
				// 从颜色名称中提取数值部分
				const match = name.match(/\((\d+)-/);
				if (match && match[1]) {
					const num = parseInt(match[1]);
					// 计算比例，假设最大值为50
					return `${(num / 50) * 100}%`;
				}
				return '0%';
			}
		}
	};
</script>

<style lang="scss" scoped>
	.container {
		background-color: #f5f5f5;
		min-height: 100vh;
		padding: 20rpx;
	}

	.device-info {
		background-color: #ffffff;
		border-radius: 20rpx;
		padding: 30rpx;
		margin-bottom: 30rpx;
		display: flex;
		align-items: center;
		box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
	}

	.device-icon {
		width: 80rpx;
		height: 80rpx;
		margin-right: 20rpx;
	}

	.device-image {
		width: 100%;
		height: 100%;
		border-radius: 10rpx;
	}

	.device-details {
		flex: 1;
	}

	.device-name {
		font-size: 32rpx;
		font-weight: 600;
		color: #333333;
		margin-bottom: 8rpx;
	}

	.device-id {
		font-size: 26rpx;
		color: #666666;
	}

	.device-status {
		background-color: #4CAF50;
		border-radius: 30rpx;
		padding: 12rpx 24rpx;
	}

	.status-text {
		color: #ffffff;
		font-size: 26rpx;
		font-weight: 500;
	}

	.color-grid {
		display: flex;
		flex-wrap: wrap;
		justify-content: space-between;
		padding: 0 10rpx;
		margin-bottom: 60rpx;
	}

	.color-item {
		width: 30%;
		margin-bottom: 40rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
	}

	.color-circle-container {
		color_code: relative;
		width: 140rpx;
		margin-bottom: 16rpx;
	}

	.color-circle {
		width: 140rpx;
		height: 80rpx;
		border-radius: 8rpx;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
		color_code: relative;
		z-index: 2;
		border: 4rpx solid transparent;
		transition: border-color 0.3s;

		&.selected {
			border-color: #4285f4;
		}
	}

	.color-progress {
		color_code: absolute;
		bottom: -10rpx;
		left: 0;
		height: 6rpx;
		background-color: #4285f4;
		border-radius: 3rpx;
		z-index: 1;
	}

	.color-name {
		font-size: 24rpx;
		color: #333333;
		text-align: center;
		line-height: 1.3;
	}

	.bottom-buttons {
		display: flex;
		justify-content: space-between;
		/* 让两个按钮分布在两端 */
		padding: 20rpx 30rpx 40rpx;
		background-color: #ffffff;
		color_code: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);
	}

	.btn-secondary {
		flex: 0 0 70%;
		/* flex-grow: 0, flex-shrink: 0, flex-basis: 60% (固定60%宽度) */
		background-color: #f0f0f0;
		border-radius: 50rpx;
		padding: 24rpx 0;
		text-align: center;
		margin-right: 20rpx;
		/* 固定间距 */
	}

	.btn-primary {
		display: flex;
		align-items: center;
		/* 垂直居中 */
		justify-content: center;
		/* 水平居中 */
		flex: 1;
		background-color: #4285f4;
		border-radius: 50rpx;
		padding: 24rpx 0;
		text-align: center;
	}



	.btn-icon {
		width: 32rpx;
		/* 图标大小 */
		height: 32rpx;
		margin-right: 10rpx;
		/* 图标与文字的间距 */
	}

	.btn-secondary text {
		color: #666666;
		font-size: 28rpx;
	}

	.btn-primary text {
		color: #ffffff;
		font-size: 28rpx;
		font-weight: 500;
	}

	/* 新增弹窗样式 */
	.popup-content {
		padding: 20rpx;
		width: 100%;
	}

	.color-display {
		width: 120rpx;
		height: 80rpx;
		margin: 0 auto 30rpx;
		border-radius: 8rpx;
		box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
	}

	.color-details {
		margin-top: 20rpx;
	}

	.detail-row {
		display: flex;
		justify-content: space-between;
		margin-bottom: 20rpx;
		font-size: 28rpx;
	}

	.detail-label {
		color: #666;
	}

	.detail-value {
		color: #333;
		font-weight: 500;
	}

	.progress-container {
		margin: 30rpx 0;
	}

	.status-message {
		text-align: center;
		font-size: 28rpx;
		font-weight: 500;
		margin-top: 20rpx;
	}

	/* 调整uni-popup默认样式 */
	.uni-popup__wrapper {
		border-radius: 20rpx;
		overflow: hidden;
	}

	.uni-popup__dialog {
		width: 80%;
		max-width: 600rpx;
	}
</style>