<view class="location-picker"><view class="header"><view class="header-btn" bindtap="{{a}}">确定</view><view class="header-title">{{b}}</view><view class="header-btn" bindtap="{{c}}">取消</view></view><view class="search-container"><view class="search-box"><view class="search-icon">🔍</view><input type="text" class="search-input" placeholder="搜索地点" bindinput="{{d}}" value="{{e}}"/></view></view><view class="current-location"><view class="location-icon">📍</view><view class="current-location-text"><view class="current-location-name">{{f}}</view><view class="current-location-detail">{{g}}</view></view><view class="relocate-btn" bindtap="{{h}}">重新定位</view></view><view class="map-container"><map class="map" latitude="{{i}}" longitude="{{j}}" markers="{{k}}" scale="{{16}}" show-location="{{false}}" enable-scroll="{{true}}" enable-zoom="{{true}}" enable-rotate="{{false}}" bindmarkertap="{{l}}" bindtap="{{m}}" bindregionchange="{{n}}"></map></view><scroll-view class="location-list" scroll-y><view wx:for="{{o}}" wx:for-item="location" wx:key="c" class="{{['location-item', location.d && 'selected']}}" bindtap="{{location.e}}"><view class="location-name">{{location.a}}</view><view class="location-address">{{location.b}}</view></view></scroll-view></view>