<view class="location-picker data-v-4925d87e"><view class="header data-v-4925d87e"><view class="header-btn data-v-4925d87e" bindtap="{{a}}">确定</view><view class="header-title data-v-4925d87e">{{b}}</view><view class="header-btn data-v-4925d87e" bindtap="{{c}}">取消</view></view><view class="search-container data-v-4925d87e"><view class="search-box data-v-4925d87e"><view class="search-icon data-v-4925d87e">🔍</view><input type="text" class="search-input data-v-4925d87e" placeholder="搜索地点" bindinput="{{d}}" value="{{e}}"/></view></view><view class="current-location data-v-4925d87e"><view class="location-icon data-v-4925d87e">📍</view><view class="current-location-text data-v-4925d87e"><view class="current-location-name data-v-4925d87e">{{f}}</view><view class="current-location-detail data-v-4925d87e">{{g}}</view></view><view class="relocate-btn data-v-4925d87e" bindtap="{{h}}">重新定位</view></view><view class="map-container data-v-4925d87e"><map class="map data-v-4925d87e" latitude="{{i}}" longitude="{{j}}" markers="{{k}}" scale="{{16}}" show-location="{{false}}" enable-scroll="{{true}}" enable-zoom="{{true}}" enable-rotate="{{false}}" bindmarkertap="{{l}}" bindtap="{{m}}" bindregionchange="{{n}}"></map></view><scroll-view class="location-list data-v-4925d87e" scroll-y><view wx:for="{{o}}" wx:for-item="location" wx:key="c" class="location-item data-v-4925d87e" bindtap="{{location.d}}"><view class="location-name data-v-4925d87e">{{location.a}}</view><view class="location-address data-v-4925d87e">{{location.b}}</view></view></scroll-view></view>