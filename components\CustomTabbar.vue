<template>
  <view class="custom-tabbar">
    <view class="tabbar-item" :class="{ active: currentPage === 'home' }" @click="switchTab('home')">
      <image class="tabbar-icon" :src="currentPage === 'home' ? homeSelectedIcon : homeIcon"></image>
      <text class="tabbar-text" :class="{ active: currentPage === 'home' }">首页</text>
    </view>
    <view class="tabbar-item" :class="{ active: currentPage === 'palette' }" @click="switchTab('palette')">
      <image class="tabbar-icon" :src="currentPage === 'palette' ? paletteSelectedIcon : paletteIcon"></image>
      <text class="tabbar-text" :class="{ active: currentPage === 'palette' }">色板</text>
    </view>
    <view class="tabbar-item" :class="{ active: currentPage === 'device' }" @click="switchTab('device')">
      <image class="tabbar-icon" :src="currentPage === 'device' ? deviceSelectedIcon : deviceIcon"></image>
      <text class="tabbar-text" :class="{ active: currentPage === 'device' }">设备</text>
    </view>
    <view class="tabbar-item" :class="{ active: currentPage === 'my' }" @click="switchTab('my')">
      <image class="tabbar-icon" :src="currentPage === 'my' ? userSelectedIcon : userIcon"></image>
      <text class="tabbar-text" :class="{ active: currentPage === 'my' }">我的</text>
    </view>
  </view>
</template>

<script>
export default {
  name: 'CustomTabbar',
  data() {
    return {
      currentPage: 'my', // 默认为当前页面
      homeIcon: '/static/tabbar/home.png',
      homeSelectedIcon: '/static/tabbar/home_selected.png',
      paletteIcon: '/static/tabbar/palette.png',
      paletteSelectedIcon: '/static/tabbar/palette_selected.png',
      deviceIcon: '/static/tabbar/device.png',
      deviceSelectedIcon: '/static/tabbar/device_selected.png',
      userIcon: '/static/tabbar/user.png',
      userSelectedIcon: '/static/tabbar/user_selected.png'
    };
  },
  created() {
    // 根据当前页面路径设置活动选项卡
    const pages = getCurrentPages();
    if (pages.length > 0) {
      const currentRoute = pages[pages.length - 1].route;
      if (currentRoute.includes('/mall/')) {
        this.currentPage = 'home';
      } else if (currentRoute.includes('/index/')) {
        this.currentPage = 'palette';
      } else if (currentRoute.includes('/device/')) {
        this.currentPage = 'device';
      } else if (currentRoute.includes('/my/')) {
        this.currentPage = 'my';
      }
    }
  },
  methods: {
    switchTab(page) {
      this.currentPage = page;
      switch (page) {
        case 'home':
          uni.switchTab({
            url: '/pages/mall/mall'
          });
          break;
        case 'palette':
          uni.switchTab({
            url: '/pages/index/index'
          });
          break;
        case 'device':
          uni.switchTab({
            url: '/pages/device/index'
          });
          break;
        case 'my':
          uni.switchTab({
            url: '/pages/my/my'
          });
          break;
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.custom-tabbar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 100rpx;
  background-color: #ffffff;
  display: flex;
  align-items: center;
  justify-content: space-around;
  box-shadow: 0 -2rpx 5rpx rgba(0, 0, 0, 0.1);
  padding-bottom: env(safe-area-inset-bottom);
  z-index: 999;
  border-top: 1rpx solid #eee;
}

.tabbar-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 10rpx 0;
}

.tabbar-icon {
  width: 48rpx;
  height: 48rpx;
  margin-bottom: 8rpx;
}

.tabbar-text {
  font-size: 24rpx;
  color: #999;
  line-height: 1;
}

.tabbar-text.active {
  color: #4285f4;
}
</style> 