<template>
	<view class="container">
		<view class="header">
			<text class="title">🔧 蓝牙WiFi耦合修复测试</text>
			<text class="subtitle">验证蓝牙连接稳定性修复效果</text>
		</view>

		<!-- 当前状态显示 -->
		<view class="status-section">
			<view class="section-title">📊 当前连接状态</view>
			<view class="status-item">
				<text class="label">连接方式:</text>
				<text class="value" :class="getConnectionMethodClass()">{{ connectionMethod || '未连接' }}</text>
			</view>
			<view class="status-item">
				<text class="label">设备状态:</text>
				<text class="value" :class="deviceStatus ? 'online' : 'offline'">
					{{ deviceStatus ? '在线' : '离线' }}
				</text>
			</view>
			<view class="status-item">
				<text class="label">设备ID:</text>
				<text class="value">{{ connectedDeviceId || '无' }}</text>
			</view>
		</view>

		<!-- 状态验证 -->
		<view class="validation-section">
			<view class="section-title">🔍 状态验证</view>
			<button class="btn btn-primary" @click="validateState">验证连接状态</button>
			<button class="btn btn-secondary" @click="autoFixState">自动修复状态</button>
			
			<view v-if="validationResult" class="validation-result">
				<view class="validation-status" :class="validationResult.isValid ? 'valid' : 'invalid'">
					{{ validationResult.isValid ? '✅ 状态正常' : '❌ 检测到问题' }}
				</view>
				<view v-if="validationResult.issues.length > 0" class="issues">
					<view class="issue" v-for="(issue, index) in validationResult.issues" :key="index">
						<text class="issue-type">{{ issue.type }}</text>
						<text class="issue-message">{{ issue.message }}</text>
						<text class="issue-severity" :class="issue.severity.toLowerCase()">{{ issue.severity }}</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 测试操作 -->
		<view class="test-section">
			<view class="section-title">🧪 测试操作</view>
			<button class="btn btn-test" @click="simulateAppLifecycle">模拟应用生命周期</button>
			<button class="btn btn-test" @click="simulateNetworkChange">模拟网络变化</button>
			<button class="btn btn-test" @click="clearAllStates">清除所有状态</button>
		</view>

		<!-- 日志显示 -->
		<view class="log-section">
			<view class="section-title">📝 状态变化日志</view>
			<button class="btn btn-small" @click="refreshLogs">刷新日志</button>
			<button class="btn btn-small" @click="clearLogs">清除日志</button>
			
			<scroll-view class="log-container" scroll-y="true">
				<view v-for="(log, index) in logs" :key="index" class="log-item">
					<view class="log-header">
						<text class="log-time">{{ formatTime(log.timestamp) }}</text>
						<text class="log-action">{{ log.action }}</text>
						<text class="log-type" :class="log.connectionType">{{ log.connectionType }}</text>
					</view>
					<view v-if="log.details" class="log-details">
						{{ JSON.stringify(log.details, null, 2) }}
					</view>
					<view v-if="log.validation && !log.validation.isValid" class="log-validation">
						⚠️ 检测到 {{ log.validation.issues.length }} 个问题
					</view>
				</view>
			</scroll-view>
		</view>
	</view>
</template>

<script>
import connectionStateValidator from '@/utils/connectionStateValidator.js';

export default {
	data() {
		return {
			connectionMethod: '',
			deviceStatus: false,
			connectedDeviceId: '',
			validationResult: null,
			logs: [],
			refreshTimer: null
		};
	},

	onLoad() {
		this.refreshStatus();
		this.refreshLogs();
		
		// 每5秒自动刷新状态
		this.refreshTimer = setInterval(() => {
			this.refreshStatus();
			this.refreshLogs();
		}, 5000);
	},

	onUnload() {
		if (this.refreshTimer) {
			clearInterval(this.refreshTimer);
		}
	},

	methods: {
		// 刷新当前状态
		refreshStatus() {
			const app = getApp();
			this.connectionMethod = app.globalData.connectionMethod;
			this.deviceStatus = app.globalData.deviceStatus;
			this.connectedDeviceId = app.globalData.connectedDeviceId;
		},

		// 验证连接状态
		validateState() {
			this.validationResult = connectionStateValidator.validateConnectionState();
			console.log('状态验证结果:', this.validationResult);
		},

		// 自动修复状态
		autoFixState() {
			const fixResult = connectionStateValidator.autoFixStateIssues();
			console.log('自动修复结果:', fixResult);
			
			if (fixResult.fixed) {
				uni.showToast({
					title: `已修复 ${fixResult.fixedIssues.length} 个问题`,
					icon: 'success'
				});
				this.refreshStatus();
				this.validateState();
			} else {
				uni.showToast({
					title: '未发现需要修复的问题',
					icon: 'none'
				});
			}
		},

		// 刷新日志
		refreshLogs() {
			this.logs = connectionStateValidator.getHistory(20);
		},

		// 清除日志
		clearLogs() {
			connectionStateValidator.clearHistory();
			this.logs = [];
		},

		// 模拟应用生命周期
		simulateAppLifecycle() {
			console.log('🔄 模拟应用生命周期变化');
			
			// 模拟onHide
			const app = getApp();
			app.pauseServices();
			
			setTimeout(() => {
				// 模拟onShow
				app.resumeServices();
				this.refreshStatus();
				this.refreshLogs();
			}, 2000);
			
			uni.showToast({
				title: '已模拟应用生命周期',
				icon: 'success'
			});
		},

		// 模拟网络变化
		simulateNetworkChange() {
			console.log('🌐 模拟网络状态变化');
			
			// 触发网络状态变化事件
			uni.onNetworkStatusChange({
				isConnected: true,
				networkType: 'wifi'
			});
			
			this.refreshStatus();
			this.refreshLogs();
			
			uni.showToast({
				title: '已模拟网络变化',
				icon: 'success'
			});
		},

		// 清除所有状态
		clearAllStates() {
			uni.showModal({
				title: '确认清除',
				content: '确定要清除所有连接状态吗？',
				success: (res) => {
					if (res.confirm) {
						const app = getApp();
						const store = app.$store || app.store;
						
						// 清除Vuex store
						if (store) {
							store.commit('CLEAR_CONNECTED_DEVICE');
						}
						
						// 清除全局状态
						app.globalData.connectionMethod = '';
						app.globalData.connectedDeviceId = '';
						app.globalData.deviceStatus = false;
						
						// 记录清除操作
						connectionStateValidator.logConnectionChange('MANUAL_STATE_CLEAR', 'none', {
							reason: 'USER_REQUESTED'
						});
						
						this.refreshStatus();
						this.refreshLogs();
						
						uni.showToast({
							title: '状态已清除',
							icon: 'success'
						});
					}
				}
			});
		},

		// 获取连接方式样式类
		getConnectionMethodClass() {
			switch (this.connectionMethod) {
				case 'bluetooth':
					return 'bluetooth';
				case 'wifi':
					return 'wifi';
				default:
					return 'none';
			}
		},

		// 格式化时间
		formatTime(timestamp) {
			return new Date(timestamp).toLocaleTimeString();
		}
	}
};
</script>

<style lang="scss" scoped>
.container {
	padding: 20rpx;
	background-color: #f5f5f5;
	min-height: 100vh;
}

.header {
	text-align: center;
	margin-bottom: 30rpx;
	
	.title {
		font-size: 36rpx;
		font-weight: bold;
		color: #333;
		display: block;
		margin-bottom: 10rpx;
	}
	
	.subtitle {
		font-size: 28rpx;
		color: #666;
		display: block;
	}
}

.section-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 20rpx;
}

.status-section, .validation-section, .test-section, .log-section {
	background: white;
	border-radius: 16rpx;
	padding: 30rpx;
	margin-bottom: 30rpx;
	box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
}

.status-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 20rpx 0;
	border-bottom: 1rpx solid #eee;
	
	&:last-child {
		border-bottom: none;
	}
	
	.label {
		font-size: 28rpx;
		color: #666;
	}
	
	.value {
		font-size: 28rpx;
		font-weight: bold;
		
		&.bluetooth {
			color: #007AFF;
		}
		
		&.wifi {
			color: #34C759;
		}
		
		&.none {
			color: #999;
		}
		
		&.online {
			color: #34C759;
		}
		
		&.offline {
			color: #FF3B30;
		}
	}
}

.btn {
	padding: 20rpx 40rpx;
	border-radius: 12rpx;
	font-size: 28rpx;
	text-align: center;
	margin: 10rpx;
	border: none;
	
	&.btn-primary {
		background-color: #007AFF;
		color: white;
	}
	
	&.btn-secondary {
		background-color: #34C759;
		color: white;
	}
	
	&.btn-test {
		background-color: #FF9500;
		color: white;
	}
	
	&.btn-small {
		padding: 15rpx 30rpx;
		font-size: 24rpx;
		display: inline-block;
		margin: 5rpx;
	}
}

.validation-result {
	margin-top: 20rpx;
	
	.validation-status {
		font-size: 32rpx;
		font-weight: bold;
		margin-bottom: 15rpx;
		
		&.valid {
			color: #34C759;
		}
		
		&.invalid {
			color: #FF3B30;
		}
	}
	
	.issues {
		.issue {
			background: #FFF3CD;
			border: 1rpx solid #FFEAA7;
			border-radius: 8rpx;
			padding: 15rpx;
			margin-bottom: 10rpx;
			
			.issue-type {
				font-weight: bold;
				color: #856404;
				display: block;
				margin-bottom: 5rpx;
			}
			
			.issue-message {
				color: #856404;
				display: block;
				margin-bottom: 5rpx;
			}
			
			.issue-severity {
				font-size: 24rpx;
				padding: 4rpx 8rpx;
				border-radius: 4rpx;
				
				&.high {
					background: #FF3B30;
					color: white;
				}
				
				&.medium {
					background: #FF9500;
					color: white;
				}
				
				&.low {
					background: #34C759;
					color: white;
				}
			}
		}
	}
}

.log-container {
	height: 600rpx;
	border: 1rpx solid #eee;
	border-radius: 8rpx;
	padding: 15rpx;
}

.log-item {
	border-bottom: 1rpx solid #eee;
	padding: 15rpx 0;
	
	&:last-child {
		border-bottom: none;
	}
	
	.log-header {
		display: flex;
		align-items: center;
		margin-bottom: 10rpx;
		
		.log-time {
			font-size: 24rpx;
			color: #999;
			margin-right: 15rpx;
		}
		
		.log-action {
			font-size: 26rpx;
			font-weight: bold;
			color: #333;
			margin-right: 15rpx;
		}
		
		.log-type {
			font-size: 24rpx;
			padding: 4rpx 8rpx;
			border-radius: 4rpx;
			
			&.bluetooth {
				background: #007AFF;
				color: white;
			}
			
			&.wifi {
				background: #34C759;
				color: white;
			}
			
			&.unknown {
				background: #999;
				color: white;
			}
		}
	}
	
	.log-details {
		font-size: 24rpx;
		color: #666;
		background: #f8f8f8;
		padding: 10rpx;
		border-radius: 4rpx;
		margin-bottom: 10rpx;
		white-space: pre-wrap;
	}
	
	.log-validation {
		font-size: 24rpx;
		color: #FF9500;
		font-weight: bold;
	}
}
</style>
