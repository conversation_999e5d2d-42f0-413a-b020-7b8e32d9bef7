<template>
	<view class="container">
		<!-- 订单状态选项卡 -->
		<view class="order-tabs">
			<view v-for="(tab, index) in tabs" :key="index" class="tab-item" :class="{ active: currentTab === index }"
				@click="switchTab(index)">
				<text>{{ tab }}</text>
				<view v-if="currentTab === index" class="active-line"></view>
			</view>
		</view>

		<!-- 加载状态 -->
		<view v-if="loading" class="loading-state">
			<text>加载中...</text>
		</view>

		<!-- 订单列表 -->
		<view v-else-if="orderList.length > 0" class="order-list">
			<view v-for="order in orderList" :key="order.id" class="order-item" @click="goToOrderDetail(order.id)">
				<!-- 订单头部 -->
				<view class="order-header">
					<text class="order-no">订单号：{{ order.orderNo }}</text>
					<text class="order-status">{{ getStatusText(order.status) }}</text>
				</view>

				<!-- 商品列表 -->
				<view class="product-list">
					<view v-for="item in order.items" :key="item.id" class="product-item">
						<image class="product-image" :src="baseUrl + item.productImage" mode="aspectFill"></image>
						<view class="product-info">
							<text class="product-name">{{ item.productName }}</text>
							<text class="product-spec">{{ item.productSpec }}</text>
							<view class="product-price-qty">
								<text class="product-price">¥{{ item.price }}</text>
								<text class="product-qty">x{{ item.quantity }}</text>
							</view>
						</view>
					</view>
				</view>

				<!-- 订单底部 -->
				<view class="order-footer">
					<text class="total-price">共{{ order.totalQuantity }}件商品 合计：¥{{ order.totalAmount }}</text>
					<view class="order-actions">
						<view v-if="order.status === 1" class="action-btn primary"
							@click.stop="payOrder(order.orderNo)">
							<text>立即付款</text>
						</view>
						<view v-if="order.status === 1" class="action-btn" @click.stop="cancelOrder(order.orderNo)">
							<text>取消订单</text>
						</view>
						<view v-if="order.status === 3" class="action-btn primary"
							@click.stop="confirmReceive(order.orderNo)">
							<text>确认收货</text>
						</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 空状态提示 -->
		<view v-else class="empty-state">
			<image class="empty-icon" src="/static/images/empty-order.png" mode="aspectFit"></image>
			<text class="empty-text">暂无相关订单</text>
			<view class="go-shopping-btn" @click="goShopping">
				<text>去逛逛</text>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		apiService
	} from '@/utils/request.js';

	export default {
		data() {
			return {
				// baseUrl : "http://127.0.0.1:6549/api",
				baseUrl : "https://www.narenqiqige.com/api",
				
				currentTab: 0,
				tabs: ['全部', '待付款', '待发货', '待收货', '已完成'],
				orderList: [],
				loading: false,
				page: 1,
				pageSize: 10,
				hasMore: true
			};
		},
		onLoad() {
			this.loadOrders();
		},
		onShow() {
			// 从其他页面返回时刷新订单列表
			this.loadOrders();
		},
		methods: {
			async switchTab(index) {
				this.currentTab = index;
				this.page = 1;
				this.orderList = [];
				this.hasMore = true;
				await this.loadOrders();
			},

			goShopping() {
				uni.switchTab({
					url: '/pages/mall/mall'
				});
			},

			// 获取订单列表
			async loadOrders() {
				if (this.loading || !this.hasMore) return;

				this.loading = true;

				try {
					// 根据选项卡确定状态参数
					let status = null;
					if (this.currentTab === 1) status = 1; // 待付款
					else if (this.currentTab === 2) status = 2; // 待发货
					else if (this.currentTab === 3) status = 3; // 待收货
					else if (this.currentTab === 4) status = 4; // 已完成

					const params = {
						page: this.page,
						pageSize: this.pageSize
					};

					if (status !== null) {
						params.status = status;
					}

					const response = await apiService.mall.orders.list(params);

					if (response.code === 200) {
						const orders = response.data.records || [];

						// 处理订单数据，获取订单项
						for (let order of orders) {
							try {
								const itemsResponse = await apiService.mall.orders.items(order.id);
								if (itemsResponse.code === 200) {
									order.items = itemsResponse.data || [];
									order.totalQuantity = order.items.reduce((sum, item) => sum + item.quantity, 0);
								} else {
									order.items = [];
									order.totalQuantity = 0;
								}
							} catch (error) {
								console.error('获取订单项失败:', error);
								order.items = [];
								order.totalQuantity = 0;
							}
						}

						if (this.page === 1) {
							this.orderList = orders;
						} else {
							this.orderList = [...this.orderList, ...orders];
						}

						this.hasMore = orders.length === this.pageSize;
						this.page++;
					} else {
						uni.showToast({
							title: response.message || '获取订单列表失败',
							icon: 'none'
						});
					}
				} catch (error) {
					console.error('获取订单列表失败:', error);
					uni.showToast({
						title: '网络错误，请重试',
						icon: 'none'
					});
				} finally {
					this.loading = false;
				}
			},

			// 获取状态文本
			getStatusText(status) {
				const statusMap = {
					1: '待付款',
					2: '待发货',
					3: '待收货',
					4: '已完成',
					5: '已取消'
				};
				return statusMap[status] || '未知状态';
			},

			// 跳转到订单详情
			goToOrderDetail(orderId) {
				uni.navigateTo({
					url: `/pages/my/order-detail?id=${orderId}`
				});
			},

			// 支付订单
			async payOrder(orderNo) {
				console.log("支付的单号",orderNo)
				try {
					console.log("付款中....")
					// 这里应该调用支付接口，暂时显示提示
					uni.showToast({
					  title: '跳转支付页面',
					  icon: 'none'
					});
					// TODO: 实现支付逻辑
					console.log("订单编号")
					apiService.mall.orders.pay(orderNo, {
							paymentMethod: 1
						})
						.then(res => {
							// console.log(res.data);
							// console.log(res.data.paySign);
							wx.requestPayment({
								timeStamp: res.data.timeStamp,
								nonceStr: res.data.nonceStr,
								package: res.data.package,
								signType: 'MD5',
								paySign: res.data.paySign,
								success: function(e) {
									console.log(e); // 查看完整返回对象

									// 支付成功处理
									uni.showToast({
										title: '支付成功',
										icon: 'success'
									});

									// 可以跳转到支付成功页面或订单详情页
									setTimeout(() => {
										// 跳转到订单详情页
										uni.redirectTo({
											url: `/pages/my/order-detail`
										});
									}, 1500);
								},
								fail: function(e) {
									console.log("支付失败", e);
									// 支付成功提示
									// uni.showToast({
									// 	title: '支付失败',
									// 	icon: 'none',
									// 	duration: 1500,
									// });

									// setTimeout(() => {
									// 	uni.switchTab({
									// 		url: '/pages/mall/mall'
									// 	});
									// }, 1500)
								}
							})
						}).catch(err => {
							console.error('支付失败:', err);
							uni.showToast({
								title: '支付失败',
								icon: 'none'
							});
						})
			} catch (error) {
				console.error('支付失败:', error);
				uni.showToast({
					title: '支付失败',
					icon: 'none'
				});
			}
		},

		// 取消订单
		async cancelOrder(orderNo) {
			try {
				uni.showModal({
					title: '确认取消',
					content: '确定要取消这个订单吗？',
					success: async (res) => {
						if (res.confirm) {
							const response = await apiService.mall.orders.cancel(orderNo);
							if (response.code === 200) {
								uni.showToast({
									title: '订单已取消',
									icon: 'success'
								});
								// 重置分页并刷新列表
								this.page = 1;
								this.orderList = [];
								this.hasMore = true;
								await this.loadOrders();
							} else {
								uni.showToast({
									title: response.message || '取消失败',
									icon: 'none'
								});
							}
						}
					}
				});
			} catch (error) {
				console.error('取消订单失败:', error);
				uni.showToast({
					title: '取消失败',
					icon: 'none'
				});
			}
		},

		// 确认收货
		async confirmReceive(orderNo) {
			try {
				uni.showModal({
					title: '确认收货',
					content: '确定已收到商品吗？',
					success: async (res) => {
						if (res.confirm) {
							const response = await apiService.mall.orders.confirm(orderNo);
							if (response.code === 200) {
								uni.showToast({
									title: '确认收货成功',
									icon: 'success'
								});
								// 重置分页并刷新列表
								this.page = 1;
								this.orderList = [];
								this.hasMore = true;
								await this.loadOrders();
							} else {
								uni.showToast({
									title: response.message || '确认收货失败',
									icon: 'none'
								});
							}
						}
					}
				});
			} catch (error) {
				console.error('确认收货失败:', error);
				uni.showToast({
					title: '确认收货失败',
					icon: 'none'
				});
			}
		}
	},

	// 下拉刷新
	onPullDownRefresh() {
			this.page = 1;
			this.orderList = [];
			this.hasMore = true;
			this.loadOrders().then(() => {
				uni.stopPullDownRefresh();
			});
		},

		// 上拉加载更多
		onReachBottom() {
			this.loadOrders();
		}
	};
</script>

<style lang="scss">
	.container {
		display: flex;
		flex-direction: column;
		min-height: 100vh;
		background-color: #f5f5f5;
	}

	.order-tabs {
		display: flex;
		background-color: #fff;
		padding: 0 20rpx;
		border-bottom: 1px solid #eee;
	}

	.tab-item {
		flex: 1;
		height: 80rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		position: relative;
		font-size: 28rpx;
		color: #666;
	}

	.tab-item.active {
		color: #4285f4;
		font-weight: 500;
	}

	.active-line {
		position: absolute;
		bottom: 0;
		width: 40rpx;
		height: 4rpx;
		background-color: #4285f4;
		border-radius: 2rpx;
	}

	.empty-state {
		flex: 1;
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		padding: 50rpx;
	}

	.empty-icon {
		width: 200rpx;
		height: 200rpx;
		margin-bottom: 30rpx;
	}

	.empty-text {
		font-size: 30rpx;
		color: #999;
		margin-bottom: 40rpx;
	}

	.go-shopping-btn {
		width: 240rpx;
		height: 80rpx;
		background-color: #4285f4;
		color: #fff;
		border-radius: 40rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		font-size: 30rpx;
	}

	.loading-state {
		flex: 1;
		display: flex;
		justify-content: center;
		align-items: center;
		font-size: 28rpx;
		color: #999;
	}

	.order-list {
		flex: 1;
		padding: 20rpx;
	}

	.order-item {
		background-color: #fff;
		border-radius: 12rpx;
		margin-bottom: 20rpx;
		overflow: hidden;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
	}

	.order-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 20rpx;
		border-bottom: 1px solid #f0f0f0;
	}

	.order-no {
		font-size: 28rpx;
		color: #333;
	}

	.order-status {
		font-size: 28rpx;
		color: #4285f4;
		font-weight: 500;
	}

	.product-list {
		padding: 20rpx;
	}

	.product-item {
		display: flex;
		margin-bottom: 20rpx;
	}

	.product-item:last-child {
		margin-bottom: 0;
	}

	.product-image {
		width: 120rpx;
		height: 120rpx;
		border-radius: 8rpx;
		margin-right: 20rpx;
	}

	.product-info {
		flex: 1;
		display: flex;
		flex-direction: column;
		justify-content: space-between;
	}

	.product-name {
		font-size: 28rpx;
		color: #333;
		line-height: 1.4;
		margin-bottom: 10rpx;
	}

	.product-spec {
		font-size: 24rpx;
		color: #999;
		margin-bottom: 10rpx;
	}

	.product-price-qty {
		display: flex;
		justify-content: space-between;
		align-items: center;
	}

	.product-price {
		font-size: 28rpx;
		color: #ff4757;
		font-weight: 500;
	}

	.product-qty {
		font-size: 24rpx;
		color: #999;
	}

	.order-footer {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 20rpx;
		border-top: 1px solid #f0f0f0;
		background-color: #fafafa;
	}

	.total-price {
		font-size: 28rpx;
		color: #333;
	}

	.order-actions {
		display: flex;
		gap: 20rpx;
	}

	.action-btn {
		padding: 12rpx 24rpx;
		border-radius: 20rpx;
		font-size: 26rpx;
		border: 1px solid #ddd;
		color: #666;
		background-color: #fff;
	}

	.action-btn.primary {
		background-color: #4285f4;
		color: #fff;
		border-color: #4285f4;
	}
</style>