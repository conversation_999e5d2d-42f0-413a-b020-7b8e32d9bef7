"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      deviceStatus: false,
      connectedDeviceId: "",
      logs: []
    };
  },
  onLoad() {
    this.refreshStatus();
    this.addLog("页面加载完成");
  },
  onShow() {
    this.refreshStatus();
    this.addLog("页面显示");
  },
  methods: {
    // 刷新状态
    refreshStatus() {
      const app = getApp();
      this.deviceStatus = app.globalData.deviceStatus;
      this.connectedDeviceId = app.globalData.connectedDeviceId;
      this.addLog(`状态刷新 - 设备状态: ${this.deviceStatus}, 设备ID: ${this.connectedDeviceId}`);
    },
    // 模拟连接
    simulateConnection() {
      const mockDevice = {
        deviceId: "BABAIYI-PSY000004",
        name: "BABAIYI-PSY000004",
        connectionType: "wifi"
      };
      this.$store.commit("SET_CONNECTED_DEVICE", mockDevice);
      const app = getApp();
      app.globalData.connectedDeviceId = mockDevice.deviceId;
      app.globalData.deviceStatus = true;
      app.globalData.connectionMethod = "wifi";
      this.refreshStatus();
      this.addLog("模拟连接成功");
    },
    // 清除连接
    clearConnection() {
      this.$store.commit("CLEAR_CONNECTED_DEVICE");
      const app = getApp();
      app.globalData.connectedDeviceId = "";
      app.globalData.deviceStatus = false;
      app.globalData.connectionMethod = "";
      this.refreshStatus();
      this.addLog("连接已清除");
    },
    // 测试持久化
    testPersistence() {
      try {
        const persistedState = common_vendor.index.getStorageSync("vuex_store_state");
        this.addLog(`持久化状态: ${JSON.stringify(persistedState)}`);
      } catch (error) {
        this.addLog(`读取持久化状态失败: ${error.message}`);
      }
    },
    // 添加日志
    addLog(message) {
      const timestamp = (/* @__PURE__ */ new Date()).toLocaleTimeString();
      this.logs.unshift(`[${timestamp}] ${message}`);
      if (this.logs.length > 20) {
        this.logs.pop();
      }
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.t(_ctx.$store.state.connectedDevice ? "已连接" : "未连接"),
    b: _ctx.$store.state.connectedDevice ? 1 : "",
    c: !_ctx.$store.state.connectedDevice ? 1 : "",
    d: common_vendor.t(_ctx.$store.state.connectedDevice ? _ctx.$store.state.connectedDevice.deviceId : "无"),
    e: common_vendor.t(_ctx.$store.state.connectedDevice ? _ctx.$store.state.connectedDevice.connectionType : "无"),
    f: common_vendor.t($data.deviceStatus ? "在线" : "离线"),
    g: $data.deviceStatus ? 1 : "",
    h: !$data.deviceStatus ? 1 : "",
    i: common_vendor.t($data.connectedDeviceId || "无"),
    j: common_vendor.o((...args) => $options.refreshStatus && $options.refreshStatus(...args)),
    k: common_vendor.o((...args) => $options.simulateConnection && $options.simulateConnection(...args)),
    l: common_vendor.o((...args) => $options.clearConnection && $options.clearConnection(...args)),
    m: common_vendor.o((...args) => $options.testPersistence && $options.testPersistence(...args)),
    n: common_vendor.o((...args) => _ctx.testReconnect && _ctx.testReconnect(...args)),
    o: common_vendor.o((...args) => _ctx.simulateRefresh && _ctx.simulateRefresh(...args)),
    p: common_vendor.f($data.logs, (log, index, i0) => {
      return {
        a: common_vendor.t(log),
        b: index
      };
    })
  };
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-7ecc3ead"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/test/connection-state-test.js.map
