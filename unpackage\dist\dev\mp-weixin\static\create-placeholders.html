<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>生成占位图片</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .container {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }
        canvas {
            border: 1px solid #ccc;
            margin-bottom: 10px;
        }
        .btn-group {
            display: flex;
            gap: 10px;
            margin-top: 10px;
        }
        button {
            padding: 8px 16px;
            background: #4285f4;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        input, select {
            padding: 8px;
        }
    </style>
</head>
<body>
    <h1>索瑞达智能染发机 - 占位图片生成器</h1>
    <p>使用此工具可以生成各种尺寸和颜色的占位图片</p>
    
    <div class="container">
        <div>
            <label>宽度 (px):</label>
            <input type="number" id="width" value="100">
        </div>
        <div>
            <label>高度 (px):</label>
            <input type="number" id="height" value="100">
        </div>
        <div>
            <label>背景颜色:</label>
            <input type="color" id="bgColor" value="#4285f4">
        </div>
        <div>
            <label>文字颜色:</label>
            <input type="color" id="textColor" value="#ffffff">
        </div>
        <div>
            <label>文字内容:</label>
            <input type="text" id="text" value="占位图">
        </div>
        <div>
            <label>图片类型:</label>
            <select id="imageType">
                <option value="logo">Logo</option>
                <option value="icon">图标</option>
                <option value="banner">Banner</option>
                <option value="product">产品图</option>
                <option value="avatar">头像</option>
            </select>
        </div>
    </div>
    
    <div class="btn-group">
        <button id="generate">生成图片</button>
        <button id="download">下载图片</button>
    </div>
    
    <h2>预览</h2>
    <canvas id="canvas"></canvas>
    
    <h2>常用尺寸</h2>
    <ul>
        <li>Logo: 60x60</li>
        <li>底部导航图标: 40x40</li>
        <li>色彩分类图: 200x150</li>
        <li>产品图: 200x200</li>
        <li>列表图标: 80x80</li>
        <li>头像: 120x120</li>
    </ul>

    <h2>批量生成</h2>
    <div class="container">
        <p>点击下面按钮自动生成应用需要的所有图片</p>
        <button id="generateAll">批量生成所有图片</button>
        <p id="status"></p>
    </div>
    
    <script>
        const canvas = document.getElementById('canvas');
        const ctx = canvas.getContext('2d');
        const widthInput = document.getElementById('width');
        const heightInput = document.getElementById('height');
        const bgColorInput = document.getElementById('bgColor');
        const textColorInput = document.getElementById('textColor');
        const textInput = document.getElementById('text');
        const generateBtn = document.getElementById('generate');
        const downloadBtn = document.getElementById('download');
        const generateAllBtn = document.getElementById('generateAll');
        const statusEl = document.getElementById('status');
        
        // 生成图片的函数
        function generateImage() {
            const width = parseInt(widthInput.value);
            const height = parseInt(heightInput.value);
            const bgColor = bgColorInput.value;
            const textColor = textColorInput.value;
            const text = textInput.value;
            
            canvas.width = width;
            canvas.height = height;
            
            // 绘制背景
            ctx.fillStyle = bgColor;
            ctx.fillRect(0, 0, width, height);
            
            // 绘制文字
            ctx.fillStyle = textColor;
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            
            // 根据图片尺寸调整字体大小
            const fontSize = Math.max(12, Math.min(30, Math.floor(width / 10)));
            ctx.font = `${fontSize}px Arial`;
            
            // 如果文字太长，分行显示
            const words = text.split(' ');
            if (words.length > 1 && width < 150) {
                const lineHeight = fontSize * 1.2;
                const y = height / 2 - lineHeight / 2;
                
                words.forEach((word, i) => {
                    ctx.fillText(word, width / 2, y + i * lineHeight);
                });
            } else {
                ctx.fillText(text, width / 2, height / 2);
            }
            
            // 显示尺寸信息
            ctx.font = `${Math.max(10, fontSize / 2)}px Arial`;
            ctx.fillText(`${width}x${height}`, width / 2, height - 10);
        }
        
        // 下载图片
        function downloadImage() {
            const imageType = document.getElementById('imageType').value;
            let fileName = `${imageType}_${widthInput.value}x${heightInput.value}.png`;
            
            const dataURL = canvas.toDataURL('image/png');
            const link = document.createElement('a');
            link.download = fileName;
            link.href = dataURL;
            link.click();
        }
        
        // 批量生成所有需要的图片
        function generateAllImages() {
            statusEl.textContent = "正在生成图片...";
            
            const images = [
                // 底部导航栏图标
                { name: 'home', width: 40, height: 40, color: '#999999', text: '首页' },
                { name: 'home_selected', width: 40, height: 40, color: '#4285f4', text: '首页' },
                { name: 'palette', width: 40, height: 40, color: '#999999', text: '色板' },
                { name: 'palette_selected', width: 40, height: 40, color: '#4285f4', text: '色板' },
                { name: 'device', width: 40, height: 40, color: '#999999', text: '设备' },
                { name: 'device_selected', width: 40, height: 40, color: '#4285f4', text: '设备' },
                { name: 'user', width: 40, height: 40, color: '#999999', text: '我的' },
                { name: 'user_selected', width: 40, height: 40, color: '#4285f4', text: '我的' },
                
                // Logo
                { name: 'soruda-logo', width: 60, height: 60, color: '#4285f4', text: 'Logo' },
                
                // 颜色分类图片
                { name: 'color1', width: 200, height: 150, color: '#5D4037', text: '生活色' },
                { name: 'color2', width: 200, height: 150, color: '#00BCD4', text: '微潮色' },
                { name: 'color3', width: 200, height: 150, color: '#FFEB3B', text: '潮色' },
                { name: 'color4', width: 200, height: 150, color: '#9E9E9E', text: '深染浅' },
                { name: 'color5', width: 200, height: 150, color: '#9C27B0', text: '浅染深' },
                
                // 图标
                { name: 'filter', width: 32, height: 32, color: '#757575', text: '筛选' },
                { name: 'location', width: 30, height: 30, color: '#4285f4', text: '位置' },
                { name: 'bluetooth', width: 60, height: 60, color: '#757575', text: '蓝牙' },
                { name: 'wifi', width: 60, height: 60, color: '#4285f4', text: 'WiFi' },
                
                // 我的页面图标
                { name: 'default-avatar', width: 120, height: 120, color: '#E0E0E0', text: '头像' },
                { name: 'store', width: 60, height: 60, color: '#4285f4', text: '门店' },
                { name: 'qrcode', width: 60, height: 60, color: '#4285f4', text: '二维码' },
                { name: 'color-test', width: 60, height: 60, color: '#4CAF50', text: '色膏' },
                { name: 'color-record', width: 60, height: 60, color: '#2196F3', text: '记录' },
                { name: 'statistics', width: 60, height: 60, color: '#673AB7', text: '统计' },
                { name: 'order', width: 60, height: 60, color: '#FF9800', text: '订单' },
                { name: 'guide-banner', width: 80, height: 80, color: '#FF9800', text: '指引' },
                { name: 'repair', width: 50, height: 50, color: '#F44336', text: '报修' },
                { name: 'policy', width: 50, height: 50, color: '#3F51B5', text: '政策' },
                { name: 'notice', width: 50, height: 50, color: '#FFC107', text: '消息' },
                { name: 'about', width: 50, height: 50, color: '#607D8B', text: '关于' },
                { name: 'manual', width: 50, height: 50, color: '#795548', text: '手册' },
                { name: 'download', width: 50, height: 50, color: '#009688', text: '下载' },
                { name: 'guide', width: 50, height: 50, color: '#FF5722', text: '引导' },
                { name: 'cooperation', width: 50, height: 50, color: '#8BC34A', text: '合作' },
                { name: 'customer-service', width: 60, height: 60, color: '#4285f4', text: '客服' },
                
                // 商城图标
                { name: 'scan', width: 50, height: 50, color: '#FFFFFF', text: '扫描' },
                
                // 服务类别图标
                { name: 'service1', width: 80, height: 80, color: '#E91E63', text: '洗发水' },
                { name: 'service2', width: 80, height: 80, color: '#9C27B0', text: '护理' },
                { name: 'service3', width: 80, height: 80, color: '#673AB7', text: '护肤' },
                { name: 'service4', width: 80, height: 80, color: '#3F51B5', text: '化妆' },
                { name: 'service5', width: 80, height: 80, color: '#2196F3', text: '美容' },
                { name: 'service6', width: 80, height: 80, color: '#03A9F4', text: '护发素' },
                { name: 'service7', width: 80, height: 80, color: '#00BCD4', text: '发膜' },
                { name: 'service8', width: 80, height: 80, color: '#009688', text: '精油' },
                { name: 'service9', width: 80, height: 80, color: '#4CAF50', text: '造型' },
                { name: 'service10', width: 80, height: 80, color: '#8BC34A', text: '药水' },
                { name: 'service11', width: 80, height: 80, color: '#CDDC39', text: '烫发' },
                { name: 'service12', width: 80, height: 80, color: '#FFEB3B', text: '染发剂' },
                { name: 'service13', width: 80, height: 80, color: '#FFC107', text: '染发' },
                { name: 'service14', width: 80, height: 80, color: '#FF9800', text: '课程' },
                { name: 'service15', width: 80, height: 80, color: '#FF5722', text: '其它' },
                
                // 产品图片
                { name: 'product1', width: 200, height: 200, color: '#42A5F5', text: '产品1' },
                { name: 'product2', width: 200, height: 200, color: '#66BB6A', text: '产品2' }
            ];
            
            // 生成提示信息
            let downloadDiv = document.createElement('div');
            downloadDiv.innerHTML = '<p>图片生成完成！请点击以下链接下载：</p>';
            document.body.appendChild(downloadDiv);
            
            // 逐个生成并提供下载链接
            images.forEach(img => {
                widthInput.value = img.width;
                heightInput.value = img.height;
                bgColorInput.value = img.color;
                textInput.value = img.text;
                
                generateImage();
                
                const dataURL = canvas.toDataURL('image/png');
                const link = document.createElement('a');
                link.href = dataURL;
                link.download = `${img.name}.png`;
                link.textContent = `下载 ${img.name}.png (${img.width}x${img.height})`;
                link.style.display = 'block';
                link.style.margin = '5px 0';
                
                downloadDiv.appendChild(link);
            });
            
            statusEl.textContent = "所有图片生成完成，请点击链接逐个下载或右键保存各图片";
        }
        
        // 事件监听
        generateBtn.addEventListener('click', generateImage);
        downloadBtn.addEventListener('click', downloadImage);
        generateAllBtn.addEventListener('click', generateAllImages);
        
        // 初始生成一个图片
        generateImage();
    </script>
</body>
</html> 