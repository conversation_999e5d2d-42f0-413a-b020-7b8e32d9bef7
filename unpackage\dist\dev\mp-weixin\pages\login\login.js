"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_auth = require("../../utils/auth.js");
const utils_request = require("../../utils/request.js");
const common_assets = require("../../common/assets.js");
const _sfc_main = {
  data() {
    return {
      currentTab: "login",
      // 当前选项卡：login 或 register
      phone: "",
      password: "",
      registerPhone: "",
      registerPassword: "",
      confirmPassword: "",
      verifyCode: "",
      countdown: 0,
      // 验证码倒计时
      agreedToTerms: true,
      // 默认勾选协议
      redirect: "",
      isLoggedIn: false
    };
  },
  onLoad(options) {
    if (options.redirect) {
      this.redirect = options.redirect;
    }
  },
  methods: {
    // 切换选项卡
    switchTab(tab) {
      this.currentTab = tab;
    },
    // 发送验证码
    async sendVerifyCode() {
      if (!this.registerPhone) {
        common_vendor.index.showToast({
          title: "请输入手机号",
          icon: "none"
        });
        return;
      }
      if (!/^1[3-9]\d{9}$/.test(this.registerPhone)) {
        common_vendor.index.showToast({
          title: "请输入正确的手机号",
          icon: "none"
        });
        return;
      }
      try {
        common_vendor.index.showLoading({
          title: "发送中..."
        });
        const result = await utils_request.apiService.user.sendVerifyCode(this.registerPhone);
        common_vendor.index.hideLoading();
        if (result.code === 200) {
          common_vendor.index.showToast({
            title: "验证码已发送",
            icon: "success"
          });
          this.countdown = 60;
          const timer = setInterval(() => {
            this.countdown--;
            if (this.countdown <= 0) {
              clearInterval(timer);
            }
          }, 1e3);
        } else {
          common_vendor.index.showToast({
            title: result.message || "发送失败",
            icon: "none"
          });
        }
      } catch (error) {
        common_vendor.index.hideLoading();
        common_vendor.index.__f__("error", "at pages/login/login.vue:179", "发送验证码失败:", error);
        let errorMessage = "发送失败，请检查网络";
        if (error && error.message) {
          errorMessage = error.message;
        } else if (error && error.data && error.data.message) {
          errorMessage = error.data.message;
        } else if (typeof error === "string") {
          errorMessage = error;
        }
        common_vendor.index.showToast({
          title: errorMessage,
          icon: "none"
        });
      }
    },
    // 处理注册
    async handleRegister() {
      if (!this.agreedToTerms) {
        common_vendor.index.showToast({
          title: "请同意用户协议和隐私政策",
          icon: "none"
        });
        return;
      }
      if (!this.registerPhone) {
        common_vendor.index.showToast({
          title: "请输入手机号",
          icon: "none"
        });
        return;
      }
      if (!/^1[3-9]\d{9}$/.test(this.registerPhone)) {
        common_vendor.index.showToast({
          title: "请输入正确的手机号",
          icon: "none"
        });
        return;
      }
      if (!this.registerPassword) {
        common_vendor.index.showToast({
          title: "请设置密码",
          icon: "none"
        });
        return;
      }
      if (this.registerPassword.length < 6) {
        common_vendor.index.showToast({
          title: "密码至少6位",
          icon: "none"
        });
        return;
      }
      if (this.registerPassword !== this.confirmPassword) {
        common_vendor.index.showToast({
          title: "两次密码不一致",
          icon: "none"
        });
        return;
      }
      try {
        common_vendor.index.showLoading({
          title: "注册中..."
        });
        const result = await utils_request.apiService.user.register({
          phone: this.registerPhone,
          password: this.registerPassword,
          verifyCode: this.verifyCode,
          userType: 1
          // 普通用户
        });
        common_vendor.index.hideLoading();
        if (result.code === 200) {
          common_vendor.index.showToast({
            title: "注册成功",
            icon: "success"
          });
          setTimeout(() => {
            this.currentTab = "login";
            this.phone = this.registerPhone;
            this.password = this.registerPassword;
          }, 1e3);
        } else {
          common_vendor.index.showToast({
            title: result.message || "注册失败",
            icon: "none"
          });
        }
      } catch (error) {
        common_vendor.index.hideLoading();
        common_vendor.index.__f__("error", "at pages/login/login.vue:294", "注册失败:", error);
        let errorMessage = "注册失败，请检查网络连接";
        if (error && error.message) {
          errorMessage = error.message;
        } else if (error && error.data && error.data.message) {
          errorMessage = error.data.message;
        } else if (typeof error === "string") {
          errorMessage = error;
        }
        common_vendor.index.showToast({
          title: errorMessage,
          icon: "none"
        });
      }
    },
    //获取手机号加密数据（自动触发）
    handleGetPhoneNumber(e) {
      if (e.detail.errMsg === "getPhoneNumber:fail user deny") {
        common_vendor.wx$1.showToast({
          title: "需授权手机号才能登录",
          icon: "none"
        });
        return;
      }
      if (e.detail.code) {
        this.handleWechatLogin(e);
      } else {
        common_vendor.wx$1.showToast({
          title: "获取手机号失败",
          icon: "none"
        });
      }
    },
    async handleLogin() {
      if (!this.agreedToTerms) {
        common_vendor.index.showToast({
          title: "请同意用户协议和隐私政策",
          icon: "none"
        });
        return;
      }
      if (!this.phone) {
        common_vendor.index.showToast({
          title: "请输入手机号",
          icon: "none"
        });
        return;
      }
      if (!this.password) {
        common_vendor.index.showToast({
          title: "请输入密码",
          icon: "none"
        });
        return;
      }
      common_vendor.index.showLoading({
        title: "登录中..."
      });
      try {
        const result = await utils_request.apiService.user.login({
          username: this.phone,
          password: this.password
        });
        common_vendor.index.hideLoading();
        if (result.code === 200) {
          common_vendor.index.setStorageSync("token", result.data.token.token);
          common_vendor.index.setStorageSync("isLoggedIn", true);
          common_vendor.index.setStorageSync("refreshToken", result.data.token.refreshToken);
          common_vendor.index.__f__("log", "at pages/login/login.vue:380", "长token", result.data.token.refreshToken);
          common_vendor.index.setStorageSync("userInfo", {
            id: result.data.userInfo.id,
            phone: result.data.userInfo.phone,
            nickname: result.data.userInfo.nickname,
            avatar: result.data.userInfo.avatar,
            username: result.data.userInfo.username
          });
          common_vendor.index.showToast({
            title: "登录成功",
            icon: "success"
          });
          setTimeout(() => {
            common_vendor.index.switchTab({
              url: "/pages/index/index"
            });
          }, 1e3);
        } else {
          common_vendor.index.showToast({
            title: result.message || "登录失败",
            icon: "none"
          });
        }
      } catch (error) {
        common_vendor.index.hideLoading();
        common_vendor.index.__f__("error", "at pages/login/login.vue:408", "登录失败:", error);
        let errorMessage = "登录失败，请检查网络连接";
        if (error && error.message) {
          errorMessage = error.message;
        } else if (error && error.data && error.data.message) {
          errorMessage = error.data.message;
        } else if (typeof error === "string") {
          errorMessage = error;
        }
        common_vendor.index.showToast({
          title: errorMessage,
          icon: "none"
        });
      } finally {
        common_vendor.index.hideLoading();
      }
    },
    async handleWechatLogin(e) {
      if (!this.agreedToTerms) {
        common_vendor.index.showToast({
          title: "请同意用户协议和隐私政策",
          mask: true,
          // 是否显示透明蒙层（防止触摸穿透）
          icon: "none"
        });
        return;
      }
      try {
        common_vendor.index.showLoading({
          title: "授权登录中...",
          mask: true
          // 是否显示透明蒙层（防止触摸穿透）
        });
        const code = await utils_auth.wxLogin();
        common_vendor.index.__f__("log", "at pages/login/login.vue:448", "Code:", code);
        const result = await utils_request.apiService.user.wxlogin({
          code,
          encryptedData: e.detail.encryptedData,
          iv: e.detail.iv
        });
        common_vendor.index.__f__("log", "at pages/login/login.vue:457", "登录结果:", result);
        setTimeout(() => {
          common_vendor.index.hideLoading();
        }, 1e3);
        common_vendor.index.setStorageSync("token", result.data.token.token);
        common_vendor.index.setStorageSync("refreshToken", result.data.token.refreshToken);
        common_vendor.index.setStorageSync("isLoggedIn", true);
        common_vendor.index.setStorageSync("userInfo", {
          id: result.data.userInfo.id,
          phone: result.data.userInfo.phone,
          nickname: result.data.userInfo.nickname,
          avatar: result.data.userInfo.avatar,
          username: result.data.userInfo.username
        });
        this.isLoggedIn = true;
        common_vendor.index.switchTab({
          url: "/pages/index/index"
        });
        common_vendor.index.showToast({
          title: "登录成功",
          icon: "success",
          duration: 500
        });
      } catch (err) {
        common_vendor.index.showToast({
          title: "登录失败请重试",
          icon: "none",
          duration: 500
        });
      } finally {
        common_vendor.index.hideLoading();
      }
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_assets._imports_0$3,
    b: $data.currentTab === "login" ? 1 : "",
    c: common_vendor.o(($event) => $options.switchTab("login")),
    d: $data.currentTab === "register" ? 1 : "",
    e: common_vendor.o(($event) => $options.switchTab("register")),
    f: $data.currentTab === "login"
  }, $data.currentTab === "login" ? {
    g: $data.phone,
    h: common_vendor.o(($event) => $data.phone = $event.detail.value),
    i: $data.password,
    j: common_vendor.o(($event) => $data.password = $event.detail.value),
    k: $data.agreedToTerms,
    l: common_vendor.o(($event) => $data.agreedToTerms = !$data.agreedToTerms),
    m: common_vendor.o((...args) => $options.handleLogin && $options.handleLogin(...args))
  } : {}, {
    n: $data.currentTab === "register"
  }, $data.currentTab === "register" ? {
    o: $data.registerPhone,
    p: common_vendor.o(($event) => $data.registerPhone = $event.detail.value),
    q: $data.registerPassword,
    r: common_vendor.o(($event) => $data.registerPassword = $event.detail.value),
    s: $data.confirmPassword,
    t: common_vendor.o(($event) => $data.confirmPassword = $event.detail.value),
    v: $data.agreedToTerms,
    w: common_vendor.o(($event) => $data.agreedToTerms = !$data.agreedToTerms),
    x: common_vendor.o((...args) => $options.handleRegister && $options.handleRegister(...args))
  } : {}, {
    y: common_assets._imports_1$1,
    z: common_vendor.o((...args) => $options.handleGetPhoneNumber && $options.handleGetPhoneNumber(...args)),
    A: common_assets._imports_2,
    B: common_vendor.o((...args) => $options.handleWechatLogin && $options.handleWechatLogin(...args))
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/login/login.js.map
