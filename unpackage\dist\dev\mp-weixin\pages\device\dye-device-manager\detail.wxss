
.container.data-v-f83b36bf {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f5f5;
}
.content.data-v-f83b36bf {
  flex: 1;
  padding: 15px;
}
.card.data-v-f83b36bf {
  background-color: #fff;
  border-radius: 8px;
  padding: 15px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
}
.section.data-v-f83b36bf {
  margin-bottom: 20px;
}
.section-title.data-v-f83b36bf {
  display: block;
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 10px;
  padding-bottom: 5px;
  border-bottom: 1px solid #eee;
}
.info-item.data-v-f83b36bf {
  display: flex;
  margin-bottom: 8px;
}
.label.data-v-f83b36bf {
  width: 80px;
  color: #666;
}
.value.data-v-f83b36bf {
  flex: 1;
}
.staff-list.data-v-f83b36bf {
  margin-top: 10px;
}
.staff-item.data-v-f83b36bf {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0;
  border-bottom: 1px solid #f5f5f5;
}
.staff-info.data-v-f83b36bf {
  display: flex;
  flex-direction: column;
}
.staff-name.data-v-f83b36bf {
  font-size: 14px;
  margin-bottom: 4px;
}
.staff-permission.data-v-f83b36bf {
  font-size: 12px;
  color: #999;
}
.staff-level.data-v-f83b36bf {
  font-size: 12px;
  font-weight: bold;
  padding: 2px 8px;
  border-radius: 10px;
  background-color: rgba(41, 121, 255, 0.1);
}
.empty-tip.data-v-f83b36bf {
  color: #999;
  text-align: center;
  padding: 10px;
}
.footer-actions.data-v-f83b36bf {
  padding: 15px;
  background-color: #fff;
  border-top: 1px solid #eee;
}
