<template>
	<view class="detail-container">
		<!-- 商品详情内容 -->
		<scroll-view scroll-y class="detail-scroll">
			<!-- 商品图片轮播 -->
			<swiper class="detail-swiper" indicator-dots autoplay circular>
				<swiper-item v-for="(img, index) in productImages" :key="index">
					<image :src="img" mode="aspectFill"></image>
				</swiper-item>
			</swiper>

			<!-- 商品标题和描述 -->
			<view class="product-title-section">
				<view class="product-title">{{productDetail.productName || productDetail.name}}</view>
				<view class="product-desc">{{productDetail.description}}</view>
				<view class="product-price-section">
					<text class="current-price">¥{{productDetail.price}}</text>
					<text class="original-price" v-if="productDetail.originalPrice">¥{{productDetail.originalPrice}}</text>
				</view>
				<view class="product-stock">库存：{{productDetail.stock}}件</view>
			</view>
			
			<!-- 商品参数 -->
			<view class="specs-section" v-if="specifications.length > 0">
				<view class="section-title">商品参数</view>
				<view class="specs-list">
					<view class="specs-item" v-for="(spec, index) in specifications" :key="index">
						<text class="specs-label">{{spec.name}}</text>
						<text class="specs-value">{{spec.value}}</text>
					</view>
				</view>
			</view>

			<!-- 商品详情 -->
			<view class="detail-content" v-if="productDetail.detail">
				<view class="section-title">商品详情</view>
				<rich-text :nodes="productDetail.detail"></rich-text>
			</view>

			<!-- 商品推荐 -->
			<view class="recommend-section" v-if="recommendations.length > 0">
				<view class="section-title">相关推荐</view>
				<view class="recommend-list">
					<view class="recommend-item" v-for="(item, index) in recommendations" :key="index" @click="goToProduct(item.id)">
						<image :src="getProductImage(item)" mode="aspectFill"></image>
						<text class="recommend-name">{{item.productName || item.name}}</text>
						<text class="recommend-price">¥{{item.price}}</text>
					</view>
				</view>
			</view>
		</scroll-view>
		
		<!-- 底部操作栏 -->
		<view class="bottom-actions">
			<view class="action-item store" @click="goToStore">
				<view class="action-icon">🏪</view>
				<text class="action-text">店铺</text>
			</view>
			<view class="action-item cart" @click="goToCart">
				<view class="action-icon">🛒</view>
				<text class="action-text">购物车</text>
			</view>
			<view class="action-item fav" @click="handleCollect" :class="{ favorited: isFavorited }">
				<view class="action-icon">{{ isFavorited ? '❤️' : '🤍' }}</view>
				<text class="action-text">{{ isFavorited ? '已收藏' : '收藏' }}</text>
			</view>
			<view class="add-cart-btn" :class="{ 'in-cart': isInCart }" @click="handleCartAction">
				{{ isInCart ? '查看购物车' : '加入购物车' }}
			</view>
			<view class="buy-now-btn" @click="buyNow">立即购买</view>
		</view>
	</view>
</template>

<script>
import { apiService } from '@/utils/request.js';
import { getFullImageUrl } from '@/utils/imageUtils.js';

export default {
	data() {
		return {
			productId: null,
			productDetail: {},
			storeInfo: {},
			recommendations: [],
			loading: false,
			specifications: [],
			isFavorited: false, // 收藏状态
			favoriteLoading: false, // 收藏操作加载状态
			isInCart: false, // 商品是否在购物车中
			cartLoading: false, // 购物车操作加载状态
			isNavigating: false // 是否正在进行页面跳转
		};
	},
	computed: {
		productImages() {
			if (!this.productDetail.images) return [];
			let images = [];
			if (typeof this.productDetail.images === 'string') {
				images = this.productDetail.images.split(',').filter(img => img.trim());
			} else {
				images = this.productDetail.images || [];
			}
			// 使用 getFullImageUrl 处理每个图片URL
			return images.map(img => getFullImageUrl(img));
		}
	},
	onLoad(options) {
		if (options.id) {
			this.productId = options.id;
			this.loadProductDetail();
			this.loadRecommendations();
			this.checkFavoriteStatus();
			this.checkCartStatus();
		}
	},
	onShow() {
		// 页面显示时重新检查购物车状态，确保状态同步
		if (this.productId) {
			// 如果正在跳转，延迟检查状态
			if (this.isNavigating) {
				setTimeout(() => {
					this.isNavigating = false;
					this.checkCartStatus();
					this.checkFavoriteStatus();
				}, 300);
			} else {
				// 立即检查状态
				this.checkCartStatus();
				this.checkFavoriteStatus();
			}
		}
	},
	methods: {
		// 加载商品详情
		async loadProductDetail() {
			if (!this.productId) return;

			this.loading = true;
			try {
				const response = await apiService.mall.products.detail(this.productId);

				if (response.code === 200) {
					// 正确提取商品数据：后端返回的data包含product和store两个字段
					this.productDetail = response.data.product || {};
					this.storeInfo = response.data.store || {};

					console.log('商品详情数据:', this.productDetail);

					// 处理规格参数
					this.specifications = [
						{name: '商品ID', value: this.productDetail.id},
						{name: '商品名称', value: this.productDetail.productName || this.productDetail.name},
						{name: '价格', value: `¥${this.productDetail.price}`},
						{name: '库存', value: `${this.productDetail.stock}件`},
						{name: '状态', value: this.productDetail.status === 1 ? '上架' : '下架'}
					];

					// 如果有规格字段，添加到参数中
					if (this.productDetail.specification) {
						this.specifications.push({name: '规格', value: this.productDetail.specification});
					}
				} else {
					console.error('获取商品详情失败:', response.message);
					uni.showToast({
						title: '获取商品详情失败',
						icon: 'none'
					});
				}
			} catch (error) {
				console.error('获取商品详情异常:', error);
				uni.showToast({
					title: '网络异常',
					icon: 'none'
				});
			} finally {
				this.loading = false;
			}
		},

		// 加载推荐商品
		async loadRecommendations() {
			try {
				const response = await apiService.mall.products.recommend(6);

				if (response.code === 200) {
					this.recommendations = response.data || [];
				} else {
					console.error('获取推荐商品失败:', response.message);
				}
			} catch (error) {
				console.error('获取推荐商品异常:', error);
			}
		},

		// 获取商品图片
		getProductImage(product) {
			if (!product.images) return '';
			if (typeof product.images === 'string') {
				const imageArray = product.images.split(',');
				return imageArray[0] || '';
			}
			return product.images[0] || '';
		},

		// 检查收藏状态
		async checkFavoriteStatus() {
			// 检查是否已登录
			const isLoggedIn = uni.getStorageSync('isLoggedIn') || false;
			if (!isLoggedIn || !this.productId) {
				this.isFavorited = false;
				return;
			}

			try {
				const response = await apiService.mall.favorites.check(this.productId);
				if (response.code === 200) {
					this.isFavorited = response.data || false;
				}
			} catch (error) {
				console.error('检查收藏状态异常:', error);
				this.isFavorited = false;
			}
		},

		// 检查购物车状态
		async checkCartStatus() {
			// 检查是否已登录
			const isLoggedIn = uni.getStorageSync('isLoggedIn') || false;
			if (!isLoggedIn || !this.productId) {
				this.isInCart = false;
				return;
			}

			try {
				const response = await apiService.mall.cart.list();
				if (response.code === 200) {
					const cartItems = response.data || [];
					// 检查当前商品是否在购物车中
					this.isInCart = cartItems.some(item =>
						item.productId == this.productId || item.product_id == this.productId
					);
				}
			} catch (error) {
				console.error('检查购物车状态异常:', error);
				this.isInCart = false;
			}
		},


		goToStore() {
			uni.navigateTo({
				url: '/pages/mall/store/store?id=1'
			});
		},
		goToCart() {
			// 设置跳转标志位，防止onShow中重复调用API
			this.isNavigating = true;

			// 检查是否已登录
			const isLoggedIn = uni.getStorageSync('isLoggedIn') || false;

			if (!isLoggedIn) {
				// 未登录时重置标志位
				this.isNavigating = false;
				// 未登录，显示登录页面
				uni.navigateTo({
					url: '/pages/login/login?redirect=/pages/cart/cart'
				});
			} else {
				// 已登录，跳转到购物车
				uni.navigateTo({
					url: '/pages/cart/cart',
					success: () => {
						console.log('跳转购物车成功');
					},
					fail: (err) => {
						console.error('跳转购物车失败:', err);
						// 跳转失败时重置标志位
						this.isNavigating = false;
						uni.showToast({
							title: '页面跳转失败',
							icon: 'none'
						});
					}
				});
			}
		},
		goToProduct(id) {
			uni.navigateTo({
				url: '/pages/mall/product/detail?id=' + id
			});
		},
		async addToCart() {
			// 检查是否已登录
			const isLoggedIn = uni.getStorageSync('isLoggedIn') || false;

			if (!isLoggedIn) {
				// 未登录，显示登录页面
				uni.navigateTo({
					url: '/pages/login/login?redirect=/pages/mall/product/detail?id=' + this.productId
				});
				return;
			}

			// 检查商品是否存在
			if (!this.productDetail || !this.productDetail.id) {
				uni.showToast({
					title: '商品信息异常',
					icon: 'none'
				});
				return;
			}

			// 检查库存
			if (this.productDetail.stock <= 0) {
				uni.showToast({
					title: '商品库存不足',
					icon: 'none'
				});
				return;
			}

			try {
				const response = await apiService.mall.cart.add({
					productId: this.productDetail.id,
					quantity: 1,
					skuId: null // 如果有规格选择，这里传入skuId
				});

				if (response.code === 200) {
					// 更新购物车状态
					this.isInCart = true;

					uni.showToast({
						title: '已加入购物车',
						icon: 'success'
					});
				} else {
					uni.showToast({
						title: response.message || '加入购物车失败',
						icon: 'none'
					});
				}
			} catch (error) {
				console.error('加入购物车异常:', error);
				uni.showToast({
					title: '网络异常，请重试',
					icon: 'none'
				});
			}
		},

		// 处理购物车按钮点击
		handleCartAction() {
			if (this.isInCart) {
				// 如果已在购物车，跳转到购物车页面
				this.goToCart();
			} else {
				// 如果未在购物车，执行加入购物车操作
				this.addToCart();
			}
		},

		buyNow() {
			// 检查是否已登录
			const isLoggedIn = uni.getStorageSync('isLoggedIn') || false;

			if (!isLoggedIn) {
				// 未登录，显示登录页面
				uni.navigateTo({
					url: '/pages/login/login?redirect=/pages/mall/product/detail?id=' + this.productId
				});
				return;
			}

			// 检查商品是否存在
			if (!this.productDetail || !this.productDetail.id) {
				uni.showToast({
					title: '商品信息异常',
					icon: 'none'
				});
				return;
			}

			// 检查库存
			if (this.productDetail.stock <= 0) {
				uni.showToast({
					title: '商品库存不足',
					icon: 'none'
				});
				return;
			}

			// 将商品信息传递给订单确认页面
			const orderData = {
				type: 'buyNow',
				products: [{
					id: this.productDetail.id,
					productName: this.productDetail.productName || this.productDetail.name,
					price: this.productDetail.price,
					quantity: 1,
					images: this.productDetail.images,
					skuId: null
				}]
			};

			uni.navigateTo({
				url: '/pages/order/confirm?data=' + encodeURIComponent(JSON.stringify(orderData))
			});
		},
		async handleCollect() {
			// 检查是否已登录
			const isLoggedIn = uni.getStorageSync('isLoggedIn') || false;

			if (!isLoggedIn) {
				// 未登录，显示登录页面
				uni.navigateTo({
					url: '/pages/login/login?redirect=/pages/mall/product/detail?id=' + this.productId
				});
				return;
			}

			// 检查商品是否存在
			if (!this.productId) {
				uni.showToast({
					title: '商品信息异常',
					icon: 'none'
				});
				return;
			}

			// 防止重复点击
			if (this.favoriteLoading) {
				return;
			}

			this.favoriteLoading = true;

			try {
				let response;
				if (this.isFavorited) {
					// 取消收藏
					response = await apiService.mall.favorites.remove(this.productId);
					if (response.code === 200) {
						this.isFavorited = false;
						uni.showToast({
							title: '取消收藏成功',
							icon: 'success'
						});
					} else {
						uni.showToast({
							title: response.message || '取消收藏失败',
							icon: 'none'
						});
					}
				} else {
					// 添加收藏
					response = await apiService.mall.favorites.add(this.productId);
					if (response.code === 200) {
						this.isFavorited = true;
						uni.showToast({
							title: '收藏成功',
							icon: 'success'
						});

						// 询问是否跳转到收藏页面
						setTimeout(() => {
							uni.showModal({
								title: '提示',
								content: '商品已收藏，是否查看我的收藏？',
								success: (res) => {
									if (res.confirm) {
										uni.navigateTo({
											url: '/pages/my/collection'
										});
									}
								}
							});
						}, 1500);
					} else {
						uni.showToast({
							title: response.message || '收藏失败',
							icon: 'none'
						});
					}
				}
			} catch (error) {
				console.error('收藏操作异常:', error);
				uni.showToast({
					title: '网络异常，请重试',
					icon: 'none'
				});
			} finally {
				this.favoriteLoading = false;
			}
		}
	}
};
</script>

<style lang="scss">
.detail-container {
	display: flex;
	flex-direction: column;
	height: 100vh;
	background-color: #f5f5f5;
}

.detail-scroll {
	flex: 1;
	margin-bottom: 100rpx;
}

.detail-swiper {
	height: 600rpx;
	
	image {
		width: 100%;
		height: 100%;
	}
}

.product-title-section {
	background-color: #ffffff;
	padding: 30rpx 20rpx;
	margin-bottom: 20rpx;
}

.product-title {
	font-size: 36rpx;
	font-weight: bold;
	margin-bottom: 20rpx;
}

.product-desc {
	font-size: 28rpx;
	color: #666666;
	line-height: 1.5;
	margin-bottom: 20rpx;
}

.product-price-section {
	display: flex;
	align-items: baseline;
	margin-bottom: 15rpx;
}

.current-price {
	font-size: 36rpx;
	color: #f44336;
	font-weight: bold;
	margin-right: 20rpx;
}

.original-price {
	font-size: 28rpx;
	color: #999999;
	text-decoration: line-through;
}

.product-stock {
	font-size: 26rpx;
	color: #666666;
}

.specs-section, .detail-content, .recommend-section {
	background-color: #ffffff;
	padding: 30rpx 20rpx;
	margin-bottom: 20rpx;
}

.section-title {
	font-size: 32rpx;
	font-weight: bold;
	margin-bottom: 30rpx;
	position: relative;
	padding-left: 20rpx;
	
	&:before {
		content: '';
		position: absolute;
		left: 0;
		top: 6rpx;
		width: 8rpx;
		height: 32rpx;
		background-color: #4285f4;
		border-radius: 4rpx;
	}
}

.specs-list {
	display: flex;
	flex-wrap: wrap;
}

.specs-item {
	width: 50%;
	margin-bottom: 20rpx;
	display: flex;
}

.specs-label {
	width: 160rpx;
	color: #999999;
	font-size: 28rpx;
}

.specs-value {
	flex: 1;
	color: #333333;
	font-size: 28rpx;
}

.detail-images {
	margin-top: 30rpx;
}

.recommend-list {
	display: flex;
	overflow-x: auto;
	padding-bottom: 20rpx;
}

.recommend-item {
	width: 200rpx;
	margin-right: 20rpx;
	flex-shrink: 0;
	
	image {
		width: 200rpx;
		height: 200rpx;
		border-radius: 8rpx;
		margin-bottom: 10rpx;
	}
}

.recommend-name {
	font-size: 24rpx;
	color: #333333;
	display: block;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
	margin-bottom: 6rpx;
}

.recommend-price {
	font-size: 26rpx;
	color: #f44336;
	font-weight: bold;
}

.bottom-actions {
	position: fixed;
	left: 0;
	right: 0;
	bottom: 0;
	display: flex;
	height: 100rpx;
	background-color: #ffffff;
	box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
	z-index: 99;
}

.action-item {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	width: 80rpx;
}

.action-icon {
	font-size: 36rpx;
	margin-bottom: 4rpx;
}

.action-text {
	font-size: 20rpx;
	color: #666666;
}

.action-item.favorited .action-text {
	color: #ff4757;
}

.add-cart-btn, .buy-now-btn {
	flex: 1;
	display: flex;
	align-items: center;
	justify-content: center;
	height: 100%;
	font-size: 28rpx;
}

.add-cart-btn {
	background-color: #ff9800;
	color: #ffffff;
	transition: all 0.3s ease;
}

.add-cart-btn.in-cart {
	background-color: #4285f4;
	color: #ffffff;
}

.buy-now-btn {
	background-color: #f44336;
	color: #ffffff;
}
</style> 