/* 水平间距 */
/* 水平间距 */
/* 全局样式修复 - 确保页面占满屏幕宽度 */
page {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
}

/* 通用容器样式 */
.container, .page-container {
  width: 100%;
  min-height: 100vh;
  box-sizing: border-box;
}

/* 修复小程序页面宽度问题 */
uni-page-body {
  width: 100% !important;
}

/* 确保所有view元素正确继承宽度 */
view {
  box-sizing: border-box;
}
.demo-container {
  min-height: 100vh;
  background-color: #f5f5f5;
}
.header {
  background-color: #4285f4;
  color: #fff;
  padding: 40rpx 30rpx;
  text-align: center;
}
.title {
  font-size: 36rpx;
  font-weight: bold;
}
.content {
  padding: 30rpx;
}
.demo-section {
  background-color: #fff;
  padding: 30rpx;
  margin-bottom: 30rpx;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}
.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 20rpx;
}
.demo-btn {
  width: 100%;
  height: 80rpx;
  line-height: 80rpx;
  background-color: #4285f4;
  color: #fff;
  border-radius: 8rpx;
  font-size: 28rpx;
  margin-bottom: 15rpx;
}
.demo-btn[disabled] {
  background-color: #ccc;
  color: #999;
}
.demo-desc {
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
}
.log-section {
  background-color: #fff;
  padding: 30rpx;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}
.log-list {
  height: 400rpx;
  border: 1rpx solid #eee;
  border-radius: 8rpx;
  padding: 15rpx;
  margin: 20rpx 0;
}
.log-item {
  font-size: 24rpx;
  color: #333;
  line-height: 1.5;
  padding: 5rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}
.clear-btn {
  width: 100%;
  height: 60rpx;
  line-height: 60rpx;
  background-color: #ff5252;
  color: #fff;
  border-radius: 8rpx;
  font-size: 26rpx;
}