"use strict";
const common_vendor = require("../../../common/vendor.js");
const utils_request = require("../../../utils/request.js");
const _sfc_main = {
  data() {
    return {
      baseUrl: "https://www.narenqiqige.com/api",
      categoryName: "",
      currentCategoryId: null,
      categories: [],
      products: [],
      loading: false,
      hasMore: true,
      currentPage: 1,
      pageSize: 10,
      imageErrors: {}
      // 记录图片加载失败的分类
    };
  },
  onLoad(options) {
    if (options.categoryId) {
      this.currentCategoryId = parseInt(options.categoryId);
    }
    if (options.categoryName) {
      this.categoryName = decodeURIComponent(options.categoryName);
    }
    this.loadCategories();
    this.loadProducts();
  },
  methods: {
    // 加载分类列表
    async loadCategories() {
      try {
        const response = await utils_request.apiService.mall.categories.list();
        if (response.code === 200) {
          this.categories = response.data || [];
          if (!this.currentCategoryId && this.categories.length > 0) {
            this.currentCategoryId = this.categories[0].id;
            this.categoryName = this.categories[0].name;
          }
        } else {
          common_vendor.index.__f__("error", "at pages/mall/category/category.vue:124", "获取分类失败:", response.message);
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/mall/category/category.vue:127", "获取分类异常:", error);
      }
    },
    // 加载商品列表
    async loadProducts(isLoadMore = false) {
      if (this.loading)
        return;
      this.loading = true;
      try {
        const params = {
          categoryId: this.currentCategoryId,
          page: isLoadMore ? this.currentPage + 1 : 1,
          pageSize: this.pageSize
        };
        const response = await utils_request.apiService.mall.products.list(params);
        if (response.code === 200) {
          const newProducts = response.data.products || response.data.records || response.data || [];
          if (isLoadMore) {
            this.products = [...this.products, ...newProducts];
            this.currentPage++;
          } else {
            this.products = newProducts;
            this.currentPage = 1;
          }
          this.hasMore = newProducts.length === this.pageSize;
        } else {
          common_vendor.index.__f__("error", "at pages/mall/category/category.vue:160", "获取商品失败:", response.message);
          common_vendor.index.showToast({
            title: "获取商品失败",
            icon: "none"
          });
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/mall/category/category.vue:167", "获取商品异常:", error);
        common_vendor.index.showToast({
          title: "网络异常",
          icon: "none"
        });
      } finally {
        this.loading = false;
      }
    },
    // 加载更多商品
    loadMoreProducts() {
      this.loadProducts(true);
    },
    // 获取商品第一张图片
    getFirstImage(images) {
      if (!images)
        return "";
      if (typeof images === "string") {
        const imageArray = images.split(",");
        return imageArray[0] || "";
      }
      return images[0] || "";
    },
    goBack() {
      common_vendor.index.navigateBack();
    },
    switchCategory(category) {
      this.currentCategoryId = category.id;
      this.categoryName = category.name;
      this.loadProducts();
    },
    navigateToProduct(productId) {
      common_vendor.index.navigateTo({
        url: "/pages/mall/product/detail?id=" + productId
      });
    },
    // 刷新数据
    refreshData() {
      this.products = [];
      this.currentPage = 1;
      this.hasMore = true;
      common_vendor.index.showLoading({
        title: "刷新中..."
      });
      this.loadProducts().finally(() => {
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({
          title: "刷新完成",
          icon: "success"
        });
      });
    },
    // 判断是否为图片URL
    isImageUrl(url) {
      if (!url)
        return false;
      return url.startsWith("http") || url.startsWith("/static") || url.startsWith("data:image") || url.startsWith("https");
    },
    // 图片加载失败处理
    onImageError(categoryId) {
      this.$set(this.imageErrors, categoryId, true);
    },
    // 获取Font Awesome图标对应的Unicode字符
    getFontAwesomeIcon(iconClass) {
      const iconMap = {
        "fas fa-mobile-alt": "📱",
        "fas fa-tshirt": "👕",
        "fas fa-home": "🏠",
        "fas fa-heart": "💄",
        "fas fa-utensils": "🍽️",
        "fas fa-running": "🏃",
        "fas fa-laptop": "💻",
        "fas fa-car": "🚗",
        "fas fa-book": "📚",
        "fas fa-music": "🎵"
      };
      return iconMap[iconClass] || "📦";
    },
    // 获取默认图标
    getDefaultIcon(categoryName) {
      const nameIconMap = {
        "数码电子": "📱",
        "时尚服装": "👕",
        "家居生活": "🏠",
        "Beauty Care": "💄",
        "Food Drinks": "🍽️",
        "Sports Outdoor": "🏃"
      };
      return nameIconMap[categoryName] || "📦";
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.f($data.categories, (category, k0, i0) => {
      return common_vendor.e({
        a: category.icon && $options.isImageUrl(category.icon)
      }, category.icon && $options.isImageUrl(category.icon) ? {
        b: $data.baseUrl + category.icon,
        c: common_vendor.o(($event) => $options.onImageError(category.id), category.id)
      } : category.icon && !$options.isImageUrl(category.icon) ? {
        e: common_vendor.t($options.getFontAwesomeIcon(category.icon))
      } : {
        f: common_vendor.t($options.getDefaultIcon(category.name))
      }, {
        d: category.icon && !$options.isImageUrl(category.icon),
        g: common_vendor.t(category.name),
        h: category.id,
        i: $data.currentCategoryId === category.id ? 1 : "",
        j: common_vendor.o(($event) => $options.switchCategory(category), category.id)
      });
    }),
    b: common_vendor.f($data.products, (product, k0, i0) => {
      return common_vendor.e({
        a: product.images
      }, product.images ? {
        b: $data.baseUrl + $options.getFirstImage(product.images)
      } : {}, {
        c: common_vendor.t(product.productName || product.name),
        d: common_vendor.t(product.brand || "官方"),
        e: common_vendor.t(product.description || "优质商品，值得信赖"),
        f: common_vendor.t(product.price),
        g: common_vendor.t(product.storeName || "官方直营"),
        h: common_vendor.t(product.sales || 0),
        i: common_vendor.t(product.rating || 99),
        j: product.id,
        k: common_vendor.o(($event) => $options.navigateToProduct(product.id), product.id)
      });
    }),
    c: $data.hasMore && !$data.loading
  }, $data.hasMore && !$data.loading ? {
    d: common_vendor.o((...args) => $options.loadMoreProducts && $options.loadMoreProducts(...args))
  } : {}, {
    e: $data.loading
  }, $data.loading ? {} : {}, {
    f: !$data.hasMore && $data.products.length > 0
  }, !$data.hasMore && $data.products.length > 0 ? {} : {}, {
    g: $data.products.length === 0 && !$data.loading
  }, $data.products.length === 0 && !$data.loading ? {
    h: common_vendor.t($data.categoryName),
    i: common_vendor.o((...args) => $options.goBack && $options.goBack(...args)),
    j: common_vendor.o((...args) => $options.refreshData && $options.refreshData(...args))
  } : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/pages/mall/category/category.js.map
