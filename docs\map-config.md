# 地图服务配置说明

## 概述

本项目使用高德地图作为唯一的地图服务提供商，提供位置定位、地址解析和POI搜索功能。

## 快速配置

### 1. 获取API密钥

1. 访问 [高德开放平台](https://lbs.amap.com/)
2. 注册开发者账号
3. 创建应用并申请Web服务API密钥
4. 确保开通以下服务：
   - 逆地理编码
   - 地点搜索

### 2. 配置密钥

编辑 `config/map.js` 文件：

```javascript
export const mapConfig = {
  // 高德地图配置
  amap: {
    key: 'YOUR_ACTUAL_API_KEY', // 替换为您的真实API密钥
    geocoderUrl: 'https://restapi.amap.com/v3/geocode/regeo',
    poiSearchUrl: 'https://restapi.amap.com/v3/place/text',
    enabled: true
  },
  // ... 其他配置
};
```

## 功能说明

### 位置定位
- 自动获取用户当前位置
- 将坐标转换为具体地址
- 支持手动重新定位

### 地址搜索
- 支持关键词搜索地点
- 返回详细的地点信息
- 自动降级到模拟数据

### 位置选择
- 可视化的位置选择界面
- 支持搜索和列表选择
- 确认后自动返回并更新

## 使用方法

### 基本用法

```javascript
import locationService from '@/utils/location.js';

// 获取当前位置
const location = await locationService.getCurrentLocation();
console.log(location.address); // 广东省东莞市南城街道

// 搜索地点
const results = await locationService.searchPOI('万达广场', '东莞');
console.log(results); // 搜索结果数组
```

### 在页面中使用

```vue
<template>
  <view @click="openLocationPicker">
    <text>{{currentLocation}}</text>
  </view>
</template>

<script>
export default {
  data() {
    return {
      currentLocation: '正在获取位置...'
    };
  },
  methods: {
    openLocationPicker() {
      uni.navigateTo({
        url: '/pages/location-picker'
      });
    }
  }
};
</script>
```

## 注意事项

1. **API配额限制**：高德地图有每日免费调用次数限制
2. **网络依赖**：需要确保设备能访问高德API服务器
3. **权限要求**：需要用户授权位置权限
4. **降级处理**：API不可用时自动使用模拟数据

## 故障排除

### 常见问题

**Q: 显示坐标而不是地址？**
A: 检查API密钥是否正确配置，确认网络连接正常

**Q: 搜索功能不工作？**
A: 确认已开通地点搜索服务，检查API密钥权限

**Q: 定位失败？**
A: 检查用户是否授权位置权限，确认设备GPS功能正常

### 调试方法

1. 查看浏览器控制台日志
2. 检查网络请求状态
3. 验证API密钥有效性
4. 测试模拟数据是否正常

## 更新记录

- 2024-06-04：简化配置，只使用高德地图
- 2024-06-04：添加POI搜索功能
- 2024-06-04：优化错误处理和降级机制
