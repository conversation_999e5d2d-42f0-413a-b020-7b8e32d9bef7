<template>
  <view class="content">
    <!-- 顶部导航 -->
    <view class="header">
      <view class="back-btn" @click="goBack">
        <text class="back-icon">←</text>
      </view>
      <view class="title">商品详情</view>
    </view>
    
    <!-- 商品图片 -->
    <view class="product-images">
      <swiper class="swiper-box" circular indicator-dots autoplay>
        <swiper-item v-for="(item, index) in productImages" :key="index">
          <image :src="item" mode="aspectFill"></image>
        </swiper-item>
      </swiper>
    </view>
    
    <!-- 添加商品详情信息 -->
    <view class="product-detail">
      <view class="product-name">{{ productName }}</view>
      <view class="product-price">{{ productPrice }}</view>
      <view class="product-desc">{{ productDesc }}</view>
    </view>
    
    <view class="btn-container">
      <button class="btn" @click="goToColorDetail">查看颜色详情</button>
    </view>
  </view>
</template>

<script>
export default {
  props: {
    productId: {
      type: [String, Number],
      default: null
    }
  },
  data() {
    return {
      productName: '',
      productPrice: '',
      productDesc: '',
      productImages: [
        "/static/images/product1.jpg",
        "/static/images/product2.jpg"
      ],
      localProductId: null
    }
  },
  created() {
    console.log('product/index组件创建, props传入的productId:', this.productId);
  },
  onLoad(options) {
    // 首先尝试使用props传递的ID
    if (this.productId) {
      this.localProductId = this.productId;
      console.log('使用props传入的商品ID:', this.localProductId);
    } 
    // 然后尝试使用URL参数
    else if (options && options.id) {
      this.localProductId = options.id;
      console.log('使用URL参数的商品ID:', this.localProductId);
    }
    
    // 加载商品数据
    if (this.localProductId) {
      this.loadProductData(this.localProductId);
    } else {
      console.log('未获取到商品ID');
      uni.showToast({
        title: '商品信息获取失败',
        icon: 'none'
      });
    }
  },
  methods: {
    // 加载商品数据
    loadProductData(id) {
      console.log('正在加载商品数据, ID:', id);
      // 模拟从服务器获取数据
      // 实际应用中，这里应该调用API获取商品数据
      const products = {
        1: {
          name: '内立柔9合一直发膏',
          price: '¥ 99.99',
          desc: '一款专业的直发产品，适合所有发质，特别是卷曲或毛躁的头发。'
        },
        2: {
          name: '炫即染发膏',
          price: '¥ 1200.00',
          desc: '快速上色的专业染发产品，色彩持久，不伤发质。'
        }
      };
      
      if (products[id]) {
        this.productName = products[id].name;
        this.productPrice = products[id].price;
        this.productDesc = products[id].desc;
        console.log('商品数据加载成功:', this.productName);
      } else {
        console.log('商品数据不存在, ID:', id);
        uni.showToast({
          title: '商品不存在',
          icon: 'none'
        });
      }
    },
    goBack() {
      uni.navigateBack();
    },
    goToColorDetail() {
      const colorInfo = {
        name: "X蓝色",
        category: "潮色",
        hex: "#0000FF"
      };
      
      uni.setStorageSync("selectedColorInfo", colorInfo);
      
      uni.navigateTo({
        url: "/pages/mall/color/detail?name=" + encodeURIComponent(colorInfo.name) + 
             "&category=" + encodeURIComponent(colorInfo.category) + 
             "&color=" + encodeURIComponent(colorInfo.hex),
        fail: (err) => {
          console.error("页面跳转失败:", err);
          uni.showModal({
            title: "跳转失败",
            content: "无法打开颜色详情页: " + JSON.stringify(err),
            showCancel: false
          });
        }
      });
    }
  }
}
</script>

<style>
.content {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
}

.header {
  display: flex;
  align-items: center;
  width: 100%;
  margin-bottom: 20px;
}

.back-btn {
  margin-right: 20px;
}

.back-icon {
  font-size: 24px;
}

.title {
  font-size: 18px;
  font-weight: bold;
}

.product-images {
  width: 100%;
  height: 300px;
  margin-bottom: 30px;
}

.swiper-box {
  width: 100%;
  height: 100%;
}

.swiper-box image {
  width: 100%;
  height: 100%;
}

/* 新增商品详情样式 */
.product-detail {
  width: 100%;
  padding: 15px;
  background-color: #fff;
  border-radius: 10px;
  margin-bottom: 20px;
}

.product-name {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 10px;
}

.product-price {
  font-size: 20px;
  color: #f44336;
  font-weight: bold;
  margin-bottom: 10px;
}

.product-desc {
  font-size: 14px;
  color: #666;
  line-height: 1.5;
}

.btn-container {
  margin-top: 20px;
  width: 100%;
}

.btn {
  background-color: #4285f4;
  color: white;
  padding: 10px;
  border-radius: 5px;
  text-align: center;
}
</style>
