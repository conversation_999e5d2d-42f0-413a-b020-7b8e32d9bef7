"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_request = require("../../utils/request.js");
const _sfc_main = {
  data() {
    return {
      // baseUrl : "http://127.0.0.1:6549/api",
      baseUrl: "https://www.narenqiqige.com/api",
      address: null,
      orderInfo: {
        type: "cart",
        // cart: 购物车结算, buyNow: 立即购买
        shopName: "官方直营",
        totalPrice: 0,
        freight: 0,
        products: []
      },
      coupon: null,
      remark: "",
      loading: false,
      dataLoading: true
    };
  },
  computed: {
    // 计算商品总价
    goodsTotal() {
      return this.orderInfo.products.reduce((total, item) => {
        return total + item.price * item.quantity;
      }, 0);
    }
  },
  onLoad(options) {
    common_vendor.index.__f__("log", "at pages/order/confirm.vue:146", "订单确认页参数:", options);
    if (options.data) {
      try {
        const orderData = JSON.parse(decodeURIComponent(options.data));
        this.orderInfo = {
          ...this.orderInfo,
          ...orderData
        };
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/order/confirm.vue:157", "解析订单数据失败:", error);
      }
    }
    if (options.type) {
      this.orderInfo.type = options.type;
    }
    this.initOrderData();
  },
  onShow() {
    const selectedAddress = common_vendor.index.getStorageSync("selectedAddress");
    if (selectedAddress) {
      this.address = selectedAddress;
      common_vendor.index.removeStorageSync("selectedAddress");
      this.calculateFreight();
    }
  },
  // 导航栏按钮点击事件
  onNavigationBarButtonTap(e) {
    if (e.index === 0) {
      this.goBack();
    }
  },
  methods: {
    // 返回上一页
    goBack() {
      common_vendor.index.navigateBack({
        delta: 1
      });
    },
    // 初始化订单数据
    async initOrderData() {
      this.dataLoading = true;
      try {
        await Promise.all([
          this.loadDefaultAddress(),
          this.loadOrderProducts()
        ]);
        await this.calculateFreight();
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/order/confirm.vue:207", "初始化订单数据失败:", error);
        common_vendor.index.showToast({
          title: "加载订单信息失败",
          icon: "none"
        });
      } finally {
        this.dataLoading = false;
      }
    },
    // 加载订单商品数据
    async loadOrderProducts() {
      if (this.orderInfo.products && this.orderInfo.products.length > 0) {
        return;
      }
      try {
        if (this.orderInfo.type === "cart") {
          const response = await utils_request.apiService.mall.cart.list();
          if (response.code === 200) {
            const cartItems = response.data || [];
            const selectedItems = cartItems.filter((item) => item.checked === 1);
            this.orderInfo.products = selectedItems.map((item) => ({
              id: item.productId,
              name: item.productName,
              price: item.price,
              quantity: item.quantity,
              skuId: item.skuId,
              skuName: item.skuName,
              images: item.imageUrl,
              cartId: item.id
            }));
          }
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/order/confirm.vue:246", "获取商品数据失败:", error);
        this.loadMockProducts();
      }
    },
    // 加载模拟商品数据（当API失败时使用）
    loadMockProducts() {
      this.orderInfo.products = [
        {
          id: 1,
          name: "Xiaomi 13 Ultra",
          price: 5999,
          quantity: 1,
          skuName: "黑色, 512GB",
          images: "/static/c1.png"
        },
        {
          id: 2,
          name: "iPhone 15 Pro",
          price: 8999,
          quantity: 2,
          skuName: "深空黑色, 256GB",
          images: "/static/c2.png"
        }
      ];
    },
    // 计算运费
    async calculateFreight() {
      try {
        if (!this.address) {
          this.orderInfo.freight = 0;
          return;
        }
        try {
          const response = await utils_request.apiService.mall.freight.calculate({
            addressId: this.address.id,
            products: this.orderInfo.products.map((item) => ({
              productId: item.id,
              quantity: item.quantity
            }))
          });
          if (response.code === 200) {
            this.orderInfo.freight = response.data.freight || 0;
            return;
          }
        } catch (apiError) {
          common_vendor.index.__f__("log", "at pages/order/confirm.vue:297", "运费API暂不可用，使用默认计算逻辑");
        }
        this.calculateDefaultFreight();
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/order/confirm.vue:303", "计算运费失败:", error);
        this.calculateDefaultFreight();
      }
    },
    // 默认运费计算逻辑
    calculateDefaultFreight() {
      const goodsTotal = this.goodsTotal;
      if (goodsTotal >= 99) {
        this.orderInfo.freight = 0;
      } else {
        this.orderInfo.freight = 10;
      }
    },
    // 加载默认地址
    async loadDefaultAddress() {
      try {
        const response = await utils_request.apiService.mall.address.list();
        if (response.code === 200) {
          const addresses = response.data || [];
          this.address = addresses.find((addr) => addr.isDefault) || addresses[0] || null;
        } else {
          this.address = {
            id: 1,
            name: "张三",
            phone: "138****8888",
            province: "广东省",
            city: "深圳市",
            district: "南山区",
            detail: "科技园南区深南大道10000号",
            isDefault: true
          };
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/order/confirm.vue:344", "获取地址失败:", error);
        this.address = {
          id: 1,
          name: "张三",
          phone: "138****8888",
          province: "广东省",
          city: "深圳市",
          district: "南山区",
          detail: "科技园南区深南大道10000号",
          isDefault: true
        };
      }
    },
    // 获取商品图片
    getProductImage(product) {
      if (!product.images)
        return "";
      if (typeof product.images === "string") {
        const imageArray = product.images.split(",");
        return imageArray[0] || "";
      }
      return product.images[0] || "";
    },
    selectAddress() {
      common_vendor.index.navigateTo({
        url: "/pages/address/list?from=order"
      });
    },
    calculateTotal() {
      let total = this.goodsTotal + this.orderInfo.freight;
      if (this.coupon) {
        total -= this.coupon.value;
      }
      return total.toFixed(2);
    },
    async submitOrder() {
      if (!this.address) {
        common_vendor.index.showToast({
          title: "请选择收货地址",
          icon: "none"
        });
        return;
      }
      if (this.loading)
        return;
      this.loading = true;
      common_vendor.index.showLoading({
        title: "正在提交..."
      });
      try {
        const orderData = {
          addressId: this.address.id,
          products: this.orderInfo.products.map((item) => ({
            productId: item.id,
            quantity: item.quantity,
            skuId: item.skuId || null
          })),
          remark: this.remark,
          couponId: this.coupon ? this.coupon.id : null
        };
        const response = await utils_request.apiService.mall.orders.create(orderData);
        if (response.code === 200) {
          common_vendor.index.hideLoading();
          common_vendor.index.showToast({
            title: "下单成功",
            icon: "success"
          });
          setTimeout(() => {
            const params = [];
            if (response.data.id) {
              params.push("id=" + response.data.id);
            }
            if (response.data.orderNo) {
              params.push("orderNo=" + response.data.orderNo);
            }
            const url = "/pages/order/success" + (params.length > 0 ? "?" + params.join("&") : "");
            common_vendor.index.redirectTo({
              url
            });
          }, 1500);
        } else {
          common_vendor.index.hideLoading();
          common_vendor.index.showToast({
            title: response.message || "下单失败",
            icon: "none"
          });
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/order/confirm.vue:445", "提交订单异常:", error);
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({
          title: "网络异常，请重试",
          icon: "none"
        });
      } finally {
        this.loading = false;
      }
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.dataLoading
  }, $data.dataLoading ? {} : common_vendor.e({
    b: $data.address
  }, $data.address ? {
    c: common_vendor.t($data.address.name),
    d: common_vendor.t($data.address.phone),
    e: common_vendor.t($data.address.province),
    f: common_vendor.t($data.address.city),
    g: common_vendor.t($data.address.district),
    h: common_vendor.t($data.address.detail)
  } : {}, {
    i: common_vendor.o((...args) => $options.selectAddress && $options.selectAddress(...args)),
    j: common_vendor.t($data.orderInfo.shopName || "官方直营"),
    k: common_vendor.f($data.orderInfo.products, (item, index, i0) => {
      return {
        a: $data.baseUrl + $options.getProductImage(item),
        b: common_vendor.t(item.productName || item.name),
        c: common_vendor.t(item.skuName || "默认规格"),
        d: common_vendor.t(item.price),
        e: common_vendor.t(item.quantity),
        f: index
      };
    }),
    l: common_vendor.t($options.goodsTotal.toFixed(2)),
    m: common_vendor.t($data.orderInfo.freight),
    n: $data.coupon
  }, $data.coupon ? {
    o: common_vendor.t($data.coupon.value)
  } : {}, {
    p: common_vendor.t($options.calculateTotal()),
    q: common_vendor.t($options.calculateTotal()),
    r: common_vendor.o((...args) => $options.submitOrder && $options.submitOrder(...args))
  }));
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/order/confirm.js.map
