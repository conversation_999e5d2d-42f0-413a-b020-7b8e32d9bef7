<template>
	<view class="login-container">
		<view class="login-header">
			<view class="login-logo">
				<image src="/static/images/logo.png" mode="aspectFit"></image>
			</view>
		</view>

		<view class="login-tabs">
			<view class="tab-item" :class="{active: currentTab === 'login'}" @click="switchTab('login')">账号登录</view>
			<view class="tab-item" :class="{active: currentTab === 'register'}" @click="switchTab('register')">手机注册</view>
		</view>

		<view class="login-form">
			<!-- 登录表单 -->
			<view v-if="currentTab === 'login'">
				<view class="input-group">
					<input type="number" v-model="phone" placeholder="请输入手机号" />
				</view>

				<view class="input-group">
					<input type="password" v-model="password" placeholder="请输入密码" />
				</view>

				<view class="agreement-row">
					<checkbox :checked="agreedToTerms" @click="agreedToTerms = !agreedToTerms" color="#4285f4" />
					<text class="agreement-text">同意《用户协议》《隐私政策》</text>
				</view>

				<button class="login-btn" @click="handleLogin">登录</button>
			</view>

			<!-- 注册表单 -->
			<view v-if="currentTab === 'register'">
				<view class="input-group">
					<input type="number" v-model="registerPhone" placeholder="请输入手机号" />
				</view>

				<!-- <view class="input-group verify-code-group">
					<input type="number" v-model="verifyCode" placeholder="请输入验证码" />
					<button class="verify-btn" @click="sendVerifyCode" :disabled="countdown > 0">
						{{ countdown > 0 ? `${countdown}s` : '获取验证码' }}
					</button>
				</view> -->

				<view class="input-group">
					<input type="password" v-model="registerPassword" placeholder="请设置密码" />
				</view>

				<view class="input-group">
					<input type="password" v-model="confirmPassword" placeholder="请确认密码" />
				</view>

				<view class="agreement-row">
					<checkbox :checked="agreedToTerms" @click="agreedToTerms = !agreedToTerms" color="#4285f4" />
					<text class="agreement-text">同意《用户协议》《隐私政策》</text>
				</view>

				<button class="login-btn" @click="handleRegister">注册</button>
			</view>

			<!-- 其他登录方式 微信图标手机号授权登录 -->
			<view class="quick-login">
				<button class="wechat-login-btn" open-type="getPhoneNumber" @getphonenumber="handleGetPhoneNumber"
					bindtap="handleWechatLogin">
					<image src="/static/wechat-icon.png" class="wechat-icon" />
				</button>
			</view>

			<!-- 未登录状态显示微信登录按钮 -->
			<!-- <view class="quick-login">
				<button class="wechat-login-btn">
					<image src="/static/wechat-icon.png" class="wechat-icon" />
				</button>
			</view> -->
			<!-- 已登录状态显示用户头像 -->
			<!-- <view v-else class="user-avatar-wrapper">
				<image :src="userInfo.avatar || '/static/default-avatar.png'" class="user-avatar" mode="aspectFill" />
			</view> -->

			<view class="divider">
				<text class="divider-text">其他登录方式</text>
			</view>

			<view class="quick-login">
				<view class="wechat-login" @click="handleWechatLogin">
					<image src="/static/images/wechat.png" mode="aspectFit" class="wechat-icon"></image>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		wxLogin
	} from '@/utils/auth';
	import {
		apiService,
		request
	} from '@/utils/request';

	export default {
		data() {
			return {
				currentTab: 'login', // 当前选项卡：login 或 register
				phone: '',
				password: '',
				registerPhone: '',
				registerPassword: '',
				confirmPassword: '',
				verifyCode: '',
				countdown: 0, // 验证码倒计时
				agreedToTerms: true, // 默认勾选协议
				redirect: '',
				isLoggedIn: false,
			}
		},
		onLoad(options) {
			if (options.redirect) {
				this.redirect = options.redirect;
			}
		},
		methods: {
			// 切换选项卡
			switchTab(tab) {
				this.currentTab = tab;
			},

			// 发送验证码
			async sendVerifyCode() {
				if (!this.registerPhone) {
					uni.showToast({
						title: '请输入手机号',
						icon: 'none'
					});
					return;
				}

				// 简单的手机号格式验证
				if (!/^1[3-9]\d{9}$/.test(this.registerPhone)) {
					uni.showToast({
						title: '请输入正确的手机号',
						icon: 'none'
					});
					return;
				}

				try {
					uni.showLoading({
						title: '发送中...'
					});

					const result = await apiService.user.sendVerifyCode(this.registerPhone);
					uni.hideLoading();

					if (result.code === 200) {
						uni.showToast({
							title: '验证码已发送',
							icon: 'success'
						});

						// 开始倒计时
						this.countdown = 60;
						const timer = setInterval(() => {
							this.countdown--;
							if (this.countdown <= 0) {
								clearInterval(timer);
							}
						}, 1000);
					} else {
						uni.showToast({
							title: result.message || '发送失败',
							icon: 'none'
						});
					}
				} catch (error) {
					uni.hideLoading();
					console.error('发送验证码失败:', error);

					// 尝试解析错误信息
					let errorMessage = '发送失败，请检查网络';
					if (error && error.message) {
						errorMessage = error.message;
					} else if (error && error.data && error.data.message) {
						errorMessage = error.data.message;
					} else if (typeof error === 'string') {
						errorMessage = error;
					}

					uni.showToast({
						title: errorMessage,
						icon: 'none'
					});
				}
			},

			// 处理注册
			async handleRegister() {
				if (!this.agreedToTerms) {
					uni.showToast({
						title: '请同意用户协议和隐私政策',
						icon: 'none'
					});
					return;
				}

				// 验证手机号
				if (!this.registerPhone) {
					uni.showToast({
						title: '请输入手机号',
						icon: 'none'
					});
					return;
				}

				if (!/^1[3-9]\d{9}$/.test(this.registerPhone)) {
					uni.showToast({
						title: '请输入正确的手机号',
						icon: 'none'
					});
					return;
				}

				// 验证验证码
				// if (!this.verifyCode) {
				// 	uni.showToast({
				// 		title: '请输入验证码',
				// 		icon: 'none'
				// 	});
				// 	return;
				// }

				// 验证密码
				if (!this.registerPassword) {
					uni.showToast({
						title: '请设置密码',
						icon: 'none'
					});
					return;
				}

				if (this.registerPassword.length < 6) {
					uni.showToast({
						title: '密码至少6位',
						icon: 'none'
					});
					return;
				}

				// 验证确认密码
				if (this.registerPassword !== this.confirmPassword) {
					uni.showToast({
						title: '两次密码不一致',
						icon: 'none'
					});
					return;
				}

				try {
					uni.showLoading({
						title: '注册中...'
					});

					const result = await apiService.user.register({
						phone: this.registerPhone,
						password: this.registerPassword,
						verifyCode: this.verifyCode,
						userType: 1 // 普通用户
					});

					uni.hideLoading();

					if (result.code === 200) {
						uni.showToast({
							title: '注册成功',
							icon: 'success'
						});

						// 注册成功后自动切换到登录选项卡
						setTimeout(() => {
							this.currentTab = 'login';
							this.phone = this.registerPhone; // 自动填入手机号
							this.password = this.registerPassword; // 自动填入密码
						}, 1000);
					} else {
						uni.showToast({
							title: result.message || '注册失败',
							icon: 'none'
						});
					}
				} catch (error) {
					uni.hideLoading();
					console.error('注册失败:', error);

					// 尝试解析错误信息
					let errorMessage = '注册失败，请检查网络连接';
					if (error && error.message) {
						errorMessage = error.message;
					} else if (error && error.data && error.data.message) {
						errorMessage = error.data.message;
					} else if (typeof error === 'string') {
						errorMessage = error;
					}

					uni.showToast({
						title: errorMessage,
						icon: 'none'
					});
				}
			},

			//获取手机号加密数据（自动触发）
			handleGetPhoneNumber(e) {
				if (e.detail.errMsg === 'getPhoneNumber:fail user deny') {
					wx.showToast({
						title: '需授权手机号才能登录',
						icon: 'none'
					});
					return;
				}
				if (e.detail.code) {
					// this.setData({
					// 	phone: e.detail.code
					// });
					// 合并用户信息和手机号，调用登录接口
					this.handleWechatLogin(e);
				} else {
					wx.showToast({
						title: '获取手机号失败',
						icon: 'none'
					});
				}
			},

			async handleLogin() {
				if (!this.agreedToTerms) {
					uni.showToast({
						title: '请同意用户协议和隐私政策',
						icon: 'none'
					});
					return;
				}

				// 简化手机号验证逻辑
				if (!this.phone) {
					uni.showToast({
						title: '请输入手机号',
						icon: 'none'
					});
					return;
				}

				if (!this.password) {
					uni.showToast({
						title: '请输入密码',
						icon: 'none'
					});
					return;
				}

				// 调用真实的登录API
				uni.showLoading({
					title: '登录中...'
				});

				try {
					const result = await apiService.user.login({
						username: this.phone,
						password: this.password
					});

					uni.hideLoading();

					if (result.code === 200) {
						// 存储token和用户信息
						uni.setStorageSync('token', result.data.token.token);
						uni.setStorageSync('isLoggedIn', true);
						uni.setStorageSync("refreshToken",result.data.token.refreshToken);
						console.log("长token",result.data.token.refreshToken)
						uni.setStorageSync('userInfo', {
							id: result.data.userInfo.id,
							phone: result.data.userInfo.phone,
							nickname: result.data.userInfo.nickname,
							avatar: result.data.userInfo.avatar,
							username: result.data.userInfo.username
						});

						uni.showToast({
							title: '登录成功',
							icon: 'success'
						});

						// 跳转到首页
						setTimeout(() => {
							uni.switchTab({
								url: '/pages/index/index'
							});
						}, 1000);
					} else {
						uni.showToast({
							title: result.message || '登录失败',
							icon: 'none'
						});
					}
				} catch (error) {
					uni.hideLoading();
					console.error('登录失败:', error);

					// 尝试解析错误信息
					let errorMessage = '登录失败，请检查网络连接';
					if (error && error.message) {
						errorMessage = error.message;
					} else if (error && error.data && error.data.message) {
						errorMessage = error.data.message;
					} else if (typeof error === 'string') {
						errorMessage = error;
					}

					uni.showToast({
						title: errorMessage,
						icon: 'none'
					});
				} finally {
					uni.hideLoading();
				}
			},
			async handleWechatLogin(e) {
				if (!this.agreedToTerms) {
					uni.showToast({
						title: '请同意用户协议和隐私政策',
						mask: true, // 是否显示透明蒙层（防止触摸穿透）
						icon: 'none'
					});
					return;
				}
				
				try {
					// 模拟微信一键授权登录
					uni.showLoading({
						title: '授权登录中...',
						mask: true // 是否显示透明蒙层（防止触摸穿透）
					});
					
					// 正确获取 code 的方式
					// 1. 获取 code
					const code = await wxLogin();
					console.log('Code:', code);
					
					
					// 2. 发送到后端换取 session_key
					const result = await apiService.user.wxlogin({
						code: code,
						encryptedData: e.detail.encryptedData,
						iv: e.detail.iv
					})
					console.log('登录结果:', result);
					
					setTimeout(() => {
						uni.hideLoading();
					}, 1000);
					
					// 登录成功，res.data 应包含 token 和用户信息
					uni.setStorageSync('token', result.data.token.token);
					uni.setStorageSync("refreshToken",result.data.token.refreshToken)
					// 存储登录状态
					uni.setStorageSync('isLoggedIn', true);
					uni.setStorageSync('userInfo', {
						id : result.data.userInfo.id,
						phone: result.data.userInfo.phone,
						nickname: result.data.userInfo.nickname,
						avatar: result.data.userInfo.avatar,
						username: result.data.userInfo.username
					});
					this.isLoggedIn = true;
					
					uni.switchTab({
						url: '/pages/index/index'
					});
					
					// 微信一键授权登录
					// 显示成功提示
					uni.showToast({
						title: '登录成功',
						icon: 'success',
						duration: 500
					});
				} catch(err) {
					uni.showToast({
						title: '登录失败请重试',
						icon: 'none',
						duration: 500
					});
				} finally {
					uni.hideLoading(); // 确保加载动画被隐藏
				}

				
			}
		}
	}
</script>

<style lang="scss">
	.login-container {
		width: 100%;
		/* 默认占父容器90%宽度 */
		//max-width: 600rpx;   /* 最大不超过600rpx（设计图常用值） */
		margin: 0 auto;
		/* 水平居中 */
		padding: 40rpx;
		background-color: #fff;
		min-height: 100vh;
	}

	/* 微信登录按钮样式 */
	.wechat-login-btn {
		background: none;
		border: none;
		padding: 0;
		margin: 0;
		line-height: 1;
	}

	.wechat-login-btn::after {
		border: none;
		/* 移除默认边框 */
	}

	.wechat-icon {
		width: 100rpx;
		height: 100rpx;
	}

	.login-header {
		display: flex;
		justify-content: center;
		margin-bottom: 60rpx;

		.login-logo {
			width: 120rpx;
			height: 120rpx;

			image {
				width: 100%;
				height: 100%;
			}
		}
	}

	.login-tabs {
		display: flex;
		border-bottom: 1px solid #eee;
		margin-bottom: 50rpx;

		.tab-item {
			flex: 1;
			text-align: center;
			padding: 20rpx 0;
			font-size: 30rpx;
			color: #999;
			position: relative;

			&.active {
				color: #4285f4;
				font-weight: bold;

				&::after {
					content: '';
					position: absolute;
					bottom: -2rpx;
					left: 30%;
					width: 40%;
					height: 4rpx;
					background-color: #4285f4;
				}
			}
		}
	}

	.login-form {
		.input-group {
			// border-bottom: 1px solid #eee;
			// padding: 20rpx 0;
			// margin-bottom: 30rpx;
			border: 1px solid #eee; // 添加完整边框
			border-radius: 8rpx; // 圆角
			padding: 20rpx;
			margin-bottom: 30rpx;

			input {
				width: 100%;
				font-size: 30rpx;
			}

			&.verify-code-group {
				display: flex;
				align-items: center;

				input {
					flex: 1;
					margin-right: 20rpx;
				}

				.verify-btn {
					background-color: #4285f4;
					color: #fff;
					border: none;
					border-radius: 6rpx;
					padding: 15rpx 20rpx;
					font-size: 26rpx;
					white-space: nowrap;

					&:disabled {
						background-color: #ccc;
						color: #999;
					}
				}
			}
		}

		.agreement-row {
			display: flex;
			align-items: center;
			margin-bottom: 50rpx;

			.agreement-text {
				font-size: 26rpx;
				color: #666;
				margin-left: 10rpx;
			}
		}

		.login-btn {
			background-color: #4285f4;
			color: #fff;
			height: 90rpx;
			line-height: 90rpx;
			font-size: 32rpx;
			border-radius: 45rpx;
			margin-bottom: 30rpx;
		}

		.divider {
			position: relative;
			display: flex;
			justify-content: center;
			margin: 60rpx 0;

			&::before {
				content: '';
				position: absolute;
				left: 0;
				top: 50%;
				width: 40%;
				height: 1px;
				background-color: #eee;
			}

			&::after {
				content: '';
				position: absolute;
				right: 0;
				top: 50%;
				width: 40%;
				height: 1px;
				background-color: #eee;
			}

			.divider-text {
				padding: 0 20rpx;
				background-color: #fff;
				color: #999;
				font-size: 26rpx;
				z-index: 1;
			}
		}

		.quick-login {
			display: flex;
			justify-content: center;

			.wechat-login {
				width: 80rpx;
				height: 80rpx;
				display: flex;
				align-items: center;
				justify-content: center;

				.wechat-icon {
					width: 80rpx;
					height: 80rpx;
				}
			}
		}
	}
</style>