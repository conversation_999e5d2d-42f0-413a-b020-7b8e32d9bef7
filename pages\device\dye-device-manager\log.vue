<template>
  <view class="container">
    <uni-nav-bar title="染膏设备操作日志" left-icon="back" @clickLeft="back"></uni-nav-bar>
    
    <view class="filter-box">
      <picker mode="date" @change="dateChange">
        <view class="filter-item">
          <text>{{ filterDate || '选择日期' }}</text>
          <uni-icons type="arrowdown" size="14"></uni-icons>
        </view>
      </picker>
      
      <picker @change="typeChange" :value="typeIndex" :range="actionTypes" range-key="name">
        <view class="filter-item">
          <text>{{ actionTypes[typeIndex]?.name || '全部类型' }}</text>
          <uni-icons type="arrowdown" size="14"></uni-icons>
        </view>
      </picker>
    </view>
    
    <scroll-view scroll-y class="log-list">
      <uni-list>
        <uni-list-item 
          v-for="(item, index) in logList" 
          :key="index"
          :title="getActionText(item.action)" 
          :note="item.createTime"
          :rightText="item.operator"
        >
          <template v-slot:footer>
            <view class="log-content">
              <text class="device-info">{{ item.deviceName }} ({{ item.deviceSn }})</text>
              <text class="action-detail" v-if="item.detail">{{ item.detail }}</text>
            </view>
          </template>
        </uni-list-item>
      </uni-list>
      
      <uni-load-more :status="loadingStatus"></uni-load-more>
    </scroll-view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      deviceId: '',
      filterDate: '',
      typeIndex: 0,
      actionTypes: [
        { id: 0, name: '全部类型' },
        { id: 1, name: '绑定' },
        { id: 2, name: '解绑' },
        { id: 3, name: '分配权限' }
      ],
      logList: [],
      loadingStatus: 'more'
    }
  },
  onLoad(options) {
    this.deviceId = options.id || ''
    this.loadLogs()
  },
  methods: {
    async loadLogs() {
      this.loadingStatus = 'loading'
      try {
        const res = await this.$api.dyeDevice.getLogs({
          deviceId: this.deviceId,
          date: this.filterDate,
          actionType: this.actionTypes[this.typeIndex]?.id || 0
        })
        this.logList = res.data
        this.loadingStatus = this.logList.length >= res.total ? 'noMore' : 'more'
      } catch (e) {
        this.loadingStatus = 'more'
        uni.showToast({ title: '加载失败', icon: 'none' })
      }
    },
    dateChange(e) {
      this.filterDate = e.detail.value
      this.loadLogs()
    },
    typeChange(e) {
      this.typeIndex = e.detail.value
      this.loadLogs()
    },
    getActionText(action) {
      const map = {
        'bind': '绑定负责人',
        'unbind': '解绑负责人',
        'assign': '分配使用权限',
        'revoke': '收回使用权限'
      }
      return map[action] || action
    },
    back() {
      uni.navigateBack()
    }
  }
}
</script>

<style scoped>
.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
}

.filter-box {
  display: flex;
  padding: 10px 15px;
  background-color: #fff;
  border-bottom: 1px solid #f5f5f5;
}

.filter-item {
  display: flex;
  align-items: center;
  margin-right: 15px;
  font-size: 14px;
  color: #666;
}

.log-list {
  flex: 1;
  overflow: hidden;
}

.log-content {
  display: flex;
  flex-direction: column;
}

.device-info {
  font-size: 12px;
  color: #999;
}

.action-detail {
  font-size: 12px;
  color: #666;
  margin-top: 3px;
}
</style>