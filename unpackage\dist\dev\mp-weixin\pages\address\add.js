"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_request = require("../../utils/request.js");
const _sfc_main = {
  data() {
    return {
      address: {
        name: "",
        phone: "",
        province: "",
        city: "",
        district: "",
        detailAddress: "",
        isDefault: false
      },
      saving: false
    };
  },
  methods: {
    goBack() {
      common_vendor.index.navigateBack();
    },
    clearInput(field) {
      this.address[field] = "";
    },
    openAreaPicker() {
      common_vendor.index.showLoading({
        title: "加载中"
      });
      setTimeout(() => {
        common_vendor.index.hideLoading();
        common_vendor.index.navigateTo({
          url: "/pages/address/region-picker"
        });
      }, 300);
    },
    switchChange(e) {
      this.address.isDefault = e.detail.value;
    },
    async saveAddress() {
      if (this.saving) {
        return;
      }
      if (!this.address.name) {
        common_vendor.index.showToast({
          title: "请输入收货人姓名",
          icon: "none"
        });
        return;
      }
      if (!this.address.phone) {
        common_vendor.index.showToast({
          title: "请输入联系电话",
          icon: "none"
        });
        return;
      }
      if (!/^1\d{10}$/.test(this.address.phone)) {
        common_vendor.index.showToast({
          title: "手机号格式不正确",
          icon: "none"
        });
        return;
      }
      if (!this.address.province || !this.address.city || !this.address.district) {
        common_vendor.index.showToast({
          title: "请选择所在地区",
          icon: "none"
        });
        return;
      }
      if (!this.address.detailAddress) {
        common_vendor.index.showToast({
          title: "请输入详细地址",
          icon: "none"
        });
        return;
      }
      try {
        this.saving = true;
        common_vendor.index.showLoading({
          title: "保存中..."
        });
        const addressData = {
          receiverName: this.address.name,
          receiverPhone: this.address.phone,
          province: this.address.province,
          city: this.address.city,
          district: this.address.district,
          detailAddress: this.address.detailAddress,
          isDefault: this.address.isDefault ? 1 : 0
        };
        const response = await utils_request.api.mall.address.add(addressData);
        if (response.code === 200) {
          common_vendor.index.hideLoading();
          common_vendor.index.showToast({
            title: "保存成功",
            icon: "success"
          });
          setTimeout(() => {
            common_vendor.index.navigateBack();
          }, 1e3);
        } else {
          common_vendor.index.hideLoading();
          common_vendor.index.showToast({
            title: response.message || "保存失败",
            icon: "none"
          });
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/address/add.vue:177", "保存地址失败:", error);
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({
          title: "网络错误，请稍后重试",
          icon: "none"
        });
      } finally {
        this.saving = false;
      }
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.address.name,
    b: common_vendor.o(($event) => $data.address.name = $event.detail.value),
    c: $data.address.name
  }, $data.address.name ? {
    d: common_vendor.o(($event) => $options.clearInput("name"))
  } : {}, {
    e: $data.address.phone,
    f: common_vendor.o(($event) => $data.address.phone = $event.detail.value),
    g: $data.address.phone
  }, $data.address.phone ? {
    h: common_vendor.o(($event) => $options.clearInput("phone"))
  } : {}, {
    i: $data.address.province
  }, $data.address.province ? {
    j: common_vendor.t($data.address.province),
    k: common_vendor.t($data.address.city),
    l: common_vendor.t($data.address.district)
  } : {}, {
    m: common_vendor.o((...args) => $options.openAreaPicker && $options.openAreaPicker(...args)),
    n: $data.address.detailAddress,
    o: common_vendor.o(($event) => $data.address.detailAddress = $event.detail.value),
    p: $data.address.isDefault,
    q: common_vendor.o((...args) => $options.switchChange && $options.switchChange(...args)),
    r: common_vendor.t($data.saving ? "保存中..." : "保存地址"),
    s: $data.saving,
    t: common_vendor.o((...args) => $options.saveAddress && $options.saveAddress(...args))
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/address/add.js.map
