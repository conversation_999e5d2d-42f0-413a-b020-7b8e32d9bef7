<template>
	<view class="container">
		<!-- 搜索栏 -->
		<view class="search-bar">
			<view class="search-input">
				<text class="search-icon">🔍</text>
				<input type="text" v-model="searchKeyword" placeholder="请输入染膏名称或制作人" @input="onSearchInput"
					@confirm="searchRecords" />
			</view>
			<text class="search-btn" @click="searchRecords">搜索</text>
		</view>

		<!-- 筛选条件 -->
		<view class="filter-bar">
			<view class="filter-item" @click="showColorFilter">
				<text>色板</text>
				<text class="dropdown-icon">▼</text>
			</view>
			<view class="filter-item" @click="showStatusFilter">
				<text>任务状态</text>
				<text class="dropdown-icon">▼</text>
			</view>
			<view class="filter-item" @click="showTimeFilter">
				<text>时间</text>
				<text class="dropdown-icon">▼</text>
			</view>
			<view class="filter-item" @click="showDeviceFilter">
				<text>设备</text>
				<text class="dropdown-icon">▼</text>
			</view>
			<view class="filter-item" @click="showMakerFilter">
				<text>制作人</text>
				<text class="dropdown-icon">▼</text>
			</view>
		</view>

		<!-- 调色记录列表 -->
		<view class="record-list" v-if="records.length > 0">
			<view class="record-item" v-for="(record, index) in records" :key="index"
				@click="viewRecipeDetails(record)">
				<view class="record-left">
					<!-- <view class="record-color" :style="{ backgroundColor: getColorHexByKey(record.colorTypeId) }"></view> -->
					<view class="record-image-container">
						<image class="record-image" :src="record.beforeImageUrl" mode="aspectFill" />
					</view>
					<view class="record-info">
						<view class="record-title">
							{{record.colorName || '未命名配方'}}
						</view>
						<view class="record-time">{{formatDateTime(record.createTime)}}</view>
						<view class="record-components">
							<text v-for="(comp, i) in parseColorComponents(record.colorComponents)" :key="i"
								class="component-tag">
								{{comp.name}} {{comp.amount}}g
							</text>
						</view>
					</view>
				</view>
				<view class="record-right">
					<view class="share-btn" @click.stop="shareRecord(record)">分享赚钱</view>
				</view>
			</view>
		</view>

		<!-- 筛选弹窗 -->
		<view class="filter-modal" v-if="showFilterModal" @click="showFilterModal = false">
			<view class="filter-content" @click.stop>
				<view class="filter-header">
					<text class="filter-title">选择筛选条件</text>
					<text class="close-btn" @click="showFilterModal = false">×</text>
				</view>
				<view class="filter-options">
					<view class="filter-option" v-for="option in getFilterOptions()" :key="option.value"
						@click="selectFilterOption(option.value)">
						{{option.label}}
					</view>
				</view>
			</view>
		</view>

		<!-- 日期选择器 -->
		<picker ref="datePicker" v-if="showDatePicker" mode="date" :value="selectedDate" @change="onDateChange"
			@cancel="showDatePicker = false">
			<view class="date-picker-trigger"></view>
		</picker>

		<!-- 空状态提示 -->
		<view class="empty-state" v-if="records.length === 0">
			<image class="empty-image" src="/static/images/empty-state.png" mode="aspectFit"></image>
			<text class="empty-text">暂无需染颜色</text>
		</view>

		<!-- 配方详情弹窗 -->
		<view class="recipe-modal" v-if="showRecipeModal">
			<view class="modal-content">
				<view class="modal-header">
					<text class="modal-title">配方详情</text>
					<view class="close-btn" @click="closeRecipeModal">×</view>
				</view>
				<view class="recipe-content">
					<image class="record-image" :src="selectedRecipe.beforeImageUrl" mode="aspectFill" />
					<view class="recipe-name">
						{{selectedRecipe.colorName || '未命名配方'}}
					</view>

					<!-- 颜色构造配比信息 -->
					<view class="color-components" v-if="selectedRecipe.colorComponents">
						<text class="components-title">颜色构造</text>
						<view class="component-item"
							v-for="(component, idx) in parseColorComponents(selectedRecipe.colorComponents)" :key="idx">
							<view class="component-color" :style="{ backgroundColor: component.color }"></view>
							<text class="component-name">{{component.name}}</text>
							<text class="component-amount">{{component.amount}}g</text>
						</view>
					</view>

					<!-- 双氧水配比 -->
					<view class="color-components" v-if="selectedRecipe.developerComponents">
						<text class="components-title">双氧水配比</text>
						<view class="component-item"
							v-for="(dev, idx) in parseDeveloperComponents(selectedRecipe.developerComponents)"
							:key="'dev'+idx">
							<text class="component-name">{{dev.name}}</text>
							<text class="component-amount">{{dev.amount}}g</text>
						</view>
					</view>

					<!-- 使用记录详情 -->
					<view class="usage-details">
						<view class="detail-item">
							<text class="detail-label">配方ID:</text>
							<text class="detail-value">{{selectedRecipe.formulaId}}</text>
						</view>
						<view class="detail-item">
							<text class="detail-label">用户ID:</text>
							<text class="detail-value">{{selectedRecipe.userId}}</text>
						</view>
						<view class="detail-item">
							<text class="detail-label">创建时间:</text>
							<text class="detail-value">{{formatDateTime(selectedRecipe.createTime)}}</text>
						</view>
					</view>
				</view>
				<view class="modal-footer">
					<view class="action-btn" @click="useRecipe">使用配方</view>
					<view class="action-btn secondary" @click="closeRecipeModal">关闭</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		apiService
	} from '../../utils/request';

	export default {
		data() {
			return {
				records: [
					// {
					//   "id": 2,
					//   "userId": 9,
					//   "colorName": "橙色改棕灰色",
					//   "colorTypeId": 2,
					//   "colorNameId": 6,
					//   "colorComponents": "{\"red\": 6, \"blue\": null, \"gold\": null, \"gray\": null, \"black\": null, \"brown\": null, \"green\": null, \"linen\": null, \"orange\": 3, \"purple\": null, \"silver\": null, \"yellow\": 3, \"custom1\": null, \"custom2\": null, \"custom3\": null, \"custom4\": null, \"bleached\": null, \"faded_bleached\": null}",
					//   "developerComponents": "{\"hydrogenPeroxide3Percent\": 12, \"hydrogenPeroxide6Percent\": 15, \"hydrogenPeroxide9Percent\": 0, \"hydrogenPeroxide12Percent\": 0}",
					//   "beforeImageUrl": null,
					//   "afterImageUrl": null,
					//   "createTime": 1751948416000,
					//   "updateTime": 1751948416000,
					//   "deleted": 0,
					//   "formulaId": 14
					// }
				],
				showRecipeModal: false,
				selectedRecipe: null,
				searchKeyword: '',
				showFilterModal: false,
				filterType: '',
				loading: false,
				statusOptions: [{
						value: '',
						label: '全部状态'
					},
					{
						value: 'completed',
						label: '已完成'
					},
					{
						value: 'processing',
						label: '进行中'
					},
					{
						value: 'failed',
						label: '失败'
					}
				],
				deviceOptions: [{
					value: '',
					label: '全部设备'
				}],
			};
		},
		onLoad() {
			this.initRecord()
		},
		methods: {
			getFilterOptions() {
				switch (this.filterType) {
					case 'color':
						return this.colorOptions;
					case 'status':
						return this.statusOptions;
					case 'device':
						return this.deviceOptions;
					case 'maker':
						return this.getMakerOptions();
					default:
						return [];
				}
			},
			initRecord() {
				apiService.record.getRecords()
					.then(res => {
						this.records = res.data
					})
			},
			// 格式化日期时间
			formatDateTime(timestamp) {
				if (!timestamp) return '';
				const date = new Date(timestamp);
				return date.toLocaleString();
			},

			// 根据key获取颜色值
			getColorHexByKey(key) {
				const colorMap = {
					1: '#FF0000', // 红色
					2: '#FFA500', // 橙色
					3: '#FFFF00', // 黄色
					4: '#008000', // 绿色
					5: '#0000FF', // 蓝色
					6: '#800080', // 紫色
					7: '#A52A2A', // 棕色
					8: '#000000', // 黑色
					9: '#808080', // 灰色
					10: '#FFD700', // 金色
					11: '#C0C0C0', // 银色
					12: '#FAF0E6' // 亚麻色
				};
				return colorMap[key] || '#CCCCCC';
			},

			// 解析颜色构造配比数据
			parseColorComponents(colorComponents) {
				if (!colorComponents) return [];

				try {
					const components = JSON.parse(colorComponents);
					const result = [];

					// 遍历所有颜色属性
					for (const [key, value] of Object.entries(components)) {
						if (value !== null && value !== 0) { // 只显示有值的颜色
							result.push({
								name: this.getColorDisplayNameByKey(key),
								amount: value,
								color: this.getColorHexByKey(key)
							});
						}
					}

					return result;
				} catch (e) {
					console.warn('解析配比数据失败:', e);
					return [];
				}
			},

			// 解析双氧水配比数据
			parseDeveloperComponents(developerComponents) {
				if (!developerComponents) return [];

				try {
					const components = JSON.parse(developerComponents);
					const result = [];

					const nameMap = {
						'hydrogenPeroxide3Percent': '3%双氧水',
						'hydrogenPeroxide6Percent': '6%双氧水',
						'hydrogenPeroxide9Percent': '9%双氧水',
						'hydrogenPeroxide12Percent': '12%双氧水'
					};

					for (const [key, value] of Object.entries(components)) {
						if (value !== null && value !== 0) {
							result.push({
								name: nameMap[key] || key,
								amount: value
							});
						}
					}

					return result;
				} catch (e) {
					console.warn('解析双氧水数据失败:', e);
					return [];
				}
			},

			// 根据key获取颜色名称
			getColorDisplayNameByKey(key) {
				const nameMap = {
					'red': '红色',
					'blue': '蓝色',
					'gold': '金色',
					'gray': '灰色',
					'black': '黑色',
					'brown': '棕色',
					'green': '绿色',
					'linen': '亚麻色',
					'orange': '橙色',
					'purple': '紫色',
					'silver': '银色',
					'yellow': '黄色',
					'custom1': '自定义1',
					'custom2': '自定义2',
					'custom3': '自定义3',
					'custom4': '自定义4',
					'bleached': '漂白',
					'faded_bleached': '褪色漂白'
				};
				return nameMap[key] || key;
			},

			// 查看配方详情
			viewRecipeDetails(record) {
				this.selectedRecipe = record;
				this.showRecipeModal = true;
			},

			// 关闭配方详情
			closeRecipeModal() {
				this.showRecipeModal = false;
			},

			// 使用配方
			useRecipe() {
				uni.showToast({
					title: '配方已应用',
					icon: 'success'
				});
				this.closeRecipeModal();
			},

			// 分享记录
			shareRecord(record) {
				try {
					// 保存记录数据到缓存，供实际效果页面使用
					uni.setStorageSync('shareRecordData', {
						...record,
						// 添加一些额外的展示数据
						timestamp: Date.now()
					});

					// 跳转到实际效果页面
					uni.navigateTo({
						url: '/pages/my/actual-effect',
						success: () => {
							console.log('跳转到实际效果页面成功');
						},
						fail: (err) => {
							console.error('跳转失败:', err);
							uni.showToast({
								title: '跳转失败',
								icon: 'none'
							});
						}
					});
				} catch (e) {
					console.error('保存数据失败:', e);
					uni.showToast({
						title: '数据保存失败',
						icon: 'none'
					});
				}
			},

			// 搜索输入事件
			onSearchInput() {
				this.searchRecords();
			},

			// 搜索记录
			searchRecords() {
				if (!this.searchKeyword) {
					return;
				}

				const keyword = this.searchKeyword.toLowerCase();
				this.records = this.records.filter(record =>
					record.colorName.toLowerCase().includes(keyword) ||
					(record.userId && record.userId.toString().includes(keyword))
				);
			},

			// 筛选相关方法
			showColorFilter() {
				this.filterType = 'color';
				this.showFilterModal = true;
			},

			showStatusFilter() {
				this.filterType = 'status';
				this.showFilterModal = true;
			},

			showTimeFilter() {
				this.showDatePicker = true;
				// 使用 nextTick 确保 picker 组件已渲染
				this.$nextTick(() => {
					// 模拟点击 picker 来打开日期选择器
					const picker = this.$refs.datePicker;
					if (picker) {
						picker.show();
					}
				});
			},

			showDeviceFilter() {
				this.filterType = 'device';
				this.showFilterModal = true;
			},

			showMakerFilter() {
				this.filterType = 'maker';
				this.showFilterModal = true;
			},

			// 选择筛选项
			selectFilterOption(value) {
				if (this.filterType === 'color') {
					this.filterOptions.colorCode = value;
				} else if (this.filterType === 'status') {
					this.filterOptions.status = value;
				} else if (this.filterType === 'device') {
					this.filterOptions.deviceCode = value;
				} else if (this.filterType === 'maker') {
					this.filterOptions.author = value;
				}

				this.showFilterModal = false;

				// 如果是时间筛选，需要重新加载数据
				if (this.filterType === 'time') {
					this.loadColorRecords();
				} else {
					// 其他筛选只需要应用前端筛选
					this.applyFilters();
				}
			},

			// 日期选择
			onDateChange(e) {
				this.selectedDate = e.detail.value;
				this.filterOptions.timeRange = e.detail.value;
				this.showDatePicker = false;
				this.loadColorRecords(); // 时间筛选需要重新加载数据
			},
		}
	};
</script>

<style lang="scss">
	.container {
		display: flex;
		flex-direction: column;
		min-height: 100vh;
		background-color: #f5f5f5;
	}

	.search-bar {
		display: flex;
		padding: 20rpx;
		background-color: #fff;
	}

	.search-input {
		flex: 1;
		display: flex;
		align-items: center;
		background-color: #f0f0f0;
		border-radius: 30rpx;
		padding: 10rpx 20rpx;
		margin-right: 20rpx;
	}

	.search-icon {
		margin-right: 10rpx;
		color: #999;
	}

	.search-btn {
		padding: 10rpx 30rpx;
		background-color: #4285f4;
		color: #fff;
		border-radius: 30rpx;
		font-size: 28rpx;
	}

	/* 调色图片样式 */
	.record-image-container {
		width: 120rpx;
		height: 120rpx;
		/* 保持正方形 */
		border-radius: 12rpx;
		/* 较小的圆角 */
		overflow: hidden;
		margin-right: 20rpx;
		flex-shrink: 0;
		background-color: #f5f5f5;
		display: flex;
		align-items: center;
		justify-content: center;
		border: 1rpx solid #eee;
	}

	/* 筛选弹窗样式 */
	.filter-modal {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: rgba(0, 0, 0, 0.5);
		display: flex;
		justify-content: center;
		align-items: center;
		z-index: 1000;
	}
	
	.filter-content {
		width: 80%;
		max-height: 60%;
		background-color: #fff;
		border-radius: 10rpx;
		overflow: hidden;
	}
	
	.filter-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 30rpx;
		background-color: #4285f4;
		color: #fff;
	}
	
	.filter-title {
		font-size: 32rpx;
		font-weight: 500;
	}
	
	.filter-options {
		max-height: 400rpx;
		overflow-y: auto;
	}
	
	.filter-option {
		padding: 30rpx;
		border-bottom: 1rpx solid #f0f0f0;
		font-size: 30rpx;
		color: #333;
	}
	
	.filter-option:last-child {
		border-bottom: none;
	}
	
	.filter-option:active {
		background-color: #f5f5f5;
	}

	.record-image {
		width: 100%;
		height: 100%;
	}

	/* 调色记录列表样式 */
	.record-list {
		flex: 1;
		background-color: #fff;
	}

	.record-item {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 30rpx 20rpx;
		border-bottom: 1rpx solid #f0f0f0;
		background-color: #fff;
	}

	.record-left {
		display: flex;
		align-items: center;
		flex: 1;
	}

	.record-color {
		width: 100rpx;
		height: 100rpx;
		border-radius: 10rpx;
		margin-right: 20rpx;
		flex-shrink: 0;
	}

	.record-info {
		flex: 1;
	}

	.record-title {
		font-size: 32rpx;
		font-weight: 500;
		color: #333;
		margin-bottom: 8rpx;
	}

	.record-time {
		font-size: 26rpx;
		color: #999;
	}

	.record-components {
		display: flex;
		flex-wrap: wrap;
		margin-top: 10rpx;
	}

	.component-tag {
		font-size: 22rpx;
		padding: 4rpx 12rpx;
		background-color: #f0f0f0;
		border-radius: 20rpx;
		margin-right: 10rpx;
		margin-bottom: 6rpx;
		color: #666;
	}

	.record-right {
		flex-shrink: 0;
	}

	.share-btn {
		background-color: #4285f4;
		color: #fff;
		padding: 12rpx 24rpx;
		border-radius: 20rpx;
		font-size: 26rpx;
	}

	/* 空状态提示 */
	.empty-state {
		flex: 1;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding: 100rpx 0;
	}

	/* 在空状态样式中添加默认图片样式 */
	.empty-image {
		width: 300rpx;
		height: 200rpx;
		margin-bottom: 30rpx;
		opacity: 0.7;
	}

	.empty-text {
		font-size: 28rpx;
		color: #999;
	}

	/* 配方详情弹窗样式 */
	.recipe-modal {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: rgba(0, 0, 0, 0.5);
		display: flex;
		justify-content: center;
		align-items: center;
		z-index: 999;
	}

	.modal-content {
		width: 80%;
		max-height: 80%;
		background-color: #fff;
		border-radius: 10rpx;
		overflow: hidden;
		display: flex;
		flex-direction: column;
	}

	.modal-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 20rpx;
		background-color: #4285f4;
		color: #fff;
	}

	.modal-title {
		font-size: 32rpx;
		font-weight: 500;
	}

	.close-btn {
		font-size: 40rpx;
		line-height: 1;
	}

	.recipe-content {
		padding: 30rpx;
		overflow-y: auto;
	}

	/* 配方详情弹窗中的图片样式 */
	.recipe-color-preview {
		width: 120rpx;
		height: 120rpx;
		border-radius: 12rpx;
		margin: 0 auto 30rpx;
		border: 1rpx solid #ddd;
		box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
	}

	.recipe-name {
		font-size: 36rpx;
		font-weight: bold;
		margin-bottom: 30rpx;
	}

	.color-components {
		margin-bottom: 30rpx;
		padding: 20rpx;
		background-color: #f8f9fa;
		border-radius: 8rpx;
	}

	.components-title {
		font-size: 28rpx;
		font-weight: bold;
		margin-bottom: 15rpx;
		color: #333;
	}

	.component-item {
		display: flex;
		align-items: center;
		padding: 10rpx 0;
		border-bottom: 1rpx solid #eee;
	}

	.component-item:last-child {
		border-bottom: none;
	}

	.component-color {
		width: 30rpx;
		height: 30rpx;
		border-radius: 4rpx;
		margin-right: 15rpx;
		border: 1rpx solid #ddd;
	}

	.component-name {
		flex: 1;
		font-size: 26rpx;
		color: #333;
	}

	.component-amount {
		font-size: 26rpx;
		color: #666;
		font-weight: 500;
	}

	.usage-details {
		margin-bottom: 30rpx;
		padding: 20rpx;
		background-color: #f8f9fa;
		border-radius: 8rpx;
	}

	.detail-item {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 8rpx 0;
		border-bottom: 1rpx solid #eee;
	}

	.detail-item:last-child {
		border-bottom: none;
	}

	.detail-label {
		font-size: 26rpx;
		color: #666;
	}

	.detail-value {
		font-size: 26rpx;
		color: #333;
		font-weight: 500;
	}

	.modal-footer {
		display: flex;
		padding: 20rpx;
		border-top: 1rpx solid #eee;
	}

	.action-btn {
		flex: 1;
		text-align: center;
		padding: 15rpx 0;
		background-color: #4285f4;
		color: #fff;
		border-radius: 6rpx;
		margin: 0 10rpx;
	}

	.action-btn.secondary {
		background-color: #f5f5f5;
		color: #666;
	}

	.filter-bar {
		display: flex;
		padding: 20rpx 10rpx;
		background-color: #fff;
		border-top: 1rpx solid #eee;
		border-bottom: 1rpx solid #eee;
		overflow-x: auto;
		white-space: nowrap;
	}

	.filter-item {
		display: flex;
		align-items: center;
		margin: 0 15rpx;
		font-size: 26rpx;
		color: #666;
	}
</style>