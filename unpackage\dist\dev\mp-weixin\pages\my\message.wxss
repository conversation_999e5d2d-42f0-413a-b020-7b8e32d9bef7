/* 水平间距 */
/* 水平间距 */
/* 全局样式修复 - 确保页面占满屏幕宽度 */
page {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
}

/* 通用容器样式 */
.container, .page-container {
  width: 100%;
  min-height: 100vh;
  box-sizing: border-box;
}

/* 修复小程序页面宽度问题 */
uni-page-body {
  width: 100% !important;
}

/* 确保所有view元素正确继承宽度 */
view {
  box-sizing: border-box;
}
.container {
  min-height: 100vh;
  background-color: #f5f5f5;
}
.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx;
  background-color: #4285f4;
  color: #fff;
}
.back-btn {
  font-size: 40rpx;
  width: 60rpx;
  display: flex;
  align-items: center;
}
.title {
  font-size: 36rpx;
  font-weight: 500;
}
.header-icons {
  display: flex;
  align-items: center;
}
.header-icon {
  margin-left: 15rpx;
  font-size: 24rpx;
}
.message-tabs {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  background-color: #4285f4;
  color: #fff;
}
.unread-tab {
  font-size: 30rpx;
}
.read-all-btn {
  font-size: 30rpx;
  display: flex;
  align-items: center;
}
.read-all-btn:before {
  content: "";
  display: inline-block;
  width: 40rpx;
  height: 40rpx;
  margin-right: 6rpx;
  background-size: contain;
}
.empty-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400rpx;
}
.empty-text {
  margin-top: 30rpx;
  font-size: 30rpx;
  color: #999;
}