/* 水平间距 */
/* 水平间距 */
/* 全局样式修复 - 确保页面占满屏幕宽度 */
page {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
}

/* 通用容器样式 */
.container, .page-container {
  width: 100%;
  min-height: 100vh;
  box-sizing: border-box;
}

/* 修复小程序页面宽度问题 */
uni-page-body {
  width: 100% !important;
}

/* 确保所有view元素正确继承宽度 */
view {
  box-sizing: border-box;
}
.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f5f5;
}
.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx;
  background-color: #4285f4;
  color: #fff;
}
.back-btn {
  font-size: 40rpx;
  width: 60rpx;
  display: flex;
  align-items: center;
}
.title {
  font-size: 36rpx;
  font-weight: 500;
}
.header-icons {
  display: flex;
  align-items: center;
}
.header-icon {
  margin-left: 15rpx;
  font-size: 24rpx;
}
.staff-section-title {
  padding: 30rpx 20rpx;
  font-size: 30rpx;
  color: #333;
  font-weight: 500;
}
.staff-list {
  background-color: #fff;
  flex: 1;
}
.empty-staff {
  padding: 100rpx 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.empty-text {
  font-size: 28rpx;
  color: #999;
}
.staff-item {
  padding: 25rpx 20rpx;
  display: flex;
  align-items: center;
  border-bottom: 1rpx solid #f0f0f0;
  background-color: #fff;
}
.staff-avatar {
  width: 90rpx;
  height: 90rpx;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 25rpx;
  flex-shrink: 0;
}
.avatar-placeholder {
  width: 100%;
  height: 100%;
  background-color: #d0d0d0;
  border-radius: 50%;
}
.staff-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}
.staff-name {
  font-size: 30rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 8rpx;
  line-height: 1.2;
}
.staff-phone {
  font-size: 26rpx;
  color: #666;
  line-height: 1.2;
}
.staff-role-tag {
  padding: 6rpx 12rpx;
  background-color: transparent;
  border-radius: 0;
  border: none;
  flex-shrink: 0;
}
.role-text {
  font-size: 26rpx;
  color: #666;
  font-weight: 400;
}
.staff-actions {
  display: flex;
}
.action-btn {
  margin-left: 20rpx;
  padding: 8rpx 20rpx;
  font-size: 24rpx;
  border-radius: 30rpx;
}
.action-btn.edit {
  background-color: #e3f2fd;
  color: #2196f3;
}
.action-btn.delete {
  background-color: #ffebee;
  color: #f44336;
}
.add-btn {
  position: fixed;
  bottom: 40rpx;
  left: 40rpx;
  right: 40rpx;
  height: 90rpx;
  background-color: #4285f4;
  border-radius: 45rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #fff;
  font-size: 32rpx;
}