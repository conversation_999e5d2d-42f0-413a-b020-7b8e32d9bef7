<view class="user-home"><view class="user-info"><view class="user-basic"><image class="user-avatar" src="{{a}}"></image><text class="username">{{b}}</text><button class="edit-btn" bindtap="{{c}}">编辑资料</button></view><view class="user-desc">用户简介</view><view class="user-stats"><view class="stat-item"><text class="stat-num">{{d}}</text><text class="stat-label">转发</text></view><view class="stat-item"><text class="stat-num">{{e}}</text><text class="stat-label">赞</text></view><view class="stat-item"><text class="stat-num">{{f}}</text><text class="stat-label">关注</text></view><view class="stat-item"><text class="stat-num">{{g}}</text><text class="stat-label">粉丝</text></view></view></view><view class="content-tabs"><view wx:for="{{h}}" wx:for-item="tab" wx:key="b" class="{{['tab-item', tab.c]}}" bindtap="{{tab.d}}">{{tab.a}}</view></view><view class="content-grid"><view wx:for="{{i}}" wx:for-item="item" wx:key="f" class="grid-item" bindtap="{{item.g}}"><image wx:if="{{item.a}}" class="grid-image" src="{{item.b}}" mode="aspectFill"></image><view class="grid-info"><text class="grid-text">{{item.c}}</text><view class="grid-meta"><text class="meta-user">{{item.d}}</text><text class="meta-distance">{{item.e}}km</text></view></view></view></view><view class="fab" bindtap="{{j}}"><text class="fab-icon">+</text></view><view wx:if="{{k}}" class="modal-mask" bindtap="{{x}}"><view class="modal-container" catchtap="{{w}}"><view class="modal-header"><text class="modal-title">发布动态</text><text class="modal-close" bindtap="{{l}}">×</text></view><view class="modal-content"><block wx:if="{{r0}}"><textarea class="content-input" placeholder="内容笔记..." auto-height value="{{m}}" bindinput="{{n}}"></textarea></block><view class="image-upload"><view class="upload-title">添加图片</view><view class="image-preview-container"><view wx:for="{{o}}" wx:for-item="image" wx:key="c" class="image-preview"><image class="preview-image" src="{{image.a}}" mode="aspectFill"></image><view class="remove-image" catchtap="{{image.b}}">×</view></view><view wx:if="{{p}}" class="upload-btn" bindtap="{{q}}"><text class="upload-icon">+</text></view></view><text class="upload-tip">最多可上传9张图片</text></view><view class="insert-product" bindtap="{{r}}">插入商品（当前账号不支持）</view><view class="subject-grid"><view wx:for="{{s}}" wx:for-item="row" wx:key="b" class="subject-row"><view wx:for="{{row.a}}" wx:for-item="subject" wx:key="b" class="{{['subject-item', subject.c && 'selected']}}" bindtap="{{subject.d}}">{{subject.a}}</view></view></view></view><view class="modal-footer"><button class="cancel-btn" bindtap="{{t}}">取消</button><button class="confirm-btn" bindtap="{{v}}">发布</button></view></view></view></view>