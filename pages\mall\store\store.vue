<template>
	<view class="store-container">
		<!-- 店铺头部信息 -->
		<view class="store-header">
			<!-- 红色渐变背景 -->
			<view class="store-bg-gradient">
				<view class="store-info">
					<view class="store-detail">
						<text class="store-name">{{storeInfo.name}}</text>
						<text class="store-rating-badge">{{(storeInfo.rating.products || 4.1).toFixed(1)}}分</text>
						<text class="store-license">营业执照：{{storeInfo.description || 'Official flagship store'}}</text>
					</view>
					<view class="follow-btn" :class="{'followed': isFollowed, 'loading': followLoading}" @click="followStore">
						<text v-if="!followLoading">{{isFollowed ? '已关注' : '+ 关注'}}</text>
						<text v-else>处理中...</text>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 分类导航 -->
		<view class="category-nav">
			<view class="nav-item active">全部商品</view>
			<view class="nav-item">新品上架</view>
			<view class="nav-item">热销商品</view>
			<view class="nav-item">优惠活动</view>
		</view>
		
		<!-- 筛选栏 -->
		<view class="filter-bar">
			<view class="filter-item">
				<text>综合</text>
				<text class="arrow">▼</text>
			</view>
			<view class="filter-item">
				<text>销量</text>
				<text class="arrow">▼</text>
			</view>
			<view class="filter-item">
				<text>价格</text>
				<text class="arrow">▼</text>
			</view>
		</view>
		
		<!-- 商品列表 -->
		<view class="product-list">
			<!-- 加载状态 -->
			<view v-if="loading" class="loading-container">
				<text>加载中...</text>
			</view>

			<!-- 商品列表 -->
			<view v-else-if="storeProducts.length > 0" class="product-grid">
				<view class="product-item" v-for="(product, index) in storeProducts" :key="index" @click="goToProduct(product.id)">
					<image class="product-image" :src="product.image" mode="aspectFill"></image>
					<view class="product-info">
						<text class="product-name">{{product.name}}</text>
						<view class="product-price-row">
							<text class="product-price">¥{{product.price}}</text>
							<text class="product-sold">已售{{product.soldCount}}</text>
						</view>
					</view>
				</view>
			</view>

			<!-- 空状态 -->
			<view v-else class="empty-container">
				<text>该店铺暂无商品</text>
			</view>
		</view>
	</view>
</template>

<script>
import { apiService } from '@/utils/request.js';

export default {
	data() {
		return {
			storeId: null,
			isFollowed: false,
			loading: false,
			followLoading: false,
			storeInfo: {
				id: null,
				name: '',
				logo: '',
				description: '',
				productCount: 0,
				followerCount: 0,
				rating: {
					products: 0,
					service: 0,
					logistics: 0
				}
			},
			storeProducts: []
		}
	},
	onLoad(options) {
		if (options.id) {
			this.storeId = options.id;
			// 加载店铺信息
			this.loadStoreInfo();
			this.loadStoreProducts();
			this.checkFollowStatus();
		}
	},
	onReady() {
		// 设置导航栏样式 - 使用稍深的红色与渐变更好融合
		uni.setNavigationBarColor({
			frontColor: '#ffffff',
			backgroundColor: '#c62828'
		});

		// 设置导航栏标题为空或简单标题
		uni.setNavigationBarTitle({
			title: ''
		});
	},
	methods: {
		async loadStoreInfo() {
			if (!this.storeId) return;

			this.loading = true;
			try {
				const response = await apiService.mall.stores.detail(this.storeId);

				if (response.code === 200) {
					const storeData = response.data;

					// 检查店铺数据是否存在
					if (!storeData) {
						console.error('店铺数据为空');
						uni.showToast({
							title: '店铺不存在',
							icon: 'none'
						});
						return;
					}

					this.storeInfo = {
						id: storeData.id || null,
						name: storeData.name || storeData.storeName || '',
						logo: storeData.logo || '/static/images/shop-logo.jpg',
						description: storeData.description || '',
						productCount: storeData.productCount || 0,
						followerCount: storeData.followerCount || 0,
						rating: {
							products: storeData.rating?.products || 0,
							service: storeData.rating?.service || 0,
							logistics: storeData.rating?.logistics || 0
						}
					};

					console.log('店铺信息加载成功:', this.storeInfo);
				} else {
					console.error('获取店铺信息失败:', response.message);

					// 如果是店铺不存在的错误，显示友好提示并返回
					if (response.message && response.message.includes('店铺不存在')) {
						uni.showModal({
							title: '提示',
							content: '该店铺不存在或已关闭，即将返回上一页',
							showCancel: false,
							success: () => {
								setTimeout(() => {
									uni.navigateBack({
										fail: () => {
											// 如果无法返回上一页，跳转到首页
											uni.switchTab({
												url: '/pages/index/index'
											});
										}
									});
								}, 1500);
							}
						});
					} else {
						uni.showToast({
							title: response.message || '获取店铺信息失败',
							icon: 'none'
						});
					}
				}
			} catch (error) {
				console.error('获取店铺信息异常:', error);

				// 如果是店铺不存在的错误，显示更友好的提示并返回上一页
				if (error.message && error.message.includes('店铺不存在')) {
					uni.showModal({
						title: '提示',
						content: '该店铺不存在或已关闭，即将返回上一页',
						showCancel: false,
						success: () => {
							setTimeout(() => {
								uni.navigateBack({
									fail: () => {
										// 如果无法返回上一页，跳转到首页
										uni.switchTab({
											url: '/pages/index/index'
										});
									}
								});
							}, 1500);
						}
					});
				} else {
					uni.showToast({
						title: '网络异常，请稍后重试',
						icon: 'none'
					});
				}
			} finally {
				this.loading = false;
			}
		},

		async loadStoreProducts() {
			if (!this.storeId) return;

			// 只有在店铺信息加载完成后才显示加载状态
			if (!this.loading) {
				this.loading = true;
			}

			try {
				// 使用正确的参数名storeId，对应后端的storeId参数
				const response = await apiService.mall.products.list({
					storeId: this.storeId,
					page: 1,
					pageSize: 10
				});

				if (response.code === 200) {
					const products = response.data.products || response.data.records || response.data || [];
					this.storeProducts = products.map(product => ({
						id: product.id,
						name: product.productName || product.name,
						price: product.price,
						soldCount: product.soldNum || product.sales || 0,
						image: this.getProductImage(product)
					}));

					// 更新店铺商品数量
					if (this.storeInfo) {
						this.storeInfo.productCount = products.length;
					}

					console.log('店铺商品加载成功:', this.storeProducts);
				} else {
					console.error('获取店铺商品失败:', response.message);
					uni.showToast({
						title: '获取商品失败',
						icon: 'none'
					});
				}
			} catch (error) {
				console.error('获取店铺商品异常:', error);
				uni.showToast({
					title: '网络异常',
					icon: 'none'
				});
			} finally {
				this.loading = false;
			}
		},

		getProductImage(product) {
			if (!product.images) return '/static/images/product1.jpg';
			let imageUrl = '';
			if (typeof product.images === 'string') {
				const imageArray = product.images.split(',');
				imageUrl = imageArray[0] || '/static/images/product1.jpg';
			} else {
				imageUrl = product.images[0] || '/static/images/product1.jpg';
			}

			// 处理完整的图片URL
			return this.getFullImageUrl(imageUrl);
		},

		// 获取完整的图片URL
		getFullImageUrl(url) {
			if (!url) return '';
			if (url.startsWith('http')) {
				return url; // 已经是完整URL
			}
			if (url.startsWith('/static') || url.startsWith('/uploads')) {
				return 'https://www.narenqiqige.com' + url; // 添加服务器基础URL
			}
			// 如果不是以/开头，添加/uploads/前缀（假设是相对路径的图片文件名）
			if (!url.startsWith('/')) {
				return 'https://www.narenqiqige.com/api/uploads/' + url;
			}
			return url;
		},
			async checkFollowStatus() {
				if (!this.storeId) return;

				try {
					const response = await apiService.mall.storeFollow.check(this.storeId);
					if (response.code === 200) {
						this.isFollowed = response.data;
					}
				} catch (error) {
					console.error('检查关注状态失败:', error);
				}
			},

			async followStore() {
				if (this.followLoading) return;

				this.followLoading = true;
				try {
					let response;
					if (this.isFollowed) {
						// 取消关注
						response = await apiService.mall.storeFollow.unfollow(this.storeId);
					} else {
						// 关注店铺
						response = await apiService.mall.storeFollow.follow(this.storeId);
					}

					if (response.code === 200) {
						this.isFollowed = !this.isFollowed;
						// 更新关注数量
						if (this.isFollowed) {
							this.storeInfo.followerCount++;
						} else {
							this.storeInfo.followerCount--;
						}

						uni.showToast({
							title: response.message || (this.isFollowed ? '关注成功' : '取消关注成功'),
							icon: 'success'
						});
					} else {
						uni.showToast({
							title: response.message || '操作失败',
							icon: 'none'
						});
					}
				} catch (error) {
					console.error('关注操作失败:', error);
					uni.showToast({
						title: '网络异常，请稍后重试',
						icon: 'none'
					});
				} finally {
					this.followLoading = false;
				}
			},
			goToProduct(productId) {
				uni.navigateTo({
					url: '/pages/mall/product/detail?id=' + productId
				});
			}
		}
	}
</script>

<style lang="scss">
	.store-container {
		background-color: #f5f5f5;
		min-height: 100vh;
	}
	
	.store-header {
		position: relative;
		margin-bottom: 20rpx;
	}

	.store-bg-gradient {
		background: linear-gradient(180deg, #c62828 0%, #d32f2f 30%, #b71c1c 100%);
		padding: 20rpx 30rpx 30rpx;
		position: relative;
		overflow: hidden;
		margin-top: -10rpx;
	}

	.store-bg-gradient::before {
		content: '';
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 100%);
		pointer-events: none;
	}

	.store-info {
		display: flex;
		align-items: flex-start;
		justify-content: space-between;
		position: relative;
		z-index: 1;
	}

	.store-detail {
		flex: 1;
		color: #ffffff;
	}

	.store-name {
		font-size: 36rpx;
		font-weight: bold;
		margin-bottom: 8rpx;
		color: #ffffff;
	}

	.store-rating-badge {
		display: inline-block;
		background-color: rgba(255, 165, 0, 0.9);
		color: #ffffff;
		font-size: 24rpx;
		padding: 4rpx 12rpx;
		border-radius: 20rpx;
		margin-bottom: 12rpx;
		font-weight: bold;
	}

	.store-license {
		font-size: 26rpx;
		color: rgba(255, 255, 255, 0.9);
		line-height: 1.4;
	}

	.follow-btn {
		background-color: rgba(255, 255, 255, 0.2);
		color: #ffffff;
		padding: 12rpx 24rpx;
		border-radius: 30rpx;
		font-size: 28rpx;
		border: 2rpx solid rgba(255, 255, 255, 0.3);
		transition: all 0.3s ease;
		backdrop-filter: blur(10rpx);
	}

	.follow-btn.followed {
		background-color: rgba(255, 255, 255, 0.9);
		color: #d32f2f;
		border-color: rgba(255, 255, 255, 0.9);
	}

	.follow-btn.loading {
		background-color: rgba(255, 255, 255, 0.1);
		opacity: 0.7;
	}
	
	.category-nav {
		display: flex;
		background-color: #ffffff;
		padding: 0 30rpx;
		border-bottom: 1rpx solid #f0f0f0;
	}

	.nav-item {
		padding: 25rpx 20rpx;
		font-size: 30rpx;
		position: relative;
		color: #999999;
		margin-right: 40rpx;
	}

	.nav-item.active {
		color: #333333;
		font-weight: 500;
	}

	.nav-item.active:after {
		content: '';
		position: absolute;
		bottom: 0;
		left: 50%;
		transform: translateX(-50%);
		width: 40rpx;
		height: 4rpx;
		background-color: #d32f2f;
		border-radius: 2rpx;
	}

	.filter-bar {
		display: flex;
		background-color: #ffffff;
		padding: 20rpx 30rpx;
		margin-bottom: 20rpx;
		border-bottom: 1rpx solid #f0f0f0;
	}

	.filter-item {
		flex: 1;
		display: flex;
		justify-content: center;
		align-items: center;
		font-size: 28rpx;
		color: #666666;
	}

	.arrow {
		font-size: 20rpx;
		margin-left: 8rpx;
		color: #999999;
		transition: transform 0.3s ease;
	}
	
	.product-list {
		padding: 0 15rpx;
		background-color: #ffffff;
	}

	.loading-container, .empty-container {
		text-align: center;
		padding: 100rpx 0;
		color: #999999;
		font-size: 28rpx;
		background-color: #ffffff;
	}

	.product-grid {
		display: flex;
		flex-wrap: wrap;
		justify-content: space-between;
		padding: 20rpx 15rpx;
	}

	.product-item {
		width: 48%;
		background-color: #ffffff;
		margin-bottom: 20rpx;
		border-radius: 12rpx;
		overflow: hidden;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
		border: 1rpx solid #f5f5f5;
	}

	.product-image {
		width: 100%;
		height: 320rpx;
		background-color: #f8f8f8;
	}

	.product-info {
		padding: 20rpx 15rpx;
	}

	.product-name {
		font-size: 28rpx;
		color: #333333;
		line-height: 1.4;
		height: 80rpx;
		overflow: hidden;
		text-overflow: ellipsis;
		display: -webkit-box;
		-webkit-line-clamp: 2;
		-webkit-box-orient: vertical;
		margin-bottom: 12rpx;
	}

	.product-price-row {
		display: flex;
		justify-content: space-between;
		align-items: flex-end;
	}

	.product-price {
		font-size: 32rpx;
		color: #d32f2f;
		font-weight: bold;
	}

	.product-sold {
		font-size: 24rpx;
		color: #999999;
	}
</style> 