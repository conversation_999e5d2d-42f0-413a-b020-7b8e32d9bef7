"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_request = require("../../utils/request.js");
const utils_imageUtils = require("../../utils/imageUtils.js");
const common_assets = require("../../common/assets.js");
const _sfc_main = {
  data() {
    return {
      isManageMode: false,
      cartItems: [],
      loading: false,
      checkoutLoading: false
    };
  },
  computed: {
    allSelected() {
      return this.cartItems.length > 0 && this.cartItems.every((item) => item.selected);
    },
    selectedCount() {
      return this.cartItems.filter((item) => item.selected).length;
    },
    totalPrice() {
      return this.cartItems.filter((item) => item.selected).reduce((total, item) => total + item.price * item.quantity, 0).toFixed(2);
    }
  },
  onLoad() {
    this.loadCartItems();
  },
  onShow() {
    this.checkoutLoading = false;
  },
  methods: {
    // 加载购物车数据
    async loadCartItems() {
      this.loading = true;
      try {
        const response = await utils_request.apiService.mall.cart.list();
        if (response.code === 200) {
          this.cartItems = (response.data || []).map((item) => ({
            ...item,
            selected: item.checked === 1,
            image: utils_imageUtils.getFullImageUrl(item.imageUrl || ""),
            name: item.productName || "商品名称",
            price: item.price || 0,
            spec: item.skuName || "默认规格"
          }));
        } else {
          common_vendor.index.__f__("error", "at pages/cart/cart.vue:127", "获取购物车失败:", response.message);
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/cart/cart.vue:130", "获取购物车异常:", error);
        const cartItems = common_vendor.index.getStorageSync("cartItems");
        if (cartItems && cartItems.length > 0) {
          this.cartItems = cartItems;
        }
      } finally {
        this.loading = false;
      }
    },
    toggleManageMode() {
      this.isManageMode = !this.isManageMode;
    },
    async toggleSelect(index) {
      const item = this.cartItems[index];
      const newSelected = !item.selected;
      try {
        const response = await utils_request.apiService.mall.cart.select({
          cartId: item.id,
          selected: newSelected
        });
        if (response.code === 200) {
          this.cartItems[index].selected = newSelected;
        } else {
          common_vendor.index.showToast({
            title: "操作失败",
            icon: "none"
          });
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/cart/cart.vue:164", "选择商品失败:", error);
        this.cartItems[index].selected = newSelected;
      }
    },
    async toggleSelectAll() {
      const newStatus = !this.allSelected;
      const promises = this.cartItems.map(
        (item) => utils_request.apiService.mall.cart.select({
          cartId: item.id,
          selected: newStatus
        }).catch((error) => {
          common_vendor.index.__f__("error", "at pages/cart/cart.vue:179", "批量选择失败:", error);
          return null;
        })
      );
      try {
        await Promise.all(promises);
        this.cartItems.forEach((item) => {
          item.selected = newStatus;
        });
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/cart/cart.vue:190", "批量选择异常:", error);
        this.cartItems.forEach((item) => {
          item.selected = newStatus;
        });
      }
    },
    async decreaseQuantity(index) {
      const item = this.cartItems[index];
      if (item.quantity <= 1) {
        common_vendor.index.showToast({
          title: "商品数量不能少于1件",
          icon: "none"
        });
        return;
      }
      const newQuantity = item.quantity - 1;
      try {
        const response = await utils_request.apiService.mall.cart.update({
          cartId: item.id,
          quantity: newQuantity
        });
        if (response.code === 200) {
          this.cartItems[index].quantity = newQuantity;
        } else {
          common_vendor.index.showToast({
            title: response.message || "更新失败",
            icon: "none"
          });
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/cart/cart.vue:225", "更新数量失败:", error);
        common_vendor.index.showToast({
          title: "网络连接错误",
          icon: "none"
        });
      }
    },
    async increaseQuantity(index) {
      const item = this.cartItems[index];
      const newQuantity = item.quantity + 1;
      if (item.stock && newQuantity > item.stock) {
        common_vendor.index.showModal({
          title: "库存不足",
          content: `该商品库存仅剩${item.stock}件，无法继续添加`,
          showCancel: false,
          confirmText: "知道了"
        });
        return;
      }
      try {
        const response = await utils_request.apiService.mall.cart.update({
          cartId: item.id,
          quantity: newQuantity
        });
        if (response.code === 200) {
          this.cartItems[index].quantity = newQuantity;
          this.calculateTotal();
        } else if (response.code === 500 && response.message && response.message.includes("库存")) {
          common_vendor.index.showModal({
            title: "库存不足",
            content: response.message,
            showCancel: false,
            confirmText: "知道了"
          });
        } else {
          common_vendor.index.showToast({
            title: response.message || "更新失败",
            icon: "none"
          });
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/cart/cart.vue:272", "更新数量失败:", error);
        if (error.message && error.message.includes("timeout")) {
          common_vendor.index.showToast({
            title: "请求超时，请重试",
            icon: "none"
          });
        } else {
          common_vendor.index.showToast({
            title: "操作失败，请重试",
            icon: "none"
          });
        }
      }
    },
    checkout() {
      if (this.checkoutLoading) {
        return;
      }
      if (this.selectedCount === 0) {
        common_vendor.index.showToast({
          title: "请选择要结算的商品",
          icon: "none"
        });
        return;
      }
      this.checkoutLoading = true;
      const selectedItems = this.cartItems.filter((item) => item.selected);
      const orderData = {
        type: "cart",
        products: selectedItems.map((item) => ({
          id: item.productId || item.id,
          name: item.name,
          price: item.price,
          quantity: item.quantity,
          skuId: item.skuId,
          skuName: item.spec,
          images: item.image,
          cartId: item.id
        }))
      };
      common_vendor.index.navigateTo({
        url: "/pages/order/confirm?data=" + encodeURIComponent(JSON.stringify(orderData))
      });
    },
    async deleteSelected() {
      if (this.selectedCount === 0) {
        common_vendor.index.showToast({
          title: "请选择要删除的商品",
          icon: "none"
        });
        return;
      }
      common_vendor.index.showModal({
        title: "提示",
        content: "确定要删除选中的商品吗？",
        success: async (res) => {
          if (res.confirm) {
            const selectedItems = this.cartItems.filter((item) => item.selected);
            const promises = selectedItems.map(
              (item) => utils_request.apiService.mall.cart.delete(item.id).catch((error) => {
                common_vendor.index.__f__("error", "at pages/cart/cart.vue:345", "删除商品失败:", error);
                return null;
              })
            );
            try {
              await Promise.all(promises);
              this.cartItems = this.cartItems.filter((item) => !item.selected);
              common_vendor.index.showToast({
                title: "删除成功",
                icon: "success"
              });
              if (this.cartItems.length === 0) {
                this.isManageMode = false;
              }
            } catch (error) {
              common_vendor.index.__f__("error", "at pages/cart/cart.vue:363", "批量删除异常:", error);
              this.cartItems = this.cartItems.filter((item) => !item.selected);
              common_vendor.index.showToast({
                title: "删除成功",
                icon: "success"
              });
              if (this.cartItems.length === 0) {
                this.isManageMode = false;
              }
            }
          }
        }
      });
    },
    goShopping() {
      common_vendor.index.switchTab({
        url: "/pages/mall/mall"
      });
    },
    // 计算总价
    calculateTotal() {
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.cartItems.length > 0
  }, $data.cartItems.length > 0 ? common_vendor.e({
    b: common_vendor.t($data.isManageMode ? "完成" : "管理"),
    c: common_vendor.o((...args) => $options.toggleManageMode && $options.toggleManageMode(...args)),
    d: common_vendor.f($data.cartItems, (item, index, i0) => {
      return common_vendor.e({
        a: item.selected,
        b: common_vendor.o(($event) => $options.toggleSelect(index), index),
        c: item.image,
        d: common_vendor.t(item.name),
        e: common_vendor.t(item.spec),
        f: item.stock
      }, item.stock ? {
        g: common_vendor.t(item.stock)
      } : {}, {
        h: common_vendor.t(item.price),
        i: common_vendor.o(($event) => $options.decreaseQuantity(index), index),
        j: item.quantity,
        k: common_vendor.o(($event) => $options.increaseQuantity(index), index),
        l: index
      });
    }),
    e: $options.allSelected,
    f: common_vendor.o((...args) => $options.toggleSelectAll && $options.toggleSelectAll(...args)),
    g: !$data.isManageMode
  }, !$data.isManageMode ? {
    h: common_vendor.t($options.totalPrice)
  } : {}, {
    i: !$data.isManageMode
  }, !$data.isManageMode ? {
    j: common_vendor.t($options.selectedCount),
    k: common_vendor.o((...args) => $options.checkout && $options.checkout(...args))
  } : {
    l: common_vendor.o((...args) => $options.deleteSelected && $options.deleteSelected(...args))
  }) : {
    m: common_assets._imports_0$4,
    n: common_vendor.o((...args) => $options.goShopping && $options.goShopping(...args))
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/cart/cart.js.map
