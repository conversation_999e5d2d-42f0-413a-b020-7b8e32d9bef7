<template>
  <view class="container">

    <!-- 数据概览 -->
    <view class="data-overview">
      <view class="overview-item">
        <text class="time-range">近1个月</text>
        <text class="data-value">{{ usageStats.oneMonthUsage || 0 }}<text class="unit">次</text></text>
      </view>
      <view class="overview-item">
        <text class="time-range">近3个月</text>
        <text class="data-value">{{ usageStats.threeMonthsUsage || 0 }}<text class="unit">次</text></text>
      </view>
      <view class="overview-item">
        <text class="time-range">近半年</text>
        <text class="data-value">{{ usageStats.sixMonthsUsage || 0 }}<text class="unit">次</text></text>
      </view>
      <view class="overview-item">
        <text class="time-range">染发次数</text>
        <text class="data-value">{{ usageStats.totalUsage || 0 }}<text class="unit">次</text></text>
      </view>
      <view class="overview-item">
        <text class="time-range">使用天数</text>
        <text class="data-value">{{ usageStats.usageDays || 0 }}<text class="unit">天</text></text>
      </view>
      <view class="overview-item">
        <text class="time-range">染膏使用</text>
        <text class="data-value">{{ dyeConsumptionStats.totalConsumption || 0 }}<text class="unit">g</text></text>
      </view>
    </view>

    <!-- 染膏消耗详情 -->
    <view class="dye-consumption-section">
      <view class="section-header">
        <text class="section-title">染膏消耗详情</text>
        <view class="refresh-btn" @click="refreshDyeConsumption">
          <text>刷新</text>
        </view>
      </view>

      <!-- 库存状态 -->
      <view v-if="inventoryStatus" class="inventory-status">
        <view class="inventory-header">
          <text class="inventory-title">库存状态</text>
          <text class="inventory-summary">{{ inventoryStatus.totalColors }}种颜色</text>
        </view>
        <view v-if="inventoryStatus.alerts && inventoryStatus.alerts.length > 0" class="alert-section">
          <text class="alert-title">⚠️ 库存预警 ({{ inventoryStatus.lowStockCount }})</text>
          <view class="alert-list">
            <view v-for="alert in inventoryStatus.alerts.slice(0, 3)" :key="alert.colorCode" class="alert-item">
              <text :class="['alert-text', alert.level]">
                {{ alert.colorName }}: {{ alert.percentage }}%
              </text>
            </view>
            <view v-if="inventoryStatus.alerts.length > 3" class="more-alerts">
              <text>还有{{ inventoryStatus.alerts.length - 3 }}个预警...</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 颜色消耗排行 -->
      <view v-if="dyeConsumptionStats.colorBreakdown && dyeConsumptionStats.colorBreakdown.length > 0" class="color-breakdown">
        <text class="breakdown-title">颜色消耗排行</text>
        <view class="color-list">
          <view v-for="(color, index) in dyeConsumptionStats.colorBreakdown.slice(0, 5)" :key="color.colorCode" class="color-item">
            <view class="color-rank">{{ index + 1 }}</view>
            <view class="color-info">
              <text class="color-name">{{ color.colorName }}</text>
              <text class="color-usage">{{ color.totalUsage }}g</text>
            </view>
            <view class="color-bar">
              <view class="color-fill" :style="{ width: (color.totalUsage / dyeConsumptionStats.colorBreakdown[0].totalUsage * 100) + '%' }"></view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 染发次数图表 -->
    <view class="chart-section">
      <view class="section-header">
        <text class="section-title">染发次数</text>
        <text class="section-title right">染膏消耗(g)</text>
      </view>
      
      <!-- 时间筛选 -->
      <view class="time-filter">
        <view class="filter-btn" :class="{ active: activeTimeRange === '7days' }" @click="changeTimeRange('7days')">
          <text>近7天</text>
        </view>
        <view class="filter-btn" :class="{ active: activeTimeRange === '30days' }" @click="changeTimeRange('30days')">
          <text>近30天</text>
        </view>
        <view class="filter-btn" :class="{ active: activeTimeRange === '6months' }" @click="changeTimeRange('6months')">
          <text>近半年</text>
        </view>
        <view class="filter-btn" :class="{ active: activeTimeRange === '1year' }" @click="changeTimeRange('1year')">
          <text>近一年</text>
        </view>
      </view>
      
      <!-- 图表区域 -->
      <view class="chart-area">
        <!-- 动态图表数据 -->
        <view class="chart-placeholder" v-if="chartData.length > 0">
          <view class="chart-line">
            <view
              class="chart-point"
              v-for="(point, index) in chartData"
              :key="index"
              :style="{ bottom: point.value + '%', left: point.position + '%' }"
            ></view>
          </view>
          <!-- 横坐标 -->
          <view class="chart-x-axis">
            <text v-for="(label, index) in chartLabels" :key="index">{{ label }}</text>
          </view>
        </view>
        <!-- 无数据状态 -->
        <view class="no-chart-data" v-else>
          <view class="no-data">
            <image class="no-data-icon" src="/static/icons/no-data.png" />
            <text class="no-data-text">暂无图表数据</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 完成率 -->
    <view class="completion-section">
      <view class="section-title">完成率</view>
      <view class="time-filter">
        <view class="filter-btn" :class="{ active: activeCompletionRange === '7days' }" @click="changeCompletionRange('7days')">
          <text>近7天</text>
        </view>
        <view class="filter-btn" :class="{ active: activeCompletionRange === '30days' }" @click="changeCompletionRange('30days')">
          <text>近30天</text>
        </view>
        <view class="filter-btn" :class="{ active: activeCompletionRange === '6months' }" @click="changeCompletionRange('6months')">
          <text>近半年</text>
        </view>
        <view class="filter-btn" :class="{ active: activeCompletionRange === '1year' }" @click="changeCompletionRange('1year')">
          <text>近一年</text>
        </view>
      </view>

      <!-- 完成率数据显示 -->
      <view class="completion-data" v-if="!hasCompletionData">
        <view class="no-data">
          <image class="no-data-icon" src="/static/icons/no-data.png" />
          <text class="no-data-text">暂无数据</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { apiService } from '@/utils/request.js';
import dyeConsumptionManager from '../../utils/dyeConsumptionManager.js';

export default {
  data() {
    return {
      usageStats: {
        oneMonthUsage: 0,
        threeMonthsUsage: 0,
        sixMonthsUsage: 0,
        totalUsage: 0,
        usageDays: 0,
        dyeConsumption: 0
      },
      // 染膏消耗统计
      dyeConsumptionStats: {
        totalConsumption: 0,
        colorBreakdown: [],
        recentUsage: []
      },
      // 库存状态
      inventoryStatus: null,
      activeTimeRange: '7days',
      activeCompletionRange: '7days',
      hasCompletionData: false,
      loading: false,
      chartData: [],
      chartLabels: []
    };
  },
  onLoad() {
    this.loadUsageStats();
    this.loadDyeConsumptionStats();
    this.loadInventoryStatus();
    this.changeTimeRange(this.activeTimeRange); // 加载默认图表数据
  },
  methods: {
    goBack() {
      uni.navigateBack();
    },

    // 加载使用统计数据
    async loadUsageStats() {
      try {
        this.loading = true;
        const response = await apiService.mall.deviceUsage.getStats();
        if (response.code === 200) {
          this.usageStats = response.data;
        } else {
          console.error('获取使用统计失败:', response.message);
          uni.showToast({
            title: '获取数据失败',
            icon: 'none'
          });
        }
      } catch (error) {
        console.error('获取使用统计异常:', error);
        uni.showToast({
          title: '网络异常',
          icon: 'none'
        });
      } finally {
        this.loading = false;
      }
    },

    // 加载染膏消耗统计
    async loadDyeConsumptionStats() {
      try {
        const app = getApp();
        const deviceCode = app.globalData.connectedDeviceId;
        if (!deviceCode) {
          console.warn('设备编码为空，无法加载染膏消耗统计');
          return;
        }

        // 获取使用统计
        const statsResponse = await apiService.mall.dyeConsumption.getUsageStatistics(deviceCode, {
          timeRange: '30days'
        });

        if (statsResponse.code === 200) {
          this.dyeConsumptionStats = {
            totalConsumption: statsResponse.data.totalConsumption || 0,
            colorBreakdown: statsResponse.data.colorBreakdown || [],
            recentUsage: statsResponse.data.recentUsage || []
          };
        }
      } catch (error) {
        console.error('加载染膏消耗统计失败:', error);
      }
    },

    // 加载库存状态
    async loadInventoryStatus() {
      try {
        const app = getApp();
        const deviceCode = app.globalData.connectedDeviceId;
        if (deviceCode) {
          this.inventoryStatus = await dyeConsumptionManager.getInventoryStatus(deviceCode);
        }
      } catch (error) {
        console.error('加载库存状态失败:', error);
      }
    },

    // 刷新染膏消耗数据
    async refreshDyeConsumption() {
      try {
        uni.showLoading({ title: '刷新中...' });
        await Promise.all([
          this.loadDyeConsumptionStats(),
          this.loadInventoryStatus()
        ]);
        uni.hideLoading();
        uni.showToast({
          title: '刷新成功',
          icon: 'success'
        });
      } catch (error) {
        uni.hideLoading();
        console.error('刷新染膏消耗数据失败:', error);
        uni.showToast({
          title: '刷新失败',
          icon: 'none'
        });
      }
    },

    // 切换时间范围
    async changeTimeRange(timeRange) {
      this.activeTimeRange = timeRange;
      try {
        const response = await apiService.mall.deviceUsage.getChartData(timeRange);
        if (response.code === 200 && response.data) {
          // 更新图表数据
          this.updateChartData(response.data);
        } else {
          console.error('获取时间范围数据失败:', response.message);
          this.chartData = [];
          this.chartLabels = [];
        }
      } catch (error) {
        console.error('获取时间范围数据失败:', error);
        this.chartData = [];
        this.chartLabels = [];
      }
    },

    // 更新图表数据
    updateChartData(data) {
      if (!data || !data.chartData || data.chartData.length === 0) {
        this.chartData = [];
        this.chartLabels = [];
        return;
      }

      // 转换后端数据为前端图表格式
      const maxValue = Math.max(...data.chartData.map(item => item.value));
      this.chartData = data.chartData.map((item, index) => ({
        value: maxValue > 0 ? (item.value / maxValue) * 80 : 0, // 转换为百分比高度
        position: (index / (data.chartData.length - 1)) * 90 + 5 // 计算水平位置
      }));

      this.chartLabels = data.labels || [];
    },

    // 切换完成率时间范围
    changeCompletionRange(timeRange) {
      this.activeCompletionRange = timeRange;
      // 这里可以加载完成率相关数据
    }
  }
};
</script>

<style lang="scss">
.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f5f5;
}

.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx;
  background-color: #4285f4;
  color: #fff;
}
  
.back-btn {
  font-size: 40rpx;
  width: 60rpx;
  display: flex;
  align-items: center;
}
  
.title {
  font-size: 36rpx;
  font-weight: 500;
}
  
.header-icons {
  display: flex;
  align-items: center;
}

.header-icon {
  margin-left: 15rpx;
  font-size: 24rpx;
}

.data-overview {
  background-color: #fff;
  border-radius: 10rpx;
  margin: 20rpx;
  padding: 30rpx;
  display: flex;
  flex-wrap: wrap;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.overview-item {
  width: 33.33%;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 20rpx;
}

.time-range {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 10rpx;
}

.data-value {
  font-size: 40rpx;
  font-weight: bold;
  color: #333;
}

.unit {
  font-size: 24rpx;
  font-weight: normal;
  margin-left: 4rpx;
}

.chart-section {
  background-color: #fff;
  border-radius: 10rpx;
  margin: 20rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.time-filter {
  display: flex;
  border-radius: 45rpx;
  overflow: hidden;
  background-color: #f5f5f5;
  margin: 20rpx 0;
}

.filter-btn {
  flex: 1;
  height: 70rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 26rpx;
  color: #666;
}

.filter-btn.active {
  background-color: #4285f4;
  color: #fff;
}

.chart-area {
  height: 400rpx;
  position: relative;
}

.chart-placeholder {
  width: 100%;
  height: 100%;
  border-bottom: 1px solid #eee;
  position: relative;
}

.chart-line {
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
}

.chart-point {
  position: absolute;
  width: 10rpx;
  height: 10rpx;
  background-color: #4285f4;
  border-radius: 50%;
}

.chart-point::after {
  content: '';
  position: absolute;
  width: 100%;
  height: 2px;
  background-color: #4285f4;
  right: -50rpx;
  top: 50%;
  transform: translateY(-50%);
}

.chart-x-axis {
  display: flex;
  justify-content: space-between;
  padding: 10rpx 0;
}

.chart-x-axis text {
  font-size: 24rpx;
  color: #999;
}

.completion-section {
  background-color: #fff;
  border-radius: 10rpx;
  margin: 20rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.completion-data {
  margin-top: 30rpx;
}

.no-data {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 0;
}

.no-data-icon {
  width: 120rpx;
  height: 120rpx;
  opacity: 0.3;
  margin-bottom: 20rpx;
}

.no-data-text {
  font-size: 28rpx;
  color: #999;
}

.no-chart-data {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 染膏消耗详情样式 */
.dye-consumption-section {
  background-color: #fff;
  border-radius: 10rpx;
  margin: 20rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.refresh-btn {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  background-color: #f0f9ff;
  color: #1890ff;
  font-size: 24rpx;
}

.inventory-status {
  margin-bottom: 30rpx;
  padding: 20rpx;
  background-color: #f8f9fa;
  border-radius: 8rpx;
}

.inventory-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15rpx;
}

.inventory-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

.inventory-summary {
  font-size: 24rpx;
  color: #666;
}

.alert-section {
  margin-top: 15rpx;
}

.alert-title {
  font-size: 26rpx;
  font-weight: bold;
  color: #ff6b35;
  margin-bottom: 12rpx;
}

.alert-list {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.alert-item {
  display: flex;
  align-items: center;
}

.alert-text {
  font-size: 24rpx;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
}

.alert-text.warning {
  background-color: #fff3cd;
  color: #856404;
}

.alert-text.critical {
  background-color: #f8d7da;
  color: #721c24;
}

.more-alerts {
  margin-top: 8rpx;
}

.more-alerts text {
  font-size: 22rpx;
  color: #999;
}

.color-breakdown {
  margin-top: 20rpx;
}

.breakdown-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.color-list {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.color-item {
  display: flex;
  align-items: center;
  gap: 15rpx;
}

.color-rank {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  background-color: #4285f4;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 22rpx;
  font-weight: bold;
}

.color-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4rpx;
}

.color-name {
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
}

.color-usage {
  font-size: 22rpx;
  color: #666;
}

.color-bar {
  width: 120rpx;
  height: 8rpx;
  background-color: #f0f0f0;
  border-radius: 4rpx;
  overflow: hidden;
}

.color-fill {
  height: 100%;
  background-color: #4285f4;
  transition: width 0.3s ease;
}
</style> 