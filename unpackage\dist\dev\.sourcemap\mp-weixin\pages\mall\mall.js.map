{"version": 3, "file": "mall.js", "sources": ["pages/mall/mall.vue", "D:/edgexiazai/HBuilderX.4.66.**********/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvbWFsbC9tYWxsLnZ1ZQ"], "sourcesContent": ["<template>\r\n\t<view class=\"container\">\r\n\t\t<!-- 顶部搜索栏 -->\r\n\t\t<view class=\"header\">\r\n\t\t\t<!-- 位置显示条 -->\r\n\t\t\t<view class=\"location-bar\" @click=\"openLocationPicker\">\r\n\t\t\t\t<view class=\"location-info\">\r\n\t\t\t\t\t<uni-icons type=\"location\" size=\"16\" color=\"#4285f4\"></uni-icons>\r\n\t\t\t\t\t<text class=\"location-text\">{{currentLocation.address}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<uni-icons type=\"arrowright\" size=\"14\" color=\"#999\"></uni-icons>\r\n\t\t\t</view>\r\n\r\n\t\t\t<view class=\"search-container\">\r\n\t\t\t\t<view class=\"search-input\" @click=\"goToSearch\">\r\n\t\t\t\t\t<uni-icons type=\"search\" size=\"18\" color=\"#999\"></uni-icons>\r\n\t\t\t\t\t<input type=\"text\" placeholder=\"搜索商品\" v-model=\"searchKeyword\" @confirm=\"doSearch\"\r\n\t\t\t\t\t\tconfirm-type=\"search\" placeholder-class=\"placeholder-style\" />\r\n\t\t\t\t\t<view class=\"search-btn blue-btn\" @click.stop=\"goToSearch\">\r\n\t\t\t\t\t\t<text>搜索</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"scan-icon\" @click=\"scanCode\">\r\n\t\t\t\t<uni-icons type=\"scan\" size=\"24\" color=\"#4285f4\"></uni-icons>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<!-- 主要内容区域 -->\r\n\t\t<scroll-view class=\"main-content\" scroll-y refresher-enabled :refresher-triggered=\"refreshing\"\r\n\t\t\t@refresherrefresh=\"onRefresh\" @scrolltolower=\"loadMoreProducts\">\r\n\t\t\t<!-- 分类专区 -->\r\n\t\t\t<view class=\"section category-section\">\r\n\t\t\t\t<view class=\"section-header\">\r\n\t\t\t\t\t<text class=\"section-title\">自营专区</text>\r\n\t\t\t\t\t<text class=\"section-subtitle\">行业赋能，降本增效</text>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<view class=\"category-grid\">\r\n\t\t\t\t\t<view class=\"category-item\" v-for=\"(category, index) in categories\" :key=\"category.id\"\r\n\t\t\t\t\t\t@click=\"navigateToCategory(category)\">\r\n\t\t\t\t\t\t<view class=\"category-icon\">\r\n\t\t\t\t\t\t\t<image v-if=\"category.icon && isImageUrl(category.icon)\"\r\n\t\t\t\t\t\t\t\t:src=\"getFullImageUrl(category.icon)\" mode=\"aspectFit\" lazy-load\r\n\t\t\t\t\t\t\t\t@error=\"onImageError(category.id)\" />\r\n\t\t\t\t\t\t\t<view v-else class=\"icon-fallback\">\r\n\t\t\t\t\t\t\t\t<text class=\"icon\">{{getDefaultIcon(category.name)}}</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<text class=\"category-name\">{{category.name}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\t\t\t<!-- 热门推荐 -->\r\n\t\t\t<view class=\"section product-section\">\r\n\t\t\t\t<view class=\"section-header\">\r\n\t\t\t\t\t<text class=\"section-title\">热门推荐</text>\r\n\t\t\t\t\t<text class=\"section-subtitle\">大家都在买</text>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<view class=\"product-grid\">\r\n\t\t\t\t\t<view class=\"product-card\" v-for=\"product in products\" :key=\"product.id\"\r\n\t\t\t\t\t\t@click=\"navigateToProduct(product.id)\">\r\n\t\t\t\t\t\t<view class=\"product-image-container\">\r\n\t\t\t\t\t\t\t<image class=\"product-image\" :src=\"getFirstImage(product.images)\" mode=\"aspectFill\"\r\n\t\t\t\t\t\t\t\tlazy-load :fade-show=\"false\" />\r\n\t\t\t\t\t\t\t<view v-if=\"product.tag\" class=\"product-tag\">{{product.tag}}</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"product-info\">\r\n\t\t\t\t\t\t\t<text class=\"product-name\">{{product.productName}}</text>\r\n\t\t\t\t\t\t\t<view class=\"meta-rating-container\">\r\n\t\t\t\t\t\t\t\t<view class=\"price-container\">\r\n\t\t\t\t\t\t\t\t\t<text class=\"current-price\">¥{{product.price}}</text>\r\n\t\t\t\t\t\t\t\t\t<text v-if=\"product.originalPrice\"\r\n\t\t\t\t\t\t\t\t\t\tclass=\"original-price\">¥{{product.originalPrice}}</text>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"product-meta\">\r\n\t\t\t\t\t\t\t\t\t<text class=\"sales\">已售{{product.sales || 0}}件</text>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"rating\">\r\n\t\t\t\t\t\t\t\t<uni-icons type=\"star-filled\" size=\"17\" color=\"#4285f4\"></uni-icons>\r\n\t\t\t\t\t\t\t\t<text>{{product.rating || '5.0'}}%</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<!-- 加载状态 -->\r\n\t\t\t\t<view v-if=\"loading\" class=\"loading-container\">\r\n\t\t\t\t\t<uni-load-more status=\"loading\"></uni-load-more>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<!-- 没有更多数据 -->\r\n\t\t\t\t<view v-if=\"!hasMore && products.length > 0\" class=\"no-more\">\r\n\t\t\t\t\t<text>— 已经到底啦 —</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\t\t\t<!-- 空状态 -->\r\n\t\t\t<view v-if=\"products.length === 0 && !loading\" class=\"empty-state\">\r\n\t\t\t\t<view class=\"empty-icon\">\r\n\t\t\t\t\t<text class=\"icon\">📦</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<text class=\"empty-text\">暂无商品数据</text>\r\n\t\t\t\t<text class=\"empty-desc\">商城正在筹备中，敬请期待</text>\r\n\t\t\t\t<button class=\"refresh-btn\" @click=\"refreshData\">重新加载</button>\r\n\t\t\t</view>\r\n\t\t</scroll-view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport {\r\n\t\tapiService\r\n\t} from '@/utils/request.js';\r\n\timport {\r\n\t\tgetFirstImage,\r\n\t\tgetFullImageUrl\r\n\t} from '@/utils/imageUtils.js';\r\n\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tcategories: [],\r\n\t\t\t\tproducts: [],\r\n\t\t\t\tloading: false,\r\n\t\t\t\trefreshing: false,\r\n\t\t\t\thasMore: true,\r\n\t\t\t\tcurrentPage: 1,\r\n\t\t\t\tpageSize: 10,\r\n\t\t\t\tsearchKeyword: '',\r\n\t\t\t\timageErrors: {},\r\n\t\t\t\tcurrentLocation: {\r\n\t\t\t\t\taddress: '定位中...',\r\n\t\t\t\t\tlatitude: null,\r\n\t\t\t\t\tlongitude: null\r\n\t\t\t\t}\r\n\t\t\t};\r\n\t\t},\r\n\t\tonLoad() {\r\n\t\t\tthis.loadInitialData();\r\n\t\t\tthis.getCurrentLocation();\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t// 获取当前位置\r\n\t\t\tgetCurrentLocation() {\r\n\t\t\t\t// 如果是点击触发，直接打开位置选择器\r\n\t\t\t\tif (arguments.length > 0 && arguments[0] === 'click') {\r\n\t\t\t\t\tthis.openLocationPicker();\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// 页面加载时自动获取位置\r\n\t\t\t\tuni.getLocation({\r\n\t\t\t\t\ttype: 'gcj02', // 使用国测局坐标系，适配微信地图\r\n\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\tconsole.log('定位成功:', res);\r\n\t\t\t\t\t\tthis.currentLocation.latitude = res.latitude;\r\n\t\t\t\t\t\tthis.currentLocation.longitude = res.longitude;\r\n\t\t\t\t\t\tthis.currentLocation.address = '当前位置';\r\n\t\t\t\t\t},\r\n\t\t\t\t\tfail: (err) => {\r\n\t\t\t\t\t\tconsole.error('定位失败:', err);\r\n\t\t\t\t\t\tthis.currentLocation.address = '点击选择位置';\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\r\n\t\t\t// 打开微信位置选择器\r\n\t\t\topenLocationPicker() {\r\n\t\t\t\tuni.chooseLocation({\r\n\t\t\t\t\tlatitude: this.currentLocation.latitude,\r\n\t\t\t\t\tlongitude: this.currentLocation.longitude,\r\n\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\tconsole.log('选择位置成功:', res);\r\n\t\t\t\t\t\tthis.currentLocation = {\r\n\t\t\t\t\t\t\taddress: res.address || res.name || '已选择位置',\r\n\t\t\t\t\t\t\tlatitude: res.latitude,\r\n\t\t\t\t\t\t\tlongitude: res.longitude\r\n\t\t\t\t\t\t};\r\n\t\t\t\t\t},\r\n\t\t\t\t\tfail: (err) => {\r\n\t\t\t\t\t\tconsole.error('选择位置失败:', err);\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '位置选择取消',\r\n\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\r\n\t\t\t// 扫码功能\r\n\t\t\tscanCode() {\r\n\t\t\t\t// uni.scanCode({\r\n\t\t\t\t// \tsuccess: (res) => {\r\n\t\t\t\t// \t\tconsole.log('扫码结果:', res);\r\n\t\t\t\t// \t\tuni.showToast({\r\n\t\t\t\t// \t\t\ttitle: '扫码成功: ' + res.result,\r\n\t\t\t\t// \t\t\ticon: 'none'\r\n\t\t\t\t// \t\t});\r\n\t\t\t\t// \t},\r\n\t\t\t\t// \tfail: (err) => {\r\n\t\t\t\t// \t\tconsole.error('扫码失败:', err);\r\n\t\t\t\t// \t\tuni.showToast({\r\n\t\t\t\t// \t\t\ttitle: '扫码失败',\r\n\t\t\t\t// \t\t\ticon: 'none'\r\n\t\t\t\t// \t\t});\r\n\t\t\t\t// \t}\r\n\t\t\t\t// });\r\n\t\t\t},\r\n\t\t\tasync loadInitialData() {\r\n\t\t\t\tawait Promise.all([\r\n\t\t\t\t\tthis.loadCategories(),\r\n\t\t\t\t\tthis.loadProducts()\r\n\t\t\t\t]);\r\n\t\t\t},\r\n\r\n\t\t\tasync loadCategories() {\r\n\t\t\t\ttry {\r\n\t\t\t\t\tconst response = await apiService.mall.categories.list();\r\n\t\t\t\t\tif (response.code === 200) {\r\n\t\t\t\t\t\tthis.categories = (response.data || []).slice(0, 15); // 限制显示数量\r\n\t\t\t\t\t}\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\tconsole.error('获取分类失败:', error);\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '获取分类失败',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\tasync loadProducts(isLoadMore = false) {\r\n\t\t\t\tif (this.loading) return;\r\n\t\t\t\tthis.loading = true;\r\n\r\n\t\t\t\ttry {\r\n\t\t\t\t\tconst params = {\r\n\t\t\t\t\t\tpage: isLoadMore ? this.currentPage + 1 : 1,\r\n\t\t\t\t\t\tpageSize: this.pageSize\r\n\t\t\t\t\t};\r\n\r\n\t\t\t\t\tconst response = await apiService.mall.products.list(params);\r\n\r\n\t\t\t\t\tif (response.code === 200) {\r\n\t\t\t\t\t\tconst newProducts = response.data.products || response.data.records || response.data || [];\r\n\r\n\t\t\t\t\t\tif (isLoadMore) {\r\n\t\t\t\t\t\t\tthis.products = [...this.products, ...newProducts];\r\n\t\t\t\t\t\t\tthis.currentPage++;\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tthis.products = newProducts;\r\n\t\t\t\t\t\t\tthis.currentPage = 1;\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\tthis.hasMore = newProducts.length === this.pageSize;\r\n\t\t\t\t\t}\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\tconsole.error('获取商品失败:', error);\r\n\t\t\t\t} finally {\r\n\t\t\t\t\tthis.loading = false;\r\n\t\t\t\t\tthis.refreshing = false;\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\tonRefresh() {\r\n\t\t\t\tif (this.refreshing) return;\r\n\t\t\t\tthis.refreshing = true;\r\n\t\t\t\tthis.loadProducts();\r\n\t\t\t\tthis.loadCategories();\r\n\t\t\t},\r\n\r\n\t\t\tloadMoreProducts() {\r\n\t\t\t\tif (!this.hasMore || this.loading) return;\r\n\t\t\t\tthis.loadProducts(true);\r\n\t\t\t},\r\n\r\n\t\t\t// 刷新数据\r\n\t\t\trefreshData() {\r\n\t\t\t\tthis.products = [];\r\n\t\t\t\tthis.categories = [];\r\n\t\t\t\tthis.currentPage = 1;\r\n\t\t\t\tthis.hasMore = true;\r\n\r\n\t\t\t\t// uni.showLoading({\r\n\t\t\t\t// \ttitle: '刷新中...'\r\n\t\t\t\t// });\r\n\r\n\t\t\t\tPromise.all([\r\n\t\t\t\t\tthis.loadCategories(),\r\n\t\t\t\t\tthis.loadProducts()\r\n\t\t\t\t]).finally(() => {\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '刷新完成',\r\n\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t});\r\n\t\t\t\t});\r\n\t\t\t},\r\n\r\n\t\t\tnavigateToCategory(category) {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: `/pages/mall/category/category?categoryId=${category.id}&categoryName=${encodeURIComponent(category.name)}`\r\n\t\t\t\t});\r\n\t\t\t},\r\n\r\n\t\t\tnavigateToProduct(productId) {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: `/pages/mall/product/detail?id=${productId}`\r\n\t\t\t\t});\r\n\t\t\t},\r\n\r\n\t\t\tdoSearch() {\r\n\t\t\t\tconst keyword = this.searchKeyword.trim();\r\n\t\t\t\tif (!keyword) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '请输入搜索内容',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t});\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: `/pages/mall/search/search?keyword=${encodeURIComponent(keyword)}`\r\n\t\t\t\t});\r\n\t\t\t},\r\n\r\n\t\t\tgoToSearch() {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: '/pages/mall/search/search'\r\n\t\t\t\t});\r\n\t\t\t},\r\n\r\n\t\t\t// 图片相关方法\r\n\t\t\tgetFirstImage(images) {\r\n\t\t\t\treturn getFirstImage(images);\r\n\t\t\t},\r\n\r\n\t\t\tgetFullImageUrl(url) {\r\n\t\t\t\treturn getFullImageUrl(url);\r\n\t\t\t},\r\n\r\n\t\t\tisImageUrl(url) {\r\n\t\t\t\tif (!url) return false;\r\n\t\t\t\treturn url.startsWith('http') || url.startsWith('/static') || url.startsWith('data:image');\r\n\t\t\t},\r\n\r\n\t\t\tonImageError(categoryId) {\r\n\t\t\t\tthis.$set(this.imageErrors, categoryId, true);\r\n\t\t\t},\r\n\r\n\t\t\tgetDefaultIcon(name) {\r\n\t\t\t\tconst iconMap = {\r\n\t\t\t\t\t'数码': '📱',\r\n\t\t\t\t\t'服装': '👕',\r\n\t\t\t\t\t'家居': '🏠',\r\n\t\t\t\t\t'美妆': '💄',\r\n\t\t\t\t\t'食品': '🍎',\r\n\t\t\t\t\t'运动': '⚽',\r\n\t\t\t\t\t'图书': '📚',\r\n\t\t\t\t\t'母婴': '👶'\r\n\t\t\t\t};\r\n\t\t\t\treturn iconMap[name] || '🛍️';\r\n\t\t\t}\r\n\t\t}\r\n\t};\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n\tpage {\r\n\t\tbackground: -webkit-linear-gradient(to bottom, #4285f4, #fff);\r\n\t\tbackground: linear-gradient(to bottom, #4285f4, #fff);\r\n\t\theight: 100%;\r\n\t}\r\n\r\n\t.container {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\theight: 100vh;\r\n\t\tposition: relative;\r\n\t\tbackground: transparent;\r\n\r\n\t\t.header {\r\n\t\t\tdisplay: flex;\r\n\t\t\tflex-direction: column;\r\n\t\t\tbackground: transparent;\r\n\t\t\tpadding: 16rpx 24rpx;\r\n\t\t\tposition: sticky;\r\n\t\t\ttop: 0;\r\n\t\t\tz-index: 100;\r\n\r\n\t\t\t.location-bar {\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tjustify-content: space-between;\r\n\t\t\t\tbackground-color: rgba(255, 255, 255, 0.9);\r\n\t\t\t\tborder-radius: 24rpx;\r\n\t\t\t\tpadding: 12rpx 20rpx;\r\n\t\t\t\tmargin-bottom: 16rpx;\r\n\t\t\t\tbox-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);\r\n\r\n\t\t\t\t.location-info {\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\talign-items: center;\r\n\r\n\t\t\t\t\t.location-text {\r\n\t\t\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\t\t\tcolor: #333;\r\n\t\t\t\t\t\tmargin-left: 8rpx;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t.search-container {\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tjustify-content: space-between;\r\n\t\t\t}\r\n\r\n\t\t\t.search-container {\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tjustify-content: space-between;\r\n\r\n\t\t\t\t.search-input {\r\n\t\t\t\t\tflex: 1;\r\n\t\t\t\t\tmargin-right: 20rpx;\r\n\t\t\t\t\tbackground-color: rgba(255, 255, 255, 0.95);\r\n\t\t\t\t\tborder-radius: 48rpx;\r\n\t\t\t\t\tpadding: 12rpx 24rpx;\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\talign-items: center;\r\n\t\t\t\t\tbox-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);\r\n\r\n\t\t\t\t\tinput {\r\n\t\t\t\t\t\tflex: 1;\r\n\t\t\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\t\t\tmargin-left: 15rpx;\r\n\t\t\t\t\t\tcolor: #333;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t.placeholder-style {\r\n\t\t\t\t\t\tcolor: #999;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t.search-btn {\r\n\t\t\t\t\t\tmargin-left: 15rpx;\r\n\t\t\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\t\t\tcolor: #999;\r\n\t\t\t\t\t\tpadding: 0 15rpx;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t.blue-btn {\r\n\t\t\t\t\t\tbackground-color: #4285f4;\r\n\t\t\t\t\t\tcolor: white !important;\r\n\t\t\t\t\t\tpadding: 8rpx 20rpx;\r\n\t\t\t\t\t\tborder-radius: 30rpx;\r\n\t\t\t\t\t\tmargin-left: 20rpx;\r\n\t\t\t\t\t\tmargin-right: 3rpx;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t.scan-icon {\r\n\t\t\t\tbackground-color: rgba(255, 255, 255, 0.95);\r\n\t\t\t\twidth: 80rpx;\r\n\t\t\t\theight: 80rpx;\r\n\t\t\t\tborder-radius: 50%;\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tjustify-content: center;\r\n\t\t\t\tbox-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);\r\n\t\t\t\tflex-shrink: 0;\r\n\t\t\t\t/* 防止图标被压缩 */\r\n\t\t\t}\r\n\r\n\t\t}\r\n\r\n\t\t.main-content {\r\n\t\t\tflex: 1;\r\n\t\t\theight: 1px;\r\n\t\t\toverflow: hidden;\r\n\t\t\tpadding-bottom: env(safe-area-inset-bottom);\r\n\t\t\tbackground-color: transparent;\r\n\t\t}\r\n\t}\r\n\r\n\t.empty-icon {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: center; // 水平居中\r\n\t\talign-items: center; // 垂直居中（如果需要）\r\n\t\t// margin-bottom: 15rpx;\r\n\t\t// width: 100%; // 确保占据整行\r\n\t}\r\n\r\n\t.section {\r\n\t\tbackground-color: #fff;\r\n\t\tborder-radius: 20rpx;\r\n\t\tmargin: 20rpx;\r\n\t\tpadding: 20rpx;\r\n\t\tbox-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);\r\n\r\n\t\t.section-header {\r\n\t\t\tmargin-bottom: 25rpx;\r\n\r\n\t\t\t.section-title {\r\n\t\t\t\tfont-size: 34rpx;\r\n\t\t\t\tfont-weight: bold;\r\n\t\t\t\tcolor: #333;\r\n\t\t\t\tmargin-right: 15rpx;\r\n\t\t\t}\r\n\r\n\t\t\t.section-subtitle {\r\n\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\tcolor: #999;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t/* 分类和商品区域调整背景为半透明 */\r\n\t.category-section,\r\n\t.product-section {\r\n\t\tbackground-color: rgba(255, 255, 255, 0.92);\r\n\t\tbackdrop-filter: blur(10rpx);\r\n\t}\r\n\r\n\t.category-section {\r\n\t\t.category-grid {\r\n\t\t\tdisplay: grid;\r\n\t\t\tgrid-template-columns: repeat(5, 1fr);\r\n\t\t\tgap: 20rpx;\r\n\r\n\t\t\t.category-item {\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\tflex-direction: column;\r\n\t\t\t\talign-items: center;\r\n\r\n\t\t\t\t.category-icon {\r\n\t\t\t\t\twidth: 90rpx;\r\n\t\t\t\t\theight: 90rpx;\r\n\t\t\t\t\tmargin-bottom: 15rpx;\r\n\t\t\t\t\tbackground-color: #f8f8f8;\r\n\t\t\t\t\t// border-radius: 50%;\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\talign-items: center;\r\n\t\t\t\t\tjustify-content: center;\r\n\t\t\t\t\toverflow: hidden;\r\n\r\n\t\t\t\t\t// image {\r\n\t\t\t\t\t// \twidth: 60rpx;\r\n\t\t\t\t\t// \theight: 60rpx;\r\n\t\t\t\t\t// }\r\n\r\n\t\t\t\t\t.icon-fallback {\r\n\t\t\t\t\t\tfont-size: 50rpx;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.category-name {\r\n\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\tcolor: #666;\r\n\t\t\t\t\ttext-align: center;\r\n\t\t\t\t\tline-height: 1.4;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t.product-section {\r\n\t\t.product-grid {\r\n\t\t\tdisplay: grid;\r\n\t\t\tgrid-template-columns: repeat(2, 1fr);\r\n\t\t\tgap: 20rpx;\r\n\r\n\t\t\t.product-card {\r\n\t\t\t\tborder-radius: 12rpx;\r\n\t\t\t\toverflow: hidden;\r\n\t\t\t\tbackground-color: #fff;\r\n\t\t\t\tbox-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);\r\n\t\t\t\ttransition: transform 0.2s;\r\n\r\n\t\t\t\t&:active {\r\n\t\t\t\t\ttransform: scale(0.98);\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.product-image-container {\r\n\t\t\t\t\tposition: relative;\r\n\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\theight: 0;\r\n\t\t\t\t\tpadding-bottom: 100%;\r\n\r\n\t\t\t\t\t.product-image {\r\n\t\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\t\theight: 100%;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t.product-tag {\r\n\t\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\t\ttop: 10rpx;\r\n\t\t\t\t\t\tleft: 10rpx;\r\n\t\t\t\t\t\tbackground-color: #ff4757;\r\n\t\t\t\t\t\tcolor: #fff;\r\n\t\t\t\t\t\tfont-size: 20rpx;\r\n\t\t\t\t\t\tpadding: 4rpx 12rpx;\r\n\t\t\t\t\t\tborder-radius: 20rpx;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.product-info {\r\n\t\t\t\t\tpadding: 20rpx 15rpx;\r\n\r\n\t\t\t\t\t.product-name {\r\n\t\t\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\t\t\tcolor: #333;\r\n\t\t\t\t\t\tdisplay: -webkit-box;\r\n\t\t\t\t\t\t-webkit-line-clamp: 2;\r\n\t\t\t\t\t\t-webkit-box-orient: vertical;\r\n\t\t\t\t\t\toverflow: hidden;\r\n\t\t\t\t\t\ttext-overflow: ellipsis;\r\n\t\t\t\t\t\theight: 70rpx;\r\n\t\t\t\t\t\tline-height: 35rpx;\r\n\t\t\t\t\t\tmargin-bottom: 15rpx;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t.price-container {\r\n\t\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\t\talign-items: center;\r\n\t\t\t\t\t\tmargin-bottom: 12rpx;\r\n\r\n\t\t\t\t\t\t.current-price {\r\n\t\t\t\t\t\t\tfont-size: 30rpx;\r\n\t\t\t\t\t\t\tcolor: #ff4757;\r\n\t\t\t\t\t\t\tfont-weight: bold;\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t.original-price {\r\n\t\t\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\t\t\tcolor: #999;\r\n\t\t\t\t\t\t\ttext-decoration: line-through;\r\n\t\t\t\t\t\t\tmargin-left: 10rpx;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t.meta-rating-container {\r\n\t\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\t\tjustify-content: space-between;\r\n\t\t\t\t\t\talign-items: center; // 确保垂直居中\r\n\t\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\t\theight: 40rpx; // 设置固定高度\r\n\t\t\t\t\t\tline-height: 40rpx; // 与高度相同确保垂直居中\r\n\t\t\t\t\t\tfont-size: 24rpx; // 统一字体大小\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t.product-meta {\r\n\t\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\t\tjustify-content: space-between;\r\n\t\t\t\t\t\talign-items: center;\r\n\t\t\t\t\t\tfont-size: 30rpx;\r\n\r\n\t\t\t\t\t\t.sales {\r\n\t\t\t\t\t\t\t// font-size: 15rpx;\r\n\t\t\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\t\t\tcolor: #999;\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t.rating {\r\n\t\t\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\t\t\talign-items: center;\r\n\t\t\t\t\t\t\tgap: 4px;\r\n\t\t\t\t\t\t\tmargin-top: 15rpx;\r\n\r\n\t\t\t\t\t\t\ttext {\r\n\t\t\t\t\t\t\t\tfont-size: 8rpx;\r\n\t\t\t\t\t\t\t\tcolor: #4285f4;\r\n\t\t\t\t\t\t\t\tmargin-left: 4rpx;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t.empty-icon .icon {\r\n\t\tfont-size: 120rpx;\r\n\t\topacity: 0.6;\r\n\t}\r\n\r\n\t.loading-container,\r\n\t.no-more {\r\n\t\tpadding: 30rpx 0;\r\n\t\ttext-align: center;\r\n\t\tcolor: #999;\r\n\t\tfont-size: 26rpx;\r\n\t}\r\n\r\n\t.empty-state {\r\n\t\tbackground-color: rgba(255, 255, 255, 0.85);\r\n\t\tborder-radius: 24rpx;\r\n\t\tmargin: 24rpx;\r\n\t\tpadding: 60rpx 0;\r\n\t\tbackdrop-filter: blur(10rpx);\r\n\r\n\t\t.empty-image {\r\n\t\t\twidth: 200rpx;\r\n\t\t\theight: 200rpx;\r\n\t\t\topacity: 0.6;\r\n\t\t\tmargin-bottom: 30rpx;\r\n\t\t}\r\n\r\n\t\t.empty-text {\r\n\t\t\tfont-size: 30rpx;\r\n\t\t\tcolor: #666;\r\n\t\t\tmargin-bottom: 5rpx;\r\n\t\t\ttext-align: center;\r\n\t\t\tdisplay: block !important;\r\n\t\t}\r\n\r\n\t\t.empty-desc {\r\n\t\t\tfont-size: 26rpx;\r\n\t\t\tcolor: #999;\r\n\t\t\tmargin-bottom: 20rpx;\r\n\t\t\ttext-align: center;\r\n\t\t\tdisplay: block !important;\r\n\t\t}\r\n\r\n\t\t.refresh-btn {\r\n\t\t\twidth: 60%;\r\n\t\t\tbackground-color: #4285f4;\r\n\t\t\tcolor: #fff;\r\n\t\t\tborder: none;\r\n\t\t\tborder-radius: 50rpx;\r\n\t\t\tpadding: 0 60rpx;\r\n\t\t\theight: 70rpx;\r\n\t\t\tline-height: 70rpx;\r\n\t\t\tfont-size: 28rpx;\r\n\r\n\t\t\t&::after {\r\n\t\t\t\tborder: none;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</style>", "import MiniProgramPage from 'E:/tempCode/new_www_production/new_www_production/pages/mall/mall.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni", "apiService", "getFirstImage", "getFullImageUrl"], "mappings": ";;;;AA0HC,MAAK,YAAU;AAAA,EACd,OAAO;AACN,WAAO;AAAA,MACN,YAAY,CAAE;AAAA,MACd,UAAU,CAAE;AAAA,MACZ,SAAS;AAAA,MACT,YAAY;AAAA,MACZ,SAAS;AAAA,MACT,aAAa;AAAA,MACb,UAAU;AAAA,MACV,eAAe;AAAA,MACf,aAAa,CAAE;AAAA,MACf,iBAAiB;AAAA,QAChB,SAAS;AAAA,QACT,UAAU;AAAA,QACV,WAAW;AAAA,MACZ;AAAA;EAED;AAAA,EACD,SAAS;AACR,SAAK,gBAAe;AACpB,SAAK,mBAAkB;AAAA,EACvB;AAAA,EACD,SAAS;AAAA;AAAA,IAER,qBAAqB;AAEpB,UAAI,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAS;AACrD,aAAK,mBAAkB;AACvB;AAAA,MACD;AAGAA,oBAAAA,MAAI,YAAY;AAAA,QACf,MAAM;AAAA;AAAA,QACN,SAAS,CAAC,QAAQ;AACjBA,wBAAY,MAAA,MAAA,OAAA,8BAAA,SAAS,GAAG;AACxB,eAAK,gBAAgB,WAAW,IAAI;AACpC,eAAK,gBAAgB,YAAY,IAAI;AACrC,eAAK,gBAAgB,UAAU;AAAA,QAC/B;AAAA,QACD,MAAM,CAAC,QAAQ;AACdA,wBAAc,MAAA,MAAA,SAAA,8BAAA,SAAS,GAAG;AAC1B,eAAK,gBAAgB,UAAU;AAAA,QAChC;AAAA,MACD,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,qBAAqB;AACpBA,oBAAAA,MAAI,eAAe;AAAA,QAClB,UAAU,KAAK,gBAAgB;AAAA,QAC/B,WAAW,KAAK,gBAAgB;AAAA,QAChC,SAAS,CAAC,QAAQ;AACjBA,wBAAY,MAAA,MAAA,OAAA,8BAAA,WAAW,GAAG;AAC1B,eAAK,kBAAkB;AAAA,YACtB,SAAS,IAAI,WAAW,IAAI,QAAQ;AAAA,YACpC,UAAU,IAAI;AAAA,YACd,WAAW,IAAI;AAAA;QAEhB;AAAA,QACD,MAAM,CAAC,QAAQ;AACdA,wBAAc,MAAA,MAAA,SAAA,8BAAA,WAAW,GAAG;AAC5BA,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO;AAAA,YACP,MAAM;AAAA,UACP,CAAC;AAAA,QACF;AAAA,MACD,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,WAAW;AAAA,IAiBV;AAAA,IACD,MAAM,kBAAkB;AACvB,YAAM,QAAQ,IAAI;AAAA,QACjB,KAAK,eAAgB;AAAA,QACrB,KAAK,aAAa;AAAA,MACnB,CAAC;AAAA,IACD;AAAA,IAED,MAAM,iBAAiB;AACtB,UAAI;AACH,cAAM,WAAW,MAAMC,cAAU,WAAC,KAAK,WAAW,KAAI;AACtD,YAAI,SAAS,SAAS,KAAK;AAC1B,eAAK,cAAc,SAAS,QAAQ,CAAA,GAAI,MAAM,GAAG,EAAE;AAAA,QACpD;AAAA,MACC,SAAO,OAAO;AACfD,yEAAc,WAAW,KAAK;AAC9BA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACP,CAAC;AAAA,MACF;AAAA,IACA;AAAA,IAED,MAAM,aAAa,aAAa,OAAO;AACtC,UAAI,KAAK;AAAS;AAClB,WAAK,UAAU;AAEf,UAAI;AACH,cAAM,SAAS;AAAA,UACd,MAAM,aAAa,KAAK,cAAc,IAAI;AAAA,UAC1C,UAAU,KAAK;AAAA;AAGhB,cAAM,WAAW,MAAMC,yBAAW,KAAK,SAAS,KAAK,MAAM;AAE3D,YAAI,SAAS,SAAS,KAAK;AAC1B,gBAAM,cAAc,SAAS,KAAK,YAAY,SAAS,KAAK,WAAW,SAAS,QAAQ;AAExF,cAAI,YAAY;AACf,iBAAK,WAAW,CAAC,GAAG,KAAK,UAAU,GAAG,WAAW;AACjD,iBAAK;AAAA,iBACC;AACN,iBAAK,WAAW;AAChB,iBAAK,cAAc;AAAA,UACpB;AAEA,eAAK,UAAU,YAAY,WAAW,KAAK;AAAA,QAC5C;AAAA,MACC,SAAO,OAAO;AACfD,yEAAc,WAAW,KAAK;AAAA,MAC/B,UAAU;AACT,aAAK,UAAU;AACf,aAAK,aAAa;AAAA,MACnB;AAAA,IACA;AAAA,IAED,YAAY;AACX,UAAI,KAAK;AAAY;AACrB,WAAK,aAAa;AAClB,WAAK,aAAY;AACjB,WAAK,eAAc;AAAA,IACnB;AAAA,IAED,mBAAmB;AAClB,UAAI,CAAC,KAAK,WAAW,KAAK;AAAS;AACnC,WAAK,aAAa,IAAI;AAAA,IACtB;AAAA;AAAA,IAGD,cAAc;AACb,WAAK,WAAW;AAChB,WAAK,aAAa;AAClB,WAAK,cAAc;AACnB,WAAK,UAAU;AAMf,cAAQ,IAAI;AAAA,QACX,KAAK,eAAgB;AAAA,QACrB,KAAK,aAAa;AAAA,MACnB,CAAC,EAAE,QAAQ,MAAM;AAChBA,sBAAG,MAAC,YAAW;AACfA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACP,CAAC;AAAA,MACF,CAAC;AAAA,IACD;AAAA,IAED,mBAAmB,UAAU;AAC5BA,oBAAAA,MAAI,WAAW;AAAA,QACd,KAAK,4CAA4C,SAAS,EAAE,iBAAiB,mBAAmB,SAAS,IAAI,CAAC;AAAA,MAC/G,CAAC;AAAA,IACD;AAAA,IAED,kBAAkB,WAAW;AAC5BA,oBAAAA,MAAI,WAAW;AAAA,QACd,KAAK,iCAAiC,SAAS;AAAA,MAChD,CAAC;AAAA,IACD;AAAA,IAED,WAAW;AACV,YAAM,UAAU,KAAK,cAAc,KAAI;AACvC,UAAI,CAAC,SAAS;AACbA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACP,CAAC;AACD;AAAA,MACD;AAEAA,oBAAAA,MAAI,WAAW;AAAA,QACd,KAAK,qCAAqC,mBAAmB,OAAO,CAAC;AAAA,MACtE,CAAC;AAAA,IACD;AAAA,IAED,aAAa;AACZA,oBAAAA,MAAI,WAAW;AAAA,QACd,KAAK;AAAA,MACN,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,cAAc,QAAQ;AACrB,aAAOE,iBAAAA,cAAc,MAAM;AAAA,IAC3B;AAAA,IAED,gBAAgB,KAAK;AACpB,aAAOC,iBAAAA,gBAAgB,GAAG;AAAA,IAC1B;AAAA,IAED,WAAW,KAAK;AACf,UAAI,CAAC;AAAK,eAAO;AACjB,aAAO,IAAI,WAAW,MAAM,KAAK,IAAI,WAAW,SAAS,KAAK,IAAI,WAAW,YAAY;AAAA,IACzF;AAAA,IAED,aAAa,YAAY;AACxB,WAAK,KAAK,KAAK,aAAa,YAAY,IAAI;AAAA,IAC5C;AAAA,IAED,eAAe,MAAM;AACpB,YAAM,UAAU;AAAA,QACf,MAAM;AAAA,QACN,MAAM;AAAA,QACN,MAAM;AAAA,QACN,MAAM;AAAA,QACN,MAAM;AAAA,QACN,MAAM;AAAA,QACN,MAAM;AAAA,QACN,MAAM;AAAA;AAEP,aAAO,QAAQ,IAAI,KAAK;AAAA,IACzB;AAAA,EACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC7WF,GAAG,WAAW,eAAe;"}