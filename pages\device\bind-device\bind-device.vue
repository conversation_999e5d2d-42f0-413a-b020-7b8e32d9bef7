<template>
  <view class="bind-device-page">
    <!-- 设备名称部分 -->
    <view class="device-name-section">
      <view class="title-hearder">设备名称</view>
      <input class="device-name-input" placeholder="请给您的设备取个名字" />
      <view class="hint-text">请扫描染膏顶部二维码绑定品牌</view>
      <view class="hint-text warning">（各仓位任意一个，双氧除外）</view>
      <view class="scan-container">
        <view class="scan-box" @click="handleScan">
          <image class="scan-icon" src="/static/icons/scan-icon.png" mode="aspectFit"></image>
          <view class="scan-text">去扫码</view>
        </view>
      </view>
    </view>
    
    <view class="divider"></view>
    
    <!-- 服务年选项 -->
    <view class="service-year-section">
      <view class="section-title">服务年</view>
      <view class="year-options">
        <view 
          v-for="(option, index) in yearOptions" 
          :key="index" 
          class="year-option"
          :class="{active: activeYear === index}"
          @click="selectYear(index)"
        >
          <view class="year">{{option.year}}年</view>
          <view class="price">￥{{option.price}}</view>
          <view class="original-price">¥{{option.originalPrice}}</view>
        </view>
      </view>
      
      <!-- 价格明细 -->
      <view class="price-detail">
        <view class="detail-item">
          <text>{{selectedYear}}年服务原价：</text>
          <text>￥{{originalPrice}}</text>
        </view>
        <view class="detail-item">
          <text>使用代理商权益抵现：</text>
          <text>- ¥{{discount}}</text>
        </view>
        <view class="detail-item total">
          <text>优惠后金额</text>
          <text>¥{{finalPrice}}</text>
        </view>
      </view>
    </view>
    
    <view class="divider"></view>
    
    <!-- 绑定按钮 -->
    <button class="bind-btn">绑定设备</button>
    
    <!-- 协议提示 -->
    <view class="agreement-hint">购买即视为同意《软件许可及服务协议》</view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      activeYear: 0,
      originalPrice: 999, // 模拟原价数据
      discount: 601,      // 模拟折扣金额
      yearOptions: [
        { year: '1', price: '398', originalPrice: '696' },
        { year: '2', price: '698', originalPrice: '1296' },
        { year: '3', price: '999', originalPrice: '1999' }
      ]
    }
  },
  onLoad(option) {
	setTimeout(()=> {},200)
	// 跳转到设备列表页面
	uni.navigateTo({
		url: '/pages/my/device-list',
		success: () => {
			console.log('跳转到设备列表页面成功');
		},
		fail: (err) => {
			console.error('跳转到设备列表页面失败：', err);
	
			// 如果页面不存在，提示用户
			uni.showToast({
				title: '设备列表页面跳转失败',
				icon: 'none',
				duration: 2000
			});
		}
	});  
  },
  computed: {
    selectedYear() {
      return this.yearOptions[this.activeYear].year
    },
    finalPrice() {
      return this.yearOptions[this.activeYear].price
    }
  },
  methods: {
    selectYear(index) {
      this.activeYear = index
      // 模拟根据选择年份变化原价和折扣
      this.originalPrice = [999, 1998, 2997][index]
      this.discount = [601, 1300, 1998][index]
    },
    handleScan() {
      uni.scanCode({
        success: (res) => {
          console.log('扫描结果:', res)
        },
        fail: (err) => {
          console.error('扫描失败:', err)
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.bind-device-page {
  padding: 20rpx 30rpx;
  background-color: #fff;
  min-height: 100vh;
}

.page-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #1a73e8;
  margin-bottom: 40rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #1a73e8;
  margin-bottom: 20rpx;
}

.title-hearder {
	font-size: 25rpx;
	font-weight: bold;
	// color: ;
	margin-bottom: 20rpx;
}

.hint-text {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 10rpx;
  
  &.warning {
    color: #a52a2a; // 深红色
  }
}

.device-name-input {
  height: 80rpx;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  padding: 0 20rpx;
  margin-bottom: 20rpx;
  font-size: 28rpx;
}

.scan-container {
  display: flex;
  justify-content: center;
  margin-top: 30rpx;
}

.scan-box {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 400rpx;
  height: 220rpx;
  margin-right: auto; /* 关键代码：自动右外边距会将其推到左侧 */
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  padding: 20rpx;
}

.scan-icon {
  width: 80rpx;
  height: 80rpx;
  margin-bottom: 15rpx;
}

.scan-text {
  font-size: 28rpx;
  // color: #1a73e8;
}

.divider {
  height: 20rpx;
  background-color: #f5f5f5;
  margin: 30rpx -30rpx;
}

.year-options {
  display: flex;
  justify-content: space-between;
  margin: 30rpx 0;
}

.year-option {
  width: 30%;
  padding: 20rpx;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  text-align: center;
  
  &.active {
    border-color: #1a73e8;
    background-color: #e8f0fe;
  }
}

.year {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
}

.price {
  font-size: 32rpx;
  color: #1a73e8;
  font-weight: bold;
}

.original-price {
  font-size: 24rpx;
  color: #999;
  text-decoration: line-through;
}

.price-detail {
  background-color: #f9f9f9;
  padding: 20rpx;
  border-radius: 8rpx;
  margin-top: 20rpx;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  font-size: 28rpx;
  color: #666;
  margin-bottom: 15rpx;
  
  &.total {
    font-weight: bold;
    color: #1a73e8;
    margin-top: 20rpx;
    margin-bottom: 0;
  }
}

.bind-btn {
  margin: 40rpx auto;
  display: block;
  background-color: #1a73e8;
  color: white;
  width: 75%;
  font-size: 32rpx;
  height: 90rpx;
  line-height: 90rpx;
  border-radius: 45rpx; /* 高度的一半，确保完全圆角 */
}

.agreement-hint {
  text-align: center;
  font-size: 24rpx;
  color: #999;
  margin-bottom: 30rpx;
}
</style>