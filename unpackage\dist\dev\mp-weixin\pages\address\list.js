"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_request = require("../../utils/request.js");
const common_assets = require("../../common/assets.js");
const _sfc_main = {
  data() {
    return {
      addressList: [],
      loading: false
    };
  },
  onLoad() {
    this.checkLoginAndLoadData();
  },
  onShow() {
    this.checkLoginAndLoadData();
  },
  methods: {
    // 检查登录状态并加载数据
    checkLoginAndLoadData() {
      const isLoggedIn = common_vendor.index.getStorageSync("isLoggedIn");
      const token = common_vendor.index.getStorageSync("token");
      if (!isLoggedIn || !token) {
        common_vendor.index.showModal({
          title: "提示",
          content: "请先登录后再查看收货地址",
          showCancel: false,
          success: () => {
            common_vendor.index.navigateTo({
              url: "/pages/login/login"
            });
          }
        });
        return;
      }
      this.loadAddressList();
    },
    async loadAddressList() {
      try {
        this.loading = true;
        const response = await utils_request.api.mall.address.list();
        if (response.code === 200) {
          this.addressList = response.data.map((item) => ({
            id: item.id,
            name: item.receiverName,
            phone: item.receiverPhone,
            province: item.province,
            city: item.city,
            district: item.district,
            detailAddress: item.detailAddress,
            isDefault: item.isDefault === 1
          }));
        } else {
          common_vendor.index.showToast({
            title: response.message || "获取地址列表失败",
            icon: "none"
          });
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/address/list.vue:99", "获取地址列表失败:", error);
        if (error.code === 401) {
          common_vendor.index.removeStorageSync("token");
          common_vendor.index.removeStorageSync("isLoggedIn");
          common_vendor.index.removeStorageSync("userInfo");
          common_vendor.index.showModal({
            title: "登录已过期",
            content: "请重新登录",
            showCancel: false,
            success: () => {
              common_vendor.index.navigateTo({
                url: "/pages/login/login"
              });
            }
          });
        } else {
          common_vendor.index.showToast({
            title: error.message || "网络错误，请稍后重试",
            icon: "none"
          });
        }
      } finally {
        this.loading = false;
      }
    },
    goBack() {
      common_vendor.index.navigateBack();
    },
    selectAddress(address) {
      const pages = getCurrentPages();
      const prevPage = pages[pages.length - 2];
      if (prevPage && prevPage.$vm) {
        prevPage.$vm.address = address;
      }
      common_vendor.index.navigateBack();
    },
    editAddress(address) {
      common_vendor.index.navigateTo({
        url: `/pages/address/edit?id=${address.id}`
      });
    },
    goToAddAddress() {
      common_vendor.index.navigateTo({
        url: "/pages/address/add"
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.loading
  }, $data.loading ? {} : $data.addressList.length > 0 ? {
    c: common_vendor.f($data.addressList, (item, index, i0) => {
      return common_vendor.e({
        a: common_vendor.t(item.name),
        b: common_vendor.t(item.phone),
        c: item.isDefault
      }, item.isDefault ? {} : {}, {
        d: common_vendor.t(item.province),
        e: common_vendor.t(item.city),
        f: common_vendor.t(item.district),
        g: common_vendor.t(item.detailAddress),
        h: common_vendor.o(($event) => $options.editAddress(item), index),
        i: index,
        j: common_vendor.o(($event) => $options.selectAddress(item), index)
      });
    })
  } : !$data.loading ? {
    e: common_assets._imports_0$5
  } : {}, {
    b: $data.addressList.length > 0,
    d: !$data.loading,
    f: common_vendor.o((...args) => $options.goToAddAddress && $options.goToAddAddress(...args))
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/address/list.js.map
