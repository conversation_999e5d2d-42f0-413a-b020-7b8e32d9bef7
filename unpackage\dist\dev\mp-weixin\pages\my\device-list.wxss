/* 水平间距 */
/* 水平间距 */
/* 全局样式修复 - 确保页面占满屏幕宽度 */
page {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
}

/* 通用容器样式 */
.container, .page-container {
  width: 100%;
  min-height: 100vh;
  box-sizing: border-box;
}

/* 修复小程序页面宽度问题 */
uni-page-body {
  width: 100% !important;
}

/* 确保所有view元素正确继承宽度 */
view {
  box-sizing: border-box;
}
.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f8f8f8;
}
.header {
  background-color: #4285f4;
  padding: 10px 0;
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  z-index: 100;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
.header .header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 15px;
  height: 44px;
}
.header .back-btn {
  width: 30px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
}
.header .title {
  font-size: 18px;
  font-weight: 500;
  color: #fff;
  flex: 1;
  text-align: center;
  margin: 0 10px;
}
.header .header-icons {
  display: flex;
  align-items: center;
  gap: 15px;
  width: 30px;
  justify-content: flex-end;
}
.status-tabs {
  background-color: #4285f4;
  position: -webkit-sticky;
  position: sticky;
  z-index: 90;
}
.status-tabs .tab-scroll {
  white-space: nowrap;
  width: 100%;
  height: 50px;
}
.status-tabs .tab-scroll .tab-container {
  display: flex;
  height: 100%;
  padding: 0 15px;
}
.status-tabs .tab-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 0 10px;
  color: rgba(255, 255, 255, 0.8);
  font-size: 15px;
  position: relative;
  height: 100%;
  min-width: 80px;
}
.status-tabs .tab-item.active {
  color: #fff;
  font-weight: bold;
}
.status-tabs .tab-item .count-badge {
  font-size: 12px;
  background-color: rgba(255, 255, 255, 0.2);
  color: #fff;
  border-radius: 10px;
  padding: 0 6px;
  height: 16px;
  line-height: 16px;
  margin-left: 4px;
}
.status-tabs .active-indicator {
  position: absolute;
  bottom: 0;
  left: 0;
  height: 3px;
  background-color: #fff;
  border-radius: 3px 3px 0 0;
  transition: all 0.3s ease;
}
.device-list {
  flex: 1;
  height: calc(100vh - 114px);
  padding: 10px 15px;
  box-sizing: border-box;
}
.empty-device {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 0;
}
.empty-device .empty-image {
  width: 150px;
  height: 100px;
  margin-bottom: 20px;
  opacity: 0.6;
}
.empty-device .empty-text {
  font-size: 15px;
  color: #999;
  margin-bottom: 25px;
}
.empty-device .add-btn {
  padding: 8px 25px;
  background-color: #4285f4;
  color: #fff;
  border-radius: 20px;
  font-size: 14px;
  box-shadow: 0 4px 12px rgba(66, 133, 244, 0.2);
}
.device-item {
  background-color: #fff;
  border-radius: 12px;
  padding: 15px;
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);
  transition: all 0.2s;
  position: relative;
  overflow: hidden;
}
.device-item:active {
  transform: scale(0.98);
  opacity: 0.9;
  background-color: #f9f9f9;
}
.device-item::after {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background-color: #4285f4;
  border-radius: 4px 0 0 4px;
}
.device-item.offline::after {
  background-color: #999;
}
.device-item.abnormal::after {
  background-color: #f44336;
}
.device-icon {
  width: 50px;
  height: 50px;
  border-radius: 10px;
  overflow: hidden;
  margin-right: 12px;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(66, 133, 244, 0.1);
}
.device-icon .icon-image {
  width: 30px;
  height: 30px;
}
.device-info {
  flex: 1;
  overflow: hidden;
}
.device-info .name-row {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
}
.device-info .device-name {
  font-size: 16px;
  color: #333;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-right: 8px;
  flex: 1;
}
.device-info .device-id {
  font-size: 12px;
  color: #999;
  margin-bottom: 6px;
  display: block;
}
.device-info .device-meta {
  display: flex;
  align-items: center;
  gap: 10px;
}
.device-info .device-meta .device-time {
  font-size: 12px;
  color: #999;
  display: flex;
  align-items: center;
}
.device-status {
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 10px;
  display: inline-block;
  white-space: nowrap;
}
.device-status.online {
  background-color: rgba(76, 175, 80, 0.1);
  color: #4caf50;
}
.device-status.offline {
  background-color: rgba(153, 153, 153, 0.1);
  color: #999;
}
.device-status.abnormal {
  background-color: rgba(244, 67, 54, 0.1);
  color: #f44336;
}
.device-actions {
  margin-left: 10px;
}
.device-actions .use-btn {
  display: flex;
  align-items: center;
  padding: 5px 12px;
  background-color: #4285f4;
  color: white;
  border-radius: 15px;
  font-size: 13px;
  white-space: nowrap;
}
.device-actions .use-btn text {
  margin-right: 3px;
}
.device-actions .used-btn {
  display: flex;
  align-items: center;
  padding: 5px 12px;
  background-color: #f44336;
  color: white;
  border-radius: 15px;
  font-size: 13px;
  white-space: nowrap;
}
.device-actions .used-btn text {
  margin-right: 3px;
}
.load-more,
.no-more {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 15px 0;
  font-size: 13px;
  color: #999;
}
.load-more .loading-icon,
.no-more .loading-icon {
  animation: rotate 1s linear infinite;
  margin-right: 5px;
}
.no-more {
  padding: 20px 0;
}
.fab {
  position: fixed;
  right: 20px;
  bottom: 80px;
  width: 56px;
  height: 56px;
  background-color: #4285f4;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(66, 133, 244, 0.3);
  z-index: 100;
  transition: all 0.2s;
}
.fab:active {
  transform: scale(0.95);
  opacity: 0.9;
}
@keyframes rotate {
from {
    transform: rotate(0deg);
}
to {
    transform: rotate(360deg);
}
}