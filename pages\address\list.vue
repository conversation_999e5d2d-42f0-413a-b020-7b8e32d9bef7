<template>
	<view class="address-list">
		<view class="loading-container" v-if="loading">
			<text class="loading-text">加载中...</text>
		</view>

		<view class="address-container" v-else-if="addressList.length > 0">
			<view class="address-item" v-for="(item, index) in addressList" :key="index" @click="selectAddress(item)">
				<view class="address-info">
					<view class="address-name-phone">
						<text class="name">{{item.name}}</text>
						<text class="phone">{{item.phone}}</text>
						<text class="default-tag" v-if="item.isDefault">默认</text>
					</view>
					<view class="address-detail">{{item.province}} {{item.city}} {{item.district}} {{item.detailAddress}}</view>
				</view>
				<view class="edit-btn" @click.stop="editAddress(item)">
					<text class="iconfont icon-edit">✎</text>
				</view>
			</view>
		</view>

		<view class="empty-address" v-else-if="!loading">
			<image class="empty-image" src="/static/images/empty-order.png" mode="aspectFit"></image>
			<text class="empty-text">您还没有添加收货地址</text>
		</view>
		
		<view class="add-address">
			<button class="add-btn" @click="goToAddAddress">添加新地址</button>
		</view>
	</view>
</template>

<script>
import { api } from '@/utils/request.js'

export default {
	data() {
		return {
			addressList: [],
			loading: false
		}
	},
	onLoad() {
		this.checkLoginAndLoadData();
	},
	onShow() {
		// 从其他页面返回时重新加载地址列表
		this.checkLoginAndLoadData();
	},
	methods: {
		// 检查登录状态并加载数据
		checkLoginAndLoadData() {
			const isLoggedIn = uni.getStorageSync('isLoggedIn');
			const token = uni.getStorageSync('token');

			if (!isLoggedIn || !token) {
				// 未登录，跳转到登录页面
				uni.showModal({
					title: '提示',
					content: '请先登录后再查看收货地址',
					showCancel: false,
					success: () => {
						uni.navigateTo({
							url: '/pages/login/login'
						});
					}
				});
				return;
			}

			// 已登录，加载地址列表
			this.loadAddressList();
		},
		async loadAddressList() {
			try {
				this.loading = true;
				const response = await api.mall.address.list();

				if (response.code === 200) {
					// 转换后端数据格式为前端需要的格式
					this.addressList = response.data.map(item => ({
						id: item.id,
						name: item.receiverName,
						phone: item.receiverPhone,
						province: item.province,
						city: item.city,
						district: item.district,
						detailAddress: item.detailAddress,
						isDefault: item.isDefault === 1
					}));
				} else {
					uni.showToast({
						title: response.message || '获取地址列表失败',
						icon: 'none'
					});
				}
			} catch (error) {
				console.error('获取地址列表失败:', error);

				// 检查是否是认证错误
				if (error.code === 401) {
					// 清除登录状态
					uni.removeStorageSync('token');
					uni.removeStorageSync('isLoggedIn');
					uni.removeStorageSync('userInfo');

					uni.showModal({
						title: '登录已过期',
						content: '请重新登录',
						showCancel: false,
						success: () => {
							uni.navigateTo({
								url: '/pages/login/login'
							});
						}
					});
				} else {
					uni.showToast({
						title: error.message || '网络错误，请稍后重试',
						icon: 'none'
					});
				}
			} finally {
				this.loading = false;
			}
		},
		goBack() {
			uni.navigateBack();
		},
		selectAddress(address) {
			// 选择地址后返回上一页并传递地址数据
			const pages = getCurrentPages();
			const prevPage = pages[pages.length - 2];

			// 将选中的地址赋值给上一页
			if (prevPage && prevPage.$vm) {
				prevPage.$vm.address = address;
			}

			uni.navigateBack();
		},
		editAddress(address) {
			// 跳转到编辑地址页面
			uni.navigateTo({
				url: `/pages/address/edit?id=${address.id}`
			});
		},
		goToAddAddress() {
			// 跳转到添加地址页面
			uni.navigateTo({
				url: '/pages/address/add'
			});
		}
	}
}
</script>

<style lang="scss">
.address-list {
	min-height: 100vh;
	background-color: #f5f5f5;
	width: 100%;
	box-sizing: border-box;
	padding-bottom: 180rpx; /* 为底部按钮留出空间 */
}

.loading-container {
	display: flex;
	justify-content: center;
	align-items: center;
	padding: 100rpx 0;
}

.loading-text {
	font-size: 28rpx;
	color: #999;
}

.address-container {
	padding: 20rpx 0;
	width: 100%;
	box-sizing: border-box;
}

.address-item {
	display: flex;
	align-items: center;
	background-color: #ffffff;
	padding: 30rpx 20rpx;
	margin-bottom: 2rpx;
	width: 100%;
	box-sizing: border-box;
}

.address-info {
	flex: 1;
	width: 0; /* 强制flex子元素重新计算宽度 */
	min-width: 0; /* 允许flex子元素收缩 */
}

.address-name-phone {
	margin-bottom: 10rpx;
	display: flex;
	align-items: center;
	flex-wrap: wrap;
}

.name {
	font-size: 30rpx;
	font-weight: bold;
	margin-right: 15rpx;
}

.phone {
	font-size: 28rpx;
	color: #666;
	margin-right: 15rpx;
}

.default-tag {
	display: inline-block;
	font-size: 22rpx;
	color: #4285f4;
	padding: 2rpx 10rpx;
	border: 1rpx solid #4285f4;
	border-radius: 20rpx;
	flex-shrink: 0;
}

.address-detail {
	font-size: 28rpx;
	color: #333;
	line-height: 1.4;
	word-break: break-all;
	overflow-wrap: break-word;
}

.edit-btn {
	padding: 10rpx;
	flex-shrink: 0;
	display: flex;
	align-items: center;
	justify-content: center;
	width: 60rpx;
	height: 60rpx;
}

.icon-edit {
	font-size: 36rpx;
	color: #999;
}

.empty-address {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 150rpx 40rpx;
	min-height: 60vh;
	text-align: center;
}

.empty-image {
	width: 200rpx;
	height: 200rpx;
	margin-bottom: 30rpx;
}

.empty-text {
	font-size: 28rpx;
	color: #999;
	text-align: center;
	line-height: 1.5;
}

.add-address {
	padding: 40rpx 20rpx;
	position: fixed;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: #fff;
	box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.1);
	/* 适配底部安全区域 */
	padding-bottom: calc(40rpx + env(safe-area-inset-bottom));
}

.add-btn {
	height: 90rpx;
	line-height: 90rpx;
	text-align: center;
	background-color: #4285f4;
	color: #ffffff;
	border-radius: 45rpx;
	font-size: 32rpx;
	width: 100%;
	box-sizing: border-box;
}

/* 响应式设计 */
@media screen and (max-width: 750rpx) {
	.address-item {
		padding: 25rpx 15rpx;
	}

	.name {
		font-size: 28rpx;
	}

	.phone {
		font-size: 26rpx;
	}

	.address-detail {
		font-size: 26rpx;
	}

	.default-tag {
		font-size: 20rpx;
	}
}

@media screen and (min-width: 751rpx) {
	.address-container {
		max-width: 750rpx;
		margin: 0 auto;
	}

	.add-address {
		max-width: 750rpx;
		left: 50%;
		transform: translateX(-50%);
		right: auto;
	}
}

/* 页面级别样式重置 */
page {
	width: 100%;
	height: 100%;
	margin: 0;
	padding: 0;
	box-sizing: border-box;
}
</style>