/* 水平间距 */
/* 水平间距 */
/* 全局样式修复 - 确保页面占满屏幕宽度 */
page {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
}

/* 通用容器样式 */
.container, .page-container {
  width: 100%;
  min-height: 100vh;
  box-sizing: border-box;
}

/* 修复小程序页面宽度问题 */
uni-page-body {
  width: 100% !important;
}

/* 确保所有view元素正确继承宽度 */
view {
  box-sizing: border-box;
}

/* 页面级别样式 */
page {
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}
.cart-container {
  min-height: 100vh;
  width: 100%;
  background-color: #f5f5f5;
  display: flex;
  flex-direction: column;
  padding-bottom: 120rpx;
  /* 为底部栏留出空间 */
  box-sizing: border-box;
  margin: 0;
  padding-left: 0;
  padding-right: 0;
}
.manage-section {
  display: flex;
  justify-content: flex-end;
  padding: 20rpx;
  background-color: #f5f5f5;
}
.manage-btn {
  background-color: #4285f4;
  color: #ffffff;
  padding: 10rpx 20rpx;
  border-radius: 20rpx;
  font-size: 28rpx;
  text-align: center;
  min-width: 80rpx;
}
.cart-list {
  padding: 20rpx;
  flex: 1;
  /* 占满剩余空间 */
}
.cart-item {
  display: flex;
  align-items: center;
  background-color: #ffffff;
  padding: 20rpx;
  margin-bottom: 20rpx;
  border-radius: 10rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}
.checkbox {
  margin-right: 20rpx;
}
.product-image {
  width: 160rpx;
  height: 160rpx;
  margin-right: 20rpx;
  border-radius: 8rpx;
}
.product-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}
.product-name {
  font-size: 28rpx;
  margin-bottom: 10rpx;
}
.product-spec {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 10rpx;
}
.stock-info {
  font-size: 22rpx;
  color: #666;
  margin-bottom: 20rpx;
}
.bottom-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.product-price {
  font-size: 32rpx;
  color: #f44336;
  font-weight: bold;
}
.quantity-selector {
  display: flex;
  align-items: center;
  border: 1rpx solid #eee;
  border-radius: 4rpx;
}
.quantity-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f5f5;
  font-size: 28rpx;
}
.quantity-input {
  width: 60rpx;
  height: 60rpx;
  text-align: center;
}
.bottom-bar {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 100rpx;
  background-color: #ffffff;
  padding: 0 20rpx;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
}
.left-section {
  display: flex;
  align-items: center;
  flex: 1;
}
.right-section {
  display: flex;
  align-items: center;
}
.select-all {
  display: flex;
  align-items: center;
}
.select-all text {
  font-size: 28rpx;
  margin-left: 10rpx;
}
.price-info {
  display: flex;
  align-items: baseline;
  margin-left: 30rpx;
  font-size: 28rpx;
}
.total-price {
  color: #f44336;
  font-size: 36rpx;
  font-weight: bold;
}
.checkout-btn, .delete-btn {
  padding: 10rpx 30rpx;
  border-radius: 40rpx;
  font-size: 28rpx;
  height: 70rpx;
  line-height: 70rpx;
  text-align: center;
}
.checkout-btn {
  background-color: #f44336;
  color: #ffffff;
  width: 240rpx;
}
.delete-btn {
  background-color: #ff5252;
  color: #ffffff;
  width: 240rpx;
}
.empty-cart {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx 20rpx;
  position: relative;
  min-height: 60vh;
  background: linear-gradient(135deg, #f8f9ff 0%, #f0f4ff 100%);
}
.empty-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #ffffff;
  padding: 60rpx 40rpx;
  border-radius: 20rpx;
  box-shadow: 0 8rpx 30rpx rgba(66, 133, 244, 0.1);
  position: relative;
  z-index: 2;
  width: 90%;
  max-width: 600rpx;
}
.empty-icon {
  margin-bottom: 30rpx;
}
.empty-icon image {
  width: 160rpx;
  height: 160rpx;
}
.empty-text {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 40rpx;
}
.empty-title {
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 10rpx;
}
.empty-subtitle {
  font-size: 26rpx;
  color: #666;
  text-align: center;
}
.go-shopping {
  background: linear-gradient(135deg, #4285f4 0%, #5a9bff 100%);
  color: #ffffff;
  padding: 24rpx 60rpx;
  border-radius: 50rpx;
  font-size: 30rpx;
  font-weight: 500;
  box-shadow: 0 6rpx 20rpx rgba(66, 133, 244, 0.3);
  border: none;
}
.go-shopping:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 15rpx rgba(66, 133, 244, 0.3);
}
.empty-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
  overflow: hidden;
}
.decoration-circle {
  position: absolute;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(66, 133, 244, 0.1) 0%, rgba(90, 155, 255, 0.05) 100%);
}
.circle-1 {
  width: 120rpx;
  height: 120rpx;
  top: 10%;
  left: 10%;
  animation: float 6s ease-in-out infinite;
}
.circle-2 {
  width: 80rpx;
  height: 80rpx;
  top: 20%;
  right: 15%;
  animation: float 8s ease-in-out infinite reverse;
}
.circle-3 {
  width: 100rpx;
  height: 100rpx;
  bottom: 15%;
  left: 20%;
  animation: float 7s ease-in-out infinite;
}
@keyframes float {
0%, 100% {
    transform: translateY(0px);
}
50% {
    transform: translateY(-20rpx);
}
}