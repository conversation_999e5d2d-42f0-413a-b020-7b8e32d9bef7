## wx-server-sdk 更新日志

### 3.0.0

1. `U` 更新 protobufjs 升级至 v7，不再支持 nodejs <= 10 的版本
1. `U` 更新 增加 `Database` 下几个接口的 d.ts 定义

### 2.6.3

1. `U` 更新 protobufjs 依赖不再锁定固定版本号

### 2.5.4

1. `F` 修复 `geoNear` 返回值没有乘以地球半径的问题

### 2.4.0

1. `U` 更新 优化了错误信息的展示

### 2.3.0

1. `A` 新增 支持[环境共享](https://developers.weixin.qq.com/miniprogram/dev/wxcloud/basis/resource-sharing.html)相关接口

### 2.2.0

1. `A` 新增 `getCloudCallSign` 接口

### 2.1.2

1. `U` 更新 更新 `node-sdk` 依赖至 `2.1.1`

### 2.1.1

1. `A` 新增 小游戏虚拟支付沙箱环境接口

### 2.1.0

1. `A` 新增 小游戏虚拟支付能力
1. `A` 新增 数据库更新和删除 API 增加 `multi` 选项

### 1.9.0

1. `A` 新增 微信支付能力
1. `A` 新增 云函数灰度能力

### 1.8.3

1. `A` 新增 定义文件 `index.d.ts` 

### 1.8.0

1. `U` 更新 云调用 `openapi` 支持设置不自动转换驼峰命名法和蛇底命名法

### 1.7.1

1. `F` 修复 `DYNAMIC_CURRENT_ENV` 在事务中使用的问题

### 1.7.0

1. `A` 新增 数据库事务 API
1. `U` 更新 不再支持 node 8 以下运行环境，使用开发者工具本地调试需使用 node 8 或以上

### 1.6.0

1. `A` 新增 定时触发器中支持使用 `DYNAMIC_CURRENT_ENV`

### 1.5.3

1. `U` 更新 `tcb-admin-node` 依赖至 `1.16.2`，修复触发器内使用云调用可能失败的问题

### 1.5.0

1. `A` 新增 `logger` 方法，支持高级日志

### 1.4.0

1. `A` 更新 `tcb-admin-node` 依赖更新至 `1.15.0`，新增 `not`、`expr`、`jsonSchema` 操作符

### 1.3.1

1. `F` 更新 `tcb-admin-node` 依赖更新至 `1.13.1`，修复 push 操作符接收数组参数的问题

### 1.3.0

1. `A` 新增 查询操作符：all, elemMatch, exists, size, mod
1. `A` 新增 更新操作符：push, pull, pullAll, addToSet, rename, max, min
1. `A` 新增 聚合流水线阶段：lookup
1. `A` 新增 可用于聚合流水线 lookup 阶段的 pipeline 操作符

### 1.2.2

1. `F` 修复 使用 `DYNAMIC_CURRENT_ENV` 时发起云调用会失败的问题

### 1.2.1

1. `A` 新增 数据库多个查询、投影操作符

### 1.1.1

1. `A` 新增 常量 `DYNAMIC_CURRENT_ENV`

### 0.8.1

1. `A` 新增 `updateConfig` 用于覆写配置

### 0.7.0 (2019.06.28)

1. `A` 新增 `getWXContext` 新增返回表示云函数最初调用来源的 `SOURCE` 字段

### v0.5.1（2019.05.15）

1. `F` 修复 部分 `tmp secret key expired` 错误问题
1. `A` 新增 云调用服务端调用 [详情](../guide/openapi/openapi.md)
1. `A` 新增 云调用开放数据调用 [详情](../guide/openapi/openapi.md)

### v0.2.2 (2019.03.22)

1. `A` 增加 云开发数据库支持地理位置 API

### 0.1.4 (2019.01.22)

1. `F` 修复 `db.createCollection` 失败时没有抛错的问题

### 0.1.2 (2018.12.25)

1. `U` 更新 `tcb-admin-node` 依赖更新至 `1.4.2`，修复了 `doc.set` 如果带复杂类型会失败的问题

### 0.1.1 (2018.12.11)

1. `U` 更新 `tcb-admin-node` 依赖更新至 `1.4.0`，主要修复了 `serverDate` 存取问题，同时增加了接口默认 15 秒超时间

### 0.1.0 (2018.12.7)

1. `U` 重构 开放服务注册

### 0.0.26 (2018.11.15)

1. `A` 修复 数据库删除数据时 `stats.removed` 返回为 0 的问题

### 0.0.23（2018.10.29）

1. `A` 新增 数据库支持正则查询

### 0.0.22 (2018.10.24)

1. `U` 更新 `callOpenAPI` 不需传 `event` 参数

### 0.0.21 (2018.10.19)

1. `U` 新增 `getWXContext` API，用于获取微信调用上下文，包括 `APPID`、`OPENID` 和 `UNIONID`

### 0.0.11（2018.08.30）

1. `U` 更新 `callFunction` 返回 requestID

### 0.0.9（2018.08.19）

1. `U` 更新 `getTempFileURL` 传入参数更新

### 0.0.7（2018.08.16）

1. `A` 新增 云开发数据库、云函数、文件存储基础能力