<template>
  <view class="container">
    <!-- 顶部导航栏 -->
    <!-- <view class="header">
      <view class="back-btn" @click="goBack">
        <image src="/static/icons/back.png" mode="aspectFit" class="back-icon"></image>
      </view>
      <view class="title">订单详情</view>
      <view class="placeholder"></view>
    </view> -->

    <!-- 订单状态 -->
    <view class="status-section">
      <view class="status-text">{{ statusInfo.text }}</view>
      <view class="status-desc">{{ statusInfo.desc }}</view>
    </view>

    <!-- 订单进度 -->
    <view class="progress-section">
      <view class="progress-item" v-for="(item, index) in progressItems" :key="index">
        <view class="dot" :class="{ active: item.active, done: item.done }"></view>
        <view class="progress-line" v-if="index < progressItems.length - 1" :class="{ active: progressItems[index + 1].active || progressItems[index + 1].done }"></view>
        <view class="progress-content" :class="{ active: item.active, done: item.done }">
          <view class="progress-title">{{ item.title }}</view>
          <view class="progress-time">{{ item.time || '' }}</view>
        </view>
      </view>
    </view>

    <!-- 收货地址 -->
    <view class="info-card">
      <view class="card-title">
        <image src="/static/icons/location.png" mode="aspectFit" class="title-icon"></image>
        <text>收货地址</text>
      </view>
      <view class="address-info">
        <view class="contact">{{ order.receiverName }} {{ order.receiverPhone }}</view>
        <view class="address">{{ order.address }}</view>
      </view>
    </view>

    <!-- 商品信息 -->
    <view class="info-card">
      <view class="card-title">
        <image src="/static/icons/shop.png" mode="aspectFit" class="title-icon"></image>
        <text>{{ order.shopName }}</text>
      </view>
      <view class="product-item">
        <image :src="baseUrl + order.productImage" mode="aspectFill" class="product-image"></image>
        <view class="product-info">
          <view class="product-name">{{ order.productName }}</view>
          <view class="product-spec">{{ order.productSpec }}</view>
          <view class="price-count">
            <view class="price">¥{{ order.price?.toFixed(2) }}</view>
            <view class="count">x{{ order.count }}</view>
          </view>
        </view>
      </view>
      <view class="order-amount">
        <view class="amount-item">
          <text>商品总价</text>
          <text>¥{{ (order.price * order.count)?.toFixed(2) }}</text>
        </view>
        <view class="amount-item">
          <text>运费</text>
          <text>¥{{ order.shippingFee?.toFixed(2) }}</text>
        </view>
        <view class="amount-divider"></view>
        <view class="amount-item total">
          <text>实付款</text>
          <text class="total-price">¥{{ (order.price * order.count + order.shippingFee)?.toFixed(2) }}</text>
        </view>
      </view>
    </view>

    <!-- 订单信息 -->
    <view class="info-card">
      <view class="card-title">
        <image src="/static/icons/order.png" mode="aspectFit" class="title-icon"></image>
        <text>订单信息</text>
      </view>
      <view class="order-info">
        <view class="info-item">
          <text class="info-label">订单编号</text>
          <view class="info-value-copy">
            <text>{{ order.orderNo }}</text>
            <view class="copy-btn" @click="copyOrderNo">复制</view>
          </view>
        </view>
        <view class="info-item">
          <text class="info-label">创建时间</text>
          <text class="info-value">{{ order.createTime }}</text>
        </view>
        <view class="info-item" v-if="order.payTime">
          <text class="info-label">付款时间</text>
          <text class="info-value">{{ order.payTime }}</text>
        </view>
        <view class="info-item" v-if="order.shippingTime">
          <text class="info-label">发货时间</text>
          <text class="info-value">{{ order.shippingTime }}</text>
        </view>
        <view class="info-item" v-if="order.completeTime">
          <text class="info-label">完成时间</text>
          <text class="info-value">{{ order.completeTime }}</text>
        </view>
      </view>
    </view>

    <!-- 底部按钮 -->
    <view class="bottom-btns">
      <view class="btn-group">
        <view class="btn btn-default" v-if="order.status === 'pending'">取消订单</view>
        <view class="btn btn-default" v-if="order.status === 'completed'">删除订单</view>
        <view class="btn btn-default">联系客服</view>
        <view class="btn btn-primary" v-if="order.status === 'pending'">付款</view>
        <view class="btn btn-primary" v-if="order.status === 'shipped'">确认收货</view>
        <view class="btn btn-primary" v-if="order.status === 'completed'">评价</view>
      </view>
    </view>
  </view>
</template>

<script>
	import { apiService } from '../../utils/request';
export default {
  data() {
    return {
      // baseUrl : "http://127.0.0.1:6549/api",
      baseUrl : "https://www.narenqiqige.com/api",
      orderId: '',
      order: {}
	  // {
   //      id: '1001',
   //      orderNo: 'DD20230515143022',
   //      shopName: '专业染发店',
   //      status: 'shipped',
   //      productName: '专业染发剂套装',
   //      productSpec: '棕色 200ml',
   //      productImage: '/static/images/product1.jpg',
   //      price: 128.00,
   //      count: 1,
   //      shippingFee: 12.00,
   //      receiverName: '张三',
   //      receiverPhone: '138****5678',
   //      address: '广东省广州市天河区体育西路123号',
   //      createTime: '2023-05-15 14:30:22',
   //      payTime: '2023-05-15 14:35:46',
   //      shippingTime: '2023-05-16 09:20:33',
   //      completeTime: ''
   //    }
    };
  },
  computed: {
    statusInfo() {
      const statusMap = {
        'pending': {
          text: '等待付款',
          desc: '请在24小时内完成支付，超时订单将自动取消'
        },
        'paid': {
          text: '等待发货',
          desc: '商家已接单，正在准备发货'
        },
        'shipped': {
          text: '等待收货',
          desc: '商品已发货，请耐心等待'
        },
        'completed': {
          text: '交易完成',
          desc: '交易已完成，感谢您的购买'
        },
        'cancelled': {
          text: '交易关闭',
          desc: '订单已取消'
        }
      };
      return statusMap[this.order.status] || { text: '未知状态', desc: '' };
    },
    progressItems() {
      const items = [
        {
          title: '提交订单',
          time: this.order.createTime,
          active: false,
          done: true || this.order.status === 'cancelled'
        },
        {
          title: '付款成功',
          time: this.order.payTime,
          active: false,
          done: this.order.status !== 'pending'
        },
        {
          title: '商家发货',
          time: this.order.shippingTime,
          active: false,
          done: this.order.status === 'shipped' || this.order.status === 'completed'
        },
        {
          title: '交易完成',
          time: this.order.completeTime,
          active: false,
          done: this.order.status === 'completed'
        }
      ];
      
      // 设置当前激活状态
      for (let i = items.length - 1; i >= 0; i--) {
        if (items[i].done && !items[i+1]?.done) {
          items[i].active = true;
          break;
        }
      }
      
      return items;
    }
  },
  onLoad(options) {
    if (options.id) {
      this.orderId = options.id;
      // 实际应用中这里应该请求API获取订单详情
      this.fetchOrderDetail(this.orderId);
    }
  },
  methods: {
    goBack() {
      uni.navigateBack();
    },
    copyOrderNo() {
      uni.setClipboardData({
        data: this.order.orderNo,
        success: () => {
          uni.showToast({
            title: '复制成功',
            icon: 'success'
          });
        }
      });
    },
    fetchOrderDetail(orderId) {
      // 这里发起网络请求获取订单详情
	  apiService.mall.orders.detail(orderId)
	            .then(res=>{
	              this.orderId = res.data.id
	              this.order = res.data;
                console.log("订单详细",res)
	            }).catch((err) => {
	  
	            })
      console.log('获取订单详情，ID:', orderId);
      // console
	  
    }
  }
};
</script>

<style lang="scss" scoped>
.container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 120rpx;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 88rpx;
  background-color: #ffffff;
  padding: 0 30rpx;
  
  .back-btn {
    width: 40rpx;
    height: 40rpx;
    display: flex;
    align-items: center;
    
    .back-icon {
      width: 36rpx;
      height: 36rpx;
    }
  }
  
  .title {
    font-size: 34rpx;
    font-weight: bold;
    color: #333;
  }
  
  .placeholder {
    width: 40rpx;
  }
}

.status-section {
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 40rpx 30rpx;
  background-color: #ff6b81;
  color: #fff;
  
  .status-text {
    font-size: 36rpx;
    font-weight: bold;
    margin-bottom: 10rpx;
  }
  
  .status-desc {
    font-size: 26rpx;
    opacity: 0.8;
  }
}

.progress-section {
  background-color: #ffffff;
  padding: 30rpx;
  margin-bottom: 20rpx;
  
  .progress-item {
    display: flex;
    position: relative;
    
    .dot {
      width: 20rpx;
      height: 20rpx;
      border-radius: 50%;
      background-color: #ddd;
      margin-top: 8rpx;
      
      &.active {
        background-color: #ff6b81;
      }
      
      &.done {
        background-color: #ff6b81;
      }
    }
    
    .progress-line {
      position: absolute;
      left: 10rpx;
      top: 28rpx;
      width: 2rpx;
      height: 60rpx;
      background-color: #ddd;
      
      &.active {
        background-color: #ff6b81;
      }
    }
    
    .progress-content {
      margin-left: 30rpx;
      margin-bottom: 30rpx;
      
      .progress-title {
        font-size: 28rpx;
        color: #666;
        margin-bottom: 6rpx;
      }
      
      .progress-time {
        font-size: 24rpx;
        color: #999;
      }
      
      &.active {
        .progress-title {
          color: #ff6b81;
          font-weight: 500;
        }
      }
      
      &.done {
        .progress-title {
          color: #333;
        }
      }
    }
  }
}

.info-card {
  background-color: #ffffff;
  margin-bottom: 20rpx;
  padding: 30rpx;
  
  .card-title {
    display: flex;
    align-items: center;
    margin-bottom: 20rpx;
    
    .title-icon {
      width: 32rpx;
      height: 32rpx;
      margin-right: 10rpx;
    }
    
    text {
      font-size: 30rpx;
      font-weight: 500;
      color: #333;
    }
  }
  
  .address-info {
    padding-left: 10rpx;
    
    .contact {
      font-size: 30rpx;
      color: #333;
      margin-bottom: 10rpx;
    }
    
    .address {
      font-size: 28rpx;
      color: #666;
      line-height: 1.5;
    }
  }
  
  .product-item {
    display: flex;
    padding: 20rpx 0;
    border-bottom: 1rpx solid #f2f2f2;
    
    .product-image {
      width: 160rpx;
      height: 160rpx;
      border-radius: 8rpx;
      background-color: #f5f5f5;
    }
    
    .product-info {
      flex: 1;
      margin-left: 20rpx;
      position: relative;
      
      .product-name {
        font-size: 28rpx;
        color: #333;
        margin-bottom: 10rpx;
      }
      
      .product-spec {
        font-size: 24rpx;
        color: #999;
        margin-bottom: 40rpx;
      }
      
      .price-count {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        display: flex;
        justify-content: space-between;
        
        .price {
          font-size: 30rpx;
          color: #ff6b81;
          font-weight: 500;
        }
        
        .count {
          font-size: 26rpx;
          color: #999;
        }
      }
    }
  }
  
  .order-amount {
    padding: 30rpx 0 10rpx;
    
    .amount-item {
      display: flex;
      justify-content: space-between;
      margin-bottom: 20rpx;
      font-size: 28rpx;
      color: #666;
    }
    
    .amount-divider {
      height: 1rpx;
      background-color: #f2f2f2;
      margin: 20rpx 0;
    }
    
    .total {
      font-size: 30rpx;
      color: #333;
      
      .total-price {
        color: #ff6b81;
        font-weight: 500;
      }
    }
  }
  
  .order-info {
    .info-item {
      display: flex;
      margin-bottom: 20rpx;
      
      .info-label {
        width: 160rpx;
        font-size: 28rpx;
        color: #999;
      }
      
      .info-value {
        flex: 1;
        font-size: 28rpx;
        color: #333;
      }
      
      .info-value-copy {
        flex: 1;
        display: flex;
        align-items: center;
        
        text {
          font-size: 28rpx;
          color: #333;
        }
        
        .copy-btn {
          margin-left: 20rpx;
          font-size: 24rpx;
          color: #2196f3;
        }
      }
    }
  }
}

.bottom-btns {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ffffff;
  padding: 20rpx 30rpx;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  
  .btn-group {
    display: flex;
    justify-content: flex-end;
    
    .btn {
      margin-left: 20rpx;
      padding: 16rpx 30rpx;
      font-size: 28rpx;
      border-radius: 40rpx;
      
      &.btn-default {
        color: #666;
        background-color: #f5f5f5;
        border: 1rpx solid #ddd;
      }
      
      &.btn-primary {
        color: #fff;
        background-color: #ff6b81;
      }
    }
  }
}
</style> 