<template>
  <view class="card-detail-container">
    <!-- 运营商信息 -->
    <view class="operator-info card">
      <view class="operator-header">
        <image class="operator-logo" src="/static/images/unicom-logo.png"></image>
        <text class="operator-name">中国联通</text>
        <view class="operator-status">
          <text class="status-badge">已激活</text>
          <text class="status-refresh" @click="refreshStatus">⟳</text>
        </view>
      </view>
      
      <view class="info-list">
        <view class="info-row">
          <text class="info-label">充值号：</text>
          <text class="info-value">********</text>
          <button class="mini-btn" size="mini" @click="switchAccount">切换</button>
        </view>
        <view class="info-row">
          <text class="info-label">WIFI名称：</text>
          <text class="info-value">Asimon</text>
        </view>
        <view class="info-row">
          <text class="info-label">WIFI密码：</text>
          <text class="info-value">Simon..@Simon</text>
          <button class="mini-btn" size="mini" @click="changeWifiPassword">修改</button>
        </view>
      </view>
    </view>
    
    <!-- 套餐信息 -->
    <view class="package-info card">
      <view class="package-header">
        <text class="section-title">我的套餐</text>
        <text class="package-expire">到期时间: 2029-01-12</text>
      </view>
      <view class="package-content">
        <view class="package-progress">
          <text class="package-name">每月99996高速流量套餐</text>
          <progress :percent="100" activeColor="#1890ff" backgroundColor="#e6f7ff" 
                    stroke-width="10" class="progress-bar"/>
          <view class="progress-text">
            <text>已用0%</text>
            <text>剩余100%</text>
          </view>
        </view>
        <button class="renew-btn" @click="renewPackage">立即续费</button>
      </view>
    </view>
    
    <!-- 公众号提示 -->
    <view class="official-account card">
      <image class="qrcode" src="/static/images/qrcode.jpg"></image>
      <view class="official-content">
        <text class="official-title">关注公众号</text>
        <text class="official-desc">获取最新优惠活动、查询流量使用情况</text>
        <button class="follow-btn" open-type="contact">关注公众号</button>
      </view>
    </view>
    
    <!-- 功能按钮网格 -->
    <view class="grid-container">
      <view class="grid-item" v-for="(item, index) in gridItems" :key="index" 
            @click="handleGridClick(item)" @touchstart="touchStart(index)" 
            @touchend="touchEnd(index)" :class="{'active': activeIndex === index}">
        <image class="grid-icon" :src="'/static/icons/grid-' + index + '.png'"></image>
        <text class="grid-text">{{item}}</text>
      </view>
    </view>
    
    <!-- 底部导航 -->
    <view class="bottom-nav">
      <view class="nav-item" @click="navigateTo('activate')">
        <image class="nav-icon" src="/static/icons/activate.png"></image>
        <text>激活</text>
      </view>
      <view class="nav-item" @click="navigateTo('service')">
        <image class="nav-icon" src="/static/icons/service.png"></image>
        <text>客服</text>
      </view>
      <view class="nav-item" @click="navigateTo('user')">
        <image class="nav-icon" src="/static/icons/user.png"></image>
        <text>我的</text>
      </view>
    </view>
    
    <!-- 操作反馈提示 -->
    <uni-popup ref="popup" type="message">
      <uni-popup-message :type="popupType" :message="popupMessage" :duration="2000"></uni-popup-message>
    </uni-popup>
  </view>
</template>

<script>
export default {
  data() {
    return {
      gridItems: [
        '我的余额', '套餐订购', '我的套餐', '订购记录',
        '实名认证', '设备重启', '切换网络', '显示 Wifi',
        '关闭设备', '恢复出厂设置', '问题投诉', '企业微信'
      ],
      activeIndex: -1,
      popupType: 'success',
      popupMessage: '',
      refreshing: false
    }
  },
  methods: {
    handleGridClick(item) {
      this.popupMessage = `即将跳转到${item}`;
      this.popupType = 'info';
      this.$refs.popup.open();
      
      // 模拟异步操作
      setTimeout(() => {
        uni.showToast({
          title: `正在打开${item}`,
          icon: 'none'
        });
      }, 500);
    },
    touchStart(index) {
      this.activeIndex = index;
    },
    touchEnd() {
      this.activeIndex = -1;
    },
    refreshStatus() {
      if (this.refreshing) return;
      
      this.refreshing = true;
      this.popupMessage = '正在刷新状态...';
      this.popupType = 'loading';
      this.$refs.popup.open();
      
      setTimeout(() => {
        this.refreshing = false;
        this.popupMessage = '状态已刷新';
        this.popupType = 'success';
        this.$refs.popup.open();
      }, 1500);
    },
    switchAccount() {
      uni.navigateTo({
        url: '/pages/account/switch'
      });
    },
    changeWifiPassword() {
      uni.navigateTo({
        url: '/pages/wifi/setting'
      });
    },
    renewPackage() {
      uni.navigateTo({
        url: '/pages/package/renew'
      });
    },
    goToUserCenter() {
      uni.navigateTo({
        url: '/pages/user/center'
      });
    },
    navigateTo(page) {
      let url = '';
      switch(page) {
        case 'activate':
          url = '/pages/card/activate';
          break;
        case 'service':
          url = '/pages/service/index';
          break;
        case 'user':
          url = '/pages/user/center';
          break;
      }
      uni.navigateTo({ url });
    }
  }
}
</script>

<style lang="scss">
$primary-color: #1890ff;
$success-color: #52c41a;
$warning-color: #fa8c16;
$error-color: #f5222d;
$text-color: #333;
$text-secondary: #666;
$border-color: #eee;
$bg-color: #f5f5f5;
$card-radius: 12rpx;
$shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);

.card-detail-container {
  padding: 20rpx 20rpx 120rpx;
  background-color: $bg-color;
  min-height: 100vh;
  box-sizing: border-box;
}

/* 顶部标题栏 */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
  padding: 0 10rpx;
  
  .title-container {
    .title {
      font-size: 38rpx;
      font-weight: bold;
      color: $text-color;
      line-height: 1.4;
    }
    
    .subtitle {
      font-size: 24rpx;
      color: $text-secondary;
    }
  }
  
  .user-avatar {
    width: 80rpx;
    height: 80rpx;
    border-radius: 50%;
    overflow: hidden;
    
    image {
      width: 100%;
      height: 100%;
    }
  }
}

/* 卡片通用样式 */
.card {
  background-color: #fff;
  border-radius: $card-radius;
  padding: 24rpx;
  margin-bottom: 24rpx;
  box-shadow: $shadow;
}

/* 运营商信息 */
.operator-info {
  .operator-header {
    display: flex;
    align-items: center;
    margin-bottom: 24rpx;
    padding-bottom: 20rpx;
    border-bottom: 1rpx solid $border-color;
    
    .operator-logo {
      width: 60rpx;
      height: 60rpx;
      margin-right: 16rpx;
    }
    
    .operator-name {
      font-size: 32rpx;
      font-weight: bold;
      color: $primary-color;
      flex: 1;
    }
    
    .operator-status {
      display: flex;
      align-items: center;
      
      .status-badge {
        background-color: $success-color;
        color: #fff;
        padding: 4rpx 12rpx;
        border-radius: 20rpx;
        font-size: 24rpx;
        margin-right: 16rpx;
      }
      
      .status-refresh {
        font-size: 32rpx;
        color: $text-secondary;
        padding: 8rpx;
      }
    }
  }
  
  .info-list {
    .info-row {
      display: flex;
      align-items: center;
      padding: 16rpx 0;
      font-size: 28rpx;
      
      &:not(:last-child) {
        border-bottom: 1rpx dashed $border-color;
      }
      
      .info-label {
        color: $text-secondary;
        width: 180rpx;
      }
      
      .info-value {
        flex: 1;
        color: $text-color;
      }
    }
  }
  
  .mini-btn {
    margin: 0;
    padding: 0 20rpx;
    height: 50rpx;
    line-height: 50rpx;
    font-size: 24rpx;
    background-color: $primary-color;
    color: #fff;
    border-radius: 25rpx;
    
    &::after {
      border: none;
    }
  }
}

/* 套餐信息 */
.package-info {
  .package-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20rpx;
    
    .section-title {
      font-size: 30rpx;
      font-weight: bold;
      color: $text-color;
    }
    
    .package-expire {
      font-size: 24rpx;
      color: $text-secondary;
    }
  }
  
  .package-content {
    .package-progress {
      margin-bottom: 20rpx;
      
      .package-name {
        font-size: 28rpx;
        color: $text-color;
        margin-bottom: 16rpx;
      }
      
      .progress-bar {
        width: 100%;
        margin-bottom: 8rpx;
      }
      
      .progress-text {
        display: flex;
        justify-content: space-between;
        font-size: 24rpx;
        color: $text-secondary;
      }
    }
    
    .renew-btn {
      background-color: $primary-color;
      color: #fff;
      height: 80rpx;
      line-height: 80rpx;
      border-radius: 40rpx;
      font-size: 30rpx;
      
      &::after {
        border: none;
      }
    }
  }
}

/* 公众号提示 */
.official-account {
  display: flex;
  align-items: center;
  padding: 30rpx;
  
  .qrcode {
    width: 160rpx;
    height: 160rpx;
    margin-right: 30rpx;
    border: 1rpx solid $border-color;
  }
  
  .official-content {
    flex: 1;
    
    .official-title {
      display: block;
      font-size: 32rpx;
      font-weight: bold;
      color: $text-color;
      margin-bottom: 12rpx;
    }
    
    .official-desc {
      display: block;
      font-size: 24rpx;
      color: $text-secondary;
      margin-bottom: 20rpx;
    }
  }
  
  .follow-btn {
    background-color: $primary-color;
    color: #fff;
    height: 60rpx;
    line-height: 60rpx;
    border-radius: 30rpx;
    font-size: 26rpx;
    padding: 0 30rpx;
    
    &::after {
      border: none;
    }
  }
}

/* 功能按钮网格 */
.grid-container {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 1px;
  background-color: $border-color;
  margin-bottom: 24rpx;
  border-radius: $card-radius;
  overflow: hidden;
  
  .grid-item {
    background-color: #fff;
    height: 180rpx;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    transition: all 0.2s;
    
    &.active {
      background-color: #f0f7ff;
      transform: scale(0.98);
    }
    
    .grid-icon {
      width: 60rpx;
      height: 60rpx;
      margin-bottom: 16rpx;
    }
    
    .grid-text {
      font-size: 26rpx;
      color: $text-color;
      text-align: center;
    }
  }
}

/* 底部导航 */
.bottom-nav {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  display: flex;
  justify-content: space-around;
  padding: 20rpx 0;
  box-shadow: 0 -4rpx 12rpx rgba(0, 0, 0, 0.08);
  z-index: 100;
  
  .nav-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    
    .nav-icon {
      width: 48rpx;
      height: 48rpx;
      margin-bottom: 8rpx;
    }
    
    text {
      font-size: 24rpx;
      color: $text-secondary;
    }
    
    &.active text {
      color: $primary-color;
    }
  }
}

/* 响应式调整 */
@media (max-width: 320px) {
  .grid-container .grid-text {
    font-size: 22rpx;
  }
  
  .package-info .package-name {
    font-size: 24rpx;
  }
}
</style>