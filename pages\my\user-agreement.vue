<template>
  <view class="container">
    <!-- 协议内容 -->
    <scroll-view class="content" scroll-y="true">
      <view class="agreement-content">
        <view class="section">
          <text class="section-title">重要提示：</text>
          <text class="section-text">请您仔细阅读以下条款，并确认您已完全理解本协议之规定，尤其是免除及限制责任的条款、知识产权条款、法律适用及争议解决条款。</text>
        </view>

        <view class="section">
          <text class="section-text">若您对本声明或本协议任何条款有异议（包括手机、电脑等移动设备客户端、下同）所提供的全部服务。</text>
        </view>

        <view class="section">
          <text class="section-title">一、协议的接受、变更与补充</text>
          <text class="section-text">1. 勾选本协议前请仔细点击"注册"，将视为您签署了本协议，表明您同意接受本协议全部条款的约束，本协议将构成您与芭佰邑科技有限公司及其经营的"芭佰邑"平台之间具有约束力的法律文件。无论您是进入会确认公司网页，还是在芭佰邑上发布任何内容，或者是直接或通过各类方式（如站外API引用等）间接使用芭佰邑网络服务和数据的行为，都将被视作已无条件接受本声明所涉全部内容。</text>
        </view>

        <view class="section">
          <text class="section-text">2. 芭佰邑有权对本协议进行修改，协议修改后，芭佰邑将通过网站公告或发送通知等方式公布修改内容，修改后的协议一经公布即具有效的代替原协议。如果您不同意本协议的修改，请立即停止访问或使用本网站或取消已经获得的服务；如果您继续使用访问或使用本网站，则视为您接受本协议的修改。</text>
        </view>

        <view class="section">
          <text class="section-text">3. 签署的本协议所列明的条款，并不能完全涵盖您与芭佰邑之间所有的权利和义务，因此，芭佰邑不定期公布的其他声明、规则等均为本协议之补充协议，为本协议不可分割的组成部分，与本协议具有同等法律效力。</text>
        </view>

        <view class="section">
          <text class="section-text">4. 如本协议与芭佰邑平台其它协议条款不一致，以其它协议条款内容为准。</text>
        </view>

        <view class="section">
          <text class="section-title">二、用户注册与账户管理</text>
          <text class="section-text">1. 用户在使用芭佰邑平台服务前，需要注册一个芭佰邑账户。用户应当使用真实、准确、完整的信息进行注册，并及时更新注册信息。</text>
        </view>

        <view class="section">
          <text class="section-text">2. 用户注册成功后，芭佰邑将给予每个用户一个用户账号及相应的密码，该用户账号和密码由用户负责保管；用户应当对以其用户账号进行的所有活动和事件负法律责任。</text>
        </view>

        <view class="section">
          <text class="section-text">3. 用户不得将账户转让、出售或以其他方式处分给第三方。如果芭佰邑发现使用者并非账户初始注册人，有权在未通知的情况下回收该账户。</text>
        </view>

        <view class="section">
          <text class="section-text">4. 用户有义务保证密码和账户的安全，用户利用该密码和账户所进行的一切活动引起的任何损失或损害，由用户自行承担全部责任，芭佰邑不承担任何责任。</text>
        </view>

        <view class="section">
          <text class="section-title">三、用户行为规范</text>
          <text class="section-text">1. 用户在使用芭佰邑平台服务时，必须遵守中华人民共和国相关法律法规，不得利用芭佰邑平台从事违法违规活动。</text>
        </view>

        <view class="section">
          <text class="section-text">2. 用户不得发布、传播任何违法、有害、威胁、辱骂、骚扰、侵权、中伤、粗俗、猥亵或其他道德上令人反感的内容。</text>
        </view>

        <view class="section">
          <text class="section-text">3. 用户不得恶意注册芭佰邑账户，包括但不限于频繁注册、批量注册账户等行为。</text>
        </view>

        <view class="section">
          <text class="section-text">4. 用户不得使用技术手段干扰芭佰邑平台的正常运行或干扰其他用户对芭佰邑平台服务的使用。</text>
        </view>

        <view class="section">
          <text class="section-title">四、平台服务内容</text>
          <text class="section-text">1. 芭佰邑平台为用户提供商品展示、在线购买、订单管理、客户服务等电子商务服务。</text>
        </view>

        <view class="section">
          <text class="section-text">2. 芭佰邑有权根据业务发展需要，增加、修改或终止部分服务功能，并会提前通知用户。</text>
        </view>

        <view class="section">
          <text class="section-text">3. 用户理解并同意，芭佰邑平台可能因维护、升级等原因需要暂停服务，芭佰邑将尽力减少因此给用户造成的影响。</text>
        </view>

        <view class="section">
          <text class="section-title">五、商品信息与交易</text>
          <text class="section-text">1. 芭佰邑平台上展示的商品信息由商家提供，芭佰邑对商品信息的真实性、准确性不承担责任，但会尽力审核。</text>
        </view>

        <view class="section">
          <text class="section-text">2. 商品价格可能因市场变化而调整，以用户下单时显示的价格为准。</text>
        </view>

        <view class="section">
          <text class="section-text">3. 用户下单后，即与相应商家形成买卖合同关系，应当按照约定履行付款义务。</text>
        </view>

        <view class="section">
          <text class="section-text">4. 如因商品质量问题需要退换货，按照国家相关法律法规和平台退换货政策执行。</text>
        </view>

        <view class="section">
          <text class="section-title">六、知识产权</text>
          <text class="section-text">1. 芭佰邑平台上的所有内容，包括但不限于文字、图片、音频、视频、软件、程序、版面设计等，均受知识产权法律保护。</text>
        </view>

        <view class="section">
          <text class="section-text">2. 未经芭佰邑书面许可，用户不得复制、传播、展示、镜像、上载、下载芭佰邑平台上的任何内容。</text>
        </view>

        <view class="section">
          <text class="section-text">3. 用户在芭佰邑平台上发布的内容，应确保拥有相应的知识产权或已获得权利人的授权。</text>
        </view>

        <view class="section">
          <text class="section-title">七、隐私保护</text>
          <text class="section-text">1. 芭佰邑重视用户隐私保护，具体隐私保护措施请参见《隐私政策》。</text>
        </view>

        <view class="section">
          <text class="section-text">2. 芭佰邑承诺不会向第三方泄露用户个人信息，但法律法规另有规定的除外。</text>
        </view>

        <view class="section">
          <text class="section-title">八、免责声明</text>
          <text class="section-text">1. 芭佰邑对因不可抗力或其他芭佰邑无法控制的原因造成的网络服务中断或其他缺陷不承担责任。</text>
        </view>

        <view class="section">
          <text class="section-text">2. 用户明确同意其使用芭佰邑平台服务所存在的风险将完全由其自己承担。</text>
        </view>

        <view class="section">
          <text class="section-text">3. 芭佰邑不担保网络服务一定能满足用户的要求，也不担保网络服务不会中断。</text>
        </view>

        <view class="section">
          <text class="section-title">九、违约责任</text>
          <text class="section-text">1. 如用户违反本协议的任何条款，芭佰邑有权立即终止向该用户提供服务。</text>
        </view>

        <view class="section">
          <text class="section-text">2. 因用户违约给芭佰邑造成损失的，用户应当承担赔偿责任。</text>
        </view>

        <view class="section">
          <text class="section-title">十、争议解决</text>
          <text class="section-text">1. 本协议的订立、执行和解释及争议的解决均应适用中华人民共和国法律。</text>
        </view>

        <view class="section">
          <text class="section-text">2. 如双方就本协议内容或其执行发生任何争议，双方应尽量友好协商解决；协商不成时，任何一方均可向芭佰邑所在地的人民法院起诉。</text>
        </view>

        <view class="section">
          <text class="section-title">十一、其他条款</text>
          <text class="section-text">1. 本协议构成双方对本协议之约定事项及其他有关事宜的完整协议，除本协议规定的之外，未赋予本协议各方其他权利。</text>
        </view>

        <view class="section">
          <text class="section-text">2. 如本协议中的任何条款无论因何种原因完全或部分无效或不具有执行力，本协议的其余条款仍应有效并且有约束力。</text>
        </view>

        <view class="section">
          <text class="section-text">3. 本协议自用户点击同意之日起生效，直至用户注销账户或芭佰邑终止向用户提供服务时终止。</text>
        </view>

        <view class="section">
          <text class="section-text">4. 芭佰邑科技有限公司保留对本协议的最终解释权。</text>
        </view>

        <view class="section">
          <text class="section-text">本协议于2024年1月1日更新并生效。</text>
        </view>

        <view class="section">
          <text class="section-text">如您对本协议有任何疑问，请联系我们的客服团队。</text>
        </view>

        <view class="section">
          <text class="section-text">感谢您选择芭佰邑平台！</text>
        </view>

        <view class="section">
          <text class="section-title">十二、平台运营规则</text>
          <text class="section-text">1. 芭佰邑平台致力于为用户提供优质的购物体验和服务。平台将根据相关法律法规和行业标准，制定并执行相应的运营规则。</text>
        </view>

        <view class="section">
          <text class="section-text">2. 用户在平台上的所有行为应当遵守平台运营规则，包括但不限于商品发布规则、交易规则、评价规则等。</text>
        </view>

        <view class="section">
          <text class="section-text">3. 平台有权根据业务发展需要和法律法规要求，对运营规则进行调整和完善，并将通过适当方式通知用户。</text>
        </view>

        <view class="section">
          <text class="section-text">4. 用户违反平台运营规则的，平台有权采取相应措施，包括但不限于警告、限制功能、暂停服务、终止服务等。</text>
        </view>

        <view class="section">
          <text class="section-title">十三、商品质量保证</text>
          <text class="section-text">1. 芭佰邑平台上销售的商品均经过严格的质量把控，我们承诺为用户提供优质的商品和服务。</text>
        </view>

        <view class="section">
          <text class="section-text">2. 所有商品均符合国家相关质量标准和安全标准，用户可以放心购买和使用。</text>
        </view>

        <view class="section">
          <text class="section-text">3. 如用户收到的商品存在质量问题，可以根据平台的退换货政策申请退换货，我们将积极处理并承担相应责任。</text>
        </view>

        <view class="section">
          <text class="section-text">4. 平台建立了完善的商品质量监控体系，定期对商品质量进行抽检和评估，确保商品质量持续稳定。</text>
        </view>

        <view class="section">
          <text class="section-title">十四、售后服务保障</text>
          <text class="section-text">1. 芭佰邑平台提供完善的售后服务体系，包括商品咨询、订单查询、退换货处理、投诉建议等服务。</text>
        </view>

        <view class="section">
          <text class="section-text">2. 用户可以通过平台客服热线、在线客服、邮件等多种方式联系我们，我们将及时响应并处理用户的问题和需求。</text>
        </view>

        <view class="section">
          <text class="section-text">3. 我们承诺在收到用户咨询或投诉后，将在24小时内给予回复，并在合理时间内解决用户的问题。</text>
        </view>

        <view class="section">
          <text class="section-text">4. 对于复杂问题，我们将建立专门的处理流程，确保问题得到妥善解决，维护用户的合法权益。</text>
        </view>

        <view class="section">
          <text class="section-title">十五、用户权益保护</text>
          <text class="section-text">1. 芭佰邑平台高度重视用户权益保护，建立了完善的用户权益保护机制。</text>
        </view>

        <view class="section">
          <text class="section-text">2. 用户在平台上享有知情权、选择权、公平交易权、安全保障权、隐私权等合法权益。</text>
        </view>

        <view class="section">
          <text class="section-text">3. 平台将采取有效措施保护用户的个人信息安全，防止用户信息泄露、滥用或非法获取。</text>
        </view>

        <view class="section">
          <text class="section-text">4. 如用户权益受到损害，可以通过平台提供的投诉渠道进行投诉，我们将认真调查并依法处理。</text>
        </view>

        <view class="section">
          <text class="section-title">十六、技术服务支持</text>
          <text class="section-text">1. 芭佰邑平台采用先进的技术架构和安全防护措施，确保平台的稳定运行和数据安全。</text>
        </view>

        <view class="section">
          <text class="section-text">2. 我们提供7×24小时的技术支持服务，确保用户能够随时正常使用平台服务。</text>
        </view>

        <view class="section">
          <text class="section-text">3. 平台定期进行系统维护和升级，以提升用户体验和服务质量。维护期间可能会暂时影响服务，我们将提前通知用户。</text>
        </view>

        <view class="section">
          <text class="section-text">4. 如用户在使用过程中遇到技术问题，可以联系我们的技术支持团队，我们将提供专业的技术指导和帮助。</text>
        </view>

        <view class="section">
          <text class="section-title">十七、合作伙伴管理</text>
          <text class="section-text">1. 芭佰邑平台与众多优质商家和服务提供商建立了合作关系，共同为用户提供优质的商品和服务。</text>
        </view>

        <view class="section">
          <text class="section-text">2. 我们对合作伙伴实行严格的准入标准和管理制度，确保合作伙伴具备相应的资质和能力。</text>
        </view>

        <view class="section">
          <text class="section-text">3. 平台定期对合作伙伴进行评估和考核，对于不符合要求的合作伙伴将终止合作关系。</text>
        </view>

        <view class="section">
          <text class="section-text">4. 用户与合作伙伴之间发生的纠纷，平台将积极协调处理，维护用户的合法权益。</text>
        </view>

        <view class="section">
          <text class="section-title">十八、数据安全管理</text>
          <text class="section-text">1. 芭佰邑平台建立了完善的数据安全管理体系，采用多层次的安全防护措施保护用户数据。</text>
        </view>

        <view class="section">
          <text class="section-text">2. 我们采用先进的加密技术对用户敏感信息进行加密存储和传输，防止数据泄露和非法访问。</text>
        </view>

        <view class="section">
          <text class="section-text">3. 平台建立了数据备份和恢复机制，确保在发生意外情况时能够及时恢复用户数据。</text>
        </view>

        <view class="section">
          <text class="section-text">4. 我们定期进行安全审计和漏洞扫描，及时发现和修复安全隐患，确保系统安全稳定运行。</text>
        </view>

        <view class="section">
          <text class="section-title">十九、法律责任承担</text>
          <text class="section-text">1. 芭佰邑平台将严格遵守相关法律法规，承担相应的法律责任。</text>
        </view>

        <view class="section">
          <text class="section-text">2. 如因平台原因给用户造成损失，我们将依法承担相应的赔偿责任。</text>
        </view>

        <view class="section">
          <text class="section-text">3. 用户因违反本协议或相关法律法规给平台或第三方造成损失的，应当承担相应的法律责任。</text>
        </view>

        <view class="section">
          <text class="section-text">4. 双方应当通过友好协商解决争议，协商不成的，可以向有管辖权的人民法院提起诉讼。</text>
        </view>

        <view class="section">
          <text class="section-title">二十、协议的生效与终止</text>
          <text class="section-text">1. 本协议自用户点击同意并成功注册账户之日起生效，对双方具有法律约束力。</text>
        </view>

        <view class="section">
          <text class="section-text">2. 用户可以随时申请注销账户，注销后本协议自动终止。但注销前产生的权利义务关系不因协议终止而消除。</text>
        </view>

        <view class="section">
          <text class="section-text">3. 如用户严重违反本协议，平台有权单方面终止本协议并注销用户账户。</text>
        </view>

        <view class="section">
          <text class="section-text">4. 协议终止后，用户应当停止使用平台服务，平台有权删除用户账户及相关数据。</text>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<script>
export default {
  data() {
    return {};
  }
};
</script>

<style lang="scss">
.container {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.content {
  height: 100vh;
  background-color: #fff;
}

.agreement-content {
  padding: 40rpx;
}

.section {
  margin-bottom: 40rpx;
}

.section-title {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  line-height: 1.6;
}

.section-text {
  display: block;
  font-size: 28rpx;
  color: #666;
  line-height: 1.8;
  text-align: justify;
}
</style>
