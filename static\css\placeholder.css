/**
 * 占位图片CSS
 * 这个样式表为应用中的图片提供纯CSS占位替代
 */

/* 通用图片占位符 */
img[src$=".png"]:not([src*="/"]), 
img[src^="../../static/"]:not([src*="data:"]) {
  position: relative;
  background-color: #e0e0e0;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  text-align: center;
}

img[src$=".png"]:not([src*="/"]):after, 
img[src^="../../static/"]:not([src*="data:"]):after {
  content: attr(alt) " " attr(width) "x" attr(height);
  position: absolute;
  font-size: 12px;
  color: #616161;
  text-align: center;
}

/* Logo */
img[src$="soruda-logo.png"],
img[src*="/soruda-logo.png"] {
  background-color: #4285f4 !important;
  border-radius: 50%;
}

img[src$="soruda-logo.png"]:after,
img[src*="/soruda-logo.png"]:after {
  content: "Logo";
  color: white;
}

/* 底部导航图标 */
img[src*="/tabbar/home.png"] {
  background-color: transparent !important;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23999"><path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z"/></svg>');
  background-size: 60%;
  background-position: center;
  background-repeat: no-repeat;
}

img[src*="/tabbar/home_selected.png"] {
  background-color: transparent !important;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%234285f4"><path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z"/></svg>');
  background-size: 60%;
  background-position: center;
  background-repeat: no-repeat;
}

img[src*="/tabbar/palette.png"] {
  background-color: transparent !important;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23999"><path d="M12 22C6.49 22 2 17.51 2 12S6.49 2 12 2s10 4.04 10 9c0 3.31-2.69 6-6 6h-1.77c-.28 0-.5.22-.5.5 0 .12.05.23.13.33.41.47.64 1.06.64 1.67A2.5 2.5 0 0 1 12 22zm0-18c-4.41 0-8 3.59-8 8s3.59 8 8 8c.28 0 .5-.22.5-.5a.54.54 0 0 0-.14-.35c-.41-.46-.63-1.05-.63-1.65a2.5 2.5 0 0 1 2.5-2.5H16c2.21 0 4-1.79 4-4 0-3.86-3.59-7-8-7z"/><circle cx="6.5" cy="11.5" r="1.5"/><circle cx="9.5" cy="7.5" r="1.5"/><circle cx="14.5" cy="7.5" r="1.5"/><circle cx="17.5" cy="11.5" r="1.5"/></svg>');
  background-size: 60%;
  background-position: center;
  background-repeat: no-repeat;
}

img[src*="/tabbar/palette_selected.png"] {
  background-color: transparent !important;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%234285f4"><path d="M12 22C6.49 22 2 17.51 2 12S6.49 2 12 2s10 4.04 10 9c0 3.31-2.69 6-6 6h-1.77c-.28 0-.5.22-.5.5 0 .12.05.23.13.33.41.47.64 1.06.64 1.67A2.5 2.5 0 0 1 12 22zm0-18c-4.41 0-8 3.59-8 8s3.59 8 8 8c.28 0 .5-.22.5-.5a.54.54 0 0 0-.14-.35c-.41-.46-.63-1.05-.63-1.65a2.5 2.5 0 0 1 2.5-2.5H16c2.21 0 4-1.79 4-4 0-3.86-3.59-7-8-7z"/><circle cx="6.5" cy="11.5" r="1.5"/><circle cx="9.5" cy="7.5" r="1.5"/><circle cx="14.5" cy="7.5" r="1.5"/><circle cx="17.5" cy="11.5" r="1.5"/></svg>');
  background-size: 60%;
  background-position: center;
  background-repeat: no-repeat;
}

img[src*="/tabbar/device.png"] {
  background-color: transparent !important;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23999"><path d="M4 6h18V4H4c-1.1 0-2 .9-2 2v11H0v3h14v-3H4V6zm19 2h-6c-.55 0-1 .45-1 1v10c0 .55.45 1 1 1h6c.55 0 1-.45 1-1V9c0-.55-.45-1-1-1zm-1 9h-4v-7h4v7z"/></svg>');
  background-size: 60%;
  background-position: center;
  background-repeat: no-repeat;
}

img[src*="/tabbar/device_selected.png"] {
  background-color: transparent !important;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%234285f4"><path d="M4 6h18V4H4c-1.1 0-2 .9-2 2v11H0v3h14v-3H4V6zm19 2h-6c-.55 0-1 .45-1 1v10c0 .55.45 1 1 1h6c.55 0 1-.45 1-1V9c0-.55-.45-1-1-1zm-1 9h-4v-7h4v7z"/></svg>');
  background-size: 60%;
  background-position: center;
  background-repeat: no-repeat;
}

img[src*="/tabbar/user.png"] {
  background-color: transparent !important;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23999"><path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/></svg>');
  background-size: 60%;
  background-position: center;
  background-repeat: no-repeat;
}

img[src*="/tabbar/user_selected.png"] {
  background-color: transparent !important;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%234285f4"><path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/></svg>');
  background-size: 60%;
  background-position: center;
  background-repeat: no-repeat;
}

/* 颜色分类图片 */
img[src*="color1.png"] {
  background-color: #5D4037 !important;
}
img[src*="color1.png"]:after {
  content: "生活色";
  color: white;
}

img[src*="color2.png"] {
  background-color: #00BCD4 !important;
}
img[src*="color2.png"]:after {
  content: "微潮色";
  color: white;
}

img[src*="color3.png"] {
  background-color: #FFEB3B !important;
}
img[src*="color3.png"]:after {
  content: "潮色";
  color: black;
}

img[src*="color4.png"] {
  background-color: #9E9E9E !important;
}
img[src*="color4.png"]:after {
  content: "深染浅";
  color: white;
}

img[src*="color5.png"] {
  background-color: #9C27B0 !important;
}
img[src*="color5.png"]:after {
  content: "浅染深";
  color: white;
}

/* 其他一些常用图标 */
img[src*="location.png"] {
  background-color: transparent !important;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23ffffff"><path d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z"/></svg>');
  background-size: 60%;
  background-position: center;
  background-repeat: no-repeat;
}

img[src*="bluetooth.png"] {
  background-color: #757575 !important;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23ffffff"><path d="M17.71 7.71L12 2h-1v7.59L6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 11 14.41V22h1l5.71-5.71-4.3-4.29 4.3-4.29zM13 5.83l1.88 1.88L13 9.59V5.83zm1.88 10.46L13 18.17v-3.76l1.88 1.88z"/></svg>');
  background-size: 60%;
  background-position: center;
  background-repeat: no-repeat;
}

img[src*="wifi.png"] {
  background-color: #4285f4 !important;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23ffffff"><path d="M1 9l2 2c4.97-4.97 13.03-4.97 18 0l2-2C16.93 2.93 7.08 2.93 1 9zm8 8l3 3 3-3c-1.65-1.66-4.34-1.66-6 0zm-4-4l2 2c2.76-2.76 7.24-2.76 10 0l2-2C15.14 9.14 8.87 9.14 5 13z"/></svg>');
  background-size: 60%;
  background-position: center;
  background-repeat: no-repeat;
}

img[src*="default-avatar.png"] {
  background-color: #E0E0E0 !important;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23757575"><path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/></svg>');
  background-size: 60%;
  background-position: center;
  background-repeat: no-repeat;
  border-radius: 50%;
}

/* 筛选图标 */
img[src*="filter.png"] {
  background-color: transparent !important;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23757575"><path d="M10 18h4v-2h-4v2zM3 6v2h18V6H3zm3 7h12v-2H6v2z"/></svg>');
  background-size: 60%;
  background-position: center;
  background-repeat: no-repeat;
}

/* 扫描图标 */
img[src*="scan.png"] {
  background-color: transparent !important;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23ffffff"><path d="M9.5 6.5v3h-3v-3h3M11 5H5v6h6V5zm-1.5 9.5v3h-3v-3h3M11 13H5v6h6v-6zm6.5-6.5v3h-3v-3h3M19 5h-6v6h6V5zm-6 8h1.5v1.5H13V13zm1.5 1.5H16V16h-1.5v-1.5zM16 13h1.5v1.5H16V13zm-3 3h1.5v1.5H13V16zm1.5 1.5H16V19h-1.5v-1.5zM16 16h1.5v1.5H16V16zm1.5-1.5H19V16h-1.5v-1.5zm0 3H19V19h-1.5v-1.5zM22 7h-2V4h-3V2h5v5zm0 15v-5h-2v3h-3v2h5zM2 22h5v-2H4v-3H2v5zM2 2v5h2V4h3V2H2z"/></svg>');
  background-size: 60%;
  background-position: center;
  background-repeat: no-repeat;
}

/* 我的页面图标 */
img[src*="store.png"] {
  background-color: #4285f4 !important;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23ffffff"><path d="M20 4H4v2h16V4zm1 10v-2l-1-5H4l-1 5v2h1v6h10v-6h4v6h2v-6h1zm-9 4H6v-4h6v4z"/></svg>');
  background-size: 60%;
  background-position: center;
  background-repeat: no-repeat;
}

img[src*="qrcode.png"] {
  background-color: #4285f4 !important;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23ffffff"><path d="M3 11h8V3H3v8zm2-6h4v4H5V5zm8-2v8h8V3h-8zm6 6h-4V5h4v4zM3 21h8v-8H3v8zm2-6h4v4H5v-4zm13-2h-3v-2h3V9h-3V7h5v8h-2zm-3 4h2v2h-2v-2zm-2 2h2v2h-2v-2zm6 0h2v-6h-2v6z"/></svg>');
  background-size: 60%;
  background-position: center;
  background-repeat: no-repeat;
}

/* 服务类别图标 */
img[src*="service1.png"] {
  background-color: #E91E63 !important;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23ffffff"><path d="M12 2C8.14 2 5 5.14 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.86-3.14-7-7-7zm.88 13.75h-1.75V14h1.75v1.75zm0-2.87h-1.75c0-2.84 2.62-2.62 2.62-4.38 0-.96-.79-1.75-1.75-1.75s-1.75.79-1.75 1.75H8.5C8.5 6.57 10.07 5 12 5s3.5 1.57 3.5 3.5c0 2.19-2.62 2.41-2.62 4.38z"/></svg>');
  background-size: 60%;
  background-position: center;
  background-repeat: no-repeat;
}

img[src*="service2.png"] {
  background-color: #9C27B0 !important;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23ffffff"><path d="M19.48 12.35c-1.57-4.08-7.16-4.3-5.81-10.23.1-.44-.37-.78-.75-.55C9.29 3.71 6.68 8 8.87 13.62c.18.46-.36.89-.75.59-1.81-1.37-2-3.34-1.84-4.75.06-.52-.62-.77-.91-.34-4.05 6.07 3.29 11.04 9.02 8.42.66-.31.51-1.22-.19-1.19z"/></svg>');
  background-size: 60%;
  background-position: center;
  background-repeat: no-repeat;
}

/* 产品图片占位符 */
img[src*="product1.png"] {
  background-color: #42A5F5 !important;
}
img[src*="product1.png"]:after {
  content: "直发膏";
  color: white;
}

img[src*="product2.png"] {
  background-color: #66BB6A !important;
}
img[src*="product2.png"]:after {
  content: "染发膏";
  color: white;
}

/* 服务网格通用样式 */
.service-item image {
  background-color: rgba(0, 0, 0, 0.05) !important;
  border-radius: 10rpx;
}

/* 修复服务网格和图标 */
.service-grid .service-item:nth-child(1) image { background-color: #E91E63 !important; }
.service-grid .service-item:nth-child(2) image { background-color: #9C27B0 !important; }
.service-grid .service-item:nth-child(3) image { background-color: #673AB7 !important; }
.service-grid .service-item:nth-child(4) image { background-color: #3F51B5 !important; }
.service-grid .service-item:nth-child(5) image { background-color: #2196F3 !important; }
.service-grid .service-item:nth-child(6) image { background-color: #03A9F4 !important; }
.service-grid .service-item:nth-child(7) image { background-color: #00BCD4 !important; }
.service-grid .service-item:nth-child(8) image { background-color: #009688 !important; }
.service-grid .service-item:nth-child(9) image { background-color: #4CAF50 !important; }
.service-grid .service-item:nth-child(10) image { background-color: #8BC34A !important; }
.service-grid .service-item:nth-child(11) image { background-color: #CDDC39 !important; }
.service-grid .service-item:nth-child(12) image { background-color: #FFEB3B !important; }
.service-grid .service-item:nth-child(13) image { background-color: #FFC107 !important; }
.service-grid .service-item:nth-child(14) image { background-color: #FF9800 !important; }
.service-grid .service-item:nth-child(15) image { background-color: #FF5722 !important; }

/* 功能菜单图标 */
.function-menu .menu-item image,
.function-grid .function-item image {
  background-color: #4285f4 !important;
  border-radius: 10rpx;
}

.function-menu .menu-row:nth-child(1) .menu-item:nth-child(1) image { background-color: #4285f4 !important; }
.function-menu .menu-row:nth-child(1) .menu-item:nth-child(2) image { background-color: #5E35B1 !important; }
.function-menu .menu-row:nth-child(1) .menu-item:nth-child(3) image { background-color: #689F38 !important; }
.function-menu .menu-row:nth-child(1) .menu-item:nth-child(4) image { background-color: #00897B !important; }

.function-menu .menu-row:nth-child(2) .menu-item:nth-child(1) image { background-color: #AFB42B !important; }
.function-menu .menu-row:nth-child(2) .menu-item:nth-child(2) image { background-color: #FFA000 !important; }
.function-menu .menu-row:nth-child(2) .menu-item:nth-child(3) image { background-color: #D81B60 !important; }
.function-menu .menu-row:nth-child(2) .menu-item:nth-child(4) image { background-color: #5D4037 !important; }

/* 颜色圆形显示 */
.color-circle {
  position: relative;
  overflow: visible !important;
}

.color-circle:after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  box-shadow: inset 0 0 0 2rpx rgba(0, 0, 0, 0.1);
}