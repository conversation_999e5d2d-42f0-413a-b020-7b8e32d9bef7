<template>
	<view class="container">
		<view class="header">
			<text class="title">API测试页面</text>
		</view>
		
		<view class="test-section">
			<text class="section-title">登录状态</text>
			<view class="status-info">
				<text>登录状态: {{ isLoggedIn ? '已登录' : '未登录' }}</text>
				<text v-if="userInfo">用户: {{ userInfo.nickname || userInfo.username }}</text>
			</view>
			
			<button v-if="!isLoggedIn" @click="goToLogin" class="btn">去登录</button>
			<button v-else @click="logout" class="btn secondary">退出登录</button>
		</view>
		
		<view class="test-section">
			<text class="section-title">API测试</text>
			
			<button @click="testCategoryAPI" class="btn">测试分类API</button>
			<button @click="testProductAPI" class="btn">测试商品API</button>
			<button @click="testAddressAPI" class="btn">测试地址API</button>
			<button @click="testCartAPI" class="btn">测试购物车API</button>
		</view>
		
		<view class="test-section">
			<text class="section-title">测试结果</text>
			<scroll-view class="result-area" scroll-y>
				<text class="result-text">{{ testResult }}</text>
			</scroll-view>
		</view>
	</view>
</template>

<script>
import { api } from '@/utils/request.js'

export default {
	data() {
		return {
			isLoggedIn: false,
			userInfo: null,
			testResult: '等待测试...\n'
		}
	},
	onLoad() {
		this.checkLoginStatus();
	},
	onShow() {
		this.checkLoginStatus();
	},
	methods: {
		checkLoginStatus() {
			this.isLoggedIn = uni.getStorageSync('isLoggedIn') || false;
			this.userInfo = uni.getStorageSync('userInfo') || null;
		},
		
		goToLogin() {
			uni.navigateTo({
				url: '/pages/login/login'
			});
		},
		
		logout() {
			uni.removeStorageSync('token');
			uni.removeStorageSync('isLoggedIn');
			uni.removeStorageSync('userInfo');
			this.isLoggedIn = false;
			this.userInfo = null;
			uni.showToast({
				title: '已退出登录',
				icon: 'success'
			});
		},
		
		addResult(text) {
			this.testResult += new Date().toLocaleTimeString() + ': ' + text + '\n';
		},
		
		async testCategoryAPI() {
			this.addResult('开始测试分类API...');
			try {
				const response = await api.mall.categories.list();
				this.addResult('分类API成功: ' + JSON.stringify(response, null, 2));
			} catch (error) {
				this.addResult('分类API失败: ' + JSON.stringify(error, null, 2));
			}
		},
		
		async testProductAPI() {
			this.addResult('开始测试商品API...');
			try {
				const response = await api.mall.products.list({ page: 1, limit: 5 });
				this.addResult('商品API成功: ' + JSON.stringify(response, null, 2));
			} catch (error) {
				this.addResult('商品API失败: ' + JSON.stringify(error, null, 2));
			}
		},
		
		async testAddressAPI() {
			this.addResult('开始测试地址API...');
			try {
				const response = await api.mall.address.list();
				this.addResult('地址API成功: ' + JSON.stringify(response, null, 2));
			} catch (error) {
				this.addResult('地址API失败: ' + JSON.stringify(error, null, 2));
			}
		},
		
		async testCartAPI() {
			this.addResult('开始测试购物车API...');
			try {
				const response = await api.mall.cart.list();
				this.addResult('购物车API成功: ' + JSON.stringify(response, null, 2));
			} catch (error) {
				this.addResult('购物车API失败: ' + JSON.stringify(error, null, 2));
			}
		}
	}
}
</script>

<style lang="scss">
.container {
	padding: 20rpx;
	min-height: 100vh;
	background-color: #f5f5f5;
}

.header {
	text-align: center;
	margin-bottom: 40rpx;
}

.title {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
}

.test-section {
	background-color: #fff;
	margin-bottom: 30rpx;
	padding: 30rpx;
	border-radius: 12rpx;
}

.section-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 20rpx;
	display: block;
}

.status-info {
	margin-bottom: 20rpx;
}

.status-info text {
	display: block;
	margin-bottom: 10rpx;
	color: #666;
}

.btn {
	width: 100%;
	height: 80rpx;
	line-height: 80rpx;
	text-align: center;
	background-color: #4285f4;
	color: #fff;
	border-radius: 8rpx;
	margin-bottom: 20rpx;
	font-size: 28rpx;
}

.btn.secondary {
	background-color: #999;
}

.result-area {
	height: 400rpx;
	background-color: #f8f8f8;
	padding: 20rpx;
	border-radius: 8rpx;
}

.result-text {
	font-size: 24rpx;
	color: #333;
	line-height: 1.5;
	white-space: pre-wrap;
}
</style>
