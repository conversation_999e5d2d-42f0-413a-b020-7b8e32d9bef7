"use strict";
const common_vendor = require("../../../common/vendor.js");
const CustomPicker = () => "../../../components/custom-picker.js";
const MasterColorinput = () => "../../../components/master-colorinput.js";
const _sfc_main = {
  components: {
    CustomPicker,
    MasterColorinput
  },
  data() {
    return {
      // 新增双氧乳比例数据
      hydrogenPeroxideRatios: [
        {
          key: "3%",
          label: "3%",
          name: "双氧乳3%",
          color: "#FFFFFF",
          fieldname: "hydrogenPeroxide3Percent",
          isOxidant: true
        },
        {
          key: "6%",
          label: "6%",
          name: "双氧乳6%",
          color: "#FFFFFF",
          fieldname: "hydrogenPeroxide6Percent",
          isOxidant: true
        },
        {
          key: "9%",
          label: "9%",
          name: "双氧乳9%",
          color: "#FFFFFF",
          fieldname: "hydrogenPeroxide9Percent",
          isOxidant: true
        },
        {
          key: "12%",
          label: "12%",
          name: "双氧乳12%",
          color: "#FFFFFF",
          fieldname: "hydrogenPeroxide12Percent",
          isOxidant: true
        }
      ],
      msgType: "info",
      // 可以是 success/warning/error/info
      isSingleMode: true,
      colorName: "",
      categoryName: "",
      colorHex: "",
      adjustAmount: null,
      // 滚轮调配量
      selectedRatio: "3%",
      isLoading: false,
      mode: "smart",
      // 默认为智能模式
      imageList: [],
      // 上传的图片列表
      // 以下是智能模式设置
      showPicker: false,
      showRatioModal: false,
      tempSelectedRatio: "",
      // 就是将中间的3% 6% 9% 12%放在里面
      selectedMixRatio: "1:1",
      // 默认选择1:1比例
      ratioOptions: ["1:1", "1:1.5", "1:2", "1:2.5", "1:3"],
      // 比例选项
      sliderValue: 0,
      // 默认选中第一个
      oxygenAmount: "",
      currentRatioIndex: 0,
      // 当前选中的索引
      ratioSettings: {
        // 存储每个百分比对应的设置
        "3%": {
          value: 0,
          proportion: ""
        },
        "6%": {
          value: 0,
          proportion: ""
        },
        "9%": {
          value: 0,
          proportion: ""
        },
        "12%": {
          value: 0,
          proportion: ""
        }
      },
      colorComponents: null,
      developerComponents: {},
      // 以下是大师模式设置
      colorList: [
        {
          name: "双氧乳3%",
          color: "#FFFFFF",
          value: 0,
          fieldname: "hydrogenPeroxide3Percent",
          isOxidant: true
        },
        {
          name: "双氧乳6%",
          color: "#FFFFFF",
          value: 0,
          fieldname: "hydrogenPeroxide6Percent",
          isOxidant: true
        },
        {
          name: "双氧乳9%",
          color: "#FFFFFF",
          value: 0,
          fieldname: "hydrogenPeroxide9Percent",
          isOxidant: true
        },
        {
          name: "双氧乳12%",
          color: "#FFFFFF",
          value: 0,
          fieldname: "hydrogenPeroxide12Percent",
          isOxidant: true
        },
        {
          name: "灰色",
          color: "#CCCCCC",
          value: 0,
          fieldname: "gray",
          isOxidant: false
        },
        {
          name: "绿色",
          color: "#2E7D32",
          value: 0,
          fieldname: "green",
          isOxidant: false
        },
        {
          name: "黄色",
          color: "#FFD600",
          value: 0,
          fieldname: "yellow",
          isOxidant: false
        },
        {
          name: "橙色",
          color: "#FFA726",
          value: 0,
          fieldname: "orange",
          isOxidant: false
        },
        {
          name: "红色",
          color: "#D32F2F",
          value: 0,
          fieldname: "red",
          isOxidant: false
        },
        {
          name: "紫色",
          color: "#8E24AA",
          fieldname: "purple",
          value: 0,
          isOxidant: false
        },
        {
          name: "棕色",
          color: "#6D4C41",
          value: 0,
          fieldname: "brown",
          isOxidant: false
        },
        {
          name: "蓝色",
          color: "#1565C0",
          value: 0,
          fieldname: "blue",
          isOxidant: false
        },
        {
          name: "自然黑",
          color: "#000000",
          value: 0,
          fieldname: "black",
          isOxidant: false
        },
        {
          name: "淡化剂",
          color: "#FFFFFF",
          value: 0,
          fieldname: "faded_bleached",
          isOxidant: false
        },
        {
          name: "灰金色",
          color: "#E0E0E0",
          value: 0,
          fieldname: "bleached",
          isOxidant: false
        },
        {
          name: "梦幻紫",
          color: "#E1BEE7",
          value: 0,
          fieldname: "silver",
          isOxidant: false
        },
        {
          name: "浅棕色",
          color: "#6D4C41",
          value: 0,
          fieldname: "gold",
          isOxidant: false
        },
        {
          name: "深灰亚麻色",
          color: "#B0BEC5",
          value: 0,
          fieldname: "linen",
          isOxidant: false
        }
        // bleached: '#e8e3d9', // 极浅灰金色 (Very light gray-gold) - 12.11
        // silver: '#d6cadd',   // 梦幻紫 (Dreamy purple) - V10.21
        // gold: '#d1b8a0',     // 浅棕色 (Light brown) - 5.0
        // linen: '#8c837b',      // 深灰亚麻色 (Dark gray linen) - 7.11
      ],
      showColorModal: false,
      currentColor: [],
      // 大师模式现在选择的颜色以及克重
      // colorAmount : '',
      selectedColor: null,
      // 用于存储选中的颜色
      selectedColorIndex: -1
      // colorData
    };
  },
  onShow() {
    if (!this.colorName) {
      this.loadColorInfo();
    }
    this.handleReset();
  },
  onLoad(options) {
    this.loadColorInfo(options);
    common_vendor.index.__f__("log", "at pages/mall/color/confirm.vue:380", "配方", this.developerComponents);
    common_vendor.index.__f__("log", "at pages/mall/color/confirm.vue:381", "配方", this.colorComponents);
  },
  mounted() {
    this.ctx = common_vendor.index.createCameraContext(this);
  },
  methods: {
    // 双氧比例
    handleHydrogenConfirm(res) {
      this.ratioSettings = {
        ...this.ratioSettings,
        // 保留其他属性不变
        [res.color.ratioKey]: {
          // ...this.ratioSettings[res.color.ratioKey], // 保留该属性的其他字段
          proportion: res.color.ratioKey,
          value: res.color.value
          // 仅更新 value
        }
      };
      this.developerComponents[res.color.fieldname] = res.color.value;
      common_vendor.index.__f__("log", "at pages/mall/color/confirm.vue:398", "this.ratioSettings", this.ratioSettings);
      common_vendor.index.__f__("log", "at pages/mall/color/confirm.vue:399", "智能模式选择的双氧乳", res);
    },
    // 新增方法：选择双氧乳比例
    selectHydrogenPeroxide(ratio) {
      const colorData = {
        name: ratio.name,
        color: ratio.color,
        value: this.ratioSettings[ratio.key].value || 0,
        fieldname: ratio.fieldname,
        isOxidant: ratio.isOxidant,
        isHydrogenPeroxide: true,
        ratioKey: ratio.key
      };
      this.$refs.colorModal.showColorModal(colorData);
    },
    // 修改确认回调方法
    handleColorConfirm(res) {
      if (this.isSingleMode) {
        this.handleReset();
      }
      if (res.color.isHydrogenPeroxide) {
        this.ratioSettings[res.color.ratioKey] = {
          value: res.color.value,
          proportion: this.selectedMixRatio
        };
        this.developerComponents[res.color.fieldname] = res.color.value;
      } else {
        this.$set(this.colorList, res.colorIndex, {
          ...this.colorList[res.colorIndex],
          value: res.color.value
        });
      }
    },
    dialogConfirm() {
      common_vendor.index.__f__("log", "at pages/mall/color/confirm.vue:443", "用户点击了“同意”");
    },
    dialogClose() {
      common_vendor.index.__f__("log", "at pages/mall/color/confirm.vue:446", "用户点击了“关闭”或遮罩层");
    },
    onInputChange(value) {
      const numValue = Number(value);
      if (isNaN(numValue)) {
        this.adjustAmount = "";
        return;
      }
      if (numValue > 250) {
        this.adjustAmount = 250;
      } else if (numValue < 10) {
        this.adjustAmount = 10;
      } else {
        this.adjustAmount = numValue;
      }
      common_vendor.index.__f__("log", "at pages/mall/color/confirm.vue:466", "当前的值为", this.adjustAmount);
    },
    onBlurChange(e) {
      let value = Number(e.detail.value);
      if (value < 10)
        value = 10;
      if (value > 250)
        value = 250;
      this.adjustAmount = value;
    },
    onInputBlur() {
      this.onInputChange(this.adjustAmount);
    },
    // 大师模式页面加载等都把这个值置为0
    handleReset() {
      this.colorList = (this.colorList || []).map((color) => ({
        ...color,
        value: 0
      }));
    },
    // 单仓模式只有一个
    switchToSingleMode() {
      this.isSingleMode = true;
      this.handleReset();
    },
    switchToComboMode() {
      this.isSingleMode = false;
      this.handleReset();
    },
    // 上传拍照
    takePhoto() {
      common_vendor.index.chooseImage({
        count: 3,
        // 最多选择3张图片
        sizeType: ["compressed"],
        sourceType: ["album", "camera"],
        success: (res) => {
          this.imageList = this.imageList.concat(res.tempFilePaths);
          this.uploadImages(res.tempFilePaths);
        },
        fail: (err) => {
          common_vendor.index.__f__("error", "at pages/mall/color/confirm.vue:509", "选择图片失败：", err);
          common_vendor.index.showToast({
            title: "选择图片失败",
            icon: "none"
          });
        }
      });
    },
    //大师模式设置
    selectColor(index) {
      common_vendor.index.__f__("log", "at pages/mall/color/confirm.vue:519", "大师模式点击颜色索引", index);
      const color = this.colorList[index];
      this.selectedColor = color;
      this.selectedColorIndex = index;
      common_vendor.index.__f__("log", "at pages/mall/color/confirm.vue:523", "this.selectedColorIndex", this.selectedColorIndex);
      this.$refs.colorModal.showColorModal(color);
      this.$refs.colorModal.updateColorIndex(index);
      this.currentColor = color;
    },
    // 染膏输入模式
    inputHydrogenPeroxide() {
    },
    //处理输入的值 单仓模式直接清掉之前输入的配色
    // @parm res.color 颜色类{name: "橙色", color: "#FFA726", value: 0, isOxidant: false}
    // @parm res.amount 输入的克重
    handleColorConfirm(res) {
      if (this.isSingleMode) {
        common_vendor.index.__f__("log", "at pages/mall/color/confirm.vue:544", "当时是单仓模式");
        this.handleReset();
      }
      this.$set(this.colorList, res.colorIndex, {
        ...this.colorList[res.colorIndex],
        value: res.color.value
      });
    },
    // 以上是大师模式的设置
    // 以下是智能模式设置
    confirmRatio() {
      this.selectedMixRatio = this.ratioOptions[this.sliderValue];
      const selectedMixRatio = this.ratioOptions[this.currentRatioIndex];
      this.ratioSettings[this.tempSelectedRatio].proportion = this.selectedMixRatio;
      const ratioParts = selectedMixRatio.split(":").map(Number);
      const dyeRatio = ratioParts[0];
      const oxygenRatio = ratioParts[1];
      common_vendor.index.__f__("log", "at pages/mall/color/confirm.vue:576", "染膏重量", this.adjustAmount);
      common_vendor.index.__f__("log", "at pages/mall/color/confirm.vue:577", "染膏比例dyeRatio", dyeRatio);
      common_vendor.index.__f__("log", "at pages/mall/color/confirm.vue:578", "双氧乳比例", oxygenRatio);
      const actualOxygenAmount = this.adjustAmount / dyeRatio * oxygenRatio;
      common_vendor.index.__f__("log", "at pages/mall/color/confirm.vue:584", "双氧乳比例克重", actualOxygenAmount);
      this.ratioSettings[this.tempSelectedRatio] = {
        value: actualOxygenAmount,
        proportion: selectedMixRatio
      };
      switch (this.tempSelectedRatio) {
        case "3%":
          this.developerComponents.hydrogenPeroxide3Percent = actualOxygenAmount;
          break;
        case "6%":
          this.developerComponents.hydrogenPeroxide6Percent = actualOxygenAmount;
          break;
        case "9%":
          this.developerComponents.hydrogenPeroxide9Percent = actualOxygenAmount;
          break;
        case "12%":
          this.developerComponents.hydrogenPeroxide12Percent = actualOxygenAmount;
          break;
      }
      this.showRatioModal = false;
    },
    hideColorModal() {
      this.showColorModal = false;
    },
    confirmColorAmount() {
      if (!this.colorAmount || isNaN(this.colorAmount) || this.colorAmount <= 0) {
        common_vendor.index.showToast({
          title: "请输入有效的克数",
          icon: "none"
        });
        return;
      }
      const index = this.colorList.findIndex((item) => item.name === this.currentColor.name);
      if (index !== -1) {
        this.colorList[index].amount = parseFloat(this.colorAmount);
      }
      this.showColorModal = false;
    },
    startColorMixing() {
      let data = [];
      const colorMap = {
        gray: "灰色",
        green: "绿色",
        yellow: "黄色",
        orange: "橙色",
        red: "红色",
        purple: "紫色",
        brown: "棕色",
        blue: "蓝色",
        black: "黑色",
        faded_bleached: "淡化剂",
        bleached: "极浅灰金色",
        // 12.11 (用bleached字段对应) 11号仓位
        silver: "梦幻紫",
        // V10.21 (用silver字段对应) 12号仓位
        gold: "浅棕色",
        // 5.0 (用gold字段对应) 13号仓位
        linen: "深灰亚麻色"
        // 7.11 (原字段) 14号仓位
      };
      const colorCodeMap = {
        gray: "#808080",
        green: "#008000",
        yellow: "#FFFF00",
        orange: "#FFA500",
        red: "#FF0000",
        purple: "#800080",
        brown: "#A52A2A",
        blue: "#0000FF",
        black: "#000000",
        faded_bleached: "#F5F5F5",
        // 浅灰色模拟淡化剂
        white: "#F5F5F5",
        // 双氧乳颜色
        bleached: "#e8e3d9",
        // 极浅灰金色 (Very light gray-gold) - 12.11
        silver: "#d6cadd",
        // 梦幻紫 (Dreamy purple) - V10.21
        gold: "#d1b8a0",
        // 浅棕色 (Light brown) - 5.0
        linen: "#8c837b",
        // 深灰亚麻色 (Dark gray linen) - 7.11
        hydrogenPeroxide3Percent: "#F5F5F5",
        hydrogenPeroxide6Percent: "#F5F5F5",
        hydrogenPeroxide9Percent: "#F5F5F5",
        hydrogenPeroxide12Percent: "#F5F5F5"
      };
      let encodedData;
      let encodedColorComponents;
      let encodedDeveloperComponents;
      let model;
      if (this.mode === "smart") {
        const proportion = Object.values(this.colorComponents || {}).reduce((sum, val) => sum + (typeof val === "number" ? val : 0), 0);
        common_vendor.index.__f__("log", "at pages/mall/color/confirm.vue:695", "当前配方颜色总数是", proportion);
        const multiple = (this.adjustAmount || 0) / proportion;
        const colorComponentsArray = [];
        for (const [key, value] of Object.entries(this.colorComponents)) {
          if (value !== null && value !== void 0 && value !== 0) {
            colorComponentsArray.push({
              name: colorMap[key] || key,
              value: multiple ? Number((value * multiple).toFixed(2)) : value,
              color: colorCodeMap[key] || "#000000",
              used: 0
            });
          }
        }
        const adjustedColorComponents = {};
        if (this.colorComponents) {
          Object.keys(this.colorComponents).forEach((key) => {
            adjustedColorComponents[key] = this.colorComponents[key];
          });
        }
        if (this.colorComponents && typeof this.colorComponents === "object") {
          for (const key in this.colorComponents) {
            if (typeof this.colorComponents[key] === "number" && multiple) {
              adjustedColorComponents[key] = multiple ? parseFloat((this.colorComponents[key] * multiple).toFixed(2)) : this.colorComponents[key];
            }
          }
        }
        const multipleDeveloper = this.adjustAmount / 4 || 0;
        const developerComponentsArray = [];
        const adjustedDeveloperComponents = {};
        common_vendor.index.__f__("log", "at pages/mall/color/confirm.vue:739", "准备拷贝对象", this.developerComponents);
        if (this.developerComponents && typeof this.developerComponents === "object") {
          for (const key in this.developerComponents) {
            if (typeof this.developerComponents[key] === "number") {
              adjustedDeveloperComponents[key] = this.developerComponents[key] ? parseFloat(this.developerComponents[key]) : 0;
            }
          }
        }
        common_vendor.index.__f__("log", "at pages/mall/color/confirm.vue:750", "当前adjustedDeveloperComponents", adjustedDeveloperComponents);
        if (adjustedDeveloperComponents && typeof adjustedDeveloperComponents === "object") {
          const hasValidData = Object.entries(adjustedDeveloperComponents).some(
            ([_, value]) => value !== null && value !== void 0 && value !== 0
          );
          if (hasValidData) {
            common_vendor.index.__f__("log", "at pages/mall/color/confirm.vue:759", "当前双氧占比", adjustedDeveloperComponents);
            common_vendor.index.__f__("log", "at pages/mall/color/confirm.vue:771", "当前染膏是一个非空对象", hasValidData);
            for (const [key, value] of Object.entries(adjustedDeveloperComponents)) {
              if (value !== null && value !== void 0 && value !== 0) {
                const percent = key.replace("hydrogenPeroxide", "").replace("Percent", "");
                common_vendor.index.__f__("log", "at pages/mall/color/confirm.vue:776", "percent", percent);
                developerComponentsArray.push({
                  name: `双氧乳${percent}%VOL`,
                  value: parseFloat(value.toFixed(2)),
                  // 保留2位小数
                  color: colorCodeMap.white,
                  used: 0
                });
              }
            }
          } else {
            [6].forEach((percent) => {
              if (this.adjustAmount) {
                developerComponentsArray.push({
                  name: `双氧乳${percent}%VOL`,
                  value: multipleDeveloper ? parseFloat(this.adjustAmount) : 0,
                  // 处理可能的 undefined
                  color: colorCodeMap.white,
                  used: 0
                });
              }
            });
            adjustedDeveloperComponents.hydrogenPeroxide6Percent = parseFloat(this.adjustAmount) || 0;
          }
        } else {
          [6].forEach((percent) => {
            if (this.adjustAmount) {
              developerComponentsArray.push({
                name: `双氧乳${percent}%VOL`,
                value: multipleDeveloper ? parseFloat(this.adjustAmount) : 0,
                // 处理可能的 undefined
                color: colorCodeMap.white,
                used: 0
              });
            }
          });
          adjustedDeveloperComponents.hydrogenPeroxide6Percent = parseFloat(this.adjustAmount) || 0;
        }
        developerComponentsArray.sort((a, b) => {
          const percentA = parseInt(a.name.match(/\d+/)[0]);
          const percentB = parseInt(b.name.match(/\d+/)[0]);
          return percentA - percentB;
        });
        const data2 = [...colorComponentsArray, ...developerComponentsArray];
        const dataString = JSON.stringify(data2);
        encodedData = encodeURIComponent(dataString);
        const colorComponents = adjustedColorComponents ? JSON.stringify(adjustedColorComponents) : "null";
        const developerComponents = adjustedDeveloperComponents ? JSON.stringify(adjustedDeveloperComponents) : "null";
        common_vendor.index.__f__("log", "at pages/mall/color/confirm.vue:866", "生成指令前置对象染膏", colorComponents);
        common_vendor.index.__f__("log", "at pages/mall/color/confirm.vue:867", "生成指令前置对象双氧乳", developerComponents);
        encodedColorComponents = encodeURIComponent(colorComponents);
        encodedDeveloperComponents = encodeURIComponent(developerComponents);
        model = encodeURIComponent("smart");
      } else if (this.mode === "master") {
        data = (this.colorList || []).filter((color) => color && color.value > 0).map((color) => ({
          name: color.name,
          color: colorCodeMap[color.fieldname] || "#F5F5F5",
          value: color.value,
          used: 0
        }));
        common_vendor.index.__f__("log", "at pages/mall/color/confirm.vue:883", "页面显示的data", data);
        const result = this.colorList.filter((item) => item.value !== 0).reduce((acc, item) => {
          acc[item.fieldname] = item.value;
          return acc;
        }, {});
        const colorComponentsArray = [];
        for (const [key, value] of Object.entries(result)) {
          if (value !== null && value !== void 0 && value !== 0) {
            colorComponentsArray.push({
              name: colorMap[key] || key,
              value,
              color: colorCodeMap[key] || "#000000",
              used: 0
            });
          }
        }
        JSON.stringify(colorComponentsArray);
        const dataString = JSON.stringify(data);
        encodedData = encodeURIComponent(dataString);
        const colorComponents = result ? JSON.stringify(result) : "null";
        encodedColorComponents = encodeURIComponent(colorComponents);
        model = encodeURIComponent("master");
      }
      common_vendor.index.navigateTo({
        url: `/pages/mall/formula-making/formula-making?data=${encodedData}&colorComponents=${encodedColorComponents}&developerComponents=${encodedDeveloperComponents}&model=${model}`,
        success: () => {
          common_vendor.index.__f__("log", "at pages/mall/color/confirm.vue:929", "跳转到配方制作页面成功");
        },
        fail: (err) => {
          common_vendor.index.__f__("error", "at pages/mall/color/confirm.vue:932", "跳转失败:", err);
          common_vendor.index.showToast({
            title: "跳转失败",
            icon: "none"
          });
        }
      });
    },
    onRatioChange(e) {
      common_vendor.index.__f__("log", "at pages/mall/color/confirm.vue:942", e);
      this.currentRatioIndex = e.detail.value;
    },
    loadColorInfo(options) {
      if (options) {
        if (options.mode === "master") {
          this.mode = "master";
          common_vendor.index.__f__("log", "at pages/mall/color/confirm.vue:953", "设置为大师模式");
        } else {
          this.mode = "smart";
        }
        if (options.name || options.category || options.color) {
          try {
            this.colorName = decodeURIComponent(options.name || "未知颜色");
            this.categoryName = decodeURIComponent(options.category || "生活色");
            this.colorHex = decodeURIComponent(options.color || "#000000");
            common_vendor.index.__f__("log", "at pages/mall/color/confirm.vue:963", "从URL参数获取数据成功:", this.colorName, this.categoryName, this.colorHex);
          } catch (e) {
            common_vendor.index.__f__("error", "at pages/mall/color/confirm.vue:965", "URL参数解析错误:", e);
            this.loadFromStorage();
          }
          return;
        }
      }
      this.loadFromStorage();
    },
    loadFromStorage() {
      try {
        const dyeColorInfo = common_vendor.index.getStorageSync("dyeColorInfo");
        common_vendor.index.__f__("log", "at pages/mall/color/confirm.vue:979", "从缓存读取到的数据:", dyeColorInfo);
        if (dyeColorInfo) {
          this.colorName = dyeColorInfo.name || "未知颜色";
          this.categoryName = dyeColorInfo.category || "生活色";
          this.colorHex = dyeColorInfo.hex || "#000000";
          this.colorComponents = dyeColorInfo.colorComponents;
          this.developerComponents = {};
          if (dyeColorInfo.mode === "master") {
            this.mode = "master";
            common_vendor.index.__f__("log", "at pages/mall/color/confirm.vue:991", "从缓存设置为大师模式");
          } else {
            this.mode = "smart";
          }
          common_vendor.index.__f__("log", "at pages/mall/color/confirm.vue:996", "从缓存读取数据成功:", this.colorName, this.categoryName, this.colorHex, this.mode);
        } else {
          common_vendor.index.__f__("error", "at pages/mall/color/confirm.vue:998", "缓存中无法获取颜色信息");
          this.colorName = "X深紫色";
          this.categoryName = "生活色";
          this.colorHex = "#5733FF";
          common_vendor.index.__f__("log", "at pages/mall/color/confirm.vue:1003", "使用默认颜色数据");
          common_vendor.index.showToast({
            title: "加载失败，使用默认色",
            icon: "none"
          });
        }
      } catch (e) {
        common_vendor.index.__f__("error", "at pages/mall/color/confirm.vue:1011", "读取缓存失败:", e);
        this.colorName = "X深紫色";
        this.categoryName = "生活色";
        this.colorHex = "#5733FF";
        common_vendor.index.__f__("log", "at pages/mall/color/confirm.vue:1017", "异常情况，使用默认颜色数据");
      }
    },
    goBack() {
      common_vendor.index.navigateBack();
    },
    onSliderChange(e) {
      this.adjustAmount = e.detail.value;
      common_vendor.index.__f__("log", "at pages/mall/color/confirm.vue:1027", "调整量设置为:", this.adjustAmount);
    },
    onRatioSliderChange(e) {
      const index = e.detail.value;
      this.sliderValue = index;
      this.selectedMixRatio = this.ratioOptions[index];
      common_vendor.index.__f__("log", "at pages/mall/color/confirm.vue:1034", "比例选择:", this.selectedMixRatio);
    },
    onRatioSliderChanging(e) {
      const index = e.detail.value;
      this.sliderValue = index;
    },
    showRatioInput(ratio) {
      this.tempSelectedRatio = ratio;
      this.sliderValue = this.ratioOptions.indexOf(this.selectedMixRatio);
      if (this.sliderValue === -1)
        this.sliderValue = 0;
      this.showRatioModal = true;
    },
    hideRatioInput() {
      this.showRatioModal = false;
    },
    // confirmRatio() {
    //   this.selectedRatio = this.tempSelectedRatio;
    //   this.selectedMixRatio = this.ratioOptions[this.sliderValue];
    //   // 保存设置到对应的百分比
    //   this.ratioSettings[this.tempSelectedRatio] = this.selectedMixRatio;
    //   // 计算实际用量
    //   const ratioValue = parseFloat(this.selectedRatio); // 获取百分比数值（去掉%符号）
    //   const actualAmount = (this.adjustAmount * ratioValue / 100).toFixed(1); // 计算实际用量并保留一位小数
    //   this.showRatioModal = false;
    //   uni.__f__('log','at pages/mall/color/confirm.vue:1067',"当前填入的染膏",JSON.stringify(this.ratioSettings))
    //   // uni.__f__('log','at pages/mall/color/confirm.vue:1069',"双氧占比", this.selectedMixRatio);
    //   // uni.__f__('log','at pages/mall/color/confirm.vue:1070',"当前所有比例设置:", this.ratioSettings);
    //   // uni.__f__('log','at pages/mall/color/confirm.vue:1071',"实际用量:", actualAmount);
    //   uni.showToast({
    //     title: `已设置 ${this.selectedRatio} 双氧，混合比例 ${this.selectedMixRatio}\n实际用量: ${actualAmount}g`,
    //     icon: 'success',
    //     duration: 3000 // 延长显示时间以便查看完整信息
    //   });
    // },
    // startColorMixing() {
    // 	if (this.isLoading) return;
    // 	this.isLoading = true;
    // 	// 模拟调色过程
    // 	uni.showLoading({
    // 		title: '调色中...'
    // 	});
    // 	setTimeout(() => {
    // 		uni.hideLoading();
    // 		this.isLoading = false;
    // 		uni.showModal({
    // 			title: '调色完成',
    // 			content: `已成功为您调配${this.colorName}，请取走染膏碗进行使用`,
    // 			showCancel: false,
    // 			success: () => {
    // 				// 返回颜色详情页
    // 				uni.navigateBack();
    // 			}
    // 		});
    // 	}, 2000);
    // },
    switchMode(mode) {
      this.mode = mode;
      common_vendor.index.__f__("log", "at pages/mall/color/confirm.vue:1107", "切换到模式:", mode);
    },
    // 上传图片到服务器
    uploadImages(tempFilePaths) {
      common_vendor.index.__f__("log", "at pages/mall/color/confirm.vue:1112", "准备上传图片:", tempFilePaths);
      tempFilePaths.forEach((filePath, index) => {
        common_vendor.index.__f__("log", "at pages/mall/color/confirm.vue:1117", `图片${index + 1}:`, filePath);
      });
      common_vendor.index.showToast({
        title: `已选择${tempFilePaths.length}张图片`,
        icon: "success"
      });
    }
  }
};
const __injectCSSVars__ = () => {
  common_vendor.useCssVars((_ctx) => ({
    "59ac9b4e": _ctx.isSingleMode ? "#4285f4" : "#fff",
    "df8dd686": _ctx.isSingleMode ? "#fff" : "#4285f4",
    "77064b14": !_ctx.isSingleMode ? "#4285f4" : "#fff",
    "3415ad78": !_ctx.isSingleMode ? "#fff" : "#4285f4"
  }));
};
const __setup__ = _sfc_main.setup;
_sfc_main.setup = __setup__ ? (props, ctx) => {
  __injectCSSVars__();
  return __setup__(props, ctx);
} : __injectCSSVars__;
if (!Array) {
  const _easycom_uni_easyinput2 = common_vendor.resolveComponent("uni-easyinput");
  const _component_MasterColorinput = common_vendor.resolveComponent("MasterColorinput");
  const _component_CustomPicker = common_vendor.resolveComponent("CustomPicker");
  (_easycom_uni_easyinput2 + _component_MasterColorinput + _component_CustomPicker)();
}
const _easycom_uni_easyinput = () => "../../../uni_modules/uni-easyinput/components/uni-easyinput/uni-easyinput.js";
if (!Math) {
  _easycom_uni_easyinput();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.t($data.categoryName),
    b: common_vendor.o((...args) => $options.takePhoto && $options.takePhoto(...args)),
    c: $data.mode === "smart" ? 1 : "",
    d: common_vendor.o(($event) => $options.switchMode("smart")),
    e: $data.mode === "master" ? 1 : "",
    f: common_vendor.o(($event) => $options.switchMode("master")),
    g: common_vendor.t($data.colorName),
    h: $data.mode === "master"
  }, $data.mode === "master" ? {
    i: $data.isSingleMode ? "#4285f4" : "#fff",
    j: $data.isSingleMode ? "#fff" : "#4285f4",
    k: common_vendor.o((...args) => $options.switchToSingleMode && $options.switchToSingleMode(...args)),
    l: !$data.isSingleMode ? "#4285f4" : "#fff",
    m: !$data.isSingleMode ? "#fff" : "#4285f4",
    n: common_vendor.o((...args) => $options.switchToComboMode && $options.switchToComboMode(...args))
  } : {}, {
    o: $data.mode === "smart"
  }, $data.mode === "smart" ? common_vendor.e({
    p: common_vendor.o($options.onBlurChange),
    q: common_vendor.o(($event) => $data.adjustAmount = $event),
    r: common_vendor.p({
      type: "digit",
      min: 10,
      max: 250,
      placeholder: "请输入调配量 (10-250g)",
      modelValue: $data.adjustAmount
    }),
    s: common_vendor.f($data.hydrogenPeroxideRatios, (ratio, index, i0) => {
      return {
        a: common_vendor.t($data.ratioSettings[ratio.key].proportion ? `${ratio.label} ${$data.ratioSettings[ratio.key].value}g` : ratio.label),
        b: index,
        c: $data.ratioSettings[ratio.key].proportion ? 1 : "",
        d: common_vendor.o(($event) => $options.selectHydrogenPeroxide(ratio), index)
      };
    }),
    t: common_vendor.sr("colorModal", "a051f7e1-1"),
    v: common_vendor.o($options.handleHydrogenConfirm),
    w: common_vendor.p({
      ["initial-color"]: $data.hydrogenPeroxideRatios,
      ["initial-color-index"]: _ctx.index
    }),
    x: $data.showRatioModal
  }, $data.showRatioModal ? {
    y: common_vendor.t($data.tempSelectedRatio),
    z: common_vendor.o($options.onRatioChange),
    A: common_vendor.p({
      ratioOptions: $data.ratioOptions,
      value: $data.currentRatioIndex
    }),
    B: common_vendor.o((...args) => $options.confirmRatio && $options.confirmRatio(...args)),
    C: common_vendor.o(() => {
    }),
    D: common_vendor.o((...args) => $options.hideRatioInput && $options.hideRatioInput(...args))
  } : {}) : {}, {
    E: $data.mode === "master"
  }, $data.mode === "master" ? {
    F: common_vendor.sr("colorModal", "a051f7e1-3"),
    G: common_vendor.o($options.handleColorConfirm),
    H: common_vendor.p({
      ["initial-color"]: $data.selectedColor,
      ["initial-color-index"]: $data.selectedColorIndex
    }),
    I: common_vendor.f($data.colorList, (item, index, i0) => {
      return common_vendor.e({
        a: item.value
      }, item.value ? {
        b: common_vendor.t(item.value)
      } : {
        c: common_vendor.t(item.value),
        d: item.color
      }, {
        e: common_vendor.t(item.name),
        f: index,
        g: common_vendor.o(($event) => $options.selectColor(index), index)
      });
    })
  } : {}, {
    J: $data.mode === "smart"
  }, $data.mode === "smart" ? {} : {}, {
    K: common_vendor.o((...args) => $options.startColorMixing && $options.startColorMixing(...args)),
    L: common_vendor.s(_ctx.__cssVars())
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-a051f7e1"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/pages/mall/color/confirm.js.map
