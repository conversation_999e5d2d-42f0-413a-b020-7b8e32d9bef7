/* 水平间距 */
/* 水平间距 */
/* 全局样式修复 - 确保页面占满屏幕宽度 */
page.data-v-463872ce {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
}

/* 通用容器样式 */
.container.data-v-463872ce, .page-container.data-v-463872ce {
  width: 100%;
  min-height: 100vh;
  box-sizing: border-box;
}

/* 修复小程序页面宽度问题 */
uni-page-body.data-v-463872ce {
  width: 100% !important;
}

/* 确保所有view元素正确继承宽度 */
view.data-v-463872ce {
  box-sizing: border-box;
}
.container.data-v-463872ce {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding: 20rpx;
}
.device-info.data-v-463872ce {
  background-color: #ffffff;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}
.device-icon.data-v-463872ce {
  width: 80rpx;
  height: 80rpx;
  margin-right: 20rpx;
}
.device-image.data-v-463872ce {
  width: 100%;
  height: 100%;
  border-radius: 10rpx;
}
.device-details.data-v-463872ce {
  flex: 1;
}
.device-name.data-v-463872ce {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 8rpx;
}
.device-id.data-v-463872ce {
  font-size: 26rpx;
  color: #666666;
}
.device-status.data-v-463872ce {
  background-color: #4CAF50;
  border-radius: 30rpx;
  padding: 12rpx 24rpx;
}
.status-text.data-v-463872ce {
  color: #ffffff;
  font-size: 26rpx;
  font-weight: 500;
}
.color-grid.data-v-463872ce {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  padding: 0 10rpx;
  margin-bottom: 60rpx;
}
.color-item.data-v-463872ce {
  width: 30%;
  margin-bottom: 40rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.color-circle-container.data-v-463872ce {
  color_code: relative;
  width: 140rpx;
  margin-bottom: 16rpx;
}
.color-circle.data-v-463872ce {
  width: 140rpx;
  height: 80rpx;
  border-radius: 8rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  color_code: relative;
  z-index: 2;
  border: 4rpx solid transparent;
  transition: border-color 0.3s;
}
.color-circle.selected.data-v-463872ce {
  border-color: #4285f4;
}
.color-progress.data-v-463872ce {
  color_code: absolute;
  bottom: -10rpx;
  left: 0;
  height: 6rpx;
  background-color: #4285f4;
  border-radius: 3rpx;
  z-index: 1;
}
.color-name.data-v-463872ce {
  font-size: 24rpx;
  color: #333333;
  text-align: center;
  line-height: 1.3;
}
.bottom-buttons.data-v-463872ce {
  display: flex;
  justify-content: space-between;
  /* 让两个按钮分布在两端 */
  padding: 20rpx 30rpx 40rpx;
  background-color: #ffffff;
  color_code: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);
}
.btn-secondary.data-v-463872ce {
  flex: 0 0 70%;
  /* flex-grow: 0, flex-shrink: 0, flex-basis: 60% (固定60%宽度) */
  background-color: #f0f0f0;
  border-radius: 50rpx;
  padding: 24rpx 0;
  text-align: center;
  margin-right: 20rpx;
  /* 固定间距 */
}
.btn-primary.data-v-463872ce {
  display: flex;
  align-items: center;
  /* 垂直居中 */
  justify-content: center;
  /* 水平居中 */
  flex: 1;
  background-color: #4285f4;
  border-radius: 50rpx;
  padding: 24rpx 0;
  text-align: center;
}
.btn-icon.data-v-463872ce {
  width: 32rpx;
  /* 图标大小 */
  height: 32rpx;
  margin-right: 10rpx;
  /* 图标与文字的间距 */
}
.btn-secondary text.data-v-463872ce {
  color: #666666;
  font-size: 28rpx;
}
.btn-primary text.data-v-463872ce {
  color: #ffffff;
  font-size: 28rpx;
  font-weight: 500;
}

/* 新增弹窗样式 */
.popup-content.data-v-463872ce {
  padding: 20rpx;
  width: 100%;
}
.color-display.data-v-463872ce {
  width: 120rpx;
  height: 80rpx;
  margin: 0 auto 30rpx;
  border-radius: 8rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}
.color-details.data-v-463872ce {
  margin-top: 20rpx;
}
.detail-row.data-v-463872ce {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20rpx;
  font-size: 28rpx;
}
.detail-label.data-v-463872ce {
  color: #666;
}
.detail-value.data-v-463872ce {
  color: #333;
  font-weight: 500;
}
.progress-container.data-v-463872ce {
  margin: 30rpx 0;
}
.status-message.data-v-463872ce {
  text-align: center;
  font-size: 28rpx;
  font-weight: 500;
  margin-top: 20rpx;
}

/* 调整uni-popup默认样式 */
.uni-popup__wrapper.data-v-463872ce {
  border-radius: 20rpx;
  overflow: hidden;
}
.uni-popup__dialog.data-v-463872ce {
  width: 80%;
  max-width: 600rpx;
}