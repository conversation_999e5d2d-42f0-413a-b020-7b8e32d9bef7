<template>
  <view class="container">
    <!-- 顶部导航栏 -->
    <view class="header">
      <view class="nav-bar">
        <view class="nav-left" @click="goBack">
          <text class="back-icon">‹</text>
        </view>
        <view class="nav-title">成员管理</view>
        <view class="nav-right">
          <text class="more-icon">⋯</text>
          <view class="record-icon">⊙</view>
        </view>
      </view>
    </view>

    <!-- 表单内容 -->
    <view class="form-container">
      <!-- 手机号输入 -->
      <view class="input-group">
        <view class="country-code">CN+86</view>
        <input 
          class="phone-input" 
          type="number" 
          placeholder="请输入手机号" 
          v-model="formData.phone"
          maxlength="11"
        />
      </view>

      <!-- 姓名输入 -->
      <view class="input-item">
        <input 
          class="text-input" 
          type="text" 
          placeholder="请输入姓名" 
          v-model="formData.name"
        />
      </view>

      <!-- 密码输入 -->
      <view class="input-item">
        <input 
          class="text-input" 
          type="password" 
          placeholder="请设置登录密码" 
          v-model="formData.password"
        />
      </view>

      <!-- 确认密码输入 -->
      <view class="input-item">
        <input 
          class="text-input" 
          type="password" 
          placeholder="请确认登录密码" 
          v-model="formData.confirmPassword"
        />
      </view>

      <!-- 添加成员按钮 -->
      <view class="submit-btn" @click="addMember">
        <text class="btn-text">添加成员</text>
      </view>
    </view>
  </view>
</template>

<script>
import { apiService } from '@/utils/request.js';

export default {
  data() {
    return {
      formData: {
        phone: '',
        name: '',
        password: '',
        confirmPassword: ''
      }
    }
  },
  methods: {
    goBack() {
      uni.navigateBack();
    },
    
    // 验证表单
    validateForm() {
      if (!this.formData.phone) {
        uni.showToast({
          title: '请输入手机号',
          icon: 'none'
        });
        return false;
      }
      
      if (!/^1[3-9]\d{9}$/.test(this.formData.phone)) {
        uni.showToast({
          title: '请输入正确的手机号',
          icon: 'none'
        });
        return false;
      }
      
      if (!this.formData.password) {
        uni.showToast({
          title: '请设置登录密码',
          icon: 'none'
        });
        return false;
      }
      
      if (this.formData.password.length < 6) {
        uni.showToast({
          title: '密码至少6位',
          icon: 'none'
        });
        return false;
      }
      
      if (this.formData.password !== this.formData.confirmPassword) {
        uni.showToast({
          title: '两次密码不一致',
          icon: 'none'
        });
        return false;
      }
      
      return true;
    },
    
    // 添加成员
    async addMember() {
      if (!this.validateForm()) {
        return;
      }

      try {
        uni.showLoading({
          title: '添加中...'
        });

        // 调用注册接口
        const response = await this.registerMember();
		

        uni.hideLoading();

        if (response.success) {
          uni.showToast({
            title: '添加成功',
            icon: 'success'
          });

          // 延迟返回上一页
          setTimeout(() => {
            uni.navigateBack();
          }, 1500);
        } else {
          uni.showToast({
            title: response.message || '添加失败',
            icon: 'none'
          });
        }
      } catch (error) {
        uni.hideLoading();
        console.error('添加成员失败:', error);
        uni.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        });
      }
    },
    
    // 调用注册接口
    async registerMember() {
      try {
        // 生成默认姓名（如果没有输入）
        const defaultName = this.formData.name || `用户${this.formData.phone.slice(-4)}`;

        const requestData = {
          phone: this.formData.phone,
          password: this.formData.password,
          nickname: defaultName, // 使用昵称字段保存姓名
          userType: "1" // 普通用户类型，注意这里改为字符串
        };

        // const result = await apiService.user.register(requestData);
        const result = await apiService.mall.stores.addStoreMember(requestData);
		
        if (result.code === 200) {
          return {
            success: true,
            data: result.data
          };
        } else {
          return {
            success: false,
            message: result.message || '注册失败'
          };
        }
      } catch (error) {
        console.error('注册请求失败:', error);
        return {
          success: false,
          message: error.message || '网络请求失败'
        };
      }
    }
  }
}
</script>

<style scoped>
.container {
  min-height: 100vh;
  background-color: #f5f5f5;
}

/* 顶部导航栏 */
.header {
  background: linear-gradient(135deg, #4285f4 0%, #5a9bff 100%);
  padding-top: var(--status-bar-height, 44rpx);
}

.nav-bar {
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20rpx;
  position: relative;
}

.nav-left {
  width: 80rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  font-size: 40rpx;
  color: #fff;
  font-weight: 300;
}

.nav-title {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  font-size: 32rpx;
  color: #fff;
  font-weight: 500;
}

.nav-right {
  width: 80rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 15rpx;
}

.more-icon, .record-icon {
  font-size: 28rpx;
  color: #fff;
}

/* 表单容器 */
.form-container {
  padding: 60rpx 40rpx;
}

/* 手机号输入组 */
.input-group {
  display: flex;
  align-items: center;
  background-color: #fff;
  border-radius: 12rpx;
  padding: 0 30rpx;
  margin-bottom: 40rpx;
  height: 100rpx;
}

.country-code {
  font-size: 28rpx;
  color: #333;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.phone-input {
  flex: 1;
  font-size: 28rpx;
  color: #333;
  height: 100%;
}

/* 普通输入框 */
.input-item {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 0 30rpx;
  margin-bottom: 40rpx;
  height: 100rpx;
  display: flex;
  align-items: center;
}

.text-input {
  width: 100%;
  font-size: 28rpx;
  color: #333;
  height: 100%;
}

/* 提交按钮 */
.submit-btn {
  background: linear-gradient(135deg, #4285f4 0%, #5a9bff 100%);
  border-radius: 50rpx;
  height: 100rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 60rpx;
}

.btn-text {
  font-size: 32rpx;
  color: #fff;
  font-weight: 500;
}

/* 输入框占位符样式 */
.phone-input::placeholder,
.text-input::placeholder {
  color: #999;
  font-size: 28rpx;
}
</style>
