"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_connectionStateValidator = require("../../utils/connectionStateValidator.js");
const _sfc_main = {
  data() {
    return {
      connectionMethod: "",
      deviceStatus: false,
      connectedDeviceId: "",
      validationResult: null,
      logs: [],
      refreshTimer: null
    };
  },
  onLoad() {
    this.refreshStatus();
    this.refreshLogs();
    this.refreshTimer = setInterval(() => {
      this.refreshStatus();
      this.refreshLogs();
    }, 5e3);
  },
  onUnload() {
    if (this.refreshTimer) {
      clearInterval(this.refreshTimer);
    }
  },
  methods: {
    // 刷新当前状态
    refreshStatus() {
      const app = getApp();
      this.connectionMethod = app.globalData.connectionMethod;
      this.deviceStatus = app.globalData.deviceStatus;
      this.connectedDeviceId = app.globalData.connectedDeviceId;
    },
    // 验证连接状态
    validateState() {
      this.validationResult = utils_connectionStateValidator.connectionStateValidator.validateConnectionState();
      common_vendor.index.__f__("log", "at pages/test/bluetooth-wifi-fix-test.vue:124", "状态验证结果:", this.validationResult);
    },
    // 自动修复状态
    autoFixState() {
      const fixResult = utils_connectionStateValidator.connectionStateValidator.autoFixStateIssues();
      common_vendor.index.__f__("log", "at pages/test/bluetooth-wifi-fix-test.vue:130", "自动修复结果:", fixResult);
      if (fixResult.fixed) {
        common_vendor.index.showToast({
          title: `已修复 ${fixResult.fixedIssues.length} 个问题`,
          icon: "success"
        });
        this.refreshStatus();
        this.validateState();
      } else {
        common_vendor.index.showToast({
          title: "未发现需要修复的问题",
          icon: "none"
        });
      }
    },
    // 刷新日志
    refreshLogs() {
      this.logs = utils_connectionStateValidator.connectionStateValidator.getHistory(20);
    },
    // 清除日志
    clearLogs() {
      utils_connectionStateValidator.connectionStateValidator.clearHistory();
      this.logs = [];
    },
    // 模拟应用生命周期
    simulateAppLifecycle() {
      common_vendor.index.__f__("log", "at pages/test/bluetooth-wifi-fix-test.vue:160", "🔄 模拟应用生命周期变化");
      const app = getApp();
      app.pauseServices();
      setTimeout(() => {
        app.resumeServices();
        this.refreshStatus();
        this.refreshLogs();
      }, 2e3);
      common_vendor.index.showToast({
        title: "已模拟应用生命周期",
        icon: "success"
      });
    },
    // 模拟网络变化
    simulateNetworkChange() {
      common_vendor.index.__f__("log", "at pages/test/bluetooth-wifi-fix-test.vue:181", "🌐 模拟网络状态变化");
      common_vendor.index.onNetworkStatusChange({
        isConnected: true,
        networkType: "wifi"
      });
      this.refreshStatus();
      this.refreshLogs();
      common_vendor.index.showToast({
        title: "已模拟网络变化",
        icon: "success"
      });
    },
    // 清除所有状态
    clearAllStates() {
      common_vendor.index.showModal({
        title: "确认清除",
        content: "确定要清除所有连接状态吗？",
        success: (res) => {
          if (res.confirm) {
            const app = getApp();
            const store = app.$store || app.store;
            if (store) {
              store.commit("CLEAR_CONNECTED_DEVICE");
            }
            app.globalData.connectionMethod = "";
            app.globalData.connectedDeviceId = "";
            app.globalData.deviceStatus = false;
            utils_connectionStateValidator.connectionStateValidator.logConnectionChange("MANUAL_STATE_CLEAR", "none", {
              reason: "USER_REQUESTED"
            });
            this.refreshStatus();
            this.refreshLogs();
            common_vendor.index.showToast({
              title: "状态已清除",
              icon: "success"
            });
          }
        }
      });
    },
    // 获取连接方式样式类
    getConnectionMethodClass() {
      switch (this.connectionMethod) {
        case "bluetooth":
          return "bluetooth";
        case "wifi":
          return "wifi";
        default:
          return "none";
      }
    },
    // 格式化时间
    formatTime(timestamp) {
      return new Date(timestamp).toLocaleTimeString();
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.t($data.connectionMethod || "未连接"),
    b: common_vendor.n($options.getConnectionMethodClass()),
    c: common_vendor.t($data.deviceStatus ? "在线" : "离线"),
    d: common_vendor.n($data.deviceStatus ? "online" : "offline"),
    e: common_vendor.t($data.connectedDeviceId || "无"),
    f: common_vendor.o((...args) => $options.validateState && $options.validateState(...args)),
    g: common_vendor.o((...args) => $options.autoFixState && $options.autoFixState(...args)),
    h: $data.validationResult
  }, $data.validationResult ? common_vendor.e({
    i: common_vendor.t($data.validationResult.isValid ? "✅ 状态正常" : "❌ 检测到问题"),
    j: common_vendor.n($data.validationResult.isValid ? "valid" : "invalid"),
    k: $data.validationResult.issues.length > 0
  }, $data.validationResult.issues.length > 0 ? {
    l: common_vendor.f($data.validationResult.issues, (issue, index, i0) => {
      return {
        a: common_vendor.t(issue.type),
        b: common_vendor.t(issue.message),
        c: common_vendor.t(issue.severity),
        d: common_vendor.n(issue.severity.toLowerCase()),
        e: index
      };
    })
  } : {}) : {}, {
    m: common_vendor.o((...args) => $options.simulateAppLifecycle && $options.simulateAppLifecycle(...args)),
    n: common_vendor.o((...args) => $options.simulateNetworkChange && $options.simulateNetworkChange(...args)),
    o: common_vendor.o((...args) => $options.clearAllStates && $options.clearAllStates(...args)),
    p: common_vendor.o((...args) => $options.refreshLogs && $options.refreshLogs(...args)),
    q: common_vendor.o((...args) => $options.clearLogs && $options.clearLogs(...args)),
    r: common_vendor.f($data.logs, (log, index, i0) => {
      return common_vendor.e({
        a: common_vendor.t($options.formatTime(log.timestamp)),
        b: common_vendor.t(log.action),
        c: common_vendor.t(log.connectionType),
        d: common_vendor.n(log.connectionType),
        e: log.details
      }, log.details ? {
        f: common_vendor.t(JSON.stringify(log.details, null, 2))
      } : {}, {
        g: log.validation && !log.validation.isValid
      }, log.validation && !log.validation.isValid ? {
        h: common_vendor.t(log.validation.issues.length)
      } : {}, {
        i: index
      });
    })
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-9e44c880"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/test/bluetooth-wifi-fix-test.js.map
