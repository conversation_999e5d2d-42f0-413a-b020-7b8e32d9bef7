/* 水平间距 */
/* 水平间距 */
/* 全局样式修复 - 确保页面占满屏幕宽度 */
page {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
}

/* 通用容器样式 */
.container, .page-container {
  width: 100%;
  min-height: 100vh;
  box-sizing: border-box;
}

/* 修复小程序页面宽度问题 */
uni-page-body {
  width: 100% !important;
}

/* 确保所有view元素正确继承宽度 */
view {
  box-sizing: border-box;
}
.content {
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
  min-height: 100vh;
}
.category-tabs {
  display: flex;
  background-color: #fff;
  padding: 20rpx 0;
  overflow-x: auto;
  white-space: nowrap;
  border-bottom: 1rpx solid #eee;
}
.category-tab {
  display: inline-flex;
  flex-direction: column;
  align-items: center;
  margin: 0 30rpx;
}
.icon-placeholder {
  width: 80rpx;
  height: 80rpx;
  background-color: transparent;
  border-radius: 50%;
  margin-bottom: 10rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.font-icon, .default-icon {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.icon-text {
  font-size: 48rpx;
}
.category-tab text {
  font-size: 24rpx;
  color: #333;
}
.product-list {
  padding: 20rpx;
}
.product-item {
  display: flex;
  background-color: #fff;
  padding: 20rpx;
  margin-bottom: 20rpx;
  border-radius: 12rpx;
}
.product-image-placeholder {
  width: 200rpx;
  height: 200rpx;
  background-color: #f0f0f0;
  border-radius: 8rpx;
}
.product-details {
  flex: 1;
  margin-left: 20rpx;
  display: flex;
  flex-direction: column;
}
.product-name {
  font-size: 28rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}
.product-brand {
  font-size: 26rpx;
  color: #333;
  margin-bottom: 10rpx;
}
.product-desc {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 10rpx;
}
.delivery-info {
  font-size: 24rpx;
  color: #4285f4;
  margin-bottom: 15rpx;
}
.product-price {
  margin-bottom: 10rpx;
}
.price-value {
  font-size: 36rpx;
  color: #f44336;
  font-weight: bold;
}
.price-unit {
  font-size: 24rpx;
  color: #666;
  margin-left: 5rpx;
}
.store-info {
  display: flex;
  justify-content: space-between;
  font-size: 24rpx;
  color: #999;
}
.load-more, .loading, .no-more {
  text-align: center;
  padding: 40rpx 20rpx;
  color: #999;
  font-size: 28rpx;
}
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 40rpx;
  background-color: #fff;
  margin: 20rpx;
  border-radius: 16rpx;
}
.empty-icon {
  margin-bottom: 30rpx;
}
.empty-icon .icon {
  font-size: 120rpx;
  opacity: 0.6;
}
.empty-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 16rpx;
}
.empty-desc {
  font-size: 28rpx;
  color: #666;
  text-align: center;
  line-height: 1.5;
  margin-bottom: 40rpx;
}
.empty-actions {
  display: flex;
  gap: 20rpx;
}
.empty-back-btn, .refresh-btn {
  border: none;
  border-radius: 15rpx;
  padding: 12rpx;
  font-size: 25rpx;
  min-width: 200rpx;
  font-weight: bold;
}
.empty-back-btn {
  background-color: #f5f5f5;
  color: #666;
}
.empty-back-btn:active {
  background-color: #e0e0e0;
}
.refresh-btn {
  background-color: #4285f4;
  color: #fff;
}
.refresh-btn:active {
  background-color: #3367d6;
}
.load-more {
  background-color: #f5f5f5;
  margin: 20rpx;
  border-radius: 8rpx;
  cursor: pointer;
}
.load-more:active {
  background-color: #e0e0e0;
}
.icon-placeholder image {
  width: 100%;
  height: 100%;
  border-radius: 50%;
}
.product-image-placeholder image {
  width: 100%;
  height: 100%;
  border-radius: 8rpx;
}
.category-tab.active {
  color: #4285f4;
}
.category-tab.active .icon-placeholder {
  border: 2rpx solid #4285f4;
}