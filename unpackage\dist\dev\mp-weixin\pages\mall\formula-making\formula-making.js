"use strict";
const common_vendor = require("../../../common/vendor.js");
const utils_request = require("../../../utils/request.js");
const utils_hairDyeManager = require("../../../utils/hairDyeManager.js");
const utils_dyeConsumptionManager = require("../../../utils/dyeConsumptionManager.js");
const utils_debounce = require("../../../utils/debounce.js");
const _sfc_main = {
  data() {
    return {
      isaction: false,
      recipeExecutor: null,
      colorComponents: null,
      developerComponents: null,
      continueToExecute: false,
      model: null,
      // 添加状态属性定义
      isExecutingQueue: false,
      isPaused: false,
      deviceStatus: false,
      commandStatus: false,
      // executionDetail: {
      // 	currentStep: '',
      // 	progress: 0
      // },
      dispensingAmounts: [
        // {
        // 	color: 'green',
        // 	name: '绿色',
        // 	// code: '2',
        // 	value: '10',
        // 	used: '0'
        // },
        // {
        // 	color: 'yellow',
        // 	name: '黄色',
        // 	// code: '3',
        // 	total: '5',
        // 	value: '0'
        // },
        // {
        // 	color: 'purple',
        // 	name: '紫色',
        // 	// code: '6',
        // 	total: '1',
        // 	value: '0'
        // }
      ],
      deviceStatus: "",
      commandStatus: "",
      inventoryStatus: null,
      // 染膏库存状态
      executionDetail: {
        // 指令执行详情
        progress: 0,
        currentStep: "",
        totalSteps: 0,
        startTime: null,
        estimatedEndTime: null
      },
      // 状态监听器相关
      statusWatcher: null,
      lastExecutingState: false,
      lastPausedState: false,
      lastStates: false,
      lastWanColorState: false,
      lastDeviceStates: false,
      lastCommandStatus: false,
      // 颜色分类
      colorName: void 0,
      // 颜色名称
      categoryName: void 0,
      // 为了记录调色记录的id
      formulaId: 0
    };
  },
  mounted() {
    const app = getApp();
    this.deviceStatus = app.globalData.deviceStatus;
    this.commandStatus = app.globalData.commandStatus;
    this.executionDetail = app.globalData.commandExecutionDetail;
    this.loadInventoryStatus();
  },
  onShow() {
    this.loadInventoryStatus();
    this.getState();
    this.initializeState();
  },
  onLoad(options) {
    try {
      if (options.data) {
        const dataString = decodeURIComponent(options.data);
        this.dispensingAmounts = JSON.parse(dataString);
        common_vendor.index.__f__("log", "at pages/mall/formula-making/formula-making.vue:238", "参数中的options.model", options.model);
        this.model = options.model;
        common_vendor.index.__f__("log", "at pages/mall/formula-making/formula-making.vue:241", "模式", this.model);
      } else {
        this.dispensingAmounts = {};
      }
      if (options.colorComponents && options.colorComponents !== "null") {
        try {
          const colorString = decodeURIComponent(options.colorComponents);
          this.colorComponents = JSON.parse(colorString);
        } catch (e) {
          common_vendor.index.__f__("error", "at pages/mall/formula-making/formula-making.vue:253", "解析 colorComponents 失败:", e);
          this.colorComponents = null;
        }
      } else {
        this.colorComponents = null;
      }
      if (options.developerComponents && options.developerComponents !== "null") {
        try {
          const devString = decodeURIComponent(options.developerComponents);
          this.developerComponents = JSON.parse(devString);
          common_vendor.index.__f__("log", "at pages/mall/formula-making/formula-making.vue:266", "双氧乳", this.developerComponents);
        } catch (e) {
          common_vendor.index.__f__("error", "at pages/mall/formula-making/formula-making.vue:268", "解析 developerComponents 失败:", e);
          this.developerComponents = null;
        }
      } else {
        this.developerComponents = null;
      }
      try {
        const cachedData = common_vendor.index.getStorageSync("selectedColorInfo");
        common_vendor.index.__f__("log", "at pages/mall/formula-making/formula-making.vue:277", "缓存中的数据", cachedData);
        if (cachedData) {
          this.colorName = cachedData.name || "未知颜色";
          this.categoryName = cachedData.category || "未知分类";
          this.formulaId = cachedData.id || 0;
          common_vendor.index.__f__("log", "at pages/mall/formula-making/formula-making.vue:283", "从缓存读取数据成功");
        } else {
          common_vendor.index.__f__("error", "at pages/mall/formula-making/formula-making.vue:285", "无法获取颜色信息");
          common_vendor.index.showToast({
            title: "加载失败",
            icon: "none"
          });
        }
      } catch (e) {
        common_vendor.index.__f__("error", "at pages/mall/formula-making/formula-making.vue:292", "读取缓存失败:", e);
      }
    } catch (mainError) {
      common_vendor.index.__f__("error", "at pages/mall/formula-making/formula-making.vue:296", "页面加载时发生错误:", mainError);
      this.dispensingAmounts = {};
      this.colorComponents = null;
      this.developerComponents = null;
    }
  },
  computed: {
    // 访问全局状态
    // isExecutingQueue() {
    // 	return getApp().globalData.isExecutingQueue;
    // },
    // isPaused() {
    // 	return getApp().globalData.isPaused;
    // },
    // connectionMethod() {
    // 	return getApp().globalData.connectionMethod;
    // },
    // // 连接方式判断
    // isBluetoothConnection() {
    // 	return this.connectionMethod === 'bluetooth';
    // },
    // isWifiConnection() {
    // 	return this.connectionMethod === 'wifi';
    // }
  },
  created() {
    this.recipeExecutor = new utils_hairDyeManager.HairDyeRecipeExecutor(this.$store);
    this.startStatusWatcher();
  },
  beforeDestroy() {
    if (this.statusWatcher) {
      clearInterval(this.statusWatcher);
    }
  },
  methods: {
    // 初始化状态
    initializeState() {
      const app = getApp();
      this.isExecutingQueue = app.globalData.isExecutingQueue || false;
      this.isPaused = app.globalData.isPaused || false;
      this.deviceStatus = app.globalData.deviceStatus || false;
      this.commandStatus = app.globalData.commandStatus || false;
      this.executionDetail = app.globalData.commandExecutionDetail || {
        currentStep: "",
        progress: 0
      };
      common_vendor.index.__f__("log", "at pages/mall/formula-making/formula-making.vue:346", "状态初始化完成:", {
        isExecutingQueue: this.isExecutingQueue,
        isPaused: this.isPaused,
        deviceStatus: this.deviceStatus,
        commandStatus: this.commandStatus
      });
    },
    // 启动状态监听器
    startStatusWatcher() {
      this.lastExecutingState = getApp().globalData.isExecutingQueue;
      this.lastPausedState = getApp().globalData.isPaused;
      this.lastWanColorState = getApp().globalData.wan_color;
      this.lastDeviceStates = getApp().globalData.deviceStatus;
      this.lastCommandStatus = getApp().globalData.commandStatus;
      this.lastExecutionDetail = getApp().globalData.commandExecutionDetail;
      common_vendor.index.__f__("log", "at pages/mall/formula-making/formula-making.vue:364", "设备是否在线", this.lastDeviceStates);
      common_vendor.index.__f__("log", "at pages/mall/formula-making/formula-making.vue:365", "当前页面在线状态", this.deviceStatus);
      this.statusWatcher = setInterval(() => {
        const app = getApp();
        const currentExecuting = app.globalData.isExecutingQueue;
        const currentPaused = app.globalData.isPaused;
        const currentWanColor = app.globalData.wan_color;
        const currentDeviceStates = app.globalData.deviceStatus;
        const currentCommandStatus = app.globalData.commandStatus;
        const currentCommandExecutionDetail = app.globalData.commandExecutionDetail;
        if (currentExecuting !== this.lastExecutingState || currentPaused !== this.lastPausedState || currentWanColor !== this.lastWanColorState || currentDeviceStates != this.deviceStatus || currentCommandStatus != this.lastCommandStatus || currentCommandExecutionDetail != this.lastExecutionDetail) {
          this.lastExecutingState = currentExecuting;
          this.lastPausedState = currentPaused;
          this.lastWanColorState = currentWanColor;
          this.lastDeviceStates = currentDeviceStates;
          this.lastCommandStatus = currentCommandStatus;
          this.lastExecutionDetail = currentCommandExecutionDetail;
          this.isExecutingQueue = currentExecuting;
          this.isPaused = currentPaused;
          this.isaction = currentWanColor;
          this.deviceStatus = currentDeviceStates;
          this.commandStatus = currentCommandStatus;
          this.executionDetail = currentCommandExecutionDetail;
          common_vendor.index.__f__("log", "at pages/mall/formula-making/formula-making.vue:401", "当前状态", this.deviceStatus);
          common_vendor.index.__f__("log", "at pages/mall/formula-making/formula-making.vue:402", "调色碗颜色", this.isaction ? "绿色" : "灰色");
          common_vendor.index.__f__("log", "at pages/mall/formula-making/formula-making.vue:403", "状态变化检测到，强制更新UI:", {
            executing: currentExecuting,
            paused: currentPaused
          });
          this.$forceUpdate();
        }
      }, 500);
    },
    getState() {
      const app = getApp();
      const currentExecuting = app.globalData.isExecutingQueue;
      const currentPaused = app.globalData.isPaused;
      const currentWanColor = app.globalData.wan_color;
      this.lastExecutingState = currentExecuting;
      this.lastPausedState = currentPaused;
      this.lastWanColorState = currentWanColor;
      this.isExecutingQueue = currentExecuting;
      this.isPaused = currentPaused;
      this.wan_color = currentWanColor;
      this.isaction = currentWanColor;
      this.$forceUpdate();
      common_vendor.index.__f__("log", "at pages/mall/formula-making/formula-making.vue:427", "页面退出需要更新:", {
        executing: currentExecuting,
        paused: currentPaused
      });
    },
    // 加载库存状态
    async loadInventoryStatus() {
      try {
        const app = getApp();
        const deviceCode = app.globalData.connectedDeviceId;
        if (deviceCode) {
          this.inventoryStatus = await utils_dyeConsumptionManager.dyeConsumptionManager.getInventoryStatus(deviceCode);
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/mall/formula-making/formula-making.vue:442", "加载库存状态失败:", error);
      }
    },
    // 更新库存状态
    updateInventoryStatus() {
      const app = getApp();
      if (app.globalData.dyeInventory.lastUpdateTime) {
        this.loadInventoryStatus();
      }
    },
    // 检查库存是否足够
    async checkStockBeforeExecution() {
      try {
        const app = getApp();
        const deviceCode = app.globalData.connectedDeviceId;
        if (!deviceCode)
          return true;
        const recipe = Object.entries({
          ...this.colorComponents,
          ...this.developerComponents
        }).reduce((acc, [key, value]) => {
          if (value >= 0.1) {
            acc[key] = value;
          }
          return acc;
        }, {});
        const stockCheck = await utils_dyeConsumptionManager.dyeConsumptionManager.checkSufficientStock(deviceCode, recipe);
        if (!stockCheck.sufficient) {
          let message = "染膏库存不足:\n";
          stockCheck.insufficient.forEach((item) => {
            message += `${item.colorName}: 需要${item.required}g, 仅剩${item.available}g
`;
          });
          common_vendor.index.showModal({
            title: "库存不足",
            content: message,
            showCancel: true,
            confirmText: "继续执行",
            cancelText: "取消",
            success: (res) => {
              if (res.confirm) {
                this.executeRecipeWithoutCheck();
              }
            }
          });
          return false;
        }
        if (stockCheck.warnings.length > 0) {
          let message = "以下染膏执行后将库存较低:\n";
          stockCheck.warnings.forEach((item) => {
            message += `${item.colorName}: 执行后剩余${item.remaining}g
`;
          });
          common_vendor.index.showModal({
            title: "库存预警",
            content: message,
            showCancel: true,
            confirmText: "继续执行",
            cancelText: "取消",
            success: (res) => {
              if (res.confirm) {
                this.executeRecipeWithoutCheck();
              }
            }
          });
          return false;
        }
        return true;
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/mall/formula-making/formula-making.vue:519", "检查库存失败:", error);
        return true;
      }
    },
    // 不检查库存直接执行
    async executeRecipeWithoutCheck() {
      const data = {
        ...this.colorComponents,
        ...this.developerComponents
      };
      common_vendor.index.__f__("log", "at pages/mall/formula-making/formula-making.vue:531", "当前指令生成", data);
      const app = getApp();
      app.updateCommandExecutionDetail({
        startTime: (/* @__PURE__ */ new Date()).toISOString(),
        progress: 0,
        currentStep: "开始执行",
        totalSteps: Object.keys(data).length
      });
      const response = await this.recipeExecutor.executeRecipe(data, this.model);
      common_vendor.index.__f__("log", "at pages/mall/formula-making/formula-making.vue:544", "this.recipeExecutor.executeRecipe返回", response);
      if (!response) {
        common_vendor.index.showToast({
          title: "设备不在线",
          icon: "none",
          duration: 2e3
        });
        app.updateCommandExecutionDetail({
          progress: 0,
          currentStep: "执行失败"
        });
      }
    },
    // 大师模式停止
    caseting: async function() {
      const app = getApp();
      try {
        if (!this.checkOnline()) {
          return;
        }
        if (!this.isExecutingQueue) {
          common_vendor.index.showToast({
            title: "请开始出料",
            icon: "none",
            duration: 2e3
            // 持续时间
          });
          return;
        }
        if (app.isBluetoothConnection()) {
          const response = this.recipeExecutor.stopMaster();
          if (!response) {
            common_vendor.index.showToast({
              title: "大师模式停止出料失败",
              icon: "none",
              duration: 2e3
              // 持续时间
            });
            return;
          }
        } else if (app.isWifiConnection()) {
          const response = await utils_request.apiService.mall.command.sendStop(app.globalData.connectedDeviceId, {
            model: "master"
          });
          if (!response) {
            common_vendor.index.showToast({
              title: "大师模式停止出料失败",
              icon: "none",
              duration: 2e3
              // 持续时间
            });
            return;
          }
        }
        const command = app.globalData.currentCommand;
        common_vendor.index.showToast({
          title: "大师模式停止当前`${command.colorCode}`",
          icon: "none",
          duration: 2e3
          // 持续时间
        });
      } catch (err) {
        common_vendor.index.__f__("log", "at pages/mall/formula-making/formula-making.vue:608", "大师模式停止出料失败", err);
      }
    },
    // 继续执行那么就发送继续的指令
    canceling: async function() {
      try {
        if (!this.checkOnline()) {
          return;
        }
        common_vendor.index.__f__("log", "at pages/mall/formula-making/formula-making.vue:618", "取消出料");
        const response = await this.recipeExecutor.canceling();
        if (!response) {
          common_vendor.index.showToast({
            title: "取消出料失败",
            icon: "none",
            duration: 2e3
          });
        } else {
          common_vendor.index.__f__("log", "at pages/mall/formula-making/formula-making.vue:627", "取消指令已发送，等待设备确认");
          const app = getApp();
          app.setCanceling();
        }
      } catch (err) {
        common_vendor.index.__f__("log", "at pages/mall/formula-making/formula-making.vue:634", "取消出料失败", err);
        common_vendor.index.showToast({
          title: "取消出料失败",
          icon: "none",
          duration: 2e3
        });
      }
      this.$forceUpdate();
    },
    stopttoning: async function() {
      if (!this.checkOnline()) {
        return;
      }
      common_vendor.index.__f__("log", "at pages/mall/formula-making/formula-making.vue:648", "暂停出料");
      const response = await this.recipeExecutor.pauseDispensing();
      if (!response) {
        common_vendor.index.showToast({
          title: "暂停出料失败",
          icon: "none",
          duration: 2e3
          // 持续时间
        });
      } else {
        const app = getApp();
        app.setPaused(true);
        common_vendor.index.__f__("log", "at pages/mall/formula-making/formula-making.vue:660", "暂停出料成功，状态已更新");
      }
      this.$forceUpdate();
    },
    resumetoning: async function() {
      if (!this.checkOnline()) {
        return;
      }
      common_vendor.index.__f__("log", "at pages/mall/formula-making/formula-making.vue:669", "恢复出料");
      const response = await this.recipeExecutor.resumeDispensing();
      if (!response) {
        common_vendor.index.showToast({
          title: "恢复出料失败",
          icon: "none",
          duration: 2e3
          // 持续时间
        });
      } else {
        const app = getApp();
        app.setPaused(false);
        common_vendor.index.__f__("log", "at pages/mall/formula-making/formula-making.vue:680", "恢复出料成功，状态已更新");
      }
      this.$forceUpdate();
    },
    checkOnline() {
      if (!this.deviceStatus) {
        common_vendor.index.showToast({
          title: "设备离线",
          icon: "error",
          // 微信小程序支持"success"/"error"/"loading"/"none"
          duration: 1e3,
          mask: true
          // 防止触摸穿透
        });
        return false;
      }
      return true;
    },
    // 记录调色记录
    recordToningOperation: async function() {
      const data = {
        formulaId: this.formulaId,
        colorComponents: this.colorComponents,
        developerComponents: this.developerComponents
      };
      try {
        const response = utils_request.apiService.record.createReCode(data);
        if (response.code === 500) {
          throw new Error("记录失败，HTTP状态码：" + response.code);
        }
        common_vendor.index.__f__("log", "at pages/mall/formula-making/formula-making.vue:713", "调色记录成功");
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/mall/formula-making/formula-making.vue:715", "记录调色操作失败:", error);
      }
    },
    starttoning: async function() {
      try {
        if (!this.checkOnline()) {
          return;
        }
        this.recordToningOperation();
        common_vendor.index.__f__("log", "at pages/mall/formula-making/formula-making.vue:731", "开始执行出料");
        const stockSufficient = await this.checkStockBeforeExecution();
        if (!stockSufficient) {
          return;
        }
        await this.executeRecipeWithoutCheck();
        common_vendor.index.__f__("log", "at pages/mall/formula-making/formula-making.vue:741", "开始出料成功，状态已更新");
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/mall/formula-making/formula-making.vue:743", "执行出错:", error);
        const app = getApp();
        app.setErrorState({
          errorType: "EXECUTION_ERROR",
          errorMessage: `执行出错: ${error.message}`,
          errorCode: null
        });
        app.updateCommandExecutionDetail({
          progress: 0,
          currentStep: "执行失败"
        });
        app.setExecutingQueue(false);
        app.setPausedStatus(false);
      }
      this.$forceUpdate();
    },
    // 防抖处理方法
    debounceStarttoning: utils_debounce.debounce(function() {
      this.starttoning();
    }, 300),
    debounceStopttoning: utils_debounce.debounce(function() {
      this.stopttoning();
    }, 300),
    debounceResumetoning: utils_debounce.debounce(function() {
      this.resumetoning();
    }, 300),
    debounceCanceling: utils_debounce.debounce(function() {
      this.canceling();
    }, 300),
    debounceCaseting: utils_debounce.debounce(function() {
      this.caseting();
    }, 300)
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.t($data.categoryName),
    b: common_vendor.t($data.deviceStatus ? "设备在线" : "设备离线"),
    c: common_vendor.n($data.deviceStatus ? "status-online" : "status-offline"),
    d: common_vendor.t($data.commandStatus ? $data.executionDetail.currentStep || "调色中" : "请放碗"),
    e: $data.commandStatus ? "SlateBlue" : "black",
    f: $data.commandStatus ? 1 : "",
    g: $data.isaction ? "/static/icons/tiaosewan_action.png" : "/static/icons/tiaosewan.png",
    h: $data.isaction ? 1 : "",
    i: !$data.isaction ? 1 : "",
    j: common_vendor.t($data.colorName),
    k: !$data.isExecutingQueue && !$data.isPaused
  }, !$data.isExecutingQueue && !$data.isPaused ? {
    l: common_vendor.o((...args) => $options.debounceStarttoning && $options.debounceStarttoning(...args))
  } : $data.isExecutingQueue && !$data.isPaused ? {
    n: common_vendor.o((...args) => $options.debounceStopttoning && $options.debounceStopttoning(...args))
  } : $data.isPaused ? {
    p: common_vendor.o((...args) => $options.debounceResumetoning && $options.debounceResumetoning(...args))
  } : {}, {
    m: $data.isExecutingQueue && !$data.isPaused,
    o: $data.isPaused,
    q: $data.isExecutingQueue || $data.isPaused
  }, $data.isExecutingQueue || $data.isPaused ? {
    r: common_vendor.o((...args) => $options.debounceCanceling && $options.debounceCanceling(...args))
  } : {}, {
    s: $data.model === "master"
  }, $data.model === "master" ? {
    t: common_vendor.o((...args) => $options.debounceCaseting && $options.debounceCaseting(...args))
  } : {}, {
    v: common_vendor.f($data.dispensingAmounts, (item, index, i0) => {
      return {
        a: common_vendor.n(item.color),
        b: item.color,
        c: common_vendor.t(item.name),
        d: common_vendor.t(item.value),
        e: common_vendor.t(item.used),
        f: index
      };
    })
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/pages/mall/formula-making/formula-making.js.map
