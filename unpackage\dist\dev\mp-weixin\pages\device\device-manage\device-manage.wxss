/* 水平间距 */
/* 水平间距 */
/* 全局样式修复 - 确保页面占满屏幕宽度 */
page {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
}

/* 通用容器样式 */
.container, .page-container {
  width: 100%;
  min-height: 100vh;
  box-sizing: border-box;
}

/* 修复小程序页面宽度问题 */
uni-page-body {
  width: 100% !important;
}

/* 确保所有view元素正确继承宽度 */
view {
  box-sizing: border-box;
}

/* 保持原有的样式不变 */
.device-management {
  width: 100%;
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f8f8f8;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif;
}
.header {
  background-color: #4285f4;
  color: white;
  padding: 20px;
  position: relative;
}
.header .main-title {
  font-size: 20px;
  font-weight: bold;
  text-align: center;
  margin-bottom: 10px;
}
.header .sub-title {
  text-align: center;
  font-size: 14px;
  margin-bottom: 15px;
}
.header .sub-title .underline {
  text-decoration: underline;
  margin-left: 5px;
}
.header .disconnect-btn-container {
  position: absolute;
  top: 20px;
  right: 20px;
}
.header .disconnect-btn-container .disconnect-btn {
  background-color: rgba(255, 255, 255, 0.2);
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 12px;
  border: 1px solid rgba(255, 255, 255, 0.3);
}
.header .divider-line {
  height: 1px;
  background-color: rgba(255, 255, 255, 0.3);
  margin-top: 10px;
}
.device-status-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 30px 0;
  background-color: white;
  position: relative;
  height: 200px;
}
.device-status-container .circles-container {
  position: relative;
  width: 180px;
  height: 180px;
  display: flex;
  justify-content: center;
  align-items: center;
}
.device-status-container .circles-container .circle {
  position: absolute;
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.device-status-container .circles-container .circle-outer {
  width: 180px;
  height: 180px;
  background: linear-gradient(135deg, #e0e0e0, #b0b0b0);
  opacity: 0.6;
}
.device-status-container .circles-container .circle-middle {
  width: 160px;
  height: 160px;
  background: linear-gradient(135deg, #d0d0d0, #a0a0a0);
  opacity: 0.7;
}
.device-status-container .circles-container .circle-inner {
  width: 140px;
  height: 140px;
  background: linear-gradient(135deg, #c0c0c0, #909090);
  opacity: 0.8;
}
.device-status-container .circles-container .circle-content {
  width: 120px;
  height: 120px;
  background-color: white;
  border: 2px solid #4285f4;
  z-index: 2;
}
.device-status-container .circles-container .circle-content .wifi-icon {
  font-size: 18px;
  margin-bottom: 2px;
}
.device-status-container .circles-container .circle-content .status-text {
  font-size: 12px;
  color: #4285f4;
  font-weight: bold;
  margin-bottom: 2px;
}
.device-status-container .circles-container .circle-content .weight-text {
  font-size: 14px;
  font-weight: bold;
  color: #333;
  margin-bottom: 2px;
}
.device-status-container .circles-container .circle-content .clear-text {
  font-size: 10px;
  color: #666;
  text-decoration: underline;
}
.tab-bar {
  display: flex;
  background-color: white;
  border-bottom: 1px solid #e0e0e0;
}
.tab-bar .tab-item {
  flex: 1;
  text-align: center;
  padding: 15px 0;
  font-size: 16px;
  color: #666;
  border-bottom: 3px solid transparent;
}
.tab-bar .tab-item.active {
  color: #4285f4;
  border-bottom-color: #4285f4;
  font-weight: bold;
}
.page-content {
  flex: 1;
  overflow-y: auto;
  background-color: white;
}
.section {
  padding: 20px;
}
.section .section-title {
  font-size: 18px;
  font-weight: bold;
  color: #333;
  margin-bottom: 20px;
  position: relative;
}
.section .view-dye-btn-top {
  position: absolute;
  top: 0;
  right: 20px;
  background-color: #4285f4;
  color: white;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 12px;
}
.dye-container .dye-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  justify-content: space-between;
}
.dye-container .dye-grid .dye-item {
  width: calc(33.333% - 14px);
  display: flex;
  flex-direction: column;
  align-items: center;
}
.dye-container .dye-grid .dye-item .dye-circle {
  width: 60px;
  height: 60px;
  border: 3px solid;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 8px;
}
.dye-container .dye-grid .dye-item .dye-circle .percentage {
  font-size: 12px;
  font-weight: bold;
  color: #333;
}
.dye-container .dye-grid .dye-item .color-name {
  font-size: 12px;
  color: #666;
  text-align: center;
}
.info-section {
  padding: 20px;
}
.info-section .section-title {
  font-size: 18px;
  font-weight: bold;
  color: #333;
  margin-bottom: 20px;
}
.info-section .info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 0;
  border-bottom: 1px solid #f0f0f0;
}
.info-section .info-item .info-label {
  font-size: 14px;
  color: #666;
}
.info-section .info-item .info-value {
  font-size: 14px;
  color: #333;
  font-weight: 500;
}
.setting-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 0;
  border-bottom: 1px solid #f0f0f0;
}
.setting-item .setting-label {
  font-size: 14px;
  color: #333;
}
.setting-item .setting-action {
  font-size: 14px;
  color: #4285f4;
}

/* 新增弹窗样式 */
.popup-content {
  padding: 20rpx;
  width: 100%;
}
.color-display {
  width: 120rpx;
  height: 80rpx;
  margin: 0 auto 30rpx;
  border-radius: 8rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}
.color-details {
  margin-top: 20rpx;
}
.detail-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20rpx;
  font-size: 28rpx;
}
.detail-label {
  color: #666;
}
.detail-value {
  color: #333;
  font-weight: 500;
}
.progress-container {
  margin: 30rpx 0;
}
.status-message {
  text-align: center;
  font-size: 28rpx;
  font-weight: 500;
  margin-top: 20rpx;
}

/* 调整uni-popup默认样式 */
.uni-popup__wrapper {
  border-radius: 20rpx;
  overflow: hidden;
}
.uni-popup__dialog {
  width: 80%;
  max-width: 600rpx;
}