<view class="container data-v-9e44c880"><view class="header data-v-9e44c880"><text class="title data-v-9e44c880">🔧 蓝牙WiFi耦合修复测试</text><text class="subtitle data-v-9e44c880">验证蓝牙连接稳定性修复效果</text></view><view class="status-section data-v-9e44c880"><view class="section-title data-v-9e44c880">📊 当前连接状态</view><view class="status-item data-v-9e44c880"><text class="label data-v-9e44c880">连接方式:</text><text class="{{['value', 'data-v-9e44c880', b]}}">{{a}}</text></view><view class="status-item data-v-9e44c880"><text class="label data-v-9e44c880">设备状态:</text><text class="{{['value', 'data-v-9e44c880', d]}}">{{c}}</text></view><view class="status-item data-v-9e44c880"><text class="label data-v-9e44c880">设备ID:</text><text class="value data-v-9e44c880">{{e}}</text></view></view><view class="validation-section data-v-9e44c880"><view class="section-title data-v-9e44c880">🔍 状态验证</view><button class="btn btn-primary data-v-9e44c880" bindtap="{{f}}">验证连接状态</button><button class="btn btn-secondary data-v-9e44c880" bindtap="{{g}}">自动修复状态</button><view wx:if="{{h}}" class="validation-result data-v-9e44c880"><view class="{{['validation-status', 'data-v-9e44c880', j]}}">{{i}}</view><view wx:if="{{k}}" class="issues data-v-9e44c880"><view wx:for="{{l}}" wx:for-item="issue" wx:key="e" class="issue data-v-9e44c880"><text class="issue-type data-v-9e44c880">{{issue.a}}</text><text class="issue-message data-v-9e44c880">{{issue.b}}</text><text class="{{['issue-severity', 'data-v-9e44c880', issue.d]}}">{{issue.c}}</text></view></view></view></view><view class="test-section data-v-9e44c880"><view class="section-title data-v-9e44c880">🧪 测试操作</view><button class="btn btn-test data-v-9e44c880" bindtap="{{m}}">模拟应用生命周期</button><button class="btn btn-test data-v-9e44c880" bindtap="{{n}}">模拟网络变化</button><button class="btn btn-test data-v-9e44c880" bindtap="{{o}}">清除所有状态</button></view><view class="log-section data-v-9e44c880"><view class="section-title data-v-9e44c880">📝 状态变化日志</view><button class="btn btn-small data-v-9e44c880" bindtap="{{p}}">刷新日志</button><button class="btn btn-small data-v-9e44c880" bindtap="{{q}}">清除日志</button><scroll-view class="log-container data-v-9e44c880" scroll-y="true"><view wx:for="{{r}}" wx:for-item="log" wx:key="i" class="log-item data-v-9e44c880"><view class="log-header data-v-9e44c880"><text class="log-time data-v-9e44c880">{{log.a}}</text><text class="log-action data-v-9e44c880">{{log.b}}</text><text class="{{['log-type', 'data-v-9e44c880', log.d]}}">{{log.c}}</text></view><view wx:if="{{log.e}}" class="log-details data-v-9e44c880">{{log.f}}</view><view wx:if="{{log.g}}" class="log-validation data-v-9e44c880"> ⚠️ 检测到 {{log.h}} 个问题 </view></view></scroll-view></view></view>