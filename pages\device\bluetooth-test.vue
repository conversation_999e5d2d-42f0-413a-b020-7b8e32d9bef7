<template>
	<view class="container">
		<view class="header">
			<text class="title">蓝牙功能测试</text>
		</view>
		
		<view class="status-section">
			<view class="status-item">
				<text class="label">蓝牙适配器状态:</text>
				<text class="value" :class="bluetoothReady ? 'success' : 'error'">
					{{ bluetoothReady ? '已就绪' : '未就绪' }}
				</text>
			</view>
			
			<view class="status-item">
				<text class="label">扫描状态:</text>
				<text class="value" :class="isScanning ? 'warning' : 'normal'">
					{{ isScanning ? '扫描中' : '未扫描' }}
				</text>
			</view>
			
			<view class="status-item">
				<text class="label">发现设备数:</text>
				<text class="value">{{ deviceCount }}</text>
			</view>
		</view>
		
		<view class="button-section">
			<button class="test-btn" @click="testBluetoothPermission">检测蓝牙权限</button>
			<button class="test-btn" @click="initBluetooth">初始化蓝牙</button>
			<button class="test-btn" @click="startScan" :disabled="isScanning">开始扫描</button>
			<button class="test-btn" @click="stopScan" :disabled="!isScanning">停止扫描</button>
		</view>
		
		<view class="device-section" v-if="deviceList.length > 0">
			<text class="section-title">发现的设备:</text>
			<view class="device-item" v-for="(device, index) in deviceList" :key="index">
				<text class="device-name">{{ device.name }}</text>
				<text class="device-id">{{ device.deviceId }}</text>
				<text class="device-rssi">{{ device.RSSI }}dBm</text>
			</view>
		</view>
		
		<view class="log-section">
			<text class="section-title">操作日志:</text>
			<scroll-view class="log-list" scroll-y>
				<view class="log-item" v-for="(log, index) in logs" :key="index">
					<text>{{ log }}</text>
				</view>
			</scroll-view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			bluetoothReady: false,
			isScanning: false,
			deviceList: [],
			logs: [],
			deviceCount: 0
		}
	},
	
	onLoad() {
		this.updateStatus()
	},
	
	methods: {
		updateStatus() {
			this.bluetoothReady = this.$store.state.bluetoothAdapterReady
			this.isScanning = this.$store.state.isScanning
			this.deviceList = [...this.$store.state.deviceList]
			this.logs = [...this.$store.state.logs]
			this.deviceCount = this.deviceList.length
		},
		
		async testBluetoothPermission() {
			this.addLog('开始检测蓝牙权限...')
			
			try {
				// #ifdef MP-WEIXIN
				const setting = await uni.getSetting()
				this.addLog(`权限设置: ${JSON.stringify(setting.authSetting)}`)
				
				if (setting.authSetting['scope.bluetooth'] === undefined) {
					this.addLog('蓝牙权限未设置，尝试请求权限...')
					try {
						await uni.authorize({ scope: 'scope.bluetooth' })
						this.addLog('蓝牙权限请求成功')
					} catch (err) {
						this.addLog(`蓝牙权限请求失败: ${err.errMsg}`)
					}
				} else if (setting.authSetting['scope.bluetooth']) {
					this.addLog('蓝牙权限已授权')
				} else {
					this.addLog('蓝牙权限被拒绝，需要手动开启')
				}
				// #endif
			} catch (err) {
				this.addLog(`检测权限失败: ${err.errMsg}`)
			}
		},
		
		async initBluetooth() {
			this.addLog('开始初始化蓝牙...')
			try {
				const result = await this.$store.dispatch('initBluetooth')
				this.updateStatus()
				this.addLog(`蓝牙初始化${result ? '成功' : '失败'}`)
			} catch (err) {
				this.addLog(`蓝牙初始化异常: ${err.message}`)
			}
		},
		
		async startScan() {
			this.addLog('开始扫描设备...')
			try {
				const result = await this.$store.dispatch('startScan')
				this.updateStatus()
				this.addLog(`扫描${result ? '启动成功' : '启动失败'}`)
				
				// 定时更新状态
				const interval = setInterval(() => {
					this.updateStatus()
					if (!this.isScanning) {
						clearInterval(interval)
					}
				}, 1000)
			} catch (err) {
				this.addLog(`扫描失败: ${err.message}`)
			}
		},
		
		async stopScan() {
			this.addLog('停止扫描...')
			try {
				await this.$store.dispatch('stopScan')
				this.updateStatus()
				this.addLog('扫描已停止')
			} catch (err) {
				this.addLog(`停止扫描失败: ${err.message}`)
			}
		},
		
		addLog(message) {
			const timestamp = new Date().toLocaleTimeString()
			this.logs.unshift(`[${timestamp}] ${message}`)
			if (this.logs.length > 20) {
				this.logs.pop()
			}
		}
	}
}
</script>

<style>
.container {
	padding: 20rpx;
	background-color: #f5f5f5;
	min-height: 100vh;
}

.header {
	text-align: center;
	padding: 40rpx 0;
	background-color: #4285f4;
	color: white;
	margin-bottom: 20rpx;
	border-radius: 10rpx;
}

.title {
	font-size: 36rpx;
	font-weight: bold;
}

.status-section {
	background-color: white;
	padding: 30rpx;
	margin-bottom: 20rpx;
	border-radius: 10rpx;
}

.status-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 20rpx 0;
	border-bottom: 1rpx solid #f0f0f0;
}

.status-item:last-child {
	border-bottom: none;
}

.label {
	font-size: 28rpx;
	color: #333;
}

.value {
	font-size: 28rpx;
	font-weight: bold;
}

.value.success {
	color: #1aad19;
}

.value.error {
	color: #e64340;
}

.value.warning {
	color: #ff9900;
}

.value.normal {
	color: #666;
}

.button-section {
	background-color: white;
	padding: 30rpx;
	margin-bottom: 20rpx;
	border-radius: 10rpx;
}

.test-btn {
	width: 100%;
	height: 80rpx;
	background-color: #4285f4;
	color: white;
	border: none;
	border-radius: 10rpx;
	font-size: 28rpx;
	margin-bottom: 20rpx;
}

.test-btn:last-child {
	margin-bottom: 0;
}

.test-btn[disabled] {
	background-color: #ccc;
}

.device-section, .log-section {
	background-color: white;
	padding: 30rpx;
	margin-bottom: 20rpx;
	border-radius: 10rpx;
}

.section-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 20rpx;
	display: block;
}

.device-item {
	padding: 20rpx 0;
	border-bottom: 1rpx solid #f0f0f0;
}

.device-item:last-child {
	border-bottom: none;
}

.device-name {
	font-size: 28rpx;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 10rpx;
}

.device-id {
	font-size: 24rpx;
	color: #999;
	display: block;
	margin-bottom: 5rpx;
}

.device-rssi {
	font-size: 24rpx;
	color: #666;
	display: block;
}

.log-list {
	max-height: 400rpx;
}

.log-item {
	font-size: 24rpx;
	color: #666;
	padding: 10rpx 0;
	border-bottom: 1rpx dashed #eee;
	word-break: break-all;
}

.log-item:last-child {
	border-bottom: none;
}
</style>
