"use strict";
const common_vendor = require("../common/vendor.js");
const BASE_URL = "https://www.narenqiqige.com";
const defaultOptions = {
  method: "GET",
  timeout: 1e4,
  // 10秒超时
  header: {
    "Content-Type": "application/json"
  }
};
const clearUserStatus = () => {
  common_vendor.index.removeStorageSync("token");
  common_vendor.index.removeStorageSync("refreshToken");
  common_vendor.index.removeStorageSync("isLoggedIn");
  common_vendor.index.removeStorageSync("userInfo");
};
const handleTokenExpired = (reject, config, resolve) => {
  const refreshToken = common_vendor.index.getStorageSync("refreshToken");
  if (!refreshToken) {
    clearUserStatus();
    reject({ code: 401, message: "请重新登录" });
    return;
  }
  common_vendor.wx$1.request({
    url: `${BASE_URL}/api/user/refresh-token`,
    method: "POST",
    header: {
      refreshAuthorization: refreshToken
    },
    success: (refreshRes) => {
      if (refreshRes.data.code === 200) {
        common_vendor.index.__f__("log", "at utils/request.js:44", "刷新token", refreshRes);
        const newToken = refreshRes.data.data.token;
        const userInfo = {
          id: refreshRes.data.data.userInfo.id,
          phone: refreshRes.data.data.userInfo.phone,
          nickname: refreshRes.data.data.userInfo.nickname,
          avatar: refreshRes.data.data.userInfo.avatar,
          username: refreshRes.data.data.userInfo.username
        };
        common_vendor.index.setStorageSync("userInfo", userInfo);
        common_vendor.index.setStorageSync("token", newToken);
        config.header.Authorization = `Bearer ${newToken}`;
        common_vendor.wx$1.request({
          ...config,
          success: resolve,
          fail: reject
        });
      } else if (refreshRes.data.code === 401) {
        clearUserStatus();
        reject({ code: 401, message: "登录已过期，请重新登录" });
      }
    },
    fail: (err) => {
      common_vendor.index.__f__("log", "at utils/request.js:73", "刷新token失败", err);
    }
  });
};
const request = (options) => {
  return new Promise((resolve, reject) => {
    const token = common_vendor.index.getStorageSync("token");
    const mergedOptions = {
      ...defaultOptions,
      ...options,
      url: options.url.startsWith("http") ? options.url : `${BASE_URL}${options.url}`,
      header: {
        ...defaultOptions.header,
        ...options.header || {},
        // 如果有token，添加到请求头
        ...token ? {
          "Authorization": `Bearer ${token}`
        } : {}
      }
    };
    common_vendor.index.__f__("log", "at utils/request.js:98", "准备发送的请求参数: ", mergedOptions);
    common_vendor.wx$1.request({
      ...mergedOptions,
      timeout: 6e4,
      success: (res) => {
        const {
          statusCode,
          data
        } = res;
        if (statusCode >= 200 && statusCode < 300) {
          if (data.code === 200) {
            resolve(data);
          } else if (data.code === 401) {
            handleTokenExpired(reject, mergedOptions, resolve);
          } else {
            const error = {
              code: data.code,
              message: data.message || "业务逻辑错误",
              data: data.data,
              statusCode
            };
            reject(error);
          }
        } else {
          const error = {
            code: statusCode,
            message: `HTTP错误: ${statusCode}`,
            data,
            statusCode
          };
          reject(error);
        }
      },
      fail: (err) => {
        const error = {
          code: -1,
          message: "网络请求失败",
          error: err.errMsg || "未知网络错误",
          statusCode: err.statusCode || -1
        };
        reject(error);
      },
      complete: (res) => {
      }
    });
  });
};
const get = (url, data, header) => {
  let finalUrl = url;
  if (data && Object.keys(data).length > 0) {
    const queryString = Object.keys(data).filter((key) => data[key] != null).map((key) => `${encodeURIComponent(key)}=${encodeURIComponent(data[key])}`).join("&");
    if (queryString) {
      finalUrl += (url.includes("?") ? "&" : "?") + queryString;
    }
  }
  return request({
    url: finalUrl,
    // 使用正确的属性名 "url"
    header,
    method: "GET"
    // 不需要传 null 或 data，GET 请求的参数已经在 URL 中
  });
};
const post = (url, data, header) => {
  return request({
    url,
    data,
    header,
    method: "POST"
  });
};
const put = (url, data, header) => {
  return request({
    url,
    data,
    header,
    method: "PUT"
  });
};
const apiService = {
  user: {
    wxlogin: (data) => post("/api/user/wx-login", data, null),
    login: (data) => post("/api/user/login", data, null),
    register: (data) => post("/api/user/register", data, null),
    adminRegister: (data) => post("/api/user/admin/register", data, null),
    sendVerifyCode: (phone) => get(`/api/user/verify-code?phone=${phone}`, null, null),
    getProfile: (userId) => get(`/api/user/${userId}`, null, null)
  },
  formula: {
    getSwatches: (data) => get("/api/formula/wx/list", data, null)
  },
  about: {
    getInfo: () => get("/api/about/info", null, null)
  },
  // 资料手册相关API
  manual: {
    list: () => get("/api/manual/list", null, null),
    detail: (id) => get(`/api/manual/${id}`, null, null)
  },
  // 新手引导相关API
  guide: {
    list: (guideType) => get("/api/guide/list", guideType ? {
      guideType
    } : null, null),
    detail: (id) => get(`/api/guide/${id}`, null, null)
  },
  // 合作加盟相关API
  partnership: {
    info: () => get("/api/partnership/info", null, null),
    detail: (id) => get(`/api/partnership/${id}`, null, null)
  },
  // 资料手册相关API
  manual: {
    list: () => get("/api/manual/list", null, null),
    detail: (id) => get(`/api/manual/${id}`, null, null)
  },
  // 新手引导相关API
  guide: {
    list: (guideType) => get("/api/guide/list", guideType ? {
      guideType
    } : null, null),
    detail: (id) => get(`/api/guide/${id}`, null, null)
  },
  // 合作加盟相关API
  partnership: {
    info: () => get("/api/partnership/info", null, null),
    detail: (id) => get(`/api/partnership/${id}`, null, null)
  },
  // 商城相关API - 根据后端实际控制器设计
  mall: {
    // 分类相关 - CategoryController
    categories: {
      list: (parentId = 0) => get("/api/category/list", {
        parentId
      }, null),
      tree: () => get("/api/category/tree", null, null),
      detail: (categoryId) => get(`/api/category/detail/${categoryId}`, null, null)
    },
    // 商品相关 - ProductController
    products: {
      list: (params) => get("/api/product/list", params, null),
      detail: (productId) => get(`/api/product/${productId}`, null, null),
      recommend: (limit = 6) => get("/api/product/recommend", {
        limit
      }, null),
      categories: () => get("/api/product/categories", null, null)
    },
    // 购物车相关 - CartController
    cart: {
      list: () => get("/api/cart/list", null, null),
      add: (data) => post("/api/cart/add", data, null),
      update: (data) => post("/api/cart/update", data, null),
      delete: (cartId) => post(`/api/cart/delete/${cartId}`, null, null),
      clear: () => post("/api/cart/clear", null, null),
      select: (data) => post("/api/cart/select", data, null),
      count: () => get("/api/cart/count", null, null)
    },
    // 订单相关 - OrderController
    orders: {
      create: (data) => post("/api/order/create", data, null),
      list: (params) => get("/api/order/list", params, null),
      detail: (orderNo) => get(`/api/order/${orderNo}`, null, null),
      pay: (orderNo, data) => post(`/api/order/pay/${orderNo}`, data, null),
      cancel: (orderNo) => post(`/api/order/cancel/${orderNo}`, null, null),
      confirm: (orderNo) => post(`/api/order/confirm/${orderNo}`, null, null),
      items: (orderId) => get(`/api/order/items/${orderId}`, null, null)
    },
    // 地址相关 - AddressController
    address: {
      list: () => get("/api/address/list", null, null),
      detail: (addressId) => get(`/api/address/detail/${addressId}`, null, null),
      add: (data) => post("/api/address/add", data, null),
      update: (data) => post("/api/address/update", data, null),
      delete: (addressId) => post(`/api/address/delete/${addressId}`, null, null),
      setDefault: (addressId) => post(`/api/address/set-default/${addressId}`, null, null)
    },
    // 店铺相关 - StoreController
    stores: {
      list: (params) => get("/api/stores", params, null),
      detail: (storeId) => get(`/api/stores/${storeId}`, null, null),
      userStores: (userId) => get(`/api/stores/user/${userId}`, null, null),
      create: (data, userId) => post(`/api/stores?userId=${userId}`, data, null),
      update: (storeId, data) => put(`/api/stores/${storeId}`, data, null),
      // 绑定码相关
      validateBindCode: (bindCode) => get(`/api/stores/validate-bind-code/${bindCode}`, null, null),
      bindStore: (bindCode, userId) => post("/api/stores/bind", {
        bindCode,
        userId
      }, null),
      // 设备与门店相关操作
      listStoreMember: () => get(`/api/stores/user-list`),
      addStoreMember: (data) => post(`/api/stores/add-user`, data),
      getStoreDeviceMapping: () => get(`/api/storeMappingDevice/list`),
      getStaffList: () => get(`/api/storeMappingDevice/staff/list`),
      assignPermission: (data) => post(`/api/storeMappingDevice/assignPermission`, data),
      storeUnbindDevice: (data) => post(`/api/storeMappingDevice/unbindDevice`, data),
      storeBindDevice: (data) => post(`/api/storeMappingDevice/bindDevice`, data)
    },
    // 店铺关注相关 - StoreFollowController
    storeFollow: {
      follow: (storeId) => post("/api/store-follow/follow", {
        storeId
      }, null),
      unfollow: (storeId) => post("/api/store-follow/unfollow", {
        storeId
      }, null),
      check: (storeId) => get(`/api/store-follow/check/${storeId}`, null, null),
      list: (params) => get("/api/store-follow/list", params, null),
      count: (storeId) => get(`/api/store-follow/count/${storeId}`, null, null)
    },
    // 收藏相关 - FavoriteController
    favorites: {
      list: () => get("/api/favorite/list", null, null),
      add: (productId) => post("/api/favorite/add", {
        productId
      }, null),
      remove: (productId) => post("/api/favorite/remove", {
        productId
      }, null),
      check: (productId) => get(`/api/favorite/check/${productId}`, null, null)
    },
    // 运费相关 - FreightController
    freight: {
      calculate: (data) => post("/api/freight/calculate", data, null)
    },
    // 搜索相关 - SearchController
    search: {
      // 搜索商品
      products: (params) => get("/api/search/products", params, null),
      // 获取搜索推荐词
      recommendations: () => get("/api/search/recommendations", null, null),
      // 获取用户搜索历史
      history: () => get("/api/search/history", null, null),
      // 保存搜索记录
      saveHistory: (keyword) => post("/api/search/history", {
        keyword
      }, null),
      // 清除搜索历史
      clearHistory: () => post("/api/search/history/clear", null, null),
      // 获取热门搜索词
      hotKeywords: (limit = 10) => get("/api/search/hot", {
        limit
      }, null),
      // 刷新推荐词
      refreshRecommendations: (limit = 10) => get("/api/search/recommendations/refresh", {
        limit
      }, null)
    },
    // 设备使用统计相关API
    deviceUsage: {
      // 获取用户设备使用统计
      getStats: () => get("/api/device-usage/stats", null, null),
      // 获取指定时间范围的使用统计
      getStatsByTimeRange: (timeRange) => get(`/api/device-usage/stats/${timeRange}`, null, null),
      // 获取使用图表数据
      getChartData: (timeRange) => get(`/api/device-usage/chart/${timeRange}`, null, null),
      // 开始使用设备
      startUsage: (deviceId) => post(`/api/device-usage/start/${deviceId}`, null, null),
      // 结束使用设备
      endUsage: (deviceId) => post(`/api/device-usage/end/${deviceId}`, null, null)
    },
    deviceOperate: {
      connectDevice: (deviceCode) => post(`/api/device/connectSee/${deviceCode}`)
    },
    command: {
      sendCommand: (deviceCode, commands) => post(`/api/command/${deviceCode}`, {
        commands
      }),
      sendCommandin: (deviceCode, command) => post(`/api/command/${deviceCode}`, command),
      createCommandRecords: (deviceCode, commands) => post(`/api/command/create/${deviceCode}`, {
        commands
      }),
      sendStop: (deviceCode, model) => post(`/api/command/stop/${deviceCode}`, model),
      sendPause: (deviceCode) => post(`/api/command/pause/${deviceCode}`),
      sendContinue: (deviceCode) => post(`/api/command/continue/${deviceCode}`),
      sendCancel: (deviceCode) => post(`/api/command/cancel/${deviceCode}`),
      allEnd: (deviceCode) => post(`/api/command/allCommandEnd/${deviceCode}`)
    },
    // 染膏消耗相关API
    dyeConsumption: {
      // 获取设备染膏使用记录
      getUsageRecords: (deviceCode, params) => get(`/api/dye-consumption/usage/${deviceCode}`, params, null),
      // 获取染膏使用统计
      getUsageStatistics: (deviceCode, params) => get(
        `/api/dye-consumption/statistics/${deviceCode}`,
        params,
        null
      ),
      // 获取染膏库存状态
      getInventoryStatus: (deviceCode) => get(`/api/dye-consumption/inventory/${deviceCode}`, null, null),
      // 手动记录染膏使用量
      recordUsage: (data) => post("/api/dye-consumption/usage/record", data, null),
      // 获取指令的使用记录
      getUsageByCommand: (commandId) => get(`/api/dye-consumption/usage/command/${commandId}`, null, null),
      // 获取颜色总使用量
      getTotalUsage: (deviceCode, colorCode) => get(
        `/api/dye-consumption/usage/total/${deviceCode}/${colorCode}`,
        null,
        null
      ),
      // 获取染膏余量预警
      getLowStockAlerts: (deviceCode) => get(`/api/dye-consumption/alerts/${deviceCode}`, null, null),
      // 扫码绑定染膏和设备api
      scanCodeBinding: (deviceCode, data) => post(
        `/api/dye-consumption/bind-color-paste/${deviceCode}`,
        data
      ),
      // 获取单个染膏库存的详细信息
      getInventoryColor: (data) => post(`/api/inventory/color/single/inventory/color/single/`, data),
      checkRemaining: (data) => get(`/api/dye-consumption/inventory/color/warning`, data),
      // 更新调色记录的照片
      updateRecordPhotos: (recordId, beforeImageUrl, afterImageUrl) => {
        const params = [];
        if (beforeImageUrl)
          params.push(`beforeImageUrl=${encodeURIComponent(beforeImageUrl)}`);
        if (afterImageUrl)
          params.push(`afterImageUrl=${encodeURIComponent(afterImageUrl)}`);
        const queryString = params.length > 0 ? `?${params.join("&")}` : "";
        return post(`/api/dye-consumption/usage/update-photos/${recordId}${queryString}`, null, null);
      }
    },
    // 设备状态监控API
    deviceStatus: {
      // 获取设备实时状态
      getRealTimeStatus: (deviceCode) => get(`/api/device/status/${deviceCode}`, null, null),
      // 获取设备连接状态
      getConnectionStatus: (deviceCode) => get(`/api/device/connection/${deviceCode}`, null, null),
      // 获取指令执行状态
      getCommandExecutionStatus: (deviceCode) => get(`/api/device/command-status/${deviceCode}`, null, null),
      // 设置设备状态
      setDeviceStatus: (deviceCode, status) => post(`/api/device/status/${deviceCode}`, {
        status
      }, null)
    }
  },
  // 设备操作等api
  device: {
    bindDevice: (deviceCode) => post(`/api/device/bind/${deviceCode}`),
    // unbindDevice: (deviceId) => post(`/api/device/unbind/${deviceId}`),
    unbindDevice: (deviceCode) => post(`/api/device/unbind/${deviceCode}`),
    getDeviceList: () => get(`/api/device/list`),
    getDeviceCodeList: () => get(`/api/device/deviceCode/list`),
    getDevicePermissionlist: () => get(`/api/device/permission/list`),
    // 权限管理相关API
    assignPermission: (data) => post("/api/device/assign-permission", data),
    getAssignedStaff: (deviceId) => get(`/api/device/${deviceId}/assigned-staff`),
    revokePermission: (data) => post("/api/device/revoke-permission", data),
    getAvailableStaff: (deviceId) => get(`/api/device/${deviceId}/available-staff`)
    // 验证设备的api
  },
  record: {
    getRecords: () => get(`/api/record/list`),
    createReCode: (data) => post(`/api/record/create`, data),
    updateRecordPhotos: (recordId, beforeImageUrl, afterImageUrl) => {
      const params = [];
      if (beforeImageUrl)
        params.push(`beforeImageUrl=${encodeURIComponent(beforeImageUrl)}`);
      if (afterImageUrl)
        params.push(`afterImageUrl=${encodeURIComponent(afterImageUrl)}`);
      const queryString = params.length > 0 ? `?${params.join("&")}` : "";
      return post(`/api/record/update-photos/${recordId}${queryString}`, null, null);
    }
  },
  content: {
    createContent: (postData) => post(`/api/content/create`, postData),
    listContent: () => get(`/api/content/list`),
    getDetail: (id) => get(`/api/content/detail/${id}`)
  }
};
const api = apiService;
exports.api = api;
exports.apiService = apiService;
exports.clearUserStatus = clearUserStatus;
exports.get = get;
exports.post = post;
exports.request = request;
//# sourceMappingURL=../../.sourcemap/mp-weixin/utils/request.js.map
