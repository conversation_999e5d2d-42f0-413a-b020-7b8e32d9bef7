"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_request = require("../../utils/request.js");
const _sfc_main = {
  data() {
    return {
      storeInfo: {
        id: null,
        name: "",
        phone: "",
        bindCode: "",
        storeType: 1,
        // 1-门店，2-供应商
        address: "",
        detailAddress: "",
        province: "",
        city: "",
        district: "",
        latitude: null,
        longitude: null,
        description: ""
      },
      storeTypeOptions: [
        { value: 1, label: "门店/Store" },
        { value: 2, label: "工作室/Studio" },
        { value: 3, label: "美发沙龙/Hair Salon" }
      ],
      saving: false,
      loading: false,
      isBindCodeValid: false,
      bindCodeValidating: false,
      isStoreAlreadyBound: false
    };
  },
  computed: {
    isBindCodeDisabled() {
      return this.isStoreAlreadyBound || this.storeInfo.id && this.storeInfo.id !== null;
    }
  },
  onLoad() {
    this.loadStoreInfo();
    this.setupLocationListener();
  },
  onUnload() {
    common_vendor.index.$off("locationSelected");
  },
  methods: {
    async loadStoreInfo() {
      this.loading = true;
      try {
        const userInfo = common_vendor.index.getStorageSync("userInfo");
        if (userInfo && userInfo.id) {
          const response = await utils_request.apiService.mall.stores.userStores(userInfo.id);
          if (response.code === 200 && response.data && response.data.length > 0) {
            const store = response.data[0];
            this.storeInfo = {
              id: store.id,
              name: store.name || "",
              phone: store.phone || "",
              bindCode: store.bindCode || store.bind_code || "",
              storeType: store.storeType || store.store_type || 1,
              address: store.address || "",
              detailAddress: "",
              // 可以从address中解析
              province: store.province || "",
              city: store.city || "",
              district: store.district || "",
              latitude: store.latitude || null,
              longitude: store.longitude || null,
              description: store.description || ""
            };
            if (store.id) {
              this.isStoreAlreadyBound = true;
              this.isBindCodeValid = true;
            }
          }
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/my/store-info.vue:151", "加载门店信息失败:", error);
      } finally {
        this.loading = false;
      }
    },
    showTypeSelector() {
      const itemList = this.storeTypeOptions.map((option) => option.label);
      common_vendor.index.showActionSheet({
        itemList,
        success: (res) => {
          this.storeInfo.storeType = this.storeTypeOptions[res.tapIndex].value;
        }
      });
    },
    getStoreTypeLabel() {
      const option = this.storeTypeOptions.find((opt) => opt.value === this.storeInfo.storeType);
      return option ? option.label : "门店/Store";
    },
    // 验证绑定码
    async validateBindCode() {
      const bindCode = this.storeInfo.bindCode.trim();
      if (!bindCode || this.isStoreAlreadyBound) {
        return;
      }
      if (bindCode.length < 6) {
        this.isBindCodeValid = false;
        return;
      }
      this.bindCodeValidating = true;
      try {
        const response = await utils_request.apiService.mall.stores.validateBindCode(bindCode);
        if (response.code === 200 && response.data) {
          this.isBindCodeValid = true;
          common_vendor.index.showToast({
            title: "绑定码有效",
            icon: "success"
          });
        } else {
          this.isBindCodeValid = false;
          common_vendor.index.showToast({
            title: "绑定码无效或已被使用",
            icon: "none"
          });
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/my/store-info.vue:206", "验证绑定码失败:", error);
        this.isBindCodeValid = false;
        common_vendor.index.showToast({
          title: "验证绑定码失败",
          icon: "none"
        });
      } finally {
        this.bindCodeValidating = false;
      }
    },
    selectAddress() {
    },
    // 设置位置选择监听器
    setupLocationListener() {
      common_vendor.index.$on("locationSelected", (location) => {
        this.updateStoreLocation(location);
      });
    },
    // 更新店铺位置信息
    updateStoreLocation(location) {
      this.storeInfo.address = location.address || location.name;
      this.storeInfo.province = this.extractProvince(location.address);
      this.storeInfo.city = this.extractCity(location.address);
      this.storeInfo.district = this.extractDistrict(location.address);
      if (location.latitude && location.longitude) {
        this.storeInfo.latitude = location.latitude;
        this.storeInfo.longitude = location.longitude;
      }
      common_vendor.index.showToast({
        title: "地址已更新",
        icon: "success"
      });
    },
    // 从地址中提取省份
    extractProvince(address) {
      if (!address)
        return "";
      const match = address.match(/^([^省]+省|[^自治区]+自治区|[^市]+市)/);
      return match ? match[1] : "";
    },
    // 从地址中提取城市
    extractCity(address) {
      if (!address)
        return "";
      const match = address.match(/省([^市]+市)|自治区([^市]+市)|^([^市]+市)/);
      return match ? match[1] || match[2] || match[3] : "";
    },
    // 从地址中提取区县
    extractDistrict(address) {
      if (!address)
        return "";
      const match = address.match(/市([^区县]+[区县])/);
      return match ? match[1] : "";
    },
    async saveStoreInfo() {
      if (this.saving) {
        return;
      }
      if (!this.storeInfo.name.trim()) {
        common_vendor.index.showToast({
          title: "请输入店铺名称",
          icon: "none"
        });
        return;
      }
      if (!this.storeInfo.phone.trim()) {
        common_vendor.index.showToast({
          title: "请输入联系手机",
          icon: "none"
        });
        return;
      }
      if (!this.isStoreAlreadyBound) {
        if (!this.storeInfo.bindCode.trim()) {
          common_vendor.index.showToast({
            title: "请输入绑定码",
            icon: "none"
          });
          return;
        }
        if (!this.isBindCodeValid) {
          common_vendor.index.showToast({
            title: "请输入有效的绑定码",
            icon: "none"
          });
          return;
        }
      }
      const phoneRegex = /^1[3-9]\d{9}$/;
      if (!phoneRegex.test(this.storeInfo.phone)) {
        common_vendor.index.showToast({
          title: "请输入正确的手机号",
          icon: "none"
        });
        return;
      }
      this.saving = true;
      try {
        const userInfo = common_vendor.index.getStorageSync("userInfo");
        common_vendor.index.__f__("log", "at pages/my/store-info.vue:326", "用户信息", userInfo);
        if (!userInfo || !userInfo.id) {
          throw new Error("用户信息不存在，请重新登录");
        }
        const baseAddress = [
          this.storeInfo.province || "",
          this.storeInfo.city || "",
          this.storeInfo.district || "",
          this.storeInfo.address || ""
        ].filter((item) => item.trim()).join("");
        const fullAddress = baseAddress + (this.storeInfo.detailAddress ? this.storeInfo.detailAddress.trim() : "");
        const storeData = {
          name: this.storeInfo.name.trim(),
          phone: this.storeInfo.phone.trim(),
          storeType: this.storeInfo.storeType,
          address: fullAddress,
          province: this.storeInfo.province || "",
          city: this.storeInfo.city || "",
          district: this.storeInfo.district || "",
          latitude: this.storeInfo.latitude,
          longitude: this.storeInfo.longitude,
          description: this.storeInfo.description || ""
        };
        let response;
        if (this.storeInfo.id) {
          response = await utils_request.apiService.mall.stores.update(this.storeInfo.id, storeData);
        } else if (this.storeInfo.bindCode.trim()) {
          response = await utils_request.apiService.mall.stores.bindStore(this.storeInfo.bindCode.trim(), userInfo.id);
          if (response.code === 200) {
            this.storeInfo.id = response.data.id;
            this.isStoreAlreadyBound = true;
            const updateResponse = await utils_request.apiService.mall.stores.update(this.storeInfo.id, storeData);
            if (updateResponse.code !== 200) {
              throw new Error(updateResponse.message || "更新店铺信息失败");
            }
            response = updateResponse;
          }
        } else {
          response = await utils_request.apiService.mall.stores.create(storeData, userInfo.id);
        }
        if (response.code === 200) {
          common_vendor.index.setStorageSync("storeInfo", JSON.stringify(this.storeInfo));
          common_vendor.index.showToast({
            title: "保存成功",
            icon: "success",
            duration: 2e3
          });
          setTimeout(() => {
            common_vendor.index.navigateBack();
          }, 2e3);
        } else {
          throw new Error(response.message || "保存失败");
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/my/store-info.vue:398", "保存门店信息失败:", error);
        common_vendor.index.showToast({
          title: error.message || "保存失败，请重试",
          icon: "none",
          duration: 3e3
        });
      } finally {
        this.saving = false;
      }
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.loading
  }, $data.loading ? {} : common_vendor.e({
    b: $data.storeInfo.name,
    c: common_vendor.o(($event) => $data.storeInfo.name = $event.detail.value),
    d: $data.storeInfo.phone,
    e: common_vendor.o(($event) => $data.storeInfo.phone = $event.detail.value),
    f: $options.isBindCodeDisabled,
    g: common_vendor.o((...args) => $options.validateBindCode && $options.validateBindCode(...args)),
    h: $data.storeInfo.bindCode,
    i: common_vendor.o(($event) => $data.storeInfo.bindCode = $event.detail.value),
    j: $options.isBindCodeDisabled
  }, $options.isBindCodeDisabled ? {} : {}, {
    k: common_vendor.t($options.getStoreTypeLabel()),
    l: common_vendor.o((...args) => $options.showTypeSelector && $options.showTypeSelector(...args)),
    m: $data.storeInfo.address,
    n: common_vendor.o(($event) => $data.storeInfo.address = $event.detail.value),
    o: $data.storeInfo.detailAddress,
    p: common_vendor.o(($event) => $data.storeInfo.detailAddress = $event.detail.value)
  }), {
    q: common_vendor.t($data.saving ? "保存中..." : "保存"),
    r: $data.saving ? 1 : "",
    s: common_vendor.o((...args) => $options.saveStoreInfo && $options.saveStoreInfo(...args))
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/my/store-info.js.map
