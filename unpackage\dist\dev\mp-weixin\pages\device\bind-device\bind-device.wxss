/* 水平间距 */
/* 水平间距 */
/* 全局样式修复 - 确保页面占满屏幕宽度 */
page.data-v-d8916166 {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
}

/* 通用容器样式 */
.container.data-v-d8916166, .page-container.data-v-d8916166 {
  width: 100%;
  min-height: 100vh;
  box-sizing: border-box;
}

/* 修复小程序页面宽度问题 */
uni-page-body.data-v-d8916166 {
  width: 100% !important;
}

/* 确保所有view元素正确继承宽度 */
view.data-v-d8916166 {
  box-sizing: border-box;
}
.bind-device-page.data-v-d8916166 {
  padding: 20rpx 30rpx;
  background-color: #fff;
  min-height: 100vh;
}
.page-title.data-v-d8916166 {
  font-size: 36rpx;
  font-weight: bold;
  color: #1a73e8;
  margin-bottom: 40rpx;
}
.section-title.data-v-d8916166 {
  font-size: 32rpx;
  font-weight: bold;
  color: #1a73e8;
  margin-bottom: 20rpx;
}
.title-hearder.data-v-d8916166 {
  font-size: 25rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
}
.hint-text.data-v-d8916166 {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 10rpx;
}
.hint-text.warning.data-v-d8916166 {
  color: #a52a2a;
}
.device-name-input.data-v-d8916166 {
  height: 80rpx;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  padding: 0 20rpx;
  margin-bottom: 20rpx;
  font-size: 28rpx;
}
.scan-container.data-v-d8916166 {
  display: flex;
  justify-content: center;
  margin-top: 30rpx;
}
.scan-box.data-v-d8916166 {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 400rpx;
  height: 220rpx;
  margin-right: auto;
  /* 关键代码：自动右外边距会将其推到左侧 */
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  padding: 20rpx;
}
.scan-icon.data-v-d8916166 {
  width: 80rpx;
  height: 80rpx;
  margin-bottom: 15rpx;
}
.scan-text.data-v-d8916166 {
  font-size: 28rpx;
}
.divider.data-v-d8916166 {
  height: 20rpx;
  background-color: #f5f5f5;
  margin: 30rpx -30rpx;
}
.year-options.data-v-d8916166 {
  display: flex;
  justify-content: space-between;
  margin: 30rpx 0;
}
.year-option.data-v-d8916166 {
  width: 30%;
  padding: 20rpx;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  text-align: center;
}
.year-option.active.data-v-d8916166 {
  border-color: #1a73e8;
  background-color: #e8f0fe;
}
.year.data-v-d8916166 {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
}
.price.data-v-d8916166 {
  font-size: 32rpx;
  color: #1a73e8;
  font-weight: bold;
}
.original-price.data-v-d8916166 {
  font-size: 24rpx;
  color: #999;
  text-decoration: line-through;
}
.price-detail.data-v-d8916166 {
  background-color: #f9f9f9;
  padding: 20rpx;
  border-radius: 8rpx;
  margin-top: 20rpx;
}
.detail-item.data-v-d8916166 {
  display: flex;
  justify-content: space-between;
  font-size: 28rpx;
  color: #666;
  margin-bottom: 15rpx;
}
.detail-item.total.data-v-d8916166 {
  font-weight: bold;
  color: #1a73e8;
  margin-top: 20rpx;
  margin-bottom: 0;
}
.bind-btn.data-v-d8916166 {
  margin: 40rpx auto;
  display: block;
  background-color: #1a73e8;
  color: white;
  width: 75%;
  font-size: 32rpx;
  height: 90rpx;
  line-height: 90rpx;
  border-radius: 45rpx;
  /* 高度的一半，确保完全圆角 */
}
.agreement-hint.data-v-d8916166 {
  text-align: center;
  font-size: 24rpx;
  color: #999;
  margin-bottom: 30rpx;
}