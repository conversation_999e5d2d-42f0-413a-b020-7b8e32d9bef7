"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_request = require("../../utils/request.js");
const _sfc_main = {
  data() {
    return {
      guideId: "",
      pageTitle: "引导详情",
      guideDetail: null,
      loading: false,
      error: ""
    };
  },
  onLoad(options) {
    if (options.id) {
      this.guideId = options.id;
    }
    if (options.title) {
      this.pageTitle = decodeURIComponent(options.title);
    }
    if (this.guideId) {
      this.loadGuideDetail();
    } else {
      this.error = "缺少引导ID参数";
    }
  },
  methods: {
    goBack() {
      common_vendor.index.navigateBack({
        delta: 1
      });
    },
    async loadGuideDetail() {
      try {
        this.loading = true;
        this.error = "";
        const response = await utils_request.get(`/api/guide/${this.guideId}`);
        if (response && response.code === 200) {
          this.guideDetail = response.data;
        } else {
          this.error = (response == null ? void 0 : response.message) || "获取引导详情失败";
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/my/guide-detail.vue:105", "获取引导详情失败:", error);
        this.error = "网络错误，请稍后重试";
      } finally {
        this.loading = false;
      }
    },
    previewImage() {
      if (this.guideDetail.imageUrl) {
        common_vendor.index.previewImage({
          urls: [this.guideDetail.imageUrl],
          current: this.guideDetail.imageUrl
        });
      }
    },
    formatContent(content) {
      if (!content)
        return "";
      return content.replace(/\n/g, "<br/>");
    },
    formatTime(time) {
      if (!time)
        return "";
      const date = new Date(time);
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, "0")}-${String(date.getDate()).padStart(2, "0")}`;
    },
    getGuideTypeText(type) {
      const typeMap = {
        "general": "通用",
        "device": "设备",
        "operation": "操作",
        "maintenance": "维护"
      };
      return typeMap[type] || "通用";
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.o((...args) => $options.goBack && $options.goBack(...args)),
    b: common_vendor.t($data.pageTitle),
    c: $data.loading
  }, $data.loading ? {} : $data.error ? {
    e: common_vendor.t($data.error),
    f: common_vendor.o((...args) => $options.loadGuideDetail && $options.loadGuideDetail(...args))
  } : $data.guideDetail ? common_vendor.e({
    h: common_vendor.t($data.guideDetail.title),
    i: common_vendor.t($options.getGuideTypeText($data.guideDetail.guideType)),
    j: common_vendor.t($data.guideDetail.viewCount || 0),
    k: $data.guideDetail.description
  }, $data.guideDetail.description ? {
    l: common_vendor.t($data.guideDetail.description)
  } : {}, {
    m: $data.guideDetail.imageUrl
  }, $data.guideDetail.imageUrl ? {
    n: $data.guideDetail.imageUrl,
    o: common_vendor.o((...args) => $options.previewImage && $options.previewImage(...args))
  } : {}, {
    p: $data.guideDetail.content
  }, $data.guideDetail.content ? {
    q: $options.formatContent($data.guideDetail.content)
  } : {}, {
    r: common_vendor.t($data.guideDetail.stepOrder || 1),
    s: common_vendor.t($options.formatTime($data.guideDetail.updateTime))
  }) : {}, {
    d: $data.error,
    g: $data.guideDetail
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/my/guide-detail.js.map
