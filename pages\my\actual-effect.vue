<template>
	<view class="container">
		<!-- 设备信息 -->
		<view class="device-info">
			<text class="device-name">芭佰邑-{{colorType}}</text>
			<text class="device-status" :class="deviceStatus  ? 'status-online' : 'status-offline'">{{deviceStatus ? '设备在线':'设备离线'}}</text>
		</view>

		<!-- 染发前后对比 -->
		<view class="comparison-section">
			<view class="image-container">
				<!-- 染发前图片 -->
				<view class="before-image">
					<image v-if="beforeImage" :src="beforeImage" mode="aspectFill" class="hair-image"
						@click="previewImage(beforeImage)"></image>
					<view v-else class="placeholder-image" @click="uploadBeforeImage">
						<view class="add-icon">+</view>
						<text class="placeholder-text">上传染发前照片</text>
					</view>
					<text class="image-label">染发前</text>
				</view>

				<!-- 染发后图片 -->
				<view class="after-image">
					<image v-if="afterImage" :src="afterImage" mode="aspectFill" class="hair-image"
						@click="previewImage(afterImage)"></image>
					<view v-else class="placeholder-image" @click="uploadAfterImage">
						<view class="add-icon">+</view>
						<text class="placeholder-text">上传染发后照片</text>
						<text class="placeholder-subtext">点击上传</text>
					</view>
					<text class="image-label">染发后</text>
				</view>
			</view>

			<!-- 颜色名称 -->
			<view class="color-title">{{colorDisplayName}}</view>
		</view>

		<!-- 颜色构造 -->
		<view class="color-structure">
			<view class="structure-title">颜色构造</view>
			<view class="color-composition">
				<view class="color-item" v-for="(item, index) in colorComponents" :key="index">
					<view class="color-block" :style="{ backgroundColor: item.color }"></view>
					<view class="color-info">
						<text class="color-name">{{item.name}}</text>
						<text class="color-amount">{{item.amount}}</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 底部操作栏 -->
		<view class="bottom-actions">
			<view class="action-icon like" @click="toggleLike">
				<text class="icon">{{isLiked ? '♥' : '♡'}}</text>
				<text class="count">{{likeCount}}</text>
			</view>
			<view class="action-icon star" @click="toggleStar">
				<text class="icon">{{isStarred ? '★' : '☆'}}</text>
				<text class="count">{{starCount}}</text>
			</view>
			<view class="action-icon share" @click="shareEffect">
				<text class="icon">↗</text>
				<text class="count">{{shareCount}}</text>
			</view>

			<view class="generate-btn" @click="generateNewFormula">
				生成新配方
			</view>
		</view>
	</view>
</template>

<script>
	import {
		apiService
	} from '@/utils/request.js';

	export default {
		data() {
			return {
				// 基本信息
				colorType: '',
				deviceStatus: false,

				// 颜色信息
				colorDisplayName: '',
				colorHex: '',
				colorCode: '',

				// 图片
				beforeImage: '',
				afterImage: '',

				// 颜色构造
				colorComponents: [],

				// 交互数据
				isLiked: false,
				likeCount: 0,
				isStarred: false,
				starCount: 0,
				shareCount: 1,

				// 原始记录数据
				recordData: null
			};
		},

		onLoad(options) {
			console.log('实际效果页面加载，参数:', options);

			// 从URL参数获取记录ID
			if (options.recordId) {
				this.loadRecordData(options.recordId);
			} else {
				// 从缓存中读取传递的数据
				try {
					const cachedData = uni.getStorageSync('shareRecordData');
					if (cachedData) {
						console.log("当前调色记录",cachedData)
						this.initializeData(cachedData);
						console.log('从缓存读取分享数据成功');
					} else {
						console.error('无法获取记录数据');
						uni.showToast({
							title: '数据加载失败',
							icon: 'none'
						});
					}
				} catch (e) {
					console.error('读取缓存失败:', e);
				}
			}
			this.loadDeviceStatus();
		},

		methods: {
			loadDeviceStatus() {
				// 从全局app中获取设备在线状态
				const app = getApp();
				this.deviceStatus = app.globalData.deviceStatus
				// this.deviceName = app.globalData.deviceName
			},
			// 初始化数据
			initializeData(record) {
				this.recordData = record;
				this.colorDisplayName = record.colorName || '未知颜色';
				this.colorHex = record.colorHex || '#000000';
				this.colorCode = record.colorCode || '';
				this.colorType = record.colorType || '未知分类'

				// 设置染发前照片（如果有的话）
				if (record.beforeImageUrl) {
					this.beforeImage = record.beforeImageUrl;
				} else if (record.beforeImage) {
					this.beforeImage = record.beforeImage;
				} else if (record.photoUrl) {
					// 兼容不同的照片字段名
					this.beforeImage = record.photoUrl;
				}

				// 设置染发后照片（如果有的话）
				if (record.afterImageUrl) {
					this.afterImage = record.afterImageUrl;
				} else if (record.afterImage) {
					this.afterImage = record.afterImage;
				}

				// 解析颜色构造（如果有详细数据）
				this.parseColorComponents(record);

				// 设置设备信息
				if (record.deviceCode) {
					this.deviceName = `芭佰邑-${record.deviceCode}`;
					this.deviceStatus = true;
				}
			},

			// 解析颜色构造
			parseColorComponents(record) {
				try {
					// 定义颜色字段的显示顺序
					const colorFieldOrder = [
						'gray', 'green', 'yellow', 'orange', 'red',
						'purple', 'brown', 'blue', 'black', 'faded_bleached',
						'bleached', 'silver', 'gold', 'linen',
						'custom1', 'custom2', 'custom3', 'custom4'
					];

					// 颜色映射表（使用您修改后的颜色值）
					const colorMap = {
						red: {
							name: '红色',
							color: '#E74C3C'
						},
						blue: {
							name: '蓝色',
							color: '#3498DB'
						},
						gold: {
							name: '浅棕色',
							color: '#F1C40F'
						},
						gray: {
							name: '灰色',
							color: '#95A5A6'
						},
						black: {
							name: '黑色',
							color: '#2C3E50'
						},
						brown: {
							name: '棕色',
							color: '#A04000'
						},
						green: {
							name: '绿色',
							color: '#2ECC71'
						},
						linen: {
							name: '深灰亚麻色',
							color: '#D7DBDD'
						},
						orange: {
							name: '橙色',
							color: '#E67E22'
						},
						purple: {
							name: '紫色',
							color: '#9B59B6'
						},
						silver: {
							name: '梦幻紫',
							color: '#AEB6BF'
						},
						yellow: {
							name: '黄色',
							color: '#F4D03F'
						},
						custom1: {
							name: '自定义1',
							color: '#EC7063'
						},
						custom2: {
							name: '自定义2',
							color: '#5DADE2'
						},
						custom3: {
							name: '自定义3',
							color: '#58D68D'
						},
						custom4: {
							name: '自定义4',
							color: '#F7DC6F'
						},
						bleached: {
							name: '极浅灰金色',
							color: '#FDFEFE'
						},
						faded_bleached: {
							name: '褪色剂',
							color: '#EAEDED'
						}
					};

					// 双氧水映射表
					const developerMap = {
						hydrogenPeroxide3Percent: {
							name: '3%双氧水',
							color: '#EAEDED'
						},
						hydrogenPeroxide6Percent: {
							name: '6%双氧水',
							color: '#EAEDED'
						},
						hydrogenPeroxide9Percent: {
							name: '9%双氧水',
							color: '#EAEDED'
						},
						hydrogenPeroxide12Percent: {
							name: '12%双氧水',
							color: '#EAEDED'
						}
					};

					const components = [];

					// 解析colorComponents JSON字符串
					const colorData = record.colorComponents ? JSON.parse(record.colorComponents) : {};

					// 按照指定顺序添加颜色成分
					colorFieldOrder.forEach(field => {
						if (colorData[field] && colorData[field] > 0 && colorMap[field]) {
							components.push({
								name: colorMap[field].name,
								amount: `${colorData[field]}g`,
								color: colorMap[field].color,
								isDeveloper: false
							});
						}
					});

					// console.log("颜色构造",)

					// 添加双氧水成分（放在最后）
					if (record.developerComponents) {
						const developerData = JSON.parse(record.developerComponents);
						for (const [key, value] of Object.entries(developerData)) {
							if (value && value > 0 && developerMap[key]) {
								components.push({
									name: developerMap[key].name,
									amount: `${value}ml`,
									color: developerMap[key].color,
									isDeveloper: true // 标记为双氧水成分
								});
							}
						}
					}

					// 按是否为双氧水分组（颜色在前，双氧水在后）
					const sortedComponents = [
						...components.filter(item => !item.isDeveloper),
						...components.filter(item => item.isDeveloper)
					];

					this.colorComponents = sortedComponents;

					console.log("颜色构造", sortedComponents)

				} catch (error) {
					console.error('解析颜色构造失败:', error);
					// 如果解析失败，使用默认构造
					this.colorComponents = this.generateColorComponents(this.colorCode, this.getColorInfo(this.colorCode));
				}
			},

			// 获取颜色信息
			getColorInfo(colorCode) {
				const colorMap = {
					'0x30': {
						name: '灰色',
						hex: '#808080'
					},
					'0x31': {
						name: '绿色',
						hex: '#00FF00'
					},
					'0x32': {
						name: '黄色',
						hex: '#FFFF00'
					},
					'0x33': {
						name: '橙色',
						hex: '#FFA500'
					},
					'0x34': {
						name: '红色',
						hex: '#FF0000'
					},
					'0x35': {
						name: '紫色',
						hex: '#800080'
					},
					'0x36': {
						name: '棕色',
						hex: '#A52A2A'
					},
					'0x37': {
						name: '蓝色',
						hex: '#0000FF'
					},
					'0x38': {
						name: '黑色',
						hex: '#000000'
					},
					'0x39': {
						name: '漂白剂',
						hex: '#F5F5DC'
					},
					'0x40': {
						name: '3%双氧水',
						hex: '#E0E0E0'
					},
					'0x41': {
						name: '12%双氧水',
						hex: '#D0D0D0'
					}
				};
				return colorMap[colorCode] || {
					name: '未知颜色',
					hex: '#CCCCCC'
				};
			},

			// 生成颜色构造
			generateColorComponents(colorCode, baseColorInfo) {
				// 根据不同颜色代码生成不同的配方构造
				const componentMap = {
					'0x31': [ // 绿色
						{
							name: '绿色',
							amount: '20',
							color: '#00FF00'
						},
						{
							name: '红色',
							amount: '5',
							color: '#FF0000'
						},
						{
							name: '紫色',
							amount: '1',
							color: '#800080'
						}
					],
					'0x33': [ // 橙色
						{
							name: '橙色',
							amount: '16',
							color: '#FFA500'
						},
						{
							name: '黄色',
							amount: '8',
							color: '#FFFF00'
						},
						{
							name: '红色',
							amount: '2',
							color: '#FF0000'
						}
					],
					'0x35': [ // 紫色
						{
							name: '紫色',
							amount: '15',
							color: '#800080'
						},
						{
							name: '蓝色',
							amount: '10',
							color: '#0000FF'
						},
						{
							name: '红色',
							amount: '3',
							color: '#FF0000'
						}
					]
				};

				// 如果有特定配方则使用，否则使用基础颜色
				return componentMap[colorCode] || [{
					name: baseColorInfo.name,
					amount: '20',
					color: baseColorInfo.hex
				}];
			},

			// 加载记录数据
			async loadRecordData(recordId) {
				try {
					// 调用API获取详细的记录数据
					const response = await apiService.dyeConsumption.getUsageByCommand(recordId);

					if (response.code === 200) {
						this.initializeData(response.data);
					} else {
						console.error('获取记录详情失败:', response.message);
						uni.showToast({
							title: '数据加载失败',
							icon: 'none'
						});
					}
				} catch (error) {
					console.error('加载记录数据失败:', error);
					uni.showToast({
						title: '网络错误',
						icon: 'none'
					});
				}
			},

			// 上传染发前照片
			uploadBeforeImage() {
				uni.chooseImage({
					count: 1,
					sizeType: ['compressed'],
					sourceType: ['camera', 'album'],
					success: (res) => {
						this.beforeImage = res.tempFilePaths[0];
						this.saveImageToRecord('before', res.tempFilePaths[0]);
					},
					fail: (err) => {
						console.error('选择照片失败:', err);
					}
				});
			},

			// 上传染发后照片
			uploadAfterImage() {
				uni.chooseImage({
					count: 1,
					sizeType: ['compressed'],
					sourceType: ['camera', 'album'],
					success: (res) => {
						this.afterImage = res.tempFilePaths[0];
						this.saveImageToRecord('after', res.tempFilePaths[0]);
					},
					fail: (err) => {
						console.error('选择照片失败:', err);
					}
				});
			},

			// 预览图片
			previewImage(imageUrl) {
				uni.previewImage({
					urls: [imageUrl],
					current: imageUrl
				});
			},

			// 保存照片到记录
			async saveImageToRecord(type, imagePath) {
				try {
					if (!this.recordData || !this.recordData.id) {
						uni.showToast({
							title: '记录数据不完整',
							icon: 'none'
						});
						return;
					}

					// 构建更新参数
					const beforeImageUrl = type === 'before' ? imagePath : undefined;
					const afterImageUrl = type === 'after' ? imagePath : undefined;

					// 调用API更新记录
					const response = await apiService.mall.dyeConsumption.updateRecordPhotos(
						this.recordData.id,
						beforeImageUrl,
						afterImageUrl
					);

					if (response.code === 200) {
						// 更新本地数据
						if (type === 'before') {
							this.recordData.beforeImageUrl = imagePath;
						} else {
							this.recordData.afterImageUrl = imagePath;
						}

						uni.showToast({
							title: '照片已保存',
							icon: 'success'
						});
					} else {
						throw new Error(response.message || '保存失败');
					}

					console.log(`保存${type}照片成功:`, imagePath);
				} catch (error) {
					console.error('保存照片失败:', error);
					uni.showToast({
						title: '保存失败',
						icon: 'none'
					});
				}
			},

			// 点赞
			toggleLike() {
				this.isLiked = !this.isLiked;
				this.likeCount += this.isLiked ? 1 : -1;
			},

			// 收藏
			toggleStar() {
				this.isStarred = !this.isStarred;
				this.starCount += this.isStarred ? 1 : -1;
			},

			// 分享效果
			shareEffect() {
				uni.showActionSheet({
					itemList: ['分享到微信', '分享到朋友圈', '保存图片'],
					success: (res) => {
						switch (res.tapIndex) {
							case 0:
								this.shareToWeChat();
								break;
							case 1:
								this.shareToMoments();
								break;
							case 2:
								this.saveImage();
								break;
						}
					}
				});
			},

			shareToWeChat() {
				this.shareCount++;
				uni.showToast({
					title: '分享到微信成功',
					icon: 'success'
				});
			},

			shareToMoments() {
				this.shareCount++;
				uni.showToast({
					title: '分享到朋友圈成功',
					icon: 'success'
				});
			},

			saveImage() {
				uni.showToast({
					title: '图片已保存',
					icon: 'success'
				});
			},

			// 生成新配方
			generateNewFormula() {
				if (!this.recordData) {
					uni.showToast({
						title: '数据不完整',
						icon: 'none'
					});
					return;
				}

				// 跳转到配方生成页面，传递当前颜色数据
				// uni.navigateTo({
				// 	url: `/pages/mall/color/detail?colorCode=${this.colorCode}&name=${encodeURIComponent(this.colorDisplayName)}&color=${encodeURIComponent(this.colorHex)}`,
				// 	success: () => {
				// 		console.log('跳转到配方生成页面成功');
				// 	},
				// 	fail: (err) => {
				// 		console.error('跳转失败:', err);
				// 		uni.showToast({
				// 			title: '跳转失败',
				// 			icon: 'none'
				// 		});
				// 	}
				// });
			}
		}
	};
</script>

<style lang="scss">
	.container {
		display: flex;
		flex-direction: column;
		min-height: 100vh;
		background-color: #f5f5f5;
	}



	.device-info {
	  display: flex;
	    justify-content: space-between; /* 左右两端对齐 */
	    align-items: center; /* 垂直居中 */
	    width: 100%; /* 确保容器占满宽度 */
	}

	.device-name {
		font-size: 32rpx;
		  color: #333;
		  white-space: nowrap; /* 不换行 */
		  overflow: hidden; /* 超出部分隐藏 */
		  text-overflow: ellipsis; /* 显示省略号 */
		  max-width: 70%; /* 限制最大宽度，防止挤压状态 */
	}

	.device-status {
	  font-size: 26rpx;
	    padding: 4rpx 12rpx;
	    border-radius: 20rpx;
	    white-space: nowrap; /* 确保状态不换行 */
	    flex-shrink: 0; /* 防止被压缩 */
		margin-right: 20px;
	}
	
	.status-online {
	  color: #07C160;
	  background-color: rgba(7, 193, 96, 0.1);
	}
	
	.status-offline {
	  color: #FA5151;
	  background-color: rgba(250, 81, 81, 0.1);
	}

	.comparison-section {
		background-color: #fff;
		padding: 30rpx;
		margin-bottom: 20rpx;
	}

	.image-container {
		display: flex;
		justify-content: space-between;
		margin-bottom: 30rpx;
	}

	.before-image,
	.after-image {
		width: 45%;
		display: flex;
		flex-direction: column;
		align-items: center;
	}

	.hair-image {
		width: 100%;
		height: 400rpx;
		border-radius: 20rpx;
		margin-bottom: 20rpx;
	}

	.placeholder-image {
		width: 100%;
		height: 400rpx;
		border: 2rpx dashed #4285f4;
		border-radius: 20rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		margin-bottom: 20rpx;
		background-color: #f8f9ff;
	}

	.add-icon {
		font-size: 60rpx;
		color: #4285f4;
		margin-bottom: 10rpx;
	}

	.placeholder-text {
		font-size: 28rpx;
		color: #4285f4;
		margin-bottom: 5rpx;
	}

	.placeholder-subtext {
		font-size: 24rpx;
		color: #999;
	}

	.image-label {
		font-size: 28rpx;
		color: #333;
		text-align: center;
	}

	.color-title {
		font-size: 36rpx;
		font-weight: bold;
		color: #333;
		text-align: center;
	}

	.color-structure {
		background-color: #fff;
		padding: 30rpx;
		margin-bottom: 20rpx;
	}

	.structure-title {
		font-size: 32rpx;
		font-weight: bold;
		color: #333;
		margin-bottom: 30rpx;
	}

	.color-composition {
		display: flex;
		flex-direction: column;
		gap: 15rpx;
	}

	.color-item {
		display: flex;
		align-items: center;
		padding: 15rpx;
		background-color: #f8f9fa;
		border-radius: 12rpx;
	}

	.color-block {
		width: 80rpx;
		height: 80rpx;
		border-radius: 12rpx;
		margin-right: 20rpx;
		border: 2rpx solid #eee;
	}

	.color-info {
		display: flex;
		flex-direction: column;
	}

	.color-name {
		font-size: 28rpx;
		color: #333;
		margin-bottom: 5rpx;
	}

	.color-amount {
		font-size: 24rpx;
		color: #666;
	}

	.bottom-actions {
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		display: flex;
		align-items: center;
		justify-content: space-between;
		background-color: #fff;
		padding: 20rpx 30rpx;
		border-top: 1rpx solid #eee;
		box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.1);
	}

	.action-icon {
		display: flex;
		flex-direction: column;
		align-items: center;
		margin-right: 30rpx;
	}

	.icon {
		font-size: 40rpx;
		color: #666;
		margin-bottom: 5rpx;
	}

	.count {
		font-size: 24rpx;
		color: #999;
	}

	.generate-btn {
		background-color: #4285f4;
		color: #fff;
		padding: 20rpx 40rpx;
		border-radius: 30rpx;
		font-size: 32rpx;
		font-weight: 500;
	}
</style>