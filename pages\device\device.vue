<template>
  <view class="content">
    <!-- 设备搜索 -->
    <view class="search-bar">
      <view class="search-input">
        <view class="search-icon-placeholder"></view>
        <input type="text" placeholder="搜索设备" />
      </view>
      <view class="add-button">
        <text>+ 添加设备</text>
      </view>
    </view>
    
    <!-- 设备分类 -->
    <view class="device-categories">
      <view class="category-tab active">
        <text>全部</text>
      </view>
      <view class="category-tab">
        <text>调色机</text>
      </view>
      <view class="category-tab">
        <text>甩料机</text>
      </view>
      <view class="category-tab">
        <text>分光仪</text>
      </view>
    </view>
    
    <!-- 设备列表 -->
    <view class="device-list">
      <!-- 未添加设备时的提示 -->
      <view class="empty-container">
        <view class="empty-image-placeholder"></view>
        <text class="empty-text">暂无设备，请添加设备</text>
      </view>
      
      <!-- 设备列表项 -->
      <view class="device-item" style="display: none;">
        <view class="device-image-placeholder"></view>
        <view class="device-info">
          <text class="device-name">调色机 A120</text>
          <text class="device-status online">在线</text>
          <text class="device-id">设备ID: DGK24601</text>
          <text class="device-location">位置: 一号门店</text>
        </view>
        <view class="device-actions">
          <view class="action-btn">
            <view class="icon-placeholder"></view>
            <text>编辑</text>
          </view>
          <view class="action-btn">
            <view class="icon-placeholder"></view>
            <text>删除</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 底部扫描按钮 -->
    <view class="bottom-scan">
      <view class="scan-btn">
        <view class="scan-icon-placeholder"></view>
        <text>扫描二维码添加设备</text>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      deviceList: [],
      activeCategory: '全部'
    };
  },
  onLoad() {
    // 模拟获取设备列表
    this.loadDevices();
  },
  methods: {
    loadDevices() {
      // 这里应该是API请求获取设备列表
      this.deviceList = [];
    },
    switchCategory(category) {
      this.activeCategory = category;
      // 根据分类筛选设备
    },
    addDevice() {
      // 添加设备逻辑
    },
    scanQRCode() {
      // 扫描二维码逻辑
    }
  }
};
</script>

<style lang="scss">
.content {
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.top-header {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 90rpx;
  background-color: #4285f4;
  padding: 0 20rpx;
  position: relative;
}

.header-title {
  color: #ffffff;
  font-size: 36rpx;
}

.top-right {
  display: flex;
  align-items: center;
  position: absolute;
  right: 20rpx;
}

.circle-btn {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.3);
  display: flex;
  justify-content: center;
  align-items: center;
  margin-left: 10rpx;
}

.circle-btn.active {
  background-color: rgba(255, 255, 255, 0.3);
}

.circle-inner {
  width: 30rpx;
  height: 30rpx;
  background-color: #fff;
  border-radius: 50%;
}

.dash-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #fff;
  margin-left: 10rpx;
}

.more-dots {
  color: #fff;
  font-size: 36rpx;
  line-height: 20rpx;
}

.search-bar {
  display: flex;
  padding: 20rpx;
  background-color: #fff;
  align-items: center;
}

.search-input {
  flex: 1;
  height: 70rpx;
  background-color: #f0f0f0;
  border-radius: 35rpx;
  display: flex;
  align-items: center;
  padding: 0 20rpx;
}

.search-icon-placeholder {
  width: 40rpx;
  height: 40rpx;
  margin-right: 10rpx;
  background-color: #ddd;
  border-radius: 50%;
}

.search-input input {
  flex: 1;
  height: 100%;
  font-size: 28rpx;
}

.add-button {
  margin-left: 20rpx;
  background-color: #4285f4;
  height: 70rpx;
  border-radius: 35rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 30rpx;
}

.add-button text {
  color: #fff;
  font-size: 28rpx;
}

.device-categories {
  display: flex;
  background-color: #fff;
  padding: 10rpx 0;
  border-bottom: 1rpx solid #eee;
}

.category-tab {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 80rpx;
  position: relative;
}

.category-tab text {
  font-size: 28rpx;
  color: #666;
}

.category-tab.active text {
  color: #4285f4;
  font-weight: bold;
}

.category-tab.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60rpx;
  height: 4rpx;
  background-color: #4285f4;
}

.device-list {
  flex: 1;
  padding: 20rpx;
}

.empty-container {
  margin-top: 100rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.empty-image-placeholder {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
  background-color: #eee;
  border-radius: 8rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

.device-item {
  display: flex;
  background-color: #fff;
  padding: 20rpx;
  border-radius: 8rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.device-image-placeholder {
  width: 120rpx;
  height: 120rpx;
  margin-right: 20rpx;
  background-color: #eee;
  border-radius: 8rpx;
}

.device-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.device-name {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.device-status {
  font-size: 24rpx;
  padding: 4rpx 16rpx;
  border-radius: 20rpx;
  display: inline-block;
  width: fit-content;
  margin-bottom: 10rpx;
}

.device-status.online {
  background-color: rgba(76, 175, 80, 0.1);
  color: #4caf50;
}

.device-status.offline {
  background-color: rgba(158, 158, 158, 0.1);
  color: #9e9e9e;
}

.device-id, .device-location {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 6rpx;
}

.device-actions {
  display: flex;
  flex-direction: column;
  justify-content: space-around;
}

.action-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.icon-placeholder {
  width: 40rpx;
  height: 40rpx;
  margin-bottom: 6rpx;
  background-color: #ddd;
  border-radius: 4rpx;
}

.action-btn text {
  font-size: 22rpx;
  color: #666;
}

.bottom-scan {
  padding: 30rpx;
}

.scan-btn {
  height: 90rpx;
  background-color: #fff;
  border: 2rpx solid #4285f4;
  border-radius: 45rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.scan-icon-placeholder {
  width: 40rpx;
  height: 40rpx;
  margin-right: 10rpx;
  background-color: #ddd;
  border-radius: 4rpx;
}

.scan-btn text {
  font-size: 28rpx;
  color: #4285f4;
}
</style> 