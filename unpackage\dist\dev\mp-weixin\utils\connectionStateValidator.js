"use strict";
const common_vendor = require("../common/vendor.js");
class ConnectionStateValidator {
  constructor() {
    this.logHistory = [];
    this.maxLogHistory = 100;
  }
  /**
   * 验证连接状态的一致性
   */
  validateConnectionState() {
    var _a, _b, _c, _d;
    const app = getApp();
    const store = app.$store || app.store;
    const validation = {
      timestamp: (/* @__PURE__ */ new Date()).toISOString(),
      globalData: {
        connectionMethod: app.globalData.connectionMethod,
        deviceStatus: app.globalData.deviceStatus,
        connectedDeviceId: app.globalData.connectedDeviceId
      },
      storeState: {
        connectedDevice: ((_a = store == null ? void 0 : store.state) == null ? void 0 : _a.connectedDevice) || null
      },
      issues: [],
      isValid: true
    };
    if ((_b = store == null ? void 0 : store.state) == null ? void 0 : _b.connectedDevice) {
      const storeConnectionType = store.state.connectedDevice.connectionType;
      const globalConnectionMethod = app.globalData.connectionMethod;
      if (storeConnectionType && storeConnectionType !== globalConnectionMethod) {
        validation.issues.push({
          type: "CONNECTION_TYPE_MISMATCH",
          message: `Store中连接类型(${storeConnectionType})与全局连接方式(${globalConnectionMethod})不一致`,
          severity: "HIGH"
        });
        validation.isValid = false;
      }
    }
    if ((_c = store == null ? void 0 : store.state) == null ? void 0 : _c.connectedDevice) {
      const storeDeviceId = store.state.connectedDevice.deviceId || store.state.connectedDevice.name;
      const globalDeviceId = app.globalData.connectedDeviceId;
      if (storeDeviceId !== globalDeviceId) {
        validation.issues.push({
          type: "DEVICE_ID_MISMATCH",
          message: `Store中设备ID(${storeDeviceId})与全局设备ID(${globalDeviceId})不一致`,
          severity: "MEDIUM"
        });
        validation.isValid = false;
      }
    }
    if (app.globalData.deviceStatus === true && !app.globalData.connectionMethod) {
      validation.issues.push({
        type: "INVALID_CONNECTION_STATE",
        message: "设备状态为在线但连接方式为空",
        severity: "HIGH"
      });
      validation.isValid = false;
    }
    if (((_d = store == null ? void 0 : store.state) == null ? void 0 : _d.connectedDevice) && !store.state.connectedDevice.connectionType) {
      validation.issues.push({
        type: "MISSING_CONNECTION_TYPE",
        message: "Store中连接设备缺少connectionType字段，可能导致状态混乱",
        severity: "HIGH"
      });
      validation.isValid = false;
    }
    this.addToHistory(validation);
    return validation;
  }
  /**
   * 记录连接状态变化
   */
  logConnectionChange(action, type, details = {}) {
    const logEntry = {
      timestamp: (/* @__PURE__ */ new Date()).toISOString(),
      action,
      connectionType: type,
      details,
      beforeState: this.getCurrentState(),
      validation: this.validateConnectionState()
    };
    this.addToHistory(logEntry);
    common_vendor.index.__f__("log", "at utils/connectionStateValidator.js:103", "🔍 连接状态变化:", {
      action,
      type,
      details,
      validation: logEntry.validation.isValid ? "✅ 正常" : "❌ 异常",
      issues: logEntry.validation.issues
    });
    if (!logEntry.validation.isValid) {
      const highSeverityIssues = logEntry.validation.issues.filter((issue) => issue.severity === "HIGH");
      if (highSeverityIssues.length > 0) {
        common_vendor.index.__f__("warn", "at utils/connectionStateValidator.js:115", "🚨 检测到严重的连接状态问题:", highSeverityIssues);
      }
    }
    return logEntry;
  }
  /**
   * 获取当前连接状态
   */
  getCurrentState() {
    var _a;
    const app = getApp();
    const store = app.$store || app.store;
    return {
      globalData: {
        connectionMethod: app.globalData.connectionMethod,
        deviceStatus: app.globalData.deviceStatus,
        connectedDeviceId: app.globalData.connectedDeviceId
      },
      storeState: {
        connectedDevice: ((_a = store == null ? void 0 : store.state) == null ? void 0 : _a.connectedDevice) || null
      }
    };
  }
  /**
   * 自动修复常见的状态不一致问题
   */
  autoFixStateIssues() {
    const validation = this.validateConnectionState();
    let fixedIssues = [];
    if (!validation.isValid) {
      const app = getApp();
      const store = app.$store || app.store;
      validation.issues.forEach((issue) => {
        var _a, _b, _c;
        switch (issue.type) {
          case "MISSING_CONNECTION_TYPE":
            if (((_a = store == null ? void 0 : store.state) == null ? void 0 : _a.connectedDevice) && app.globalData.connectionMethod) {
              const updatedDevice = {
                ...store.state.connectedDevice,
                connectionType: app.globalData.connectionMethod
              };
              store.commit("SET_CONNECTED_DEVICE", updatedDevice);
              fixedIssues.push("添加了缺失的connectionType");
              common_vendor.index.__f__("log", "at utils/connectionStateValidator.js:163", "🔧 自动修复：为连接设备添加connectionType:", app.globalData.connectionMethod);
            }
            break;
          case "CONNECTION_TYPE_MISMATCH":
            if ((_c = (_b = store == null ? void 0 : store.state) == null ? void 0 : _b.connectedDevice) == null ? void 0 : _c.connectionType) {
              app.globalData.connectionMethod = store.state.connectedDevice.connectionType;
              fixedIssues.push("同步了连接类型");
              common_vendor.index.__f__("log", "at utils/connectionStateValidator.js:172", "🔧 自动修复：同步全局连接方式为:", store.state.connectedDevice.connectionType);
            }
            break;
        }
      });
    }
    return {
      fixed: fixedIssues.length > 0,
      fixedIssues,
      finalValidation: this.validateConnectionState()
    };
  }
  /**
   * 添加到历史记录
   */
  addToHistory(entry) {
    this.logHistory.unshift(entry);
    if (this.logHistory.length > this.maxLogHistory) {
      this.logHistory.pop();
    }
  }
  /**
   * 获取历史记录
   */
  getHistory(limit = 10) {
    return this.logHistory.slice(0, limit);
  }
  /**
   * 清除历史记录
   */
  clearHistory() {
    this.logHistory = [];
  }
  /**
   * 生成状态报告
   */
  generateReport() {
    const validation = this.validateConnectionState();
    const recentHistory = this.getHistory(5);
    return {
      currentValidation: validation,
      recentHistory,
      summary: {
        isHealthy: validation.isValid,
        issueCount: validation.issues.length,
        highSeverityIssues: validation.issues.filter((issue) => issue.severity === "HIGH").length,
        lastValidationTime: validation.timestamp
      }
    };
  }
}
const connectionStateValidator = new ConnectionStateValidator();
exports.connectionStateValidator = connectionStateValidator;
//# sourceMappingURL=../../.sourcemap/mp-weixin/utils/connectionStateValidator.js.map
