/* 水平间距 */
/* 水平间距 */
/* 全局样式修复 - 确保页面占满屏幕宽度 */
page {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
}

/* 通用容器样式 */
.container, .page-container {
  width: 100%;
  min-height: 100vh;
  box-sizing: border-box;
}

/* 修复小程序页面宽度问题 */
uni-page-body {
  width: 100% !important;
}

/* 确保所有view元素正确继承宽度 */
view {
  box-sizing: border-box;
}
.address-list {
  min-height: 100vh;
  background-color: #f5f5f5;
  width: 100%;
  box-sizing: border-box;
  padding-bottom: 180rpx;
  /* 为底部按钮留出空间 */
}
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 100rpx 0;
}
.loading-text {
  font-size: 28rpx;
  color: #999;
}
.address-container {
  padding: 20rpx 0;
  width: 100%;
  box-sizing: border-box;
}
.address-item {
  display: flex;
  align-items: center;
  background-color: #ffffff;
  padding: 30rpx 20rpx;
  margin-bottom: 2rpx;
  width: 100%;
  box-sizing: border-box;
}
.address-info {
  flex: 1;
  width: 0;
  /* 强制flex子元素重新计算宽度 */
  min-width: 0;
  /* 允许flex子元素收缩 */
}
.address-name-phone {
  margin-bottom: 10rpx;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}
.name {
  font-size: 30rpx;
  font-weight: bold;
  margin-right: 15rpx;
}
.phone {
  font-size: 28rpx;
  color: #666;
  margin-right: 15rpx;
}
.default-tag {
  display: inline-block;
  font-size: 22rpx;
  color: #4285f4;
  padding: 2rpx 10rpx;
  border: 1rpx solid #4285f4;
  border-radius: 20rpx;
  flex-shrink: 0;
}
.address-detail {
  font-size: 28rpx;
  color: #333;
  line-height: 1.4;
  word-break: break-all;
  overflow-wrap: break-word;
}
.edit-btn {
  padding: 10rpx;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60rpx;
  height: 60rpx;
}
.icon-edit {
  font-size: 36rpx;
  color: #999;
}
.empty-address {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 150rpx 40rpx;
  min-height: 60vh;
  text-align: center;
}
.empty-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
}
.empty-text {
  font-size: 28rpx;
  color: #999;
  text-align: center;
  line-height: 1.5;
}
.add-address {
  padding: 40rpx 20rpx;
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #fff;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.1);
  /* 适配底部安全区域 */
  padding-bottom: calc(40rpx + env(safe-area-inset-bottom));
}
.add-btn {
  height: 90rpx;
  line-height: 90rpx;
  text-align: center;
  background-color: #4285f4;
  color: #ffffff;
  border-radius: 45rpx;
  font-size: 32rpx;
  width: 100%;
  box-sizing: border-box;
}

/* 响应式设计 */
@media screen and (max-width: 750rpx) {
.address-item {
    padding: 25rpx 15rpx;
}
.name {
    font-size: 28rpx;
}
.phone {
    font-size: 26rpx;
}
.address-detail {
    font-size: 26rpx;
}
.default-tag {
    font-size: 20rpx;
}
}
@media screen and (min-width: 751rpx) {
.address-container {
    max-width: 750rpx;
    margin: 0 auto;
}
.add-address {
    max-width: 750rpx;
    left: 50%;
    transform: translateX(-50%);
    right: auto;
}
}
/* 页面级别样式重置 */
page {
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}