<template>
  <view class="container">
    <!-- 设备信息区域 -->
    <view class="device-info">
      <view class="device-icon">
        <image src="/static/icons/equipment-icon.png" class="device-image"></image>
      </view>
      <view class="device-details">
        <view class="device-name">芭佰邑-智能染发机</view>
        <view class="device-id">编号: Srd202467c516</view>
      </view>
      <view class="device-status">
        <text class="status-text">连接</text>
      </view>
    </view>

    <!-- 颜色网格区域 -->
    <view class="color-grid">
      <view class="color-item" v-for="(color, index) in colorList" :key="index" @click="selectColor(color, index)">
        <view class="color-box" :class="{ 'selected': selectedIndex === index }">
          <view class="color-circle-container">
            <view class="color-circle" :style="{ backgroundColor: color.hex }"></view>
            <!-- 比例条 -->
            <view class="color-progress" :style="{ width: getProgressWidth(color.name) }"></view>
          </view>
          <view class="color-name">{{ color.name }}</view>
        </view>
      </view>
    </view>

    <!-- 底部按钮区域 -->
    <view class="bottom-buttons">
      <view class="btn-secondary" @click="showAllColors">
        <text>检测 灰色(1-0/1) 色</text>
      </view>
      <view class="btn-primary" @click="startDetection">
        <text>龙卷风装置</text>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      selectedIndex: null,
      colorList: [
        { name: '灰色(1-0/1)', hex: '#8B8B8B', value: 20 },
        { name: '黄色(2-0/3)', hex: '#FFD700', value: 35 },
        { name: '红色(3-0/4)', hex: '#FF0000', value: 15 },
        { name: '棕色(4-0/77)', hex: '#8B4513', value: 10 },
        { name: '蓝色(5-0/88)', hex: '#0000FF', value: 5 },
        { name: '黑色(6-2/0)', hex: '#000000', value: 30 },
        { name: '深色(7-0/00)', hex: '#2F2F2F', value: 25 },
        { name: '灰蓝(8-10/0)', hex: '#708090', value: 40 },
        { name: '双灰(9-40/0)', hex: '#A9A9A9', value: 50 }
      ]
    };
  },
  methods: {
    selectColor(color, index) {
      console.log('选择颜色:', color);
      this.selectedIndex = index;
    },
    showAllColors() {
      console.log('显示所有颜色');
      // 处理显示所有颜色逻辑
    },
    startDetection() {
      console.log('开始检测');
      // 处理开始检测逻辑
    },
    getProgressWidth(name) {
      // 从颜色名称中提取数值部分
      const match = name.match(/\((\d+)-/);
      if (match && match[1]) {
        const num = parseInt(match[1]);
        // 计算比例，假设最大值为50
        return `${(num / 50) * 100}%`;
      }
      return '0%';
    }
  }
};
</script>

<style lang="scss" scoped>
.container {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding: 20rpx;
}

.device-info {
  background-color: #ffffff;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.device-icon {
  width: 80rpx;
  height: 80rpx;
  margin-right: 20rpx;
}

.device-image {
  width: 100%;
  height: 100%;
  border-radius: 10rpx;
}

.device-details {
  flex: 1;
}

.device-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 8rpx;
}

.device-id {
  font-size: 26rpx;
  color: #666666;
}

.device-status {
  background-color: #4CAF50;
  border-radius: 30rpx;
  padding: 12rpx 24rpx;
}

.status-text {
  color: #ffffff;
  font-size: 26rpx;
  font-weight: 500;
}

.color-grid {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  padding: 0 10rpx;
  margin-bottom: 60rpx;
}

.color-item {
  width: 30%;
  margin-bottom: 40rpx;
}

.color-box {
  border: 2rpx solid #e0e0e0;
  border-radius: 12rpx;
  padding: 16rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  transition: all 0.3s;
  
  &.selected {
    border-color: #4285f4;
    box-shadow: 0 0 10rpx rgba(66, 133, 244, 0.3);
  }
}

.color-circle-container {
  position: relative;
  width: 140rpx;
  margin-bottom: 16rpx;
}

.color-circle {
  width: 140rpx;
  height: 80rpx;
  border-radius: 8rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.color-progress {
  position: absolute;
  bottom: -10rpx;
  left: 0;
  height: 6rpx;
  background-color: #4285f4;
  border-radius: 3rpx;
}

.color-name {
  font-size: 24rpx;
  color: #333333;
  text-align: center;
  line-height: 1.3;
}

.bottom-buttons {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #ffffff;
  padding: 20rpx 30rpx 40rpx;
  display: flex;
  gap: 20rpx;
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.btn-secondary {
  flex: 1;
  background-color: #f0f0f0;
  border-radius: 50rpx;
  padding: 24rpx 0;
  text-align: center;
}

.btn-secondary text {
  color: #666666;
  font-size: 28rpx;
}

.btn-primary {
  flex: 1;
  background-color: #4285f4;
  border-radius: 50rpx;
  padding: 24rpx 0;
  text-align: center;
}

.btn-primary text {
  color: #ffffff;
  font-size: 28rpx;
  font-weight: 500;
}
</style>