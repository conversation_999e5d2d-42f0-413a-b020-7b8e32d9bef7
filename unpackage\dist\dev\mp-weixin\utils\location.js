"use strict";
const common_vendor = require("../common/vendor.js");
const config_map = require("../config/map.js");
class LocationService {
  constructor() {
    this.lastLocation = null;
    this.locationCache = /* @__PURE__ */ new Map();
  }
  /**
   * 检查位置权限
   */
  checkLocationPermission() {
    return new Promise((resolve) => {
      common_vendor.index.getSetting({
        success: resolve,
        fail: () => resolve({ authSetting: {} })
      });
    });
  }
  /**
   * 请求位置权限
   */
  requestLocationPermission() {
    return new Promise((resolve, reject) => {
      common_vendor.index.authorize({
        scope: "scope.userLocation",
        success: resolve,
        fail: (err) => {
          common_vendor.index.showModal({
            title: "位置权限",
            content: "需要位置权限来为您提供更好的服务，是否前往设置开启？",
            confirmText: "去设置",
            cancelText: "取消",
            success: (res) => {
              if (res.confirm) {
                common_vendor.index.openSetting({
                  success: (settingRes) => {
                    if (settingRes.authSetting["scope.userLocation"]) {
                      resolve();
                    } else {
                      reject(new Error("用户拒绝授权位置权限"));
                    }
                  },
                  fail: () => reject(new Error("打开设置页面失败"))
                });
              } else {
                reject(new Error("用户拒绝授权位置权限"));
              }
            }
          });
        }
      });
    });
  }
  /**
   * 获取位置信息
   */
  getLocationInfo() {
    return new Promise((resolve, reject) => {
      common_vendor.index.__f__("log", "at utils/location.js:71", "=== 开始获取GPS坐标 ===");
      common_vendor.index.getLocation({
        type: "gcj02",
        // 使用国测局坐标系
        altitude: false,
        success: (res) => {
          common_vendor.index.__f__("log", "at utils/location.js:77", "=== GPS坐标获取成功 ===");
          common_vendor.index.__f__("log", "at utils/location.js:78", "纬度 (latitude):", res.latitude);
          common_vendor.index.__f__("log", "at utils/location.js:79", "经度 (longitude):", res.longitude);
          common_vendor.index.__f__("log", "at utils/location.js:80", "精度 (accuracy):", res.accuracy);
          common_vendor.index.__f__("log", "at utils/location.js:81", "完整GPS数据:", res);
          this.lastLocation = res;
          resolve(res);
        },
        fail: (err) => {
          common_vendor.index.__f__("error", "at utils/location.js:87", "=== GPS坐标获取失败 ===");
          common_vendor.index.__f__("error", "at utils/location.js:88", "错误信息:", err);
          reject(new Error("获取位置失败: " + (err.errMsg || "未知错误")));
        }
      });
    });
  }
  /**
   * 逆地理编码 - 高德地图
   */
  async reverseGeocodeAmap(latitude, longitude) {
    try {
      common_vendor.index.__f__("log", "at utils/location.js:100", "=== 开始逆地理编码 ===");
      common_vendor.index.__f__("log", "at utils/location.js:101", "输入坐标 - 纬度:", latitude, "经度:", longitude);
      const cacheKey = `${latitude.toFixed(4)},${longitude.toFixed(4)}`;
      if (this.locationCache.has(cacheKey)) {
        common_vendor.index.__f__("log", "at utils/location.js:107", "使用缓存的地址数据");
        return this.locationCache.get(cacheKey);
      }
      const config = config_map.getMapServiceConfig("amap");
      const url = `${config.geocoderUrl}?location=${longitude},${latitude}&key=${config.key}&radius=1000&extensions=all`;
      common_vendor.index.__f__("log", "at utils/location.js:114", "高德API请求URL:", url);
      const response = await common_vendor.index.request({
        url,
        method: "GET",
        timeout: config_map.mapConfig.default.timeout
      });
      common_vendor.index.__f__("log", "at utils/location.js:122", "=== 高德API响应 ===");
      common_vendor.index.__f__("log", "at utils/location.js:123", "状态码:", response.statusCode);
      common_vendor.index.__f__("log", "at utils/location.js:124", "响应数据:", response.data);
      if (response.statusCode === 200 && response.data.status === "1") {
        const regeocode = response.data.regeocode;
        const address = regeocode.formatted_address || "";
        common_vendor.index.__f__("log", "at utils/location.js:130", "=== 地址解析成功 ===");
        common_vendor.index.__f__("log", "at utils/location.js:131", "解析后的地址:", address);
        common_vendor.index.__f__("log", "at utils/location.js:132", "详细地址信息:", regeocode);
        this.locationCache.set(cacheKey, address);
        return address;
      } else {
        common_vendor.index.__f__("error", "at utils/location.js:139", "=== 高德地图API返回错误 ===");
        common_vendor.index.__f__("error", "at utils/location.js:140", "错误响应:", response.data);
        throw new Error("高德地图API调用失败");
      }
    } catch (error) {
      common_vendor.index.__f__("error", "at utils/location.js:144", "高德地图逆地理编码失败:", error);
      throw error;
    }
  }
  /**
   * 主要的逆地理编码方法
   */
  async reverseGeocode(latitude, longitude) {
    try {
      const availableService = config_map.getAvailableMapService();
      if (availableService === "amap") {
        return await this.reverseGeocodeAmap(latitude, longitude);
      }
      common_vendor.index.__f__("warn", "at utils/location.js:161", "没有配置高德地图服务，请在 config/map.js 中配置API密钥");
      return this.getMockAddress(latitude, longitude);
    } catch (error) {
      common_vendor.index.__f__("error", "at utils/location.js:165", "逆地理编码失败:", error);
      return this.getMockAddress(latitude, longitude);
    }
  }
  /**
   * 获取模拟地址（当地图服务不可用时使用）
   */
  getMockAddress(latitude, longitude) {
    if (latitude >= 22 && latitude <= 25 && longitude >= 113 && longitude <= 115) {
      return "广东省东莞市南城街道";
    } else if (latitude >= 39 && latitude <= 41 && longitude >= 115 && longitude <= 118) {
      return "北京市朝阳区";
    } else if (latitude >= 31 && latitude <= 32 && longitude >= 121 && longitude <= 122) {
      return "上海市浦东新区";
    } else if (latitude >= 22 && latitude <= 24 && longitude >= 113 && longitude <= 115) {
      return "广东省深圳市南山区";
    } else {
      return "当前位置";
    }
  }
  /**
   * 获取完整的位置信息（包括地址）
   */
  async getCurrentLocation() {
    try {
      const savedLocation = common_vendor.index.getStorageSync("userLocation");
      if (savedLocation && savedLocation.timestamp && Date.now() - savedLocation.timestamp < 5 * 60 * 1e3) {
        common_vendor.index.__f__("log", "at utils/location.js:198", "使用缓存的位置信息");
        return savedLocation;
      }
      const setting = await this.checkLocationPermission();
      if (!setting.authSetting["scope.userLocation"]) {
        await this.requestLocationPermission();
      }
      const location = await this.getLocationInfo();
      const address = await this.reverseGeocode(location.latitude, location.longitude);
      const result = {
        latitude: location.latitude,
        longitude: location.longitude,
        address,
        timestamp: Date.now()
      };
      common_vendor.index.setStorageSync("userLocation", result);
      return result;
    } catch (error) {
      common_vendor.index.__f__("error", "at utils/location.js:229", "获取位置失败:", error);
      const savedLocation = common_vendor.index.getStorageSync("userLocation");
      if (savedLocation && savedLocation.address) {
        return savedLocation;
      }
      throw error;
    }
  }
  /**
   * 高德地图POI搜索
   */
  async searchPOI(keyword, city = "东莞") {
    try {
      const config = config_map.getMapServiceConfig("amap");
      if (!config || !config.enabled) {
        throw new Error("高德地图服务未启用");
      }
      const url = `${config.poiSearchUrl}?key=${config.key}&keywords=${encodeURIComponent(keyword)}&city=${encodeURIComponent(city)}&output=json`;
      const response = await common_vendor.index.request({
        url,
        method: "GET",
        timeout: config_map.mapConfig.default.timeout
      });
      if (response.statusCode === 200 && response.data.status === "1") {
        const pois = response.data.pois || [];
        return pois.map((poi) => ({
          name: poi.name,
          address: poi.address,
          location: poi.location,
          type: poi.type
        }));
      } else {
        throw new Error("POI搜索失败");
      }
    } catch (error) {
      common_vendor.index.__f__("error", "at utils/location.js:271", "POI搜索异常:", error);
      return [
        { name: `${keyword}附近`, address: `广东省东莞市${keyword}` },
        { name: `${keyword}商圈`, address: `广东省东莞市南城街道${keyword}` },
        { name: `${keyword}地铁站`, address: `广东省东莞市${keyword}地铁站` }
      ];
    }
  }
  /**
   * 清除位置缓存
   */
  clearCache() {
    this.locationCache.clear();
    common_vendor.index.removeStorageSync("userLocation");
  }
}
const locationService = new LocationService();
exports.locationService = locationService;
//# sourceMappingURL=../../.sourcemap/mp-weixin/utils/location.js.map
