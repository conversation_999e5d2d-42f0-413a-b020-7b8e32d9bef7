"use strict";
const common_vendor = require("../common/vendor.js");
const BASE_URL = "https://www.narenqiqige.com";
function getFullImageUrl(url) {
  if (!url)
    return "";
  if (url.startsWith("http")) {
    return url;
  }
  if (url.startsWith("/static/uploads/")) {
    const fullUrl = BASE_URL + "/api" + url;
    common_vendor.index.__f__("log", "at utils/imageUtils.js:20", "图片URL转换:", url, "->", fullUrl);
    return fullUrl;
  }
  if (url.startsWith("/static")) {
    const fullUrl = BASE_URL + "/api" + url;
    common_vendor.index.__f__("log", "at utils/imageUtils.js:27", "静态资源URL转换:", url, "->", fullUrl);
    return fullUrl;
  }
  if (url.startsWith("/uploads")) {
    const fullUrl = BASE_URL + "/api/static" + url;
    common_vendor.index.__f__("log", "at utils/imageUtils.js:34", "上传文件URL转换:", url, "->", fullUrl);
    return fullUrl;
  }
  if (!url.startsWith("/")) {
    if (url.includes("uploads/")) {
      const fullUrl = BASE_URL + "/api/static/" + url;
      common_vendor.index.__f__("log", "at utils/imageUtils.js:43", "相对路径URL转换:", url, "->", fullUrl);
      return fullUrl;
    } else {
      const fullUrl = BASE_URL + "/api/static/uploads/images/" + url;
      common_vendor.index.__f__("log", "at utils/imageUtils.js:47", "文件名URL转换:", url, "->", fullUrl);
      return fullUrl;
    }
  }
  common_vendor.index.__f__("log", "at utils/imageUtils.js:52", "未匹配的URL:", url);
  return url;
}
function getFirstImage(images) {
  if (!images)
    return "";
  let imageUrl = "";
  if (typeof images === "string") {
    const imageArray = images.split(",");
    imageUrl = imageArray[0] || "";
  } else {
    imageUrl = images[0] || "";
  }
  return getFullImageUrl(imageUrl);
}
exports.getFirstImage = getFirstImage;
exports.getFullImageUrl = getFullImageUrl;
//# sourceMappingURL=../../.sourcemap/mp-weixin/utils/imageUtils.js.map
