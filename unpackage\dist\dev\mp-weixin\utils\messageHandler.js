"use strict";
const common_vendor = require("../common/vendor.js");
const utils_dyeConsumptionManager = require("./dyeConsumptionManager.js");
class MessageHandler {
  constructor() {
    this.messageQueue = [];
    this.isProcessing = false;
    this.listeners = /* @__PURE__ */ new Map();
    this.messageHistory = [];
    this.maxHistorySize = 100;
  }
  /**
   * 注册消息监听器
   * @param {string} messageType - 消息类型
   * @param {Function} handler - 处理函数
   */
  registerListener(messageType, handler) {
    if (!this.listeners.has(messageType)) {
      this.listeners.set(messageType, []);
    }
    this.listeners.get(messageType).push(handler);
  }
  /**
   * 移除消息监听器
   * @param {string} messageType - 消息类型
   * @param {Function} handler - 处理函数
   */
  removeListener(messageType, handler) {
    if (this.listeners.has(messageType)) {
      const handlers = this.listeners.get(messageType);
      const index = handlers.indexOf(handler);
      if (index > -1) {
        handlers.splice(index, 1);
      }
    }
  }
  /**
   * 处理蓝牙消息
   * @param {Object} message - 蓝牙消息
   */
  async handleBluetoothMessage(message) {
    try {
      const processedMessage = {
        source: "bluetooth",
        timestamp: (/* @__PURE__ */ new Date()).toISOString(),
        originalData: message,
        ...this.parseBluetoothMessage(message)
      };
      await this.processMessage(processedMessage);
    } catch (error) {
      common_vendor.index.__f__("error", "at utils/messageHandler.js:58", "处理蓝牙消息失败:", error);
      this.handleError("BLUETOOTH_MESSAGE_ERROR", error, message);
    }
  }
  /**
   * 处理WiFi消息
   * @param {Object} message - WiFi消息
   */
  async handleWifiMessage(message) {
    try {
      const processedMessage = {
        source: "wifi",
        timestamp: (/* @__PURE__ */ new Date()).toISOString(),
        originalData: message,
        ...this.parseWifiMessage(message)
      };
      await this.processMessage(processedMessage);
    } catch (error) {
      common_vendor.index.__f__("error", "at utils/messageHandler.js:78", "处理WiFi消息失败:", error);
      this.handleError("WIFI_MESSAGE_ERROR", error, message);
    }
  }
  /**
   * 解析蓝牙消息
   * @param {Object} message - 原始蓝牙消息
   * @returns {Object} 解析后的消息
   */
  parseBluetoothMessage(message) {
    const { opcode, data } = message;
    switch (opcode) {
      case 36:
      case 37:
        return {
          type: "DISPENSE_COMPLETE",
          colorCode: data && data.length > 0 ? `0x${data[0].toString(16).padStart(2, "0").toUpperCase()}` : null,
          weight: data && data.length > 1 ? data[1] : 0,
          opcode
        };
      case 6:
        return {
          type: "BOWL_PLACED",
          opcode
        };
      case 38:
        return {
          type: "BLUETOOTH_COMMAND_SUCCESS",
          opcode
        };
      default:
        return {
          type: "UNKNOWN_BLUETOOTH_MESSAGE",
          opcode,
          data
        };
    }
  }
  /**
   * 解析WiFi消息
   * @param {Object} message - 原始WiFi消息
   * @returns {Object} 解析后的消息
   */
  parseWifiMessage(message) {
    const { command, isOnline, deviceCode } = message;
    if (command) {
      const commandHex = this.stringToHex(command);
      switch (commandHex) {
        case "2024":
        case "2025":
          return {
            type: "DISPENSE_COMPLETE",
            colorCode: message.colorCode,
            weight: message.weight,
            commandHex
          };
        case "06":
          return {
            type: "BOWL_PLACED",
            commandHex
          };
        default:
          return {
            type: "UNKNOWN_WIFI_COMMAND",
            commandHex,
            command
          };
      }
    }
    if (isOnline !== void 0) {
      return {
        type: "DEVICE_STATUS_UPDATE",
        isOnline: isOnline === "true",
        deviceCode
      };
    }
    return {
      type: "UNKNOWN_WIFI_MESSAGE",
      data: message
    };
  }
  /**
   * 处理消息
   * @param {Object} message - 处理后的消息
   */
  async processMessage(message) {
    this.messageQueue.push(message);
    this.addToHistory(message);
    if (!this.isProcessing) {
      await this.processMessageQueue();
    }
  }
  /**
   * 处理消息队列
   */
  async processMessageQueue() {
    this.isProcessing = true;
    while (this.messageQueue.length > 0) {
      const message = this.messageQueue.shift();
      try {
        await this.dispatchMessage(message);
      } catch (error) {
        common_vendor.index.__f__("error", "at utils/messageHandler.js:201", "分发消息失败:", error);
        this.handleError("MESSAGE_DISPATCH_ERROR", error, message);
      }
    }
    this.isProcessing = false;
  }
  /**
   * 分发消息到相应的处理器
   * @param {Object} message - 消息对象
   */
  async dispatchMessage(message) {
    const { type } = message;
    await this.updateGlobalState(message);
    switch (type) {
      case "DISPENSE_COMPLETE":
        await this.handleDispenseComplete(message);
        break;
      case "BOWL_PLACED":
        await this.handleBowlPlaced(message);
        break;
      case "BLUETOOTH_COMMAND_SUCCESS":
        await this.handleBluetoothCommandSuccess(message);
        break;
      case "DEVICE_STATUS_UPDATE":
        await this.handleDeviceStatusUpdate(message);
        break;
      default:
        common_vendor.index.__f__("warn", "at utils/messageHandler.js:238", "未知消息类型:", type);
    }
    this.notifyListeners(type, message);
  }
  /**
   * 处理出料完成消息
   * @param {Object} message - 消息对象
   */
  async handleDispenseComplete(message) {
    const { colorCode, weight, source } = message;
    if (colorCode && weight) {
      const app2 = getApp();
      const deviceCode = app2.globalData.connectedDeviceId;
      if (deviceCode) {
        const currentCommand = app2.globalData.currentCommand;
        const commandId = currentCommand ? currentCommand.backendId : null;
        common_vendor.index.__f__("log", "at utils/messageHandler.js:261", `🎯 处理出料完成: 颜色${colorCode}, 用量${weight}g, 后端指令ID: ${commandId}`);
        const consumptionData = {
          consumption: {
            [colorCode]: {
              amount: weight,
              colorCode
            }
          }
        };
        let dyeInfo = null;
        try {
          const dyeColorInfo = common_vendor.index.getStorageSync("dyeColorInfo");
          if (dyeColorInfo) {
            dyeInfo = {
              mode: dyeColorInfo.mode || "smart",
              formulaName: dyeColorInfo.name,
              colorHex: dyeColorInfo.hex,
              colorComponents: dyeColorInfo.colorComponents || dyeColorInfo.developerComponents
            };
            common_vendor.index.__f__("log", "at utils/messageHandler.js:285", "获取到调色信息:", dyeInfo);
          }
        } catch (e) {
          common_vendor.index.__f__("warn", "at utils/messageHandler.js:288", "获取调色信息失败:", e);
        }
        const success = await utils_dyeConsumptionManager.dyeConsumptionManager.recordConsumptionWithInfo(deviceCode, consumptionData, commandId, dyeInfo);
        if (success) {
          app2.updateDyeConsumption({
            currentUsage: { [colorCode]: weight },
            totalConsumption: app2.globalData.dyeConsumption.totalConsumption + weight
          });
          common_vendor.index.__f__("log", "at utils/messageHandler.js:301", `✅ ${source}染膏消耗记录成功: 颜色${colorCode}, 用量${weight}g, 后端指令ID: ${commandId}`);
        } else {
          common_vendor.index.__f__("error", "at utils/messageHandler.js:303", `❌ ${source}染膏消耗记录失败: 颜色${colorCode}, 用量${weight}g`);
        }
      }
    }
    const app = getApp();
    app.updateCommandExecutionDetail({
      progress: 100,
      currentStep: "出料完成",
      estimatedEndTime: (/* @__PURE__ */ new Date()).toISOString()
    });
  }
  /**
   * 处理已放碗消息
   * @param {Object} message - 消息对象
   */
  async handleBowlPlaced(message) {
    const app = getApp();
    app.updateCommandStatus();
    app.updateCommandExecutionDetail({
      currentStep: "已放碗",
      progress: 10
    });
  }
  /**
   * 处理蓝牙指令成功消息
   * @param {Object} message - 消息对象
   */
  async handleBluetoothCommandSuccess(message) {
    const app = getApp();
    app.setConnectionMethod("wifi");
    common_vendor.index.__f__("log", "at utils/messageHandler.js:341", "蓝牙指令下发成功，准备切换到WiFi连接");
  }
  /**
   * 处理设备状态更新消息
   * @param {Object} message - 消息对象
   */
  async handleDeviceStatusUpdate(message) {
    const { isOnline, deviceCode } = message;
    const app = getApp();
    app.updateDeviceStatusDetail({
      isOnline,
      lastHeartbeat: (/* @__PURE__ */ new Date()).toISOString(),
      connectionQuality: message.source === "bluetooth" ? 100 : 90
    });
  }
  /**
   * 更新全局状态
   * @param {Object} message - 消息对象
   */
  async updateGlobalState(message) {
    const app = getApp();
    app.updateDeviceStatusDetail({
      lastHeartbeat: (/* @__PURE__ */ new Date()).toISOString()
    });
    if (message.type !== "UNKNOWN_BLUETOOTH_MESSAGE" && message.type !== "UNKNOWN_WIFI_MESSAGE") {
      app.clearErrorState();
    }
  }
  /**
   * 通知监听器
   * @param {string} messageType - 消息类型
   * @param {Object} message - 消息对象
   */
  notifyListeners(messageType, message) {
    if (this.listeners.has(messageType)) {
      const handlers = this.listeners.get(messageType);
      handlers.forEach((handler) => {
        try {
          handler(message);
        } catch (error) {
          common_vendor.index.__f__("error", "at utils/messageHandler.js:389", "监听器处理失败:", error);
        }
      });
    }
  }
  /**
   * 处理错误
   * @param {string} errorType - 错误类型
   * @param {Error} error - 错误对象
   * @param {Object} originalMessage - 原始消息
   */
  handleError(errorType, error, originalMessage) {
    const app = getApp();
    app.setErrorState({
      errorType,
      errorMessage: error.message,
      errorCode: null
    });
    common_vendor.index.__f__("error", "at utils/messageHandler.js:409", `消息处理错误 [${errorType}]:`, error, originalMessage);
  }
  /**
   * 添加到历史记录
   * @param {Object} message - 消息对象
   */
  addToHistory(message) {
    this.messageHistory.push(message);
    if (this.messageHistory.length > this.maxHistorySize) {
      this.messageHistory.splice(0, this.messageHistory.length - this.maxHistorySize);
    }
  }
  /**
   * 获取消息历史
   * @param {number} limit - 限制数量
   * @returns {Array} 消息历史
   */
  getMessageHistory(limit = 50) {
    return this.messageHistory.slice(-limit);
  }
  /**
   * 清除消息历史
   */
  clearHistory() {
    this.messageHistory = [];
  }
  /**
   * 字符串转十六进制
   * @param {string} str - 字符串
   * @returns {string} 十六进制字符串
   */
  stringToHex(str) {
    let hex = "";
    for (let i = 0; i < str.length; i++) {
      hex += str.charCodeAt(i).toString(16).padStart(2, "0");
    }
    return hex;
  }
}
const messageHandler = new MessageHandler();
exports.messageHandler = messageHandler;
//# sourceMappingURL=../../.sourcemap/mp-weixin/utils/messageHandler.js.map
