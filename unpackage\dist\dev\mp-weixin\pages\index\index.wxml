<view class="content"><scroll-view class="color-category" scroll-x show-scrollbar="false" scroll-into-view="{{b}}"><view wx:for="{{a}}" wx:for-item="item" wx:key="c" class="{{['category-item', item.b && 'active']}}" id="{{item.d}}" bindtap="{{item.e}}" data-index="{{item.f}}" data-category="{{item.g}}">{{item.a}}</view></scroll-view><scroll-view class="color-images" scroll-x show-scrollbar="{{false}}" enhanced scroll-with-animation="{{true}}" enable-flex="{{true}}" scroll-left="{{d}}" bindscroll="{{e}}"><view wx:for="{{c}}" wx:for-item="item" wx:key="i" class="{{['color-image-item', item.h && 'active']}}" id="{{item.j}}" bindtap="{{item.k}}" data-index="{{item.l}}" data-category="{{item.m}}"><image wx:if="{{item.a}}" class="color-image-placeholder" src="{{item.b}}" mode="scaleToFill" lazy-load></image><view wx:else class="color-image-placeholder" style="{{'background-color:' + item.c}}"></view><view class="color-name"><view wx:if="{{item.d}}" class="color-name-split"><view class="color-name-chinese">{{item.e}}</view><view class="color-name-english">{{item.f}}</view></view><view wx:else class="color-name-single">{{item.g}}</view></view></view></scroll-view><view class="trend-title"><view class="trend-text">{{f}} <text class="subtitle">共{{g}}个颜色</text></view><view class="filter"><picker range="{{j}}" bindchange="{{k}}"><view class="filter"><image src="{{h}}" style="width:34rpx;height:34rpx;margin-top:10rpx;margin-right:5rpx" class="filter-icon-placeholder"></image><text style="margin-right:30rpx;margin-top:6rpx">{{i}}</text></view></picker></view></view><view class="color-grid"><view wx:for="{{l}}" wx:for-item="color" wx:key="f" class="color-grid-item" bindtap="{{color.g}}"><view class="color-circle-container"><image class="color-circle" src="{{color.a}}" mode="aspectFill" lazy-load></image></view><view class="color-name"><view wx:if="{{color.b}}" class="color-name-split"><view class="color-name-chinese">{{color.c}}</view><view class="color-name-english">{{color.d}}</view></view><view wx:else class="color-name-single">{{color.e}}</view></view></view></view></view>