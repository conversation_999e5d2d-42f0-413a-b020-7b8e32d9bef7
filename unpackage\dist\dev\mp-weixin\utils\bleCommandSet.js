"use strict";
const common_vendor = require("../common/vendor.js");
const BLE_CONSTANTS = {
  // 协议头定义
  PROTOCOL_HEADER: 170,
  // 协议起始字节
  PROTOCOL_FOOTER: 85,
  // 协议结束字节
  // 指令类型
  CMD_TYPE_CONTROL: 1,
  // 控制类指令
  CMD_TYPE_QUERY: 2,
  // 查询类指令
  // 响应码
  RESPONSE_SUCCESS: 0,
  // 操作成功
  RESPONSE_FAILURE: 1
  // 操作失败
};
const BLUETOOTH_COMMANDS = {
  // ****************** 操作指令码 ******************
  OPCODES: {
    // 基础控制指令
    LIGHT_ON: 1,
    // 开灯
    LIGHT_OFF: 2,
    // 关灯
    DEVICE_RESET: 3,
    // 设备重启
    CHUNK_SUCCESS: 7,
    // 蓝牙下发分片配置成功
    CHUNK_ERROR: 8,
    // 蓝牙下发分片配置失败
    BLUETOOTH_SENDS_END: 9,
    // 蓝牙下发完成
    BLUETOOTH_CONNECT_SUCCESS: 16,
    CALIBRATION: 18,
    // 电子秤发送砝码
    ALREADY_FLAT: 19,
    // 电子秤已经放平
    CALIBRATION_RESPONSE: 20,
    // 电子秤校准响应
    ZERO_CALIBRATION_CONFIG: 21,
    // 开始电子秤校验指令
    // 染发机专用指令
    BOWL_ALREADY_PLACED: 5,
    // 已放碗指令
    START_DISPENSE: 32,
    // 智能模式
    PAUSE_DISPENSE: 33,
    // 暂停出料
    CANCEL_DISCHARGE: 34,
    // 取消出料
    SET_DISPENSE_SPEED: 35,
    // 设置出料速度
    PRE_END_DISPENSE: 36,
    /// 上一次出料完成	
    END_DISPENSE: 37,
    // 指令全部执行完毕
    BLUETOOTH_SENDS_COMMANDS: 38,
    // 蓝牙下发
    CONTINUE_DISCHARGING: 39,
    // 继续出料
    MASTER_MODE: 40,
    // 大师模式
    STOP_DISPENSE: 41
    // 停止出料
  },
  // 颜色仓位代码
  HAIRDYECOLOR: {
    HYDROGENPEROXIDE3PERCENT: 10,
    // 双氧乳3%含量  A仓位
    HYDROGENPEROXIDE12PERCENT: 11,
    // 双氧乳12%含量 B仓位
    GRAY: 48,
    // 灰色0.1  	1号仓位
    GREEN: 49,
    // 绿色G9.33  	2号仓位
    YELLOW: 50,
    // 黄色Y9.33 	3号仓位
    ORANGE: 51,
    // 橙色6.64 	4号仓位
    RED: 52,
    // 红色0.6		5号仓位
    PURPLE: 53,
    // 紫色0.2	6号仓位
    BROWN: 54,
    // 棕色5.52	7号仓位
    BLUE: 55,
    // 蓝色B8.11	8号仓位
    BLACK: 56,
    // 基色（自然黑）3.0		9号仓位
    FADED_BLEACHED: 57,
    // 0/00（退色）	10号仓位
    BLEACHED: 64,
    // 极浅灰金色12.11 (用bleached字段对应) 11号仓位
    SILVER: 65,
    // 梦幻紫V10.21 (用silver字段对应) 12号仓位
    GOLD: 66,
    // 浅棕色5.0 (用gold字段对应) 13号仓位
    LINEN: 67
    // 深灰亚麻色7.11 (原字段) 14号仓位
  },
  // ****************** 指令生成方法 ******************
  COMMANDS: {
    /**
     * 生成基础控制指令
     * @param {number} opcode - 操作码 
     * @param {Array} params - 参数数组
     * @returns {Buffer} 完整的指令缓冲区
     */
    _generateCommand: function(opcode, params = []) {
      const dataWithoutCRC = [
        BLE_CONSTANTS.CMD_TYPE_CONTROL,
        opcode,
        ...params
      ];
      const crcBytes = BLUETOOTH_COMMANDS.utils.crc16Modbus(dataWithoutCRC);
      const fullData = [
        BLE_CONSTANTS.PROTOCOL_HEADER,
        ...dataWithoutCRC,
        ...crcBytes,
        BLE_CONSTANTS.PROTOCOL_FOOTER
      ];
      common_vendor.index.__f__("log", "at utils/bleCommandSet.js:97", "数据部分", dataWithoutCRC);
      common_vendor.index.__f__("log", "at utils/bleCommandSet.js:100", "CRC16部分", crcBytes);
      common_vendor.index.__f__("log", "at utils/bleCommandSet.js:103", "返回完整数据包", fullData);
      return fullData;
    },
    /**
     * 大师模式暂停
     * @returns {Object} 指令对象
     */
    masterStop: function() {
      const cmdBuffer = BLUETOOTH_COMMANDS.COMMANDS._generateCommand(
        BLUETOOTH_COMMANDS.OPCODES.STOP_DISPENSE
      );
      return {
        value: cmdBuffer,
        description: "大师模式暂停指令",
        voicePrompt: "STOP_MASTER_DISPENSE",
        requiresResponse: true
      };
    },
    /**
     * 蓝牙下发结束
     */
    bluetoothSendEnd: function() {
      const cmdBuffer = BLUETOOTH_COMMANDS.COMMANDS._generateCommand(
        BLUETOOTH_COMMANDS.OPCODES.BLUETOOTH_SENDS_END
      );
      return {
        value: cmdBuffer,
        description: `发送WiFi配置 (TLV格式)`,
        requiresResponse: true
      };
    },
    /**
     * 蓝牙连接成功发送
     */
    bluetoothConnectSuccess: function() {
      const cmdBuffer = BLUETOOTH_COMMANDS.COMMANDS._generateCommand(
        BLUETOOTH_COMMANDS.OPCODES.BLUETOOTH_CONNECT_SUCCESS
      );
      return {
        value: cmdBuffer,
        description: `蓝牙连接成功`,
        requiresResponse: true
      };
    },
    /**
     * 蓝牙下发指令(原来)
     * @param {Object} params - wifi参数对象 
     * @param {string} params.SSID - WiFi名称
     * @param {string} params.password - WiFi密码
     * @returns {Object} 指令对象
     */
    bluetoothSends: function(len) {
      const cmdBuffer = BLUETOOTH_COMMANDS.COMMANDS._generateCommand(
        BLUETOOTH_COMMANDS.OPCODES.BLUETOOTH_SENDS_COMMANDS,
        [len]
      );
      return {
        value: cmdBuffer,
        description: `发送WiFi配置 (TLV格式)`,
        requiresResponse: true
      };
    },
    /**
     * 根据蓝牙下发参数来生成指令
     * @param {参数数组} params 
     */
    generateInstructionFromParams: function(params, len) {
      const cmdBuffer = BLUETOOTH_COMMANDS.COMMANDS._generateCommand(
        BLUETOOTH_COMMANDS.OPCODES.BLUETOOTH_SENDS_COMMANDS,
        [len, ...params]
      );
      return {
        value: cmdBuffer,
        description: `指令对象`,
        requiresResponse: true
      };
    },
    // 开始电子秤校验指令
    startZeroCalibration: function() {
      BLUETOOTH_COMMANDS.utils.intToBytes(grams, 2);
      const cmdBuffer = BLUETOOTH_COMMANDS.COMMANDS._generateCommand(
        BLUETOOTH_COMMANDS.OPCODES.ZERO_CALIBRATION_CONFIG
      );
      return {
        value: cmdBuffer,
        description: `开始电子秤校验`,
        voicePrompt: "ZERO_CALIBRATION_CONFIG",
        requiresResponse: true
      };
    },
    /**
     * @param {Object} grams 放入托盘砝码的克数
     */
    electronicScaleCalibration: function(grams2) {
      const gramBytes = BLUETOOTH_COMMANDS.utils.intToBytes(grams2, 2);
      const cmdBuffer = BLUETOOTH_COMMANDS.COMMANDS._generateCommand(
        BLUETOOTH_COMMANDS.OPCODES.CALIBRATION,
        gramBytes
      );
      return {
        value: cmdBuffer,
        description: `$电子校验秤需要放入的砝码: ${grams2}克`,
        voicePrompt: "PUT_BOWL",
        requiresResponse: true
      };
    },
    /**
     * 开始出料指令
     * @param {number} grams - 出料克数
     * @returns {Object} 指令对象
     * @param  {model} 出料模式 
     */
    startDispense: function(grams2, colorCode = null, model) {
      if (grams2 <= 0)
        throw new Error("克数必须大于0");
      const gramBytes = BLUETOOTH_COMMANDS.utils.intToBytes(grams2, 2);
      common_vendor.index.__f__("log", "at utils/bleCommandSet.js:253", "克重小端序", ...gramBytes);
      const params = [];
      if (colorCode !== null) {
        params.push(colorCode);
      }
      params.push(...gramBytes);
      let modleCode;
      if (model === "master") {
        modleCode = BLUETOOTH_COMMANDS.OPCODES.MASTER_MODE;
      } else if (model == "smart") {
        modleCode = BLUETOOTH_COMMANDS.OPCODES.START_DISPENSE;
      }
      const cmdBuffer = BLUETOOTH_COMMANDS.COMMANDS._generateCommand(
        // BLUETOOTH_COMMANDS.OPCODES.START_DISPENSE,
        modleCode,
        params
      );
      return {
        value: cmdBuffer,
        colorCode,
        weight: grams2,
        description: `${colorCode == null ? void 0 : colorCode.toString(16)}开始出料: ${grams2}克`,
        voicePrompt: "PUT_BOWL",
        requiresResponse: true
      };
    },
    /**
     * 取消出料指令
     * @returns {Object} 指令对象
     */
    stopDispense: function() {
      const cmdBuffer = BLUETOOTH_COMMANDS.COMMANDS._generateCommand(
        BLUETOOTH_COMMANDS.OPCODES.CANCEL_DISCHARGE
      );
      return {
        value: cmdBuffer,
        description: "停止出料",
        voicePrompt: "STOP_DISPENSE",
        requiresResponse: true
      };
    },
    /**
     * 继续出料指令
     * @returns {Object} 指令对象
     */
    continue_discharging: function() {
      const cmdBuffer = BLUETOOTH_COMMANDS.COMMANDS._generateCommand(
        BLUETOOTH_COMMANDS.OPCODES.CONTINUE_DISCHARGING
      );
      return {
        value: cmdBuffer,
        description: "继续出料",
        voicePrompt: "CONTINUE_DISCHARGING",
        requiresResponse: true
      };
    },
    /**
     * 暂停出料指令
     * @returns {Object} 指令对象
     */
    pauseDispense: function() {
      const cmdBuffer = BLUETOOTH_COMMANDS.COMMANDS._generateCommand(
        BLUETOOTH_COMMANDS.OPCODES.PAUSE_DISPENSE
      );
      return {
        value: cmdBuffer,
        description: "暂停出料",
        voicePrompt: "PAUSE_DISPENSE",
        requiresResponse: true
      };
    },
    /**
     * 全部出料完成
     */
    dischargingCompleted: function() {
      const cmdBuffer = BLUETOOTH_COMMANDS.COMMANDS._generateCommand(
        BLUETOOTH_COMMANDS.OPCODES.END_DISPENSE
      );
      return {
        value: cmdBuffer,
        description: "全部出料完成",
        voicePrompt: "PAUSE_DISPENSE",
        requiresResponse: true
      };
    }
  },
  // ****************** 工具方法 ******************
  utils: {
    // 计算出料指令
    calculateAdjustedTarget: function(target) {
      const adjustmentTable = [
        { target: 200, adjustment: 6 },
        { target: 100, adjustment: 3 },
        { target: 90, adjustment: 3 },
        { target: 70, adjustment: 2 },
        { target: 50, adjustment: 1 },
        { target: 30, adjustment: 1 },
        { target: 20, adjustment: 0 },
        { target: 10, adjustment: 1 },
        { target: 5, adjustment: 1 },
        { target: 2, adjustment: 0.5 },
        { target: 1, adjustment: 0 },
        { target: 0.1, adjustment: 0 }
      ];
      const exactMatch = adjustmentTable.find((item) => item.target === target);
      if (exactMatch)
        return target - exactMatch.adjustment;
      let lower = adjustmentTable[0], upper = adjustmentTable[0];
      for (const item of adjustmentTable) {
        if (item.target <= target && item.target > lower.target)
          lower = item;
        if (item.target >= target && item.target < upper.target)
          upper = item;
      }
      if (lower === upper) {
        return target - lower.adjustment;
      } else {
        const ratio = (target - lower.target) / (upper.target - lower.target);
        const interpolatedAdjustment = lower.adjustment + ratio * (upper.adjustment - lower.adjustment);
        return target - interpolatedAdjustment;
      }
    },
    /**
     * 将染膏
     * @param {number} num - 要转换的真实克重
     * @param {number} byteCount - 字节数
     * @param {boolean} [littleEndian=true] - 是否小端序 ,暂时都是小程序端的
     * @returns {Array} 字节数组
     */
    intToBytes: function(num, byteCount, littleEndian = true) {
      const bytes = [];
      for (let i = 0; i < byteCount; i++) {
        const shift = i * 8;
        bytes.push(num * 10 >> shift & 255);
      }
      return bytes;
    },
    /**
     * 将大小端序解析成克数
     * @param {*} bytes 
     * @param {*} littleEndian 
     * @returns 
     */
    bytesToInt: function(bytes, littleEndian = true) {
      let num = 0;
      if (littleEndian) {
        for (let i = 0; i < bytes.length; i++) {
          num += bytes[i] << i * 8;
        }
      } else {
        for (let i = 0; i < bytes.length; i++) {
          num += bytes[i] << (bytes.length - 1 - i) * 8;
        }
      }
      return num;
    },
    /**
     * CRC16 Modbus校验计算
     * @param {Array} data - 数据数组
     * @returns {Array} [高位字节, 低位字节]
     */
    crc16Modbus: function(data) {
      let crc = 65535;
      for (let i = 0; i < data.length; i++) {
        crc ^= data[i];
        for (let j = 0; j < 8; j++) {
          if (crc & 1) {
            crc = crc >> 1 ^ 40961;
          } else {
            crc = crc >> 1;
          }
        }
      }
      return [
        crc & 255,
        // 低字节
        crc >> 8 & 255
        // 高字节
      ];
    },
    /**
     * 解析设备响应
     * @param {Buffer|string} response - 设备返回数据（完整帧），可以是Buffer或十六进制字符串
     * @returns {Object} 解析结果
     * @throws {Error} 当数据格式错误时抛出异常
     */
    parseResponse: function(response) {
      if (!(response instanceof ArrayBuffer)) {
        throw new Error("响应数据必须是ArrayBuffer");
      }
      const bytes = new Uint8Array(response);
      if (bytes.length === 0) {
        throw new Error("响应数据为空");
      }
      if (bytes.length < 5) {
        throw new Error(`响应数据过短（${bytes.length}字节），至少需要5字节`);
      }
      const HEADER = 170;
      const FOOTER = 85;
      if (bytes[0] !== HEADER) {
        throw new Error(
          `无效协议头（预期0x${HEADER.toString(16)}，实际0x${bytes[0].toString(16)}）`
        );
      }
      if (bytes[bytes.length - 1] !== FOOTER) {
        throw new Error(
          `无效协议尾（预期0x${FOOTER.toString(16)}，实际0x${bytes[bytes.length - 1].toString(16)}）`
        );
      }
      const dataWithoutHeadAndTail = bytes.slice(1, -1);
      const opcode = dataWithoutHeadAndTail[1];
      const receivedCrc = dataWithoutHeadAndTail.slice(-2);
      const dataForCrc = dataWithoutHeadAndTail.slice(0, -2);
      const calculatedCrc = this.crc16Modbus(dataForCrc);
      if (calculatedCrc[0] !== receivedCrc[0] || calculatedCrc[1] !== receivedCrc[1]) {
        throw new Error(
          `CRC校验失败（计算值: 0x${calculatedCrc[0].toString(16)}${calculatedCrc[1].toString(16)}，接收值: 0x${receivedCrc[0].toString(16)}${receivedCrc[1].toString(16)}）`
        );
      }
      return {
        header: bytes[0],
        opcode,
        footer: bytes[bytes.length - 1],
        crcValid: true,
        isValid: true,
        description: this.getCommandDescription(opcode),
        details: {
          status: "成功"
        },
        // 辅助方法
        getPayloadAsInt: function(littleEndian = true) {
          if (dataForCrc.length < 2)
            return 0;
          if (littleEndian) {
            return dataForCrc[1] << 8 | dataForCrc[0];
          } else {
            return dataForCrc[0] << 8 | dataForCrc[1];
          }
        },
        getPayloadAsString: function() {
          return String.fromCharCode.apply(null, dataForCrc);
        },
        // 新增方法：获取原始payload字节数组
        getPayloadBytes: function() {
          return Array.from(dataForCrc);
        }
      };
    },
    // 工具函数：字符串转 ArrayBuffer
    stringToArrayBuffer: function(str) {
      const encoder = new TextEncoder();
      return encoder.encode(str).buffer;
    },
    arrayBufferToHex: function(buffer) {
      if (!buffer)
        return "";
      const hexArr = Array.prototype.map.call(
        new Uint8Array(buffer),
        (bit) => ("00" + bit.toString(16)).slice(-2)
      );
      return hexArr.join("");
    },
    // 辅助函数：获取指令描述
    getCommandDescription: function(opcode) {
      if (opcode === BLUETOOTH_COMMANDS.OPCODES.PRE_END_DISPENSE) {
        return "出料预结束指令";
      }
      if (opcode === BLUETOOTH_COMMANDS.OPCODES.END_DISPENSE) {
        return "出料结束指令";
      }
      return "未知指令";
    }
  }
};
function getCommand(commandName, ...args) {
  try {
    if (!BLUETOOTH_COMMANDS.COMMANDS[commandName]) {
      throw new Error(`未知指令: ${commandName}`);
    }
    const commandFunc = BLUETOOTH_COMMANDS.COMMANDS[commandName];
    const result = commandFunc(...args);
    return result;
  } catch (error) {
    common_vendor.index.__f__("error", "at utils/bleCommandSet.js:630", `执行指令 ${commandName} 时发生错误:`);
    common_vendor.index.__f__("error", "at utils/bleCommandSet.js:631", "错误详情:", error);
    common_vendor.index.__f__("error", "at utils/bleCommandSet.js:632", "调用栈:", error.stack);
    throw new Error(`指令执行失败: ${error.message}`);
  }
}
exports.BLUETOOTH_COMMANDS = BLUETOOTH_COMMANDS;
exports.getCommand = getCommand;
//# sourceMappingURL=../../.sourcemap/mp-weixin/utils/bleCommandSet.js.map
