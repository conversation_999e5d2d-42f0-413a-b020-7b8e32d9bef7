<template>
	<view class="confirm-container">
		<!-- 加载状态 -->
		<view v-if="dataLoading" class="loading-container">
			<view class="loading-content">
				<view class="loading-spinner"></view>
				<text class="loading-text">正在加载订单信息...</text>
			</view>
		</view>

		<!-- 主要内容 -->
		<view v-else>
			<!-- 收货地址 -->
			<view class="address-section" @click="selectAddress">
				<view class="address-info" v-if="address">
					<view class="address-name-phone">
						<text class="name">{{address.name}}</text>
						<text class="phone">{{address.phone}}</text>
					</view>
					<view class="address-detail">{{address.province}} {{address.city}} {{address.district}} {{address.detail}}</view>
				</view>
				<view class="no-address" v-else>
					<text>无地址</text>
				</view>
				<view class="address-arrow">></view>
			</view>
		
		<!-- 商品列表 -->
		<view class="goods-list">
			<view class="shop-name">{{orderInfo.shopName || '官方直营'}}</view>
			<view class="goods-item" v-for="(item, index) in orderInfo.products" :key="index">
				<image class="goods-image" :src="baseUrl + getProductImage(item)" mode="aspectFill"></image>
				<view class="goods-info">
					<view class="goods-main-info">
						<text class="goods-name">{{item.productName || item.name}}</text>
						<text class="goods-spec">{{item.skuName || '默认规格'}}</text>
					</view>
					<view class="goods-price-qty">
						<view class="price-section">
							<text class="goods-price">¥{{item.price}}</text>
							<text class="price-label">单价</text>
						</view>
						<view class="qty-section">
							<text class="goods-qty">x{{item.quantity}}</text>
							<text class="qty-label">数量</text>
						</view>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 配送方式 -->
		<!-- <view class="delivery-section">
			<view class="section-item">
				<text class="item-label">配送方式</text>
				<view class="item-value">
					<text>快递配送</text>
					<text class="item-arrow">></text>
				</view>
			</view>
		</view> -->
		
		<!-- 优惠券 -->
		<!-- <view class="coupon-section">
			<view class="section-item">
				<text class="item-label">优惠券</text>
				<view class="item-value">
					<text class="coupon-value" v-if="coupon">-¥{{coupon.value}}</text>
					<text v-else>暂无可用</text>
					<text class="item-arrow">></text>
				</view>
			</view>
		</view> -->
		
		<!-- 订单备注 -->
		<!-- <view class="remark-section">
			<view class="section-item">
				<text class="item-label">订单备注</text>
				<input class="remark-input" placeholder="选填，请先和商家协商一致" v-model="remark" />
			</view>
		</view> -->
		
		<!-- 订单金额 -->
		<view class="amount-section">
			<view class="amount-item">
				<text>商品金额</text>
				<text>¥{{goodsTotal.toFixed(2)}}</text>
			</view>
			<view class="amount-item">
				<text>运费</text>
				<text>¥{{orderInfo.freight}}</text>
			</view>
			<view class="amount-item" v-if="coupon">
				<text>优惠券</text>
				<text>-¥{{coupon.value}}</text>
			</view>
			<view class="amount-total">
				<text>实付款</text>
				<text class="total-price">¥{{calculateTotal()}}</text>
			</view>
		</view>
		
			<!-- 底部结算栏 -->
			<view class="bottom-bar">
				<view class="price-box">
					<text>实付款：</text>
					<text class="final-price">¥{{calculateTotal()}}</text>
				</view>
				<view class="submit-btn" @click="submitOrder">提交订单</view>
			</view>
		</view>
	</view>
</template>

<script>
import { apiService } from '@/utils/request.js';

export default {
	data() {
		return {
			// baseUrl : "http://127.0.0.1:6549/api",
			baseUrl : "https://www.narenqiqige.com/api",
			address: null,
			orderInfo: {
				type: 'cart', // cart: 购物车结算, buyNow: 立即购买
				shopName: '官方直营',
				totalPrice: 0,
				freight: 0,
				products: []
			},
			coupon: null,
			remark: '',
			loading: false,
			dataLoading: true
		}
	},
	computed: {
		// 计算商品总价
		goodsTotal() {
			return this.orderInfo.products.reduce((total, item) => {
				return total + (item.price * item.quantity);
			}, 0);
		}
	},
	onLoad(options) {
		console.log('订单确认页参数:', options);

		// 处理传入的订单数据
		if (options.data) {
			try {
				const orderData = JSON.parse(decodeURIComponent(options.data));
				this.orderInfo = {
					...this.orderInfo,
					...orderData
				};
			} catch (error) {
				console.error('解析订单数据失败:', error);
			}
		}

		// 设置订单类型
		if (options.type) {
			this.orderInfo.type = options.type;
		}

		this.initOrderData();
	},
	onShow() {
		// 检查是否有新选择的地址
		const selectedAddress = uni.getStorageSync('selectedAddress');
		if (selectedAddress) {
			this.address = selectedAddress;
			uni.removeStorageSync('selectedAddress');
			// 重新计算运费
			this.calculateFreight();
		}
	},
	
	// 导航栏按钮点击事件
	onNavigationBarButtonTap(e) {
		if (e.index === 0) { // 返回按钮
			this.goBack();
		}
	},
	methods: {
		// 返回上一页
		goBack() {
			uni.navigateBack({
				delta: 1
			});
		},
		
		// 初始化订单数据
		async initOrderData() {
			this.dataLoading = true;

			try {
				// 先加载地址和商品数据
				await Promise.all([
					this.loadDefaultAddress(),
					this.loadOrderProducts()
				]);

				// 地址和商品数据加载完成后计算运费
				await this.calculateFreight();
			} catch (error) {
				console.error('初始化订单数据失败:', error);
				uni.showToast({
					title: '加载订单信息失败',
					icon: 'none'
				});
			} finally {
				this.dataLoading = false;
			}
		},

		// 加载订单商品数据
		async loadOrderProducts() {
			// 如果已经有商品数据，跳过
			if (this.orderInfo.products && this.orderInfo.products.length > 0) {
				return;
			}

			try {
				if (this.orderInfo.type === 'cart') {
					// 从购物车获取选中的商品
					const response = await apiService.mall.cart.list();
					if (response.code === 200) {
						const cartItems = response.data || [];
						const selectedItems = cartItems.filter(item => item.checked === 1);

						this.orderInfo.products = selectedItems.map(item => ({
							id: item.productId,
							name: item.productName,
							price: item.price,
							quantity: item.quantity,
							skuId: item.skuId,
							skuName: item.skuName,
							images: item.imageUrl,
							cartId: item.id
						}));
					}
				}
				// 如果是立即购买，商品数据应该已经通过参数传入
			} catch (error) {
				console.error('获取商品数据失败:', error);
				// 如果API失败，使用模拟数据
				this.loadMockProducts();
			}
		},

		// 加载模拟商品数据（当API失败时使用）
		loadMockProducts() {
			this.orderInfo.products = [
				{
					id: 1,
					name: 'Xiaomi 13 Ultra',
					price: 5999,
					quantity: 1,
					skuName: '黑色, 512GB',
					images: '/static/c1.png'
				},
				{
					id: 2,
					name: 'iPhone 15 Pro',
					price: 8999,
					quantity: 2,
					skuName: '深空黑色, 256GB',
					images: '/static/c2.png'
				}
			];
		},

		// 计算运费
		async calculateFreight() {
			try {
				if (!this.address) {
					this.orderInfo.freight = 0;
					return;
				}

				// 尝试调用运费计算API
				try {
					const response = await apiService.mall.freight.calculate({
						addressId: this.address.id,
						products: this.orderInfo.products.map(item => ({
							productId: item.id,
							quantity: item.quantity
						}))
					});

					if (response.code === 200) {
						this.orderInfo.freight = response.data.freight || 0;
						return;
					}
				} catch (apiError) {
					console.log('运费API暂不可用，使用默认计算逻辑');
				}

				// API不可用时使用默认运费逻辑
				this.calculateDefaultFreight();
			} catch (error) {
				console.error('计算运费失败:', error);
				this.calculateDefaultFreight();
			}
		},

		// 默认运费计算逻辑
		calculateDefaultFreight() {
			const goodsTotal = this.goodsTotal;

			// 运费计算规则：
			// 1. 满99免运费
			// 2. 不满99的，按重量和距离计算，这里简化为固定10元
			if (goodsTotal >= 99) {
				this.orderInfo.freight = 0;
			} else {
				this.orderInfo.freight = 10;
			}
		},

		// 加载默认地址
		async loadDefaultAddress() {
			try {
				const response = await apiService.mall.address.list();
				if (response.code === 200) {
					const addresses = response.data || [];
					// 找到默认地址或第一个地址
					this.address = addresses.find(addr => addr.isDefault) || addresses[0] || null;
				} else {
					// API失败时使用模拟地址
					this.address = {
						id: 1,
						name: '张三',
						phone: '138****8888',
						province: '广东省',
						city: '深圳市',
						district: '南山区',
						detail: '科技园南区深南大道10000号',
						isDefault: true
					};
				}
			} catch (error) {
				console.error('获取地址失败:', error);
				// 异常时使用模拟地址
				this.address = {
					id: 1,
					name: '张三',
					phone: '138****8888',
					province: '广东省',
					city: '深圳市',
					district: '南山区',
					detail: '科技园南区深南大道10000号',
					isDefault: true
				};
			}
		},

		// 获取商品图片
		getProductImage(product) {
			if (!product.images) return '';
			if (typeof product.images === 'string') {
				const imageArray = product.images.split(',');
				return imageArray[0] || '';
			}
			return product.images[0] || '';
		},

		selectAddress() {
			// 导航到地址列表页面
			uni.navigateTo({
				url: '/pages/address/list?from=order'
			});
		},

		calculateTotal() {
			let total = this.goodsTotal + this.orderInfo.freight;
			if (this.coupon) {
				total -= this.coupon.value;
			}
			return total.toFixed(2);
		},

		async submitOrder() {
			if (!this.address) {
				uni.showToast({
					title: '请选择收货地址',
					icon: 'none'
				});
				return;
			}

			if (this.loading) return;

			this.loading = true;
			uni.showLoading({
				title: '正在提交...'
			});

			try {
				// 构建订单数据
				const orderData = {
					addressId: this.address.id,
					products: this.orderInfo.products.map(item => ({
						productId: item.id,
						quantity: item.quantity,
						skuId: item.skuId || null
					})),
					remark: this.remark,
					couponId: this.coupon ? this.coupon.id : null
				};

				const response = await apiService.mall.orders.create(orderData);

				if (response.code === 200) {
					uni.hideLoading();
					uni.showToast({
						title: '下单成功',
						icon: 'success'
					});

					// 跳转到订单成功页面，传递订单ID和订单号
					setTimeout(() => {
						const params = [];
						if (response.data.id) {
							params.push('id=' + response.data.id);
						}
						if (response.data.orderNo) {
							params.push('orderNo=' + response.data.orderNo);
						}

						const url = '/pages/order/success' + (params.length > 0 ? '?' + params.join('&') : '');
						uni.redirectTo({
							url: url
						});
					}, 1500);
				} else {
					uni.hideLoading();
					uni.showToast({
						title: response.message || '下单失败',
						icon: 'none'
					});
				}
			} catch (error) {
				console.error('提交订单异常:', error);
				uni.hideLoading();
				uni.showToast({
					title: '网络异常，请重试',
					icon: 'none'
				});
			} finally {
				this.loading = false;
			}
		}
	}
}
</script>

<style lang="scss">
.confirm-container {
	min-height: 100vh;
	background-color: #f5f5f5;
	padding-bottom: 120rpx;
	width: 100%;
	box-sizing: border-box;
}

.loading-container {
	display: flex;
	justify-content: center;
	align-items: center;
	min-height: 100vh;
	background-color: #f5f5f5;
}

.loading-content {
	display: flex;
	flex-direction: column;
	align-items: center;
}

.loading-spinner {
	width: 60rpx;
	height: 60rpx;
	border: 4rpx solid #f0f0f0;
	border-top: 4rpx solid #f44336;
	border-radius: 50%;
	animation: spin 1s linear infinite;
	margin-bottom: 20rpx;
}

@keyframes spin {
	0% { transform: rotate(0deg); }
	100% { transform: rotate(360deg); }
}

.loading-text {
	font-size: 28rpx;
	color: #666666;
}

.address-section {
	background-color: #ffffff;
	padding: 25rpx 30rpx;
	margin: 0 0 2rpx 0;
	display: flex;
	align-items: center;
	width: 100%;
	box-sizing: border-box;
}

.address-info {
	flex: 1;
}

.address-name-phone {
	margin-bottom: 10rpx;
}

.name {
	font-size: 30rpx;
	font-weight: bold;
	margin-right: 20rpx;
}

.phone {
	font-size: 28rpx;
	color: #666666;
}

.address-detail {
	font-size: 28rpx;
	color: #333333;
}

.no-address {
	flex: 1;
	font-size: 30rpx;
	color: #666666;
	text-align: center;
}

.address-arrow {
	font-size: 24rpx;
	color: #999999;
	margin-left: 20rpx;
}

.goods-list {
	background-color: #ffffff;
	margin: 0 0 2rpx 0;
	padding: 30rpx;
	width: 100%;
	box-sizing: border-box;
	overflow: hidden; /* 防止内容溢出 */
}

.shop-name {
	font-size: 28rpx;
	font-weight: bold;
	margin-bottom: 20rpx;
}

.goods-item {
	display: flex;
	margin-bottom: 20rpx;
	width: 100%;
	align-items: flex-start;
	box-sizing: border-box;
	padding: 15rpx 0;
	gap: 15rpx; /* 增加间距控制 */
}

.goods-image {
	width: 120rpx;
	height: 120rpx;
	border-radius: 8rpx;
	flex-shrink: 0;
}

.goods-info {
	flex: 1;
	display: flex;
	flex-direction: column;
	justify-content: space-between;
	min-height: 120rpx;
	width: 0; /* 强制flex子元素重新计算宽度 */
	box-sizing: border-box;
	padding-right: 0; /* 移除右侧间距，最大化利用空间 */
}

.goods-main-info {
	flex: 1;
	margin-bottom: 15rpx;
}

.goods-name {
	font-size: 30rpx;
	margin-bottom: 8rpx;
	line-height: 1.4;
	word-break: break-all;
	overflow-wrap: break-word;
	width: 100%;
	font-weight: 500;
	color: #333333;
}

.goods-spec {
	font-size: 24rpx;
	color: #999999;
	line-height: 1.3;
	word-break: break-all;
	overflow-wrap: break-word;
	width: 100%;
}

.goods-price-qty {
	display: flex;
	justify-content: space-between;
	align-items: flex-end;
	width: 100%;
	box-sizing: border-box;
	gap: 15rpx; /* 减少间距以充分利用空间 */
}

.price-section, .qty-section {
	display: flex;
	flex-direction: column;
	align-items: center;
	flex: 1;
	padding: 10rpx 8rpx;
	background-color: #f8f9fa;
	border-radius: 6rpx;
	min-width: 0; /* 允许flex子元素收缩 */
}

.goods-price {
	font-size: 32rpx;
	color: #f44336;
	font-weight: bold;
	margin-bottom: 4rpx;
	white-space: nowrap;
}

.goods-qty {
	font-size: 28rpx;
	color: #333333;
	font-weight: 500;
	margin-bottom: 4rpx;
	white-space: nowrap;
}

.price-label, .qty-label {
	font-size: 20rpx;
	color: #999999;
	white-space: nowrap;
}

.delivery-section, .coupon-section, .remark-section {
	background-color: #ffffff;
	margin: 0 0 2rpx 0;
	width: 100%;
	box-sizing: border-box;
}

.section-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 25rpx 30rpx;
	font-size: 28rpx;
}

.item-label {
	color: #333333;
}

.item-value {
	color: #666666;
	display: flex;
	align-items: center;
}

.item-arrow {
	margin-left: 10rpx;
	color: #999999;
	font-size: 24rpx;
}

.coupon-value {
	color: #f44336;
}

.remark-input {
	text-align: right;
	width: 70%;
}

.amount-section {
	background-color: #ffffff;
	padding: 30rpx;
	margin: 0 0 2rpx 0;
	width: 100%;
	box-sizing: border-box;
}

.amount-item {
	display: flex;
	justify-content: space-between;
	margin-bottom: 20rpx;
	font-size: 26rpx;
	color: #666666;
}

.amount-total {
	display: flex;
	justify-content: space-between;
	padding-top: 20rpx;
	border-top: 1rpx solid #f0f0f0;
}

.total-price {
	font-size: 32rpx;
	font-weight: bold;
	color: #f44336;
}

.bottom-bar {
	position: fixed;
	left: 0;
	right: 0;
	bottom: 0;
	height: 100rpx;
	background-color: #ffffff;
	display: flex;
	align-items: center;
	box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.price-box {
	flex: 1;
	display: flex;
	align-items: center;
	padding-left: 30rpx;
	font-size: 28rpx;
}

.final-price {
	color: #f44336;
	font-size: 36rpx;
	font-weight: bold;
}

.submit-btn {
	width: 240rpx;
	height: 100%;
	background-color: #f44336;
	color: #ffffff;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 30rpx;
}

/* 响应式设计优化 */
@media screen and (max-width: 750rpx) {
	.goods-image {
		width: 100rpx;
		height: 100rpx;
	}

	.goods-info {
		min-height: 100rpx;
		padding-right: 0;
	}

	.goods-name {
		font-size: 28rpx;
	}

	.goods-price {
		font-size: 28rpx;
	}

	.goods-qty {
		font-size: 24rpx;
	}

	.price-section, .qty-section {
		padding: 6rpx 8rpx;
		gap: 15rpx;
	}

	.price-label, .qty-label {
		font-size: 18rpx;
	}
}

@media screen and (min-width: 751rpx) {
	.goods-info {
		padding-right: 0; /* 在较大屏幕上也移除右侧内边距以充分利用空间 */
	}

	.goods-price-qty {
		gap: 18rpx;
	}

	.price-section, .qty-section {
		padding: 12rpx 10rpx;
	}
}

/* 整体容器样式 */
.confirm-container {
	background-color: #f5f5f5;
	min-height: 100vh;
	padding-bottom: 120rpx;
	box-sizing: border-box;
	width: 100%;
	display: flex;
	flex-direction: column;
}
</style>