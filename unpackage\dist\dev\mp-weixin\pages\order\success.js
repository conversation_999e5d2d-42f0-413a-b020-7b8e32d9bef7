"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_request = require("../../utils/request.js");
const _sfc_main = {
  data() {
    return {
      orderId: null,
      orderNo: null,
      orderData: null,
      loading: true
    };
  },
  onLoad(options) {
    common_vendor.index.__f__("log", "at pages/order/success.vue:59", "订单成功页面参数:", options);
    if (options.id) {
      this.orderId = options.id;
    }
    if (options.orderNo) {
      this.orderNo = options.orderNo;
    }
    this.loadOrderData();
  },
  methods: {
    async loadOrderData() {
      try {
        this.loading = true;
        const token = common_vendor.index.getStorageSync("token");
        let response;
        response = await common_vendor.index.request({
          // url: 'http://127.0.0.1:6549/api/order/' + this.orderNo,
          url: "https://www.narenqiqige.com/api/order/" + this.orderNo,
          method: "GET",
          header: {
            "Authorization": "Bearer " + token,
            "Content-Type": "application/json"
          }
        });
        common_vendor.index.__f__("log", "at pages/order/success.vue:110", "订单支付页返回订单详情", response);
        if (response && response.data && response.data.code === 200) {
          common_vendor.index.__f__("log", "at pages/order/success.vue:113", "返回订单结果", response);
          this.orderData = response.data.data.order;
          if (!this.orderId && this.orderData.id) {
            this.orderId = this.orderData.id;
          }
        }
        common_vendor.index.__f__("log", "at pages/order/success.vue:120", "this.orderData", this.orderData);
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/order/success.vue:122", "获取订单信息失败:", error);
        common_vendor.index.showToast({
          title: "获取订单信息失败",
          icon: "none"
        });
      } finally {
        this.loading = false;
      }
    },
    formatTime(timeStr) {
      if (!timeStr)
        return (/* @__PURE__ */ new Date()).toLocaleString();
      try {
        const date = new Date(timeStr);
        return date.toLocaleString("zh-CN", {
          year: "numeric",
          month: "2-digit",
          day: "2-digit",
          hour: "2-digit",
          minute: "2-digit",
          second: "2-digit"
        });
      } catch (error) {
        return timeStr;
      }
    },
    goPay() {
      if (this.orderId) {
        common_vendor.index.__f__("log", "at pages/order/success.vue:152", "支付中....");
        utils_request.apiService.mall.orders.pay(this.orderNo, { paymentMethod: 1 }).then((res) => {
          common_vendor.wx$1.requestPayment({
            timeStamp: res.data.timeStamp,
            nonceStr: res.data.nonceStr,
            package: res.data.package,
            signType: "MD5",
            paySign: res.data.paySign,
            success: function(e) {
              common_vendor.index.__f__("log", "at pages/order/success.vue:167", e);
              common_vendor.index.showToast({
                title: "支付成功",
                icon: "success"
              });
              setTimeout(() => {
                common_vendor.index.redirectTo({
                  url: `/pages/my/order-detail`
                });
              }, 1500);
            },
            fail: function(e) {
              common_vendor.index.__f__("log", "at pages/order/success.vue:184", "支付失败", e);
              common_vendor.index.showToast({
                title: "支付失败",
                icon: "none",
                duration: 1500
              });
              setTimeout(() => {
                common_vendor.index.switchTab({
                  url: "/pages/mall/mall"
                });
              }, 1500);
            }
          });
        });
      } else {
        common_vendor.index.showToast({
          title: "订单信息不完整",
          icon: "none"
        });
      }
    },
    viewOrder() {
      if (this.orderId) {
        common_vendor.index.navigateTo({
          url: `/pages/my/order-detail?id=${this.orderId}`
        });
      } else {
        common_vendor.index.showToast({
          title: "订单信息不完整",
          icon: "none"
        });
      }
    },
    goHome() {
      common_vendor.index.switchTab({
        url: "/pages/mall/mall"
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.loading
  }, $data.loading ? {} : common_vendor.e({
    b: $data.orderData
  }, $data.orderData ? common_vendor.e({
    c: common_vendor.t($data.orderData.totalAmount || $data.orderData.price || "0.00"),
    d: common_vendor.t($data.orderData.orderNo || $data.orderNo),
    e: common_vendor.t($options.formatTime($data.orderData.createTime)),
    f: $data.orderData.receiverName
  }, $data.orderData.receiverName ? {
    g: common_vendor.t($data.orderData.receiverName)
  } : {}) : {}, {
    h: common_vendor.o((...args) => $options.goPay && $options.goPay(...args)),
    i: common_vendor.o((...args) => $options.viewOrder && $options.viewOrder(...args)),
    j: common_vendor.o((...args) => $options.goHome && $options.goHome(...args))
  }));
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/order/success.js.map
