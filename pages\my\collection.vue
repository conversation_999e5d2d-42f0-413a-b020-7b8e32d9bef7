<template>
	<view class="user-home">
		<!-- 用户信息区域 -->
		<view class="user-info">
			<view class="user-basic">
				<image class="user-avatar" :src="userInfo.avatar || '/static/logo.png'"></image>
				<text class="username">{{ userInfo.nickname }}</text>
				<button class="edit-btn" @click="navigateToEdit">编辑资料</button>
			</view>

			<view class="user-desc">用户简介</view>

			<view class="user-stats">
				<view class="stat-item">
					<text class="stat-num">{{ userInfo.forwardCount || 0 }}</text>
					<text class="stat-label">转发</text>
				</view>
				<view class="stat-item">
					<text class="stat-num">{{ userInfo.likeCount || 0 }}</text>
					<text class="stat-label">赞</text>
				</view>
				<view class="stat-item">
					<text class="stat-num">{{ userInfo.followingCount || 0 }}</text>
					<text class="stat-label">关注</text>
				</view>
				<view class="stat-item">
					<text class="stat-num">{{ userInfo.followerCount || 0 }}</text>
					<text class="stat-label">粉丝</text>
				</view>
			</view>
		</view>


		<!-- 内容分类导航 -->
		<view class="content-tabs">
			<view v-for="tab in tabs" :key="tab.type" :class="['tab-item', activeTab === tab.type ? 'active' : '']"
				@click="switchTab(tab.type)">
				{{ tab.name }}
			</view>
		</view>

		<!-- 内容列表 - 图片样式 -->
		<view class="content-grid">
			<view v-for="(item, index) in contentList" :key="index" class="grid-item" @click="navigateToDetail(item)">
				<image v-if="item.image" class="grid-image" :src="baseUrl + item.image" mode="aspectFill"></image>
				<view class="grid-info">
					<text class="grid-text">{{ item.content }}</text>
					<view class="grid-meta">
						<text class="meta-user">{{ item.userName }}</text>
						<text class="meta-distance">{{ item.distance || 1460.75 }}km</text>
					</view>
				</view>
			</view>
		</view>


		<view class="fab" @click="showModal = true">
			<text class="fab-icon">+</text>
		</view>

		<view class="modal-mask" v-if="showModal" @click="showModal = false">
			<view class="modal-container" @click.stop>
				<view class="modal-header">
					<text class="modal-title">发布动态</text>
					<text class="modal-close" @click="showModal = false">×</text>
				</view>
				<view class="modal-content">
					<textarea class="content-input" v-model="newContent" placeholder="内容笔记..." auto-height></textarea>

					<!-- 图片上传区域 -->
					<view class="image-upload">
						<view class="upload-title">添加图片</view>
						<view class="image-preview-container">
							<!-- 已选图片预览 -->
							<view class="image-preview" v-for="(image, index) in previewImages" :key="index">
								<image class="preview-image" :src="image" mode="aspectFill"></image>
								<view class="remove-image" @click.stop="removeImage(index)">×</view>
							</view>
							<!-- 上传按钮 -->
							<view class="upload-btn" @click="chooseImage" v-if="previewImages.length < 9">
								<text class="upload-icon">+</text>
							</view>
						</view>
						<text class="upload-tip">最多可上传9张图片</text>
					</view>

					<view class="insert-product" @click="showProductTip">插入商品（当前账号不支持）</view>

					<view class="subject-grid">
						<view class="subject-row" v-for="(row, rowIndex) in subjectRows" :key="rowIndex">
						<view 
							class="subject-item" 
							v-for="subject in row" 
							:key="subject"
							:class="{ 'selected': selectedSubject === subject }"
							@click="selectSubject(subject)"
						>
							{{ subject }}
						</view>
						</view>
  					</view>

				</view>
				<view class="modal-footer">
					<button class="cancel-btn" @click="showModal = false">取消</button>
					<button class="confirm-btn" @click="addNewContent">发布</button>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		apiService
	} from '../../utils/request';
	export default {
		data() {
			return {
				baseUrl: 'https://www.narenqiqige.com',
				previewImages: [], // 图片预览URL数组
				tempFilePaths: [], // 临时文件路径
				userInfo: {
					username: "",
					forwardCount: 0,
					likeCount: 0,
					followingCount: 0,
					followerCount: 0
				},
				tabs: [{
						type: 'post',
						name: '作品'
					},
					{
						type: 'collect',
						name: '收藏'
					},
					{
						type: 'like',
						name: '喜欢'
					}
				],
				activeTab: 'post',
				contentList: [
					// {
					// 	content: "测试内容",
					// 	userName: "微信用户",
					// 	distance: 1460.75,
					// 	image: "/static/icons/qrcode-icon.png"
					// },
					// {
					// 	content: "深紫色62",
					// 	userName: "微信用户",
					// 	distance: 1460.75,
					// 	image: "/static/icons/qrcode-icon.png"
					// }
				],
				// 添加记录的弹窗
				showModal: false,
				newContent: '',
				selectedSubject: '',
				subjects: [
					'日常', '语言', '数学', '物理学',
					'化学', '经济学', '管理学', '生物学',
					'哲学', '工程学', '医学', '管理学',
					'会计学', '法律', '计算机', '农林环',
					'建筑学', '新闻传', '历史人', '旅游管',
					'餐饮管', '健康体', '心理学', '考古学',
					'地理学', '传播学', '神学', '政治学',
					'社会学', '地质学', '宗教学', '建筑学'
				]
			}
		},
		onLoad() {
			this.initContentList()
			this.initUserInfo()
		},
		computed: {
			subjectRows() {
				const rows = [];
				for (let i = 0; i < this.subjects.length; i += 4) {
					rows.push(this.subjects.slice(i, i + 4));
				}
				return rows;
			}
		},
		methods: {
			initUserInfo() {
				const userInfo = uni.getStorageSync("userInfo");
				this.userInfo = userInfo;
				this.userName = userInfo.nickname
				console.log("缓存中的用户数据",userInfo)
			},
			async initContentList() {
				const response = await apiService.content.listContent();
				console.log("内容初始化",response)
				this.contentList = response.data
			},
			// 选择图片
			chooseImage() {
				uni.chooseImage({
					count: 9 - this.previewImages.length,
					success: (res) => {
						// 这里可以上传图片到服务器
						// this.uploadImages(res.tempFilePaths);
						this.previewImages = [...this.previewImages, ...res.tempFilePaths];
						this.tempFilePaths = [...this.tempFilePaths, ...res.tempFilePaths];

					}
				});
			},

			uploadImages(imagePaths) {
				// 上传图片到服务器
				imagePaths.forEach(imagePath => {
					uni.uploadFile({
						url: 'https://www.narenqiqige.com/api/upload/single',
						filePath: imagePath,
						name: 'file',
						header: {
							'Authorization': 'Bearer ' + uni.getStorageSync('token')
						},
						success: (uploadRes) => {
							console.log('图片上传成功：', uploadRes);
							// 这里可以保存上传后的图片URL

						},
						fail: (err) => {
							console.error('图片上传失败：', err);
						}
					});
				});
			},

			// 删除图片
			removeImage(index) {
				this.previewImages.splice(index, 1);
				this.tempFilePaths.splice(index, 1);
			},


			// 选择主题或者分类
			selectSubject(subject) {
				this.selectedSubject = this.selectedSubject === subject ? '' : subject;
			},

			showProductTip() {
				uni.showToast({
					title: '当前账号不支持插入商品',
					icon: 'none'
				});
			},
			// 添加记录数据
			// 修改 addNewContent 方法
			async addNewContent() {
				if (!this.newContent.trim()) {
					uni.showToast({
						title: '请输入内容',
						icon: 'none'
					});
					return;
				}

				// 显示加载中
				uni.showLoading({
					title: '发布中...',
					mask: true
				});

				try {
					// 1. 先上传所有图片
					const uploadedImages = [];
					for (const filePath of this.tempFilePaths) {
						// console.log("当前上传的图片",filePath)
						const res = await this.uploadSingleImage(filePath);
						console.log("当前上传的图片返回结果", res)
						if (res && res.fileUrls) {
							uploadedImages.push(...res.fileUrls);
						}
					}

					// 2. 提交内容数据到服务器
					const postData = {
						content: this.newContent,
						images: uploadedImages,
						subject: this.selectedSubject
					};
					console.log("发送的数据", postData)

					const response = await apiService.content.createContent(postData)

					if (response.code === 200) {
						console.log("发布成功", response)
						// 发布成功后的处理
						this.contentList.unshift(response.data);
						this.resetForm();
						uni.showToast({
							title: '发布成功'
						});
					} else {
						throw new Error(response.message || '发布失败');
					}
				} catch (error) {
					console.error('发布失败:', error);
					uni.showToast({
						title: error.message || '发布失败',
						icon: 'none'
					});
				} finally {
					uni.hideLoading();
				}
			},

			// 单独的上传图片方法
			uploadSingleImage(filePath) {
				return new Promise((resolve, reject) => {
					uni.uploadFile({
						url: 'https://www.narenqiqige.com/api/upload/single',
						filePath: filePath,
						name: 'file',
						header: {
							'Authorization': 'Bearer ' + uni.getStorageSync('token')
						},
						success: (uploadRes) => {
							const resData = JSON.parse(uploadRes.data);
							resolve(resData.data);
						},
						fail: (err) => {
							console.error('图片上传失败:', err);
							reject(err);
						}
					});
				});
			},

			// 重置表单
			resetForm() {
				this.newContent = '';
				this.selectedSubject = '';
				this.previewImages = [];
				this.tempFilePaths = [];
				this.showModal = false;
			},

			previewImage(url) {
				uni.previewImage({
					urls: this.contentList.map(item => item.image).filter(Boolean),
					current: url
				});
			},
			navigateToDetail(content) {
				uni.navigateTo({
					url: `/pages/my/content-detail?id=${content.id}`,
					success: () => {
						
					}
				});
				// const content = this.contentList.find(item => item.id === id);
				// uni.navigateTo({
				// 	url: `/pages/my/content-detail?id=${content.id}`,
				// 	success: () => {
				// 		// 通过事件总线传递数据（实际开发建议用vuex或pinia）
				// 		// uni.$emit('contentDetail', content);
				// 	}
				// });
			},
			loadContentDetail() {
				uni.request({
					url: `/api/content/detail/${this.contentId}`,
					success: (res) => {
						this.contentInfo = res.data.data;
					}
				});
			},
			loadComments() {
				uni.request({
					url: `/api/comment/list/${this.contentId}`,
					success: (res) => {
						this.comments = res.data.data;
					}
				});
			},
			sendComment() {
				if (!this.commentContent.trim()) return;

				uni.request({
					url: '/api/comment/add',
					method: 'POST',
					data: {
						contentId: this.contentId,
						content: this.commentContent
					},
					success: (res) => {
						this.commentContent = '';
						this.loadComments();
						this.contentInfo.commentCount += 1;
					}
				});
			}
		}
	}
</script>

<style lang="scss">
	.user-home {
		background-color: #fff;
		min-height: 100vh;

		/* 用户信息区域 */
		.user-info {
			padding: 30rpx;
			border-bottom: 1rpx solid #f0f0f0;

			.user-basic {
				display: flex;
				align-items: center;
				justify-content: space-between;
				margin-bottom: 30rpx;

				.user-avatar {
					width: 80rpx;
					height: 80rpx;
					border-radius: 50%;
					margin-right: 20rpx;
					border: 1rpx solid #f0f0f0;
				}

				.username {
					font-size: 36rpx;
					font-weight: bold;
					flex: 1;
				}

				.edit-btn {
					background-color: transparent;
					color: #666;
					font-size: 26rpx;
					padding: 0;
					margin: 0;
					line-height: 1;

					&::after {
						border: none;
					}
				}
			}

			.user-desc {
				font-size: 30rpx;
				color: #333;
				margin-bottom: 30rpx;
			}

			.user-stats {
				display: flex;
				justify-content: space-around;
				text-align: center;

				.stat-item {
					flex: 1;

					.stat-num {
						display: block;
						font-size: 32rpx;
						font-weight: bold;
						color: #333;
					}

					.stat-label {
						display: block;
						font-size: 26rpx;
						color: #999;
						margin-top: 5rpx;
					}
				}
			}
		}

		/* 内容分类导航 */
		.content-tabs {
			display: flex;
			border-bottom: 1rpx solid #f0f0f0;

			.tab-item {
				flex: 1;
				text-align: center;
				padding: 25rpx 0;
				font-size: 30rpx;
				color: #666;
				position: relative;

				&.active {
					color: #000;
					font-weight: bold;

					&::after {
						content: '';
						position: absolute;
						bottom: 0;
						left: 50%;
						transform: translateX(-50%);
						width: 80rpx;
						height: 4rpx;
						background-color: #000;
						border-radius: 2rpx;
					}
				}
			}
		}

		/* 网格布局样式 */
		.content-grid {
			display: flex;
			flex-wrap: wrap;
			padding: 20rpx;
			justify-content: space-between;

			.grid-item {
				width: 48%;
				margin-bottom: 20rpx;
				background-color: #fff;
				border-radius: 12rpx;
				overflow: hidden;
				box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);

				.grid-image {
					width: 100%;
					height: 300rpx;
					display: block;
				}

				.grid-info {
					padding: 20rpx;

					.grid-text {
						font-size: 28rpx;
						color: #333;
						display: -webkit-box;
						-webkit-box-orient: vertical;
						-webkit-line-clamp: 2;
						overflow: hidden;
						text-overflow: ellipsis;
						margin-bottom: 15rpx;
						line-height: 1.4;
					}

					.grid-meta {
						display: flex;
						justify-content: space-between;
						font-size: 24rpx;
						color: #999;

						.meta-user {
							max-width: 60%;
							overflow: hidden;
							text-overflow: ellipsis;
							white-space: nowrap;
						}
					}
				}
			}
		}

		/* 响应式调整 */
		@media (max-width: 400px) {
			.content-grid .grid-item {
				width: 48%;

				.grid-image {
					height: 250rpx;
				}
			}
		}

		@media (min-width: 600px) {
			.content-grid .grid-item {
				width: 48%;

				.grid-image {
					height: 350rpx;
				}
			}
		}



		/* 内容列表 */
		.content-list {
			padding: 20rpx;

			.content-item {
				margin-bottom: 40rpx;
				border-bottom: 1rpx solid #f0f0f0;
				padding-bottom: 20rpx;

				.content-row {
					display: flex;
					flex-direction: column;

					.content-text {
						font-size: 32rpx;
						color: #333;
						margin-bottom: 20rpx;
					}

					.content-image {
						width: 100%;
						height: 400rpx;
						border-radius: 8rpx;
						margin-bottom: 20rpx;
					}
				}

				.content-meta {
					display: flex;
					justify-content: space-between;
					font-size: 26rpx;
					color: #999;

					.meta-item {
						display: flex;
						align-items: center;
					}
				}
			}
		}

		// 加号按钮样式
		.fab {
			position: fixed;
			right: 40rpx;
			bottom: 80rpx;
			width: 100rpx;
			height: 100rpx;
			background-color: #007AFF;
			border-radius: 50%;
			display: flex;
			align-items: center;
			justify-content: center;
			box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.2);
			z-index: 999;

			.fab-icon {
				color: white;
				font-size: 50rpx;
				font-weight: bold;
				margin-bottom: 6rpx;
			}
		}

		.modal-mask {
			position: fixed;
			top: 0;
			left: 0;
			right: 0;
			bottom: 0;
			background-color: rgba(0, 0, 0, 0.5);
			display: flex;
			align-items: center;
			justify-content: center;
			z-index: 1000;
		}

		.modal-container {
			width: 90%;
			max-height: 80vh;
			background-color: #fff;
			border-radius: 20rpx;
			overflow: hidden;
			display: flex;
			flex-direction: column;
		}

		.modal-header {
			padding: 30rpx;
			border-bottom: 1rpx solid #f0f0f0;
			display: flex;
			justify-content: space-between;
			align-items: center;

			.modal-title {
				font-size: 36rpx;
				font-weight: bold;
			}

			.modal-close {
				font-size: 50rpx;
				color: #999;
			}
		}

		.modal-content {
			padding: 30rpx;
			flex: 1;
			overflow-y: auto;

			.content-input {
				width: 100%;
				min-height: 200rpx;
				font-size: 32rpx;
				margin-bottom: 30rpx;
			}

			.insert-product {
				color: #007AFF;
				font-size: 28rpx;
				margin-bottom: 40rpx;
				padding: 10rpx 0;
			}
		}

		.subject-grid {
			margin-top: 20rpx;
		}

		.subject-row {
			display: flex;
			margin-bottom: 20rpx;
		}

		.subject-item {
			flex: 1;
			text-align: center;
			padding: 15rpx 0;
			font-size: 28rpx;
			color: #333;
			border: 1rpx solid #f0f0f0;
			margin: 0 10rpx;
			border-radius: 8rpx;
			transition: all 0.3s;
			
			&:active {
			background-color: #f0f0f0;
			}
			
			&.selected {
			background-color: #007AFF;
			color: white;
			border-color: #007AFF;
			}
  		}

		.modal-footer {
			display: flex;
			padding: 20rpx 30rpx;
			border-top: 1rpx solid #f0f0f0;

			button {
				flex: 1;
				height: 80rpx;
				line-height: 80rpx;
				font-size: 32rpx;
				margin: 0 10rpx;

				&::after {
					border: none;
				}
			}

			.cancel-btn {
				background-color: #f0f0f0;
				color: #333;
			}

			.confirm-btn {
				background-color: #007AFF;
				color: white;
			}
		}

		// 发布动态样式
		/* 图片上传样式 */
		.image-upload {
			margin-bottom: 40rpx;

			.upload-title {
				font-size: 28rpx;
				color: #333;
				margin-bottom: 20rpx;
			}

			.image-preview-container {
				display: flex;
				flex-wrap: wrap;
				gap: 20rpx;

				.image-preview {
					width: 160rpx;
					height: 160rpx;
					position: relative;

					.preview-image {
						width: 100%;
						height: 100%;
					}

					.remove-image {
						position: absolute;
						top: 0;
						right: 0;
						width: 40rpx;
						height: 40rpx;
						background: rgba(0, 0, 0, 0.5);
						color: white;
					}
				}

				.upload-btn {
					width: 160rpx;
					height: 160rpx;
					border: 1rpx dashed #ccc;
					border-radius: 8rpx;
					display: flex;
					align-items: center;
					/* 新增：垂直居中 */
					justify-content: center;
					/* 新增：水平居中 */

					.upload-icon {
						font-size: 60rpx;
						color: #999;
						margin-bottom: 0;
						/* 移除原有下边距 */
						line-height: 1;
						/* 防止文本偏移 */
					}
				}
			}

			.upload-tip {
				font-size: 24rpx;
				color: #999;
			}
		}
	}
</style>