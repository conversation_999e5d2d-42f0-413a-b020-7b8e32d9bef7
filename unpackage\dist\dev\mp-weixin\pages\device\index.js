"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_request = require("../../utils/request.js");
require("../../store/wifi.js");
const utils_dyeConsumptionManager = require("../../utils/dyeConsumptionManager.js");
const utils_writeDataChunked = require("../../utils/writeDataChunked.js");
const _sfc_main = {
  data() {
    return {
      activeTab: "bluetooth",
      // 当前选中的选项卡
      // 联网相关数据
      networkStatus: true,
      bluetoothStatus: true,
      wifiStatus: true,
      // 以下是wifi模块
      // wifiInfo : {
      // },
      wifiInfo: {
        deviceId: "",
        password: "",
        SSID: ""
      },
      showPassword: false,
      // 蓝牙相关数据
      isScanning: false,
      deviceList: [],
      connectedDevice: null,
      logs: [],
      isUsingDevice: false,
      usageStartTime: null,
      usageDuration: "00:00:00",
      usageTimer: null,
      // 设备状态详情
      deviceStatusDetail: {
        isOnline: false,
        connectionQuality: 0,
        lastHeartbeat: null,
        firmwareVersion: "",
        batteryLevel: 100
      },
      // 染膏库存状态
      inventoryStatus: null,
      transferState: utils_writeDataChunked.TransferState.IDLE,
      lastTransferState: utils_writeDataChunked.TransferState.IDLE,
      transferStateButtom: true
    };
  },
  onLoad() {
    try {
      this.initBluetooth();
    } catch (err) {
      common_vendor.index.__f__("log", "at pages/device/index.vue:248", "蓝牙初始化失败", err);
    }
    try {
      this.initWifi();
    } catch (err) {
      common_vendor.index.__f__("log", "at pages/device/index.vue:253", "wifi初始化失败");
    }
  },
  onUnload() {
    this.stopScanning();
    this.stopUsageTimer();
    this.startStatusWatcher();
  },
  onShow() {
    this.updatePageState();
    this.updateDeviceStatusFromGlobal();
    this.loadInventoryStatus();
  },
  beforeDestroy() {
    if (this.statusWatcher) {
      clearInterval(this.statusWatcher);
    }
  },
  created() {
    this.startStatusWatcher();
  },
  methods: {
    startStatusWatcher() {
      this.lastTransferState = utils_writeDataChunked.TransferState.IDLE;
      this.statusWatcher = setInterval(() => {
        const currentTransferState = utils_writeDataChunked.transferManager.transferState;
        if (this.lastTransferState != currentTransferState) {
          common_vendor.index.__f__("log", "at pages/device/index.vue:291", "监听到分片变化值为", currentTransferState);
          switch (currentTransferState) {
            case utils_writeDataChunked.TransferState.TRANSFERRING:
              common_vendor.index.showLoading({
                title: "配置中请稍等...",
                mask: true
                // 添加遮罩层，防止用户点击
              });
              break;
            case utils_writeDataChunked.TransferState.SUCCESS:
              common_vendor.index.hideLoading();
              common_vendor.index.showToast({
                title: "配置设备wifi成功",
                mask: true,
                // 是否显示透明蒙层（防止触摸穿透）
                icon: "none"
              });
              break;
            case utils_writeDataChunked.TransferState.FAILED:
            case utils_writeDataChunked.TransferState.TIMEOUT:
              common_vendor.index.hideLoading();
              common_vendor.index.showToast({
                title: "配置设备wifi失败,请重试",
                mask: true,
                // 是否显示透明蒙层（防止触摸穿透）
                icon: "none"
              });
              break;
          }
          this.lastTransferState = currentTransferState;
        }
      }, 500);
    },
    // 格式化时间
    formatTime(timestamp) {
      if (!timestamp)
        return "";
      const date = new Date(timestamp);
      return date.toLocaleString();
    },
    // 从全局状态更新设备状态
    updateDeviceStatusFromGlobal() {
      const app = getApp();
      this.deviceStatusDetail = {
        ...app.globalData.deviceStatusDetail
      };
    },
    // 加载库存状态
    async loadInventoryStatus() {
      try {
        const app = getApp();
        const deviceCode = app.globalData.connectedDeviceId;
        if (deviceCode && this.connectedDevice) {
          this.inventoryStatus = await utils_dyeConsumptionManager.dyeConsumptionManager.getInventoryStatus(deviceCode);
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/device/index.vue:362", "加载库存状态失败:", error);
      }
    },
    // 刷新库存状态
    async refreshInventoryStatus() {
      try {
        const app = getApp();
        const deviceCode = app.globalData.connectedDeviceId;
        if (deviceCode) {
          common_vendor.index.showLoading({
            title: "刷新中..."
          });
          this.inventoryStatus = await utils_dyeConsumptionManager.dyeConsumptionManager.getInventoryStatus(deviceCode, false);
          common_vendor.index.hideLoading();
          common_vendor.index.showToast({
            title: "刷新成功",
            icon: "success"
          });
        }
      } catch (error) {
        common_vendor.index.hideLoading();
        common_vendor.index.__f__("error", "at pages/device/index.vue:384", "刷新库存状态失败:", error);
        common_vendor.index.showToast({
          title: "刷新失败",
          icon: "none"
        });
      }
    },
    scanDevices() {
      const app = getApp();
      if (!app.checkLoginStatus()) {
        common_vendor.index.showToast({
          title: "未登录请先登录",
          icon: "none",
          duration: 1500
        });
        return;
      }
      common_vendor.index.scanCode({
        success: async (res) => {
          try {
            common_vendor.index.__f__("log", "at pages/device/index.vue:405", "扫码结果:", res.result);
            const device = JSON.parse(res.result);
            if (!device.deviceId) {
              common_vendor.index.showToast({
                title: "不是有效设备编号",
                icon: "none",
                duration: 1500
              });
              return;
            }
            const response = await utils_request.apiService.device.bindDevice(device.deviceId);
            common_vendor.index.__f__("log", "at pages/device/index.vue:419", "返回结果", response);
            switch (response.code) {
              case 200:
                common_vendor.index.showToast({
                  title: response.message,
                  icon: "none",
                  duration: 2e3
                });
                setTimeout(() => {
                  this.activeTab = "bluetooth";
                }, 4e3);
                this.wifiInfo.deviceId = device.deviceId;
                break;
              case 401:
                break;
              case 500:
                common_vendor.index.showToast({
                  title: response.message || "设备已被绑定,请重新",
                  icon: "none",
                  duration: 1500
                });
                break;
              default:
                common_vendor.index.showToast({
                  title: response.message || "未知错误",
                  icon: "none",
                  duration: 1500
                });
            }
            app.globalData.deviceList = /* @__PURE__ */ new Set([
              ...app.globalData.deviceList,
              device.deviceId
            ]);
            common_vendor.index.__f__("log", "at pages/device/index.vue:457", "绑定机器的设备编号的设备编号", app.globalData.connectedDeviceId);
          } catch (e) {
            common_vendor.index.__f__("error", "at pages/device/index.vue:460", "扫码绑定设备出错:", e);
            common_vendor.index.showToast({
              title: e.message || "网络错误请检查网络后,重新扫码",
              icon: "none",
              duration: 1500
            });
          }
        },
        fail: (err) => {
          common_vendor.index.showToast({
            title: "扫码失败: " + (err.errMsg || "未知错误"),
            icon: "none",
            duration: 1500
          });
        }
      });
    },
    async initWifi() {
      common_vendor.index.__f__("log", "at pages/device/index.vue:479", "WiFi模块开始初始化");
      try {
        common_vendor.index.startWifi({
          success: () => {
            common_vendor.index.__f__("log", "at pages/device/index.vue:484", "WiFi模块初始化成功");
            common_vendor.index.getConnectedWifi({
              success: (res) => {
                const app = getApp();
                common_vendor.index.__f__("log", "at pages/device/index.vue:489", "当前连接的Wi-Fi信息:", res.wifi);
                this.wifiInfo.SSID = res.wifi.SSID;
                app.globalData.currentWifi = res.wifi;
              },
              fail: (err) => {
                common_vendor.index.__f__("error", "at pages/device/index.vue:499", "获取Wi-Fi信息失败:", err);
              }
            });
          },
          fail: (err) => {
            common_vendor.index.__f__("error", "at pages/device/index.vue:506", "WiFi模块初始化失败", err);
          }
        });
      } catch (err) {
        common_vendor.index.__f__("error", "at pages/device/index.vue:510", "调用WiFi接口异常", err);
      }
    },
    // 获取WiFi列表
    getWifiList() {
      common_vendor.index.__f__("log", "at pages/device/index.vue:516", "获取位置等信息");
      common_vendor.index.getSetting({
        success(res) {
          common_vendor.index.__f__("log", "at pages/device/index.vue:519", "配置列表", res);
          if (!res.authSetting["scope.userLocation"]) {
            common_vendor.index.authorize({
              scope: "scope.userLocation",
              success() {
                common_vendor.index.__f__("log", "at pages/device/index.vue:524", "位置权限授权成功");
                this.requestWifiList();
              },
              fail() {
                common_vendor.index.__f__("log", "at pages/device/index.vue:528", "位置权限授权失败");
              }
            });
          } else {
            this.requestWifiList();
          }
        }
      });
    },
    // connectWifi() {
    // 	this.$store.dispatch('writeDataChunked', {
    // 						wifiInfo: this.wifiInfo,
    // 						// len
    // 					}).then(() => {
    // 						// uni.hideLoading();
    // 						// uni.showToast({
    // 						// 	title: 'wifi配置成功',
    // 						// 	icon: 'success'
    // 						// });
    // 						//断开蓝牙
    // 						// this.$store.dispatch('disconnectDevice');
    // 					}).catch(err => {
    // 						uni.__f__('log','at pages/device/index.vue:550',"配置失败请检查", err);
    // 						uni.hideLoading();
    // 						uni.showToast({
    // 							title: '连接网络错误,请确保蓝牙和wifi打开后重试',
    // 							icon: 'none',
    // 							duration: 2500
    // 						});
    // 						//断开蓝牙
    // 						this.$store.dispatch('disconnectDevice');
    // 					});
    // },
    connectWifi() {
      const isLogin = common_vendor.index.getStorageSync("isLoggedIn");
      if (!isLogin) {
        common_vendor.index.showModal({
          title: "未登录请先登录"
        });
        return;
      }
      if (!this.wifiInfo.deviceId) {
        common_vendor.index.showToast({
          title: "请输入设备编号",
          icon: "none"
        });
        return;
      }
      if (!this.wifiInfo.SSID) {
        common_vendor.index.showToast({
          title: "请输入wifi账号",
          icon: "none"
        });
        return;
      }
      if (!this.wifiInfo.password) {
        common_vendor.index.showToast({
          title: "请输入wifi密码",
          icon: "none"
        });
        return;
      }
      if (utils_writeDataChunked.transferManager.transferState !== utils_writeDataChunked.TransferState.IDLE) {
        common_vendor.index.showToast({
          title: "配置中请稍后重试",
          mask: true,
          // 是否显示透明蒙层（防止触摸穿透）
          icon: "none"
        });
        return;
      }
      const app = getApp();
      const startTime = Date.now();
      const timeout = 6e4;
      let retryCount = 0;
      const maxRetryCount = 5;
      const tryConnect = () => {
        const currentTime = Date.now();
        if (currentTime - startTime >= timeout) {
          common_vendor.index.hideLoading();
          common_vendor.index.showToast({
            title: "连接超时，未找到设备",
            icon: "none"
          });
          return;
        }
        this.$store.dispatch("disconnectDevice");
        try {
          this.$store.dispatch("startScan");
          const deviceList = this.$store.getters.deviceList;
          app.addDeviceList(this.wifiInfo.deviceId);
          common_vendor.index.__f__("log", "at pages/device/index.vue:635", "所有设备", deviceList);
          const connectDevice = deviceList.find((device) => device.name === this.wifiInfo.deviceId) || null;
          common_vendor.index.__f__("warn", "at pages/device/index.vue:638", "需要连接的设备信息", connectDevice);
          if (!connectDevice) {
            retryCount++;
            if (retryCount <= maxRetryCount) {
              common_vendor.index.__f__("log", "at pages/device/index.vue:643", `未找到设备，正在第${retryCount}次重试...`);
              setTimeout(tryConnect, 3e3);
            } else {
              common_vendor.index.hideLoading();
              common_vendor.index.showToast({
                title: "未找到设备，请检查设备编号",
                icon: "none"
              });
            }
            return;
          }
          this.$store.dispatch("connectDevice", connectDevice).then(() => {
            this.$store.dispatch("writeDataChunked", {
              wifiInfo: this.wifiInfo
              // len
            }).then(() => {
            }).catch((err) => {
              common_vendor.index.__f__("log", "at pages/device/index.vue:671", "配置失败请检查", err);
              common_vendor.index.hideLoading();
              common_vendor.index.showToast({
                title: "连接网络错误,请确保蓝牙和wifi打开后重试",
                icon: "none",
                duration: 2500
              });
              this.$store.dispatch("disconnectDevice");
            });
          }).catch((err) => {
            common_vendor.index.__f__("log", "at pages/device/index.vue:683", "配置失败请检查", err);
            common_vendor.index.hideLoading();
            common_vendor.index.showToast({
              title: "连接网络错误,请确保蓝牙和wifi打开后重试",
              icon: "none",
              duration: 2500
            });
            this.$store.dispatch("disconnectDevice");
          });
        } catch (err) {
          common_vendor.index.__f__("log", "at pages/device/index.vue:695", "蓝牙连接错误", err);
          retryCount++;
          if (retryCount <= maxRetryCount) {
            common_vendor.index.__f__("log", "at pages/device/index.vue:698", `发生异常，正在第${retryCount}次重试...`);
            setTimeout(tryConnect, 3e3);
          } else {
            common_vendor.index.hideLoading();
            common_vendor.index.showToast({
              title: "连接过程中发生错误",
              icon: "none"
            });
          }
        }
      };
      tryConnect();
    },
    // 切换选项卡
    switchTab(tab) {
      this.activeTab = tab;
    },
    /**
     * 更新页面状态
     */
    updatePageState() {
      this.isScanning = this.$store.state.isScanning;
      this.deviceList = this.$store.state.deviceList;
      this.connectedDevice = this.$store.state.connectedDevice ? {
        ...this.$store.state.connectedDevice
      } : null;
      this.logs = [...this.$store.state.logs];
    },
    /**
     * 初始化蓝牙
     */
    async initBluetooth() {
      try {
        common_vendor.index.showLoading({
          title: "初始化蓝牙...",
          mask: true
        });
        const result = await this.$store.dispatch("initBluetooth");
        this.updatePageState();
        if (result) {
          common_vendor.index.showToast({
            title: "蓝牙初始化成功",
            icon: "success",
            duration: 1500
          });
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/device/index.vue:751", "初始化蓝牙失败:", error);
        common_vendor.index.showToast({
          title: "蓝牙初始化失败",
          icon: "error",
          duration: 2e3
        });
      } finally {
        common_vendor.index.hideLoading();
      }
    },
    /**
     * 切换搜索状态
     */
    toggleScan() {
      if (this.isScanning) {
        this.stopScanning();
      } else {
        this.startScanning();
      }
    },
    /**
     * 开始扫描蓝牙设备
     */
    async startScanning() {
      try {
        const isLogin = common_vendor.index.getStorageSync("isLoggedIn");
        if (!isLogin) {
          common_vendor.index.showModal({
            title: "未登录请先登录"
          });
          return;
        }
        common_vendor.index.showLoading({
          title: "开始扫描...",
          mask: true
        });
        const app = getApp();
        await app.getDeviceConnectId();
        const result = await this.$store.dispatch("startScan");
        this.updatePageState();
        if (result) {
          common_vendor.index.showToast({
            title: "开始扫描设备",
            icon: "success",
            duration: 1500
          });
          const updateInterval = setInterval(() => {
            if (!this.$store.state.isScanning) {
              clearInterval(updateInterval);
              return;
            }
            this.deviceList = [...this.$store.state.deviceList];
            if (!this.$store.state.connectedDevice) {
              this.connectedDevice = null;
            }
          }, 1e3);
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/device/index.vue:819", "开始扫描失败:", error);
        this.updatePageState();
      } finally {
        common_vendor.index.hideLoading();
      }
    },
    /**
     * 停止扫描蓝牙设备
     */
    async stopScanning() {
      try {
        await this.$store.dispatch("stopScan");
        this.updatePageState();
        common_vendor.index.showToast({
          title: "已停止扫描",
          icon: "success",
          duration: 1500
        });
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/device/index.vue:840", "停止扫描失败:", error);
        this.updatePageState();
      }
    },
    /**
     * 连接选定的蓝牙设备
     */
    async connectDevice(device) {
      const app = getApp();
      if (app.isWifiConnection() && app.globalData.deviceStatus) {
        common_vendor.index.showToast({
          title: "设备已通过wifi连接,请先断开wifi连接",
          icon: "none",
          duration: 2e3
        });
        return;
      }
      if (this.connectedDevice && this.connectedDevice.deviceId === device.deviceId) {
        common_vendor.index.showToast({
          title: "设备已连接",
          icon: "none",
          duration: 1500
        });
        return;
      }
      try {
        await this.$store.dispatch("connectDevice", device);
        this.updatePageState();
        common_vendor.index.showToast({
          title: "连接成功",
          icon: "success",
          duration: 2e3
        });
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/device/index.vue:878", "连接设备失败:", error);
        this.updatePageState();
        common_vendor.index.showToast({
          title: "连接失败",
          icon: "error",
          duration: 2e3
        });
      }
    },
    /**
     * 断开当前已连接的设备
     */
    async disconnectDevice() {
      try {
        if (this.isUsingDevice) {
          await this.endUsage();
        }
        await this.$store.dispatch("disconnectDevice");
        this.updatePageState();
        common_vendor.index.showToast({
          title: "已断开连接",
          icon: "success",
          duration: 1500
        });
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/device/index.vue:908", "断开连接失败:", error);
        this.updatePageState();
        common_vendor.index.showToast({
          title: "断开失败",
          icon: "error",
          duration: 2e3
        });
      }
    },
    /**
     * 开始使用设备
     */
    async startUsage() {
      if (!this.connectedDevice) {
        common_vendor.index.showToast({
          title: "请先连接设备",
          icon: "none"
        });
        return;
      }
      try {
        const deviceId = 1;
        const response = await utils_request.apiService.mall.deviceUsage.startUsage(
          deviceId
        );
        if (response.code === 200) {
          this.isUsingDevice = true;
          this.usageStartTime = /* @__PURE__ */ new Date();
          this.startUsageTimer();
          common_vendor.index.showToast({
            title: "开始使用记录",
            icon: "success"
          });
        } else {
          common_vendor.index.showToast({
            title: response.message || "开始使用失败",
            icon: "none"
          });
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/device/index.vue:953", "开始使用失败:", error);
        common_vendor.index.showToast({
          title: "开始使用失败",
          icon: "none"
        });
      }
    },
    /**
     * 结束使用设备
     */
    async endUsage() {
      if (!this.isUsingDevice) {
        return;
      }
      try {
        const deviceId = 1;
        const response = await utils_request.apiService.mall.deviceUsage.endUsage(
          deviceId
        );
        if (response.code === 200) {
          this.isUsingDevice = false;
          this.stopUsageTimer();
          common_vendor.index.showToast({
            title: "使用记录已保存",
            icon: "success"
          });
        } else {
          common_vendor.index.showToast({
            title: response.message || "结束使用失败",
            icon: "none"
          });
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/device/index.vue:990", "结束使用失败:", error);
        common_vendor.index.showToast({
          title: "结束使用失败",
          icon: "none"
        });
      }
    },
    /**
     * 开始使用时长计时器
     */
    startUsageTimer() {
      this.usageTimer = setInterval(() => {
        if (this.usageStartTime) {
          const now = /* @__PURE__ */ new Date();
          const diff = now - this.usageStartTime;
          this.usageDuration = this.formatDuration(diff);
        }
      }, 1e3);
    },
    /**
     * 停止使用时长计时器
     */
    stopUsageTimer() {
      if (this.usageTimer) {
        clearInterval(this.usageTimer);
        this.usageTimer = null;
      }
      this.usageDuration = "00:00:00";
      this.usageStartTime = null;
    },
    /**
     * 格式化使用时长
     */
    formatDuration(milliseconds) {
      const seconds = Math.floor(milliseconds / 1e3);
      const hours = Math.floor(seconds / 3600);
      const minutes = Math.floor(seconds % 3600 / 60);
      const secs = seconds % 60;
      return `${hours.toString().padStart(2, "0")}:${minutes.toString().padStart(2, "0")}:${secs.toString().padStart(2, "0")}`;
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.activeTab === "network"
  }, $data.activeTab === "network" ? {} : {}, {
    b: $data.activeTab === "network" ? 1 : "",
    c: common_vendor.o(($event) => $options.switchTab("network")),
    d: $data.activeTab === "bluetooth"
  }, $data.activeTab === "bluetooth" ? {} : {}, {
    e: $data.activeTab === "bluetooth" ? 1 : "",
    f: common_vendor.o(($event) => $options.switchTab("bluetooth")),
    g: $data.activeTab === "wifi"
  }, $data.activeTab === "wifi" ? {} : {}, {
    h: $data.activeTab === "wifi" ? 1 : "",
    i: common_vendor.o(($event) => $options.switchTab("wifi")),
    j: $data.activeTab === "bluetooth"
  }, $data.activeTab === "bluetooth" ? common_vendor.e({
    k: common_vendor.f($data.deviceList, (item, index, i0) => {
      return {
        a: common_vendor.t(item.name || "未知设备"),
        b: common_vendor.t(item.deviceId),
        c: common_vendor.t(item.RSSI || "未知"),
        d: common_vendor.t($data.connectedDevice && $data.connectedDevice.deviceId === item.deviceId ? "已连接" : "连接"),
        e: $data.connectedDevice && $data.connectedDevice.deviceId === item.deviceId ? 1 : "",
        f: index,
        g: common_vendor.o(($event) => $options.connectDevice(item), index)
      };
    }),
    l: $data.deviceList.length === 0 && $data.isScanning
  }, $data.deviceList.length === 0 && $data.isScanning ? {} : $data.deviceList.length === 0 ? {} : {}, {
    m: $data.deviceList.length === 0,
    n: common_vendor.t($data.isScanning ? "停止搜索" : "搜索蓝牙"),
    o: $data.isScanning ? 1 : "",
    p: common_vendor.o((...args) => $options.toggleScan && $options.toggleScan(...args)),
    q: $data.connectedDevice
  }, $data.connectedDevice ? {
    r: common_vendor.o((...args) => $options.disconnectDevice && $options.disconnectDevice(...args))
  } : {}) : {}, {
    s: $data.activeTab === "network"
  }, $data.activeTab === "network" ? {
    t: common_vendor.t($data.networkStatus ? "已开启" : "已关闭"),
    v: $data.networkStatus ? 1 : "",
    w: common_vendor.o(($event) => _ctx.toggleConnection("network")),
    x: common_vendor.t($data.bluetoothStatus ? "已开启" : "已关闭"),
    y: $data.bluetoothStatus ? 1 : "",
    z: common_vendor.o(($event) => _ctx.toggleConnection("bluetooth")),
    A: common_vendor.t($data.wifiStatus ? "已开启" : "已关闭"),
    B: $data.wifiStatus ? 1 : "",
    C: common_vendor.o(($event) => _ctx.toggleConnection("wifi")),
    D: common_vendor.o((...args) => $options.scanDevices && $options.scanDevices(...args))
  } : {}, {
    E: $data.activeTab === "wifi"
  }, $data.activeTab === "wifi" ? {
    F: $data.wifiInfo.deviceId,
    G: common_vendor.o(($event) => $data.wifiInfo.deviceId = $event.detail.value),
    H: $data.wifiInfo.SSID,
    I: common_vendor.o(($event) => $data.wifiInfo.SSID = $event.detail.value),
    J: $data.wifiInfo.password,
    K: common_vendor.o(($event) => $data.wifiInfo.password = $event.detail.value),
    L: common_vendor.o((...args) => $options.connectWifi && $options.connectWifi(...args))
  } : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/device/index.js.map
