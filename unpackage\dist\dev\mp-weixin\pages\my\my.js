"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_request = require("../../utils/request.js");
const common_assets = require("../../common/assets.js");
const CustomTabbar = () => "../../components/CustomTabbar.js";
const _sfc_main = {
  components: {
    CustomTabbar
  },
  data() {
    return {
      useCustomTabbar: false,
      // 默认使用系统tabbar，需要时可以设置为true
      userInfo: {
        phone: "",
        nickname: "",
        avatar: ""
      },
      isLogin: false,
      // 统计数据
      statistics: {
        favoriteCount: 0,
        worksCount: 0,
        colorRecordCount: 0,
        formulaCount: 0
      },
      // 门店信息
      storeInfo: {
        name: "<PERSON>",
        id: null
      },
      // 关注信息
      followCount: 0,
      fansCount: 0
    };
  },
  onLoad() {
  },
  onShow() {
    this.checkLoginStatus();
    if (this.isLogin) {
      this.loadUserData();
    }
  },
  methods: {
    // 检查登录状态
    checkLoginStatus() {
      const token = common_vendor.index.getStorageSync("token");
      const wasLogin = this.isLogin;
      this.isLogin = !!token;
      common_vendor.index.__f__("log", "at pages/my/my.vue:233", "登录状态", this.isLogin);
      if (this.isLogin) {
        this.userInfo = common_vendor.index.getStorageSync("userInfo") || this.userInfo;
        if (!wasLogin) {
          this.loadUserData();
        }
      } else {
        this.resetUserData();
      }
    },
    // 重置用户数据
    resetUserData() {
      this.statistics = {
        favoriteCount: 0,
        worksCount: 0,
        colorRecordCount: 0,
        formulaCount: 0
      };
      this.storeInfo = {
        name: "Tony",
        id: null
      };
      this.followCount = 0;
      this.fansCount = 0;
    },
    // 加载用户数据
    async loadUserData() {
      try {
        await Promise.all([
          this.loadFavoriteCount(),
          this.loadUserStores()
        ]);
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/my/my.vue:271", "加载用户数据失败:", error);
      }
    },
    // 加载收藏数量
    async loadFavoriteCount() {
      try {
        const response = await utils_request.apiService.mall.favorites.list();
        if (response.code === 200 && response.data) {
          this.statistics.favoriteCount = response.data.length || 0;
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/my/my.vue:283", "加载收藏数量失败:", error);
      }
    },
    // 加载用户门店信息
    async loadUserStores() {
      try {
        const userInfo = common_vendor.index.getStorageSync("userInfo");
        if (userInfo && userInfo.id) {
          const response = await utils_request.apiService.mall.stores.userStores(userInfo.id);
          if (response.code === 200 && response.data && response.data.length > 0) {
            this.storeInfo.name = response.data[0].name || "Tony";
            this.storeInfo.id = response.data[0].id;
          }
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/my/my.vue:301", "加载门店信息失败:", error);
      }
    },
    // 点击头像容器
    handleAvatarTap() {
      if (!this.isLogin) {
        common_vendor.index.navigateTo({
          url: "/pages/login/login"
        });
        return;
      }
      common_vendor.index.navigateTo({
        url: "/pages/outLogin/outLogin"
      });
    },
    // 授权回调
    async handleUserInfo(e) {
      if (e.detail.errMsg !== "getUserInfo:ok") {
        common_vendor.index.showToast({
          title: "授权取消",
          icon: "none"
        });
        return;
      }
      try {
        const loginRes = await common_vendor.index.login({
          provider: "weixin"
        });
        common_vendor.index.__f__("log", "at pages/my/my.vue:336", loginRes.code);
      } catch (error) {
      }
    },
    navigateToCollections() {
      common_vendor.index.navigateTo({
        url: "/pages/my/collection",
        success: () => {
          common_vendor.index.__f__("log", "at pages/my/my.vue:365", "跳转到收藏页面成功");
        },
        fail: (err) => {
          common_vendor.index.__f__("error", "at pages/my/my.vue:368", "跳转到收藏页面失败：", err);
          common_vendor.index.showToast({
            title: "收藏页面开发中",
            icon: "none",
            duration: 2e3
          });
        }
      });
    },
    navigateToWorks() {
      common_vendor.index.navigateTo({
        url: "/pages/my/collection?tab=works",
        success: () => {
          common_vendor.index.__f__("log", "at pages/my/my.vue:384", "跳转到作品页面成功");
        },
        fail: (err) => {
          common_vendor.index.__f__("error", "at pages/my/my.vue:387", "跳转到作品页面失败：", err);
          common_vendor.index.showToast({
            title: "作品页面开发中",
            icon: "none",
            duration: 2e3
          });
        }
      });
    },
    navigateToColorRecord() {
      common_vendor.index.navigateTo({
        url: "/pages/my/color-record",
        success: () => {
          common_vendor.index.__f__("log", "at pages/my/my.vue:403", "跳转到调色记录页面成功");
        },
        fail: (err) => {
          common_vendor.index.__f__("error", "at pages/my/my.vue:406", "跳转到调色记录页面失败：", err);
          common_vendor.index.showToast({
            title: "调色记录页面跳转失败",
            icon: "none",
            duration: 2e3
          });
        }
      });
    },
    navigateToStoreInfo() {
      common_vendor.index.navigateTo({
        url: "/pages/my/store-info",
        success: () => {
          common_vendor.index.__f__("log", "at pages/my/my.vue:422", "跳转到门店信息页面成功");
        },
        fail: (err) => {
          common_vendor.index.__f__("error", "at pages/my/my.vue:425", "跳转到门店信息页面失败：", err);
          common_vendor.index.showToast({
            title: "门店信息页面跳转失败",
            icon: "none",
            duration: 2e3
          });
        }
      });
    },
    navigateToStaffManage() {
      common_vendor.index.navigateTo({
        url: "/pages/my/staff-manage",
        success: () => {
          common_vendor.index.__f__("log", "at pages/my/my.vue:441", "跳转到人员管理页面成功");
        },
        fail: (err) => {
          common_vendor.index.__f__("error", "at pages/my/my.vue:444", "跳转到人员管理页面失败：", err);
          common_vendor.index.showToast({
            title: "人员管理页面跳转失败",
            icon: "none",
            duration: 2e3
          });
        }
      });
    },
    navigateToDeviceList() {
      common_vendor.index.navigateTo({
        url: "/pages/device/bind-device/bind-device",
        success: () => {
          common_vendor.index.__f__("log", "at pages/my/my.vue:477", "跳转到设备绑定页面成功");
        },
        fail: (err) => {
          common_vendor.index.__f__("error", "at pages/my/my.vue:480", "跳转到设备绑定页面失败：", err);
          common_vendor.index.showToast({
            title: "设备绑定跳转失败",
            icon: "none",
            duration: 2e3
          });
        }
      });
    },
    navigateToStoreQrcode() {
      common_vendor.index.navigateTo({
        url: "/pages/my/store-qrcode",
        success: () => {
          common_vendor.index.__f__("log", "at pages/my/my.vue:496", "跳转到门店二维码页面成功");
        },
        fail: (err) => {
          common_vendor.index.__f__("error", "at pages/my/my.vue:499", "跳转到门店二维码页面失败：", err);
          common_vendor.index.showToast({
            title: "门店二维码页面跳转失败",
            icon: "none",
            duration: 2e3
          });
        }
      });
    },
    navigateToColorTest() {
      common_vendor.index.navigateTo({
        url: "/pages/my/color-test",
        success: () => {
          common_vendor.index.__f__("log", "at pages/my/my.vue:515", "跳转到色膏检测页面成功");
        },
        fail: (err) => {
          common_vendor.index.__f__("error", "at pages/my/my.vue:518", "跳转到色膏检测页面失败：", err);
          common_vendor.index.showToast({
            title: "色膏检测页面跳转失败",
            icon: "none",
            duration: 2e3
          });
        }
      });
    },
    navigateToUsageStats() {
      common_vendor.index.navigateTo({
        url: "/pages/my/usage-stats",
        success: () => {
          common_vendor.index.__f__("log", "at pages/my/my.vue:534", "跳转到使用统计页面成功");
        },
        fail: (err) => {
          common_vendor.index.__f__("error", "at pages/my/my.vue:537", "跳转到使用统计页面失败：", err);
          common_vendor.index.showToast({
            title: "使用统计页面跳转失败",
            icon: "none",
            duration: 2e3
          });
        }
      });
    },
    navigateToMyOrders() {
      common_vendor.index.navigateTo({
        url: "/pages/my/order",
        success: () => {
          common_vendor.index.__f__("log", "at pages/my/my.vue:553", "跳转到我的订单页面成功");
        },
        fail: (err) => {
          common_vendor.index.__f__("error", "at pages/my/my.vue:556", "跳转到我的订单页面失败：", err);
          common_vendor.index.showToast({
            title: "我的订单页面跳转失败",
            icon: "none",
            duration: 2e3
          });
        }
      });
    },
    navigateToMyDeviceManage() {
      common_vendor.index.navigateTo({
        url: "/pages/device/dye-device-manager/dye-device-manager",
        success: () => {
          common_vendor.index.__f__("log", "at pages/my/my.vue:571", "跳转到我的设备权限页面成功");
        },
        fail: (err) => {
          common_vendor.index.__f__("error", "at pages/my/my.vue:574", "跳转到我的设备权限失败：", err);
          common_vendor.index.showToast({
            title: "跳转到我的设备权限失败",
            icon: "none",
            duration: 2e3
          });
        }
      });
    },
    navigateToRepair() {
      common_vendor.index.navigateTo({
        url: "/pages/my/repair",
        success: () => {
          common_vendor.index.__f__("log", "at pages/my/my.vue:590", "跳转到报修页面成功");
        },
        fail: (err) => {
          common_vendor.index.__f__("error", "at pages/my/my.vue:593", "跳转到报修页面失败：", err);
          common_vendor.index.showToast({
            title: "报修页面跳转失败",
            icon: "none",
            duration: 2e3
          });
        }
      });
    },
    navigateToPolicy() {
      common_vendor.index.navigateTo({
        url: "/pages/my/policy",
        success: function(res) {
          common_vendor.index.__f__("log", "at pages/my/my.vue:608", "跳转成功");
        },
        fail: function(err) {
          common_vendor.index.__f__("log", "at pages/my/my.vue:611", "跳转失败");
          common_vendor.index.showToast({
            icon: "none",
            title: "跳转失败"
          });
        }
      });
    },
    navigateToMessage() {
      common_vendor.index.navigateTo({
        url: "/pages/my/message",
        success: () => {
          common_vendor.index.__f__("log", "at pages/my/my.vue:624", "跳转到消息页面成功");
        },
        fail: (err) => {
          common_vendor.index.__f__("error", "at pages/my/my.vue:627", "跳转到消息页面失败：", err);
          common_vendor.index.showToast({
            title: "消息页面跳转失败",
            icon: "none",
            duration: 2e3
          });
        }
      });
    },
    navigateToAbout() {
      common_vendor.index.navigateTo({
        url: "/pages/my/about",
        success: function(res) {
          common_vendor.index.__f__("log", "at pages/my/my.vue:642", "跳转到关于我们成功");
        },
        fail: function(err) {
          common_vendor.index.__f__("log", "at pages/my/my.vue:645", "跳转到关于我们失败", err);
          common_vendor.index.showToast({
            icon: "none",
            title: "关于我们页面跳转失败"
          });
        }
      });
    },
    navigateToManual() {
      common_vendor.index.navigateTo({
        url: "/pages/my/manual",
        success: function(res) {
          common_vendor.index.__f__("log", "at pages/my/my.vue:657", "跳转到资料手册成功");
        },
        fail: function(err) {
          common_vendor.index.__f__("log", "at pages/my/my.vue:660", "跳转到资料手册失败", err);
          common_vendor.index.showToast({
            icon: "none",
            title: "资料手册页面跳转失败"
          });
        }
      });
    },
    navigateToGuide() {
      common_vendor.index.navigateTo({
        url: "/pages/my/guide",
        success: function(res) {
          common_vendor.index.__f__("log", "at pages/my/my.vue:672", "跳转到新手引导成功");
        },
        fail: function(err) {
          common_vendor.index.__f__("log", "at pages/my/my.vue:675", "跳转到新手引导失败", err);
          common_vendor.index.showToast({
            icon: "none",
            title: "新手引导页面跳转失败"
          });
        }
      });
    },
    navigateToPartnership() {
      common_vendor.index.navigateTo({
        url: "/pages/my/partnership",
        success: function(res) {
          common_vendor.index.__f__("log", "at pages/my/my.vue:687", "跳转到合作加盟成功");
        },
        fail: function(err) {
          common_vendor.index.__f__("log", "at pages/my/my.vue:690", "跳转到合作加盟失败", err);
          common_vendor.index.showToast({
            icon: "none",
            title: "合作加盟页面跳转失败"
          });
        }
      });
    },
    // 显示客服联系方式
    showCustomerService() {
      const phoneNumber = "13826059861";
      common_vendor.index.showModal({
        title: "客服电话",
        content: `客服电话：${phoneNumber}

可在手机端拨打`,
        confirmText: "复制号码",
        cancelText: "取消",
        success: (res) => {
          if (res.confirm) {
            this.copyPhoneNumber(phoneNumber);
          }
        }
      });
    },
    // 复制电话号码到剪切板
    copyPhoneNumber(phoneNumber) {
      common_vendor.index.setClipboardData({
        data: phoneNumber,
        success: () => {
          common_vendor.index.__f__("log", "at pages/my/my.vue:721", "复制电话号码成功");
          common_vendor.index.showToast({
            title: "电话号码已复制",
            icon: "success",
            duration: 2e3
          });
        },
        fail: (err) => {
          common_vendor.index.__f__("error", "at pages/my/my.vue:729", "复制电话号码失败：", err);
          common_vendor.index.showToast({
            title: "复制失败",
            icon: "none",
            duration: 2e3
          });
        }
      });
    },
    handleScan() {
      common_vendor.index.scanCode({
        success: (res) => {
          common_vendor.index.__f__("log", "at pages/my/my.vue:742", "扫码成功：", res);
          common_vendor.index.showToast({
            title: "扫码成功",
            icon: "success",
            duration: 2e3
          });
        },
        fail: (err) => {
          common_vendor.index.__f__("error", "at pages/my/my.vue:755", "扫码失败：", err);
          common_vendor.index.showToast({
            title: "扫码失败",
            icon: "none",
            duration: 2e3
          });
        }
      });
    }
  }
};
if (!Array) {
  const _component_custom_tabbar = common_vendor.resolveComponent("custom-tabbar");
  _component_custom_tabbar();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: !$data.isLogin
  }, !$data.isLogin ? {} : {
    b: $data.userInfo.avatarUrl || "/static/logo.png"
  }, {
    c: common_vendor.o((...args) => $options.handleAvatarTap && $options.handleAvatarTap(...args)),
    d: !$data.isLogin
  }, !$data.isLogin ? {} : {
    e: common_vendor.t($data.userInfo.nickname || $data.userInfo.phone || "微信用户")
  }, {
    f: $data.isLogin
  }, $data.isLogin ? {
    g: common_vendor.t($data.followCount ? $data.followCount : 0),
    h: common_vendor.t($data.fansCount ? $data.fansCount : 0)
  } : {}, {
    i: common_vendor.t($data.statistics.favoriteCount),
    j: common_vendor.o((...args) => $options.navigateToCollections && $options.navigateToCollections(...args)),
    k: common_vendor.t($data.statistics.worksCount),
    l: common_vendor.o((...args) => $options.navigateToWorks && $options.navigateToWorks(...args)),
    m: common_vendor.t($data.statistics.colorRecordCount),
    n: common_vendor.o((...args) => $options.navigateToColorRecord && $options.navigateToColorRecord(...args)),
    o: common_vendor.t($data.statistics.formulaCount),
    p: common_vendor.t($data.storeInfo.name),
    q: common_assets._imports_0$1,
    r: common_vendor.o((...args) => $options.navigateToStoreInfo && $options.navigateToStoreInfo(...args)),
    s: common_assets._imports_1,
    t: common_vendor.o((...args) => $options.navigateToStaffManage && $options.navigateToStaffManage(...args)),
    v: common_assets._imports_0$2,
    w: common_vendor.o((...args) => $options.navigateToDeviceList && $options.navigateToDeviceList(...args)),
    x: common_assets._imports_3,
    y: common_vendor.o((...args) => $options.navigateToStoreQrcode && $options.navigateToStoreQrcode(...args)),
    z: common_assets._imports_4,
    A: common_vendor.o((...args) => $options.navigateToColorTest && $options.navigateToColorTest(...args)),
    B: common_assets._imports_5,
    C: common_vendor.o((...args) => $options.navigateToColorRecord && $options.navigateToColorRecord(...args)),
    D: common_vendor.o((...args) => $options.navigateToUsageStats && $options.navigateToUsageStats(...args)),
    E: common_assets._imports_6,
    F: common_vendor.o((...args) => $options.navigateToMyOrders && $options.navigateToMyOrders(...args)),
    G: common_assets._imports_6,
    H: common_vendor.o((...args) => $options.navigateToMyDeviceManage && $options.navigateToMyDeviceManage(...args)),
    I: common_assets._imports_7,
    J: common_vendor.o((...args) => $options.navigateToGuide && $options.navigateToGuide(...args)),
    K: common_assets._imports_8,
    L: common_vendor.o((...args) => $options.navigateToRepair && $options.navigateToRepair(...args)),
    M: common_assets._imports_9,
    N: common_vendor.o((...args) => $options.navigateToPolicy && $options.navigateToPolicy(...args)),
    O: common_assets._imports_10,
    P: common_vendor.o((...args) => $options.navigateToMessage && $options.navigateToMessage(...args)),
    Q: common_assets._imports_11,
    R: common_vendor.o((...args) => $options.navigateToAbout && $options.navigateToAbout(...args)),
    S: common_assets._imports_12,
    T: common_vendor.o((...args) => $options.navigateToManual && $options.navigateToManual(...args)),
    U: common_assets._imports_13,
    V: common_assets._imports_14,
    W: common_vendor.o((...args) => $options.navigateToGuide && $options.navigateToGuide(...args)),
    X: common_assets._imports_15,
    Y: common_vendor.o((...args) => $options.navigateToPartnership && $options.navigateToPartnership(...args)),
    Z: common_vendor.o((...args) => $options.showCustomerService && $options.showCustomerService(...args)),
    aa: $data.useCustomTabbar
  }, $data.useCustomTabbar ? {} : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/my/my.js.map
