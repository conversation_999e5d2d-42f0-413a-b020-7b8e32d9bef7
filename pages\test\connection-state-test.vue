<template>
  <view class="container">
    <view class="header">
      <text class="title">连接状态测试</text>
    </view>
    
    <view class="status-section">
      <view class="status-item">
        <text class="label">Vuex Store 连接状态:</text>
        <text class="value" :class="{ online: $store.state.connectedDevice, offline: !$store.state.connectedDevice }">
          {{ $store.state.connectedDevice ? '已连接' : '未连接' }}
        </text>
      </view>
      
      <view class="status-item">
        <text class="label">设备ID:</text>
        <text class="value">{{ $store.state.connectedDevice ? $store.state.connectedDevice.deviceId : '无' }}</text>
      </view>
      
      <view class="status-item">
        <text class="label">连接类型:</text>
        <text class="value">{{ $store.state.connectedDevice ? $store.state.connectedDevice.connectionType : '无' }}</text>
      </view>
      
      <view class="status-item">
        <text class="label">GlobalData 设备状态:</text>
        <text class="value" :class="{ online: deviceStatus, offline: !deviceStatus }">
          {{ deviceStatus ? '在线' : '离线' }}
        </text>
      </view>
      
      <view class="status-item">
        <text class="label">GlobalData 设备ID:</text>
        <text class="value">{{ connectedDeviceId || '无' }}</text>
      </view>
    </view>
    
    <view class="actions-section">
      <button class="action-btn" @click="refreshStatus">刷新状态</button>
      <button class="action-btn" @click="simulateConnection">模拟连接</button>
      <button class="action-btn" @click="clearConnection">清除连接</button>
      <button class="action-btn" @click="testPersistence">测试持久化</button>
      <button class="action-btn" @click="testReconnect">测试续传</button>
      <button class="action-btn" @click="simulateRefresh">模拟刷新</button>
    </view>
    
    <view class="logs-section">
      <text class="section-title">操作日志:</text>
      <scroll-view scroll-y class="logs-container">
        <view class="log-item" v-for="(log, index) in logs" :key="index">
          <text class="log-text">{{ log }}</text>
        </view>
      </scroll-view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      deviceStatus: false,
      connectedDeviceId: '',
      logs: []
    };
  },
  
  onLoad() {
    this.refreshStatus();
    this.addLog('页面加载完成');
  },
  
  onShow() {
    this.refreshStatus();
    this.addLog('页面显示');
  },
  
  methods: {
    // 刷新状态
    refreshStatus() {
      const app = getApp();
      this.deviceStatus = app.globalData.deviceStatus;
      this.connectedDeviceId = app.globalData.connectedDeviceId;
      this.addLog(`状态刷新 - 设备状态: ${this.deviceStatus}, 设备ID: ${this.connectedDeviceId}`);
    },
    
    // 模拟连接
    simulateConnection() {
      const mockDevice = {
        deviceId: 'BABAIYI-PSY000004',
        name: 'BABAIYI-PSY000004',
        connectionType: 'wifi'
      };
      
      // 更新Vuex store
      this.$store.commit('SET_CONNECTED_DEVICE', mockDevice);
      
      // 更新globalData
      const app = getApp();
      app.globalData.connectedDeviceId = mockDevice.deviceId;
      app.globalData.deviceStatus = true;
      app.globalData.connectionMethod = 'wifi';
      
      this.refreshStatus();
      this.addLog('模拟连接成功');
    },
    
    // 清除连接
    clearConnection() {
      // 清除Vuex store
      this.$store.commit('CLEAR_CONNECTED_DEVICE');
      
      // 清除globalData
      const app = getApp();
      app.globalData.connectedDeviceId = '';
      app.globalData.deviceStatus = false;
      app.globalData.connectionMethod = '';
      
      this.refreshStatus();
      this.addLog('连接已清除');
    },
    
    // 测试持久化
    testPersistence() {
      try {
        const persistedState = uni.getStorageSync('vuex_store_state');
        this.addLog(`持久化状态: ${JSON.stringify(persistedState)}`);
      } catch (error) {
        this.addLog(`读取持久化状态失败: ${error.message}`);
      }
    },
    
    // 添加日志
    addLog(message) {
      const timestamp = new Date().toLocaleTimeString();
      this.logs.unshift(`[${timestamp}] ${message}`);
      if (this.logs.length > 20) {
        this.logs.pop();
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 30rpx;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.status-section {
  background-color: #fff;
  border-radius: 10rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  padding: 10rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.status-item:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.label {
  font-size: 28rpx;
  color: #666;
}

.value {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
}

.value.online {
  color: #4285f4;
}

.value.offline {
  color: #999;
}

.actions-section {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
  margin-bottom: 30rpx;
}

.action-btn {
  flex: 1;
  min-width: 200rpx;
  background-color: #4285f4;
  color: #fff;
  border: none;
  border-radius: 10rpx;
  padding: 20rpx;
  font-size: 28rpx;
}

.logs-section {
  background-color: #fff;
  border-radius: 10rpx;
  padding: 30rpx;
}

.section-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.logs-container {
  height: 400rpx;
  background-color: #f8f8f8;
  border-radius: 8rpx;
  padding: 20rpx;
}

.log-item {
  margin-bottom: 10rpx;
}

.log-text {
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
}
</style>
