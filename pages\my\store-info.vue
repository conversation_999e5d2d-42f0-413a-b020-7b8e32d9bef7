<template>
  <view class="container">
    <!-- 加载状态 -->
    <view v-if="loading" class="loading-container">
      <text class="loading-text">加载中...</text>
    </view>

    <!-- 店铺信息表单 -->
    <view v-else class="form-container">
      <view class="form-item">
        <text class="form-label">店铺名称</text>
        <input class="form-input" type="text" placeholder="请输入店铺或店长名称" v-model="storeInfo.name" />
      </view>
      
      <view class="form-item">
        <text class="form-label">联系手机</text>
        <input class="form-input" type="number" placeholder="请填写真实手机号码" v-model="storeInfo.phone" />
      </view>
      
      <view class="form-item">
        <text class="form-label">绑定码</text>
        <view class="form-input-container">
          <input
            class="form-input-field"
            type="text"
            placeholder="请输入管理端提供的绑定码"
            v-model="storeInfo.bindCode"
            :disabled="isBindCodeDisabled"
            @blur="validateBindCode"
          />
          <view v-if="isBindCodeDisabled" class="bind-status">已绑定</view>
        </view>
      </view>
      
      <view class="form-item">
        <text class="form-label">所属类型</text>
        <view class="form-input selector" @click="showTypeSelector">
          <text>{{ getStoreTypeLabel() }}</text>
          <text class="dropdown-icon">▼</text>
        </view>
      </view>
      
      <view class="form-item">
        <text class="form-label">企业地址</text>
        <!-- <view class="form-input address" @click="selectAddress">
          <text>{{ storeInfo.address || '请输入企业地址' }}</text>
          <text class="location-icon">◎</text>
        </view> -->
		<view class="form-item">
		  <!-- <text class="form-label">企业地址</text> -->
		  <input class="form-input" type="text" placeholder="请输入企业地址" v-model="storeInfo.address" />
		</view>
      </view>
      
      <view class="form-detail-address">
        <input class="detail-input" type="text" placeholder="详细地址(如街道、小区、门牌号)" v-model="storeInfo.detailAddress" />
      </view>
    </view>
    
    <!-- 底部保存按钮 -->
    <view class="save-btn" :class="{ disabled: saving }" @click="saveStoreInfo">
      <text>{{ saving ? '保存中...' : '保存' }}</text>
    </view>
  </view>
</template>

<script>
import { apiService } from '@/utils/request.js';

export default {
  data() {
    return {
      storeInfo: {
        id: null,
        name: '',
        phone: '',
        bindCode: '',
        storeType: 1, // 1-门店，2-供应商
        address: '',
        detailAddress: '',
        province: '',
        city: '',
        district: '',
        latitude: null,
        longitude: null,
        description: ''
      },
      storeTypeOptions: [
        { value: 1, label: '门店/Store' },
        { value: 2, label: '工作室/Studio' },
        { value: 3, label: '美发沙龙/Hair Salon' }
      ],
      saving: false,
      loading: false,
      isBindCodeValid: false,
      bindCodeValidating: false,
      isStoreAlreadyBound: false
    };
  },
  computed: {
    isBindCodeDisabled() {
      // 如果已经有店铺ID，说明已经绑定了，禁用输入
      return this.isStoreAlreadyBound || (this.storeInfo.id && this.storeInfo.id !== null);
    }
  },
  onLoad() {
    // 加载已有的门店信息
    this.loadStoreInfo();
    // 设置位置选择监听器
    this.setupLocationListener();
  },
  onUnload() {
    // 移除事件监听器
    uni.$off('locationSelected');
  },
  methods: {
    async loadStoreInfo() {
      this.loading = true;
      try {
        const userInfo = uni.getStorageSync('userInfo');
        if (userInfo && userInfo.id) {
          const response = await apiService.mall.stores.userStores(userInfo.id);
          if (response.code === 200 && response.data && response.data.length > 0) {
            // 使用第一个门店作为当前门店
            const store = response.data[0];
            this.storeInfo = {
              id: store.id,
              name: store.name || '',
              phone: store.phone || '',
              bindCode: store.bindCode || store.bind_code || '',
              storeType: store.storeType || store.store_type || 1,
              address: store.address || '',
              detailAddress: '', // 可以从address中解析
              province: store.province || '',
              city: store.city || '',
              district: store.district || '',
              latitude: store.latitude || null,
              longitude: store.longitude || null,
              description: store.description || ''
            };

            // 如果有店铺ID，说明已经绑定了店铺
            if (store.id) {
              this.isStoreAlreadyBound = true;
              this.isBindCodeValid = true;
            }
          }
        }
      } catch (error) {
        console.error('加载门店信息失败:', error);
        // 静默失败，使用默认值
      } finally {
        this.loading = false;
      }
    },
    showTypeSelector() {
      // 显示门店类型选择器
      const itemList = this.storeTypeOptions.map(option => option.label);
      uni.showActionSheet({
        itemList,
        success: (res) => {
          this.storeInfo.storeType = this.storeTypeOptions[res.tapIndex].value;
        }
      });
    },
    getStoreTypeLabel() {
      const option = this.storeTypeOptions.find(opt => opt.value === this.storeInfo.storeType);
      return option ? option.label : '门店/Store';
    },
    // 验证绑定码
    async validateBindCode() {
      const bindCode = this.storeInfo.bindCode.trim();

      // 如果绑定码为空或已经绑定，不需要验证
      if (!bindCode || this.isStoreAlreadyBound) {
        return;
      }

      // 如果绑定码长度不符合要求
      if (bindCode.length < 6) {
        this.isBindCodeValid = false;
        return;
      }

      this.bindCodeValidating = true;

      try {
        const response = await apiService.mall.stores.validateBindCode(bindCode);

        if (response.code === 200 && response.data) {
          this.isBindCodeValid = true;
          // 可以显示店铺信息预览
          uni.showToast({
            title: '绑定码有效',
            icon: 'success'
          });
        } else {
          this.isBindCodeValid = false;
          uni.showToast({
            title: '绑定码无效或已被使用',
            icon: 'none'
          });
        }
      } catch (error) {
        console.error('验证绑定码失败:', error);
        this.isBindCodeValid = false;
        uni.showToast({
          title: '验证绑定码失败',
          icon: 'none'
        });
      } finally {
        this.bindCodeValidating = false;
      }
    },
    selectAddress() {
      // 打开位置选择器
      // uni.navigateTo({
      //   url: '/pages/location-picker'
      // });
    },

    // 设置位置选择监听器
    setupLocationListener() {
      // 监听从位置选择页面返回的数据
      uni.$on('locationSelected', (location) => {
        this.updateStoreLocation(location);
      });
    },

    // 更新店铺位置信息
    updateStoreLocation(location) {
      this.storeInfo.address = location.address || location.name;
      this.storeInfo.province = this.extractProvince(location.address);
      this.storeInfo.city = this.extractCity(location.address);
      this.storeInfo.district = this.extractDistrict(location.address);

      // 如果有经纬度信息，也保存
      if (location.latitude && location.longitude) {
        this.storeInfo.latitude = location.latitude;
        this.storeInfo.longitude = location.longitude;
      }

      uni.showToast({
        title: '地址已更新',
        icon: 'success'
      });
    },

    // 从地址中提取省份
    extractProvince(address) {
      if (!address) return '';
      const match = address.match(/^([^省]+省|[^自治区]+自治区|[^市]+市)/);
      return match ? match[1] : '';
    },

    // 从地址中提取城市
    extractCity(address) {
      if (!address) return '';
      const match = address.match(/省([^市]+市)|自治区([^市]+市)|^([^市]+市)/);
      return match ? (match[1] || match[2] || match[3]) : '';
    },

    // 从地址中提取区县
    extractDistrict(address) {
      if (!address) return '';
      const match = address.match(/市([^区县]+[区县])/);
      return match ? match[1] : '';
    },
    async saveStoreInfo() {
      // 防抖处理 - 如果正在保存中，直接返回
      if (this.saving) {
        return;
      }

      // 验证表单
      if (!this.storeInfo.name.trim()) {
        uni.showToast({
          title: '请输入店铺名称',
          icon: 'none'
        });
        return;
      }

      if (!this.storeInfo.phone.trim()) {
        uni.showToast({
          title: '请输入联系手机',
          icon: 'none'
        });
        return;
      }

      // 如果没有绑定店铺，需要验证绑定码
      if (!this.isStoreAlreadyBound) {
        if (!this.storeInfo.bindCode.trim()) {
          uni.showToast({
            title: '请输入绑定码',
            icon: 'none'
          });
          return;
        }

        if (!this.isBindCodeValid) {
          uni.showToast({
            title: '请输入有效的绑定码',
            icon: 'none'
          });
          return;
        }
      }

      // 验证手机号格式
      const phoneRegex = /^1[3-9]\d{9}$/;
      if (!phoneRegex.test(this.storeInfo.phone)) {
        uni.showToast({
          title: '请输入正确的手机号',
          icon: 'none'
        });
        return;
      }

      this.saving = true;

      try {
        const userInfo = uni.getStorageSync('userInfo');
		console.log("用户信息",userInfo)
        if (!userInfo || !userInfo.id) {
          throw new Error('用户信息不存在，请重新登录');
        }

        // 准备保存的数据
        // 拼接完整地址：定位地址 + 详细地址
        const baseAddress = [
          this.storeInfo.province || '',
          this.storeInfo.city || '',
          this.storeInfo.district || '',
          this.storeInfo.address || ''
        ].filter(item => item.trim()).join('');

        const fullAddress = baseAddress + (this.storeInfo.detailAddress ? this.storeInfo.detailAddress.trim() : '');

        const storeData = {
          name: this.storeInfo.name.trim(),
          phone: this.storeInfo.phone.trim(),
          storeType: this.storeInfo.storeType,
          address: fullAddress,
          province: this.storeInfo.province || '',
          city: this.storeInfo.city || '',
          district: this.storeInfo.district || '',
          latitude: this.storeInfo.latitude,
          longitude: this.storeInfo.longitude,
          description: this.storeInfo.description || ''
        };

        let response;
        if (this.storeInfo.id) {
          // 更新现有门店
          response = await apiService.mall.stores.update(this.storeInfo.id, storeData);
        } else if (this.storeInfo.bindCode.trim()) {
          // 通过绑定码绑定店铺
          response = await apiService.mall.stores.bindStore(this.storeInfo.bindCode.trim(), userInfo.id);

          if (response.code === 200) {
            // 绑定成功后，立即更新店铺信息
            this.storeInfo.id = response.data.id;
            this.isStoreAlreadyBound = true;

            // 绑定成功后，立即更新店铺的详细信息
            const updateResponse = await apiService.mall.stores.update(this.storeInfo.id, storeData);
            if (updateResponse.code !== 200) {
              throw new Error(updateResponse.message || '更新店铺信息失败');
            }
            response = updateResponse; // 使用更新的响应
          }
        } else {
          // 创建新门店
          response = await apiService.mall.stores.create(storeData, userInfo.id);
        }

        if (response.code === 200) {
          // 保存成功，更新本地存储
          uni.setStorageSync('storeInfo', JSON.stringify(this.storeInfo));

          uni.showToast({
            title: '保存成功',
            icon: 'success',
            duration: 2000
          });

          // 延迟返回上一页
          setTimeout(() => {
            uni.navigateBack();
          }, 2000);
        } else {
          throw new Error(response.message || '保存失败');
        }
      } catch (error) {
        console.error('保存门店信息失败:', error);
        uni.showToast({
          title: error.message || '保存失败，请重试',
          icon: 'none',
          duration: 3000
        });
      } finally {
        this.saving = false;
      }
    }
  }
};
</script>

<style lang="scss">
.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f5f5;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400rpx;
}

.loading-text {
  font-size: 30rpx;
  color: #999;
}

.form-container {
  background-color: #fff;
  padding: 0 20rpx;
}

.form-item {
  display: flex;
  align-items: center;
  padding: 30rpx 0;
  border-bottom: 1rpx solid #eee;
}

.form-label {
  width: 180rpx;
  font-size: 30rpx;
  color: #663c00;
  font-weight: 500;
}

.form-input {
  flex: 1;
  font-size: 30rpx;
  color: #999;
}

.form-input.disabled {
  color: #999;
  background-color: #f8f8f8;
  padding: 10rpx;
  border-radius: 8rpx;
  font-style: italic;
}

.form-input-container {
  flex: 1;
  display: flex;
  height: 50rpx;
  align-items: center;
  position: relative;
}

.form-input-field {
  flex: 1;
  font-size: 30rpx;
  height: 70rpx;
  color: #333;
  padding: 10rpx;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  background-color: #fff;
}

.form-input-field:disabled {
  background-color: #f8f8f8;
  color: #999;
}

.bind-status {
  position: absolute;
  right: 10rpx;
  font-size: 24rpx;
  color: #4285f4;
  background-color: #e3f2fd;
  padding: 4rpx 8rpx;
  border-radius: 4rpx;
}

.form-input.selector, .form-input.address {
  display: flex;
  justify-content: space-between;
}

.dropdown-icon, .location-icon {
  color: #999;
  font-size: 24rpx;
}

.form-detail-address {
  background-color: #fff;
  padding: 20rpx 0 30rpx 180rpx;
  border-bottom: 1rpx solid #eee;
}

.detail-input {
  font-size: 28rpx;
  color: #999;
}

.save-btn {
  position: fixed;
  bottom: 40rpx;
  left: 40rpx;
  right: 40rpx;
  height: 90rpx;
  background-color: #4285f4;
  border-radius: 45rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #fff;
  font-size: 32rpx;

  &.disabled {
    background-color: #ccc;
    color: #999;
  }
}
</style> 