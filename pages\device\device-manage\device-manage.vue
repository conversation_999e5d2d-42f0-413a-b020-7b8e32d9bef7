<template>
	<view class="device-management">
		<!-- 设备状态指示器 -->
		<view class="device-status-container">
			<view class="circles-container">
				<view class="circle circle-outer"></view>
				<view class="circle circle-middle"></view>
				<view class="circle circle-inner"></view>
				<view class="circle circle-content">
					<view class="wifi-icon">📶</view>
					<text class="status-text">{{ deviceStatus.isOnline ? '在线' : '离线' }}</text>
					<text class="weight-text">{{ deviceStatus.weight }}g</text>
					<text class="clear-text" @click="clearWeight">清零</text>
				</view>
			</view>
		</view>

		<!-- 标签栏 -->
		<view class="tab-bar">
			<view v-for="(tab, index) in tabs" :key="index" class="tab-item" :class="{ active: activeTab === index }"
				@click="switchTab(index)">
				{{ tab }}
			</view>
		</view>

		<!-- 页面内容 -->
		<view class="page-content">
			<!-- 操作设备页面 -->
			<view v-if="activeTab === 0" class="section">
				<view class="section-title">染膏余量</view>
				<!-- <view class="view-dye-btn-top" @click="goTo('dyeDetail')">查看染膏</view> -->
				<view class="dye-container">
					<view class="dye-grid">
						<view class="dye-item" v-for="(item, index) in colorList" :key="index"
							@click="selectColor(item, index)">
							<view class="dye-circle" :style="{ borderColor: item.hex }">
								<text class="percentage">{{ getDyePercentage(item) }}%</text>
							</view>
							<text class="color-name">{{ item.name }}</text>
						</view>
					</view>
				</view>
			</view>

			<!-- 设备信息页面 -->
			<view v-if="activeTab === 1" class="info-section">
				<view class="section-title">设备信息</view>
				<view class="info-item">
					<text class="info-label">设备编号</text>
					<text class="info-value">{{ deviceInfo.deviceNo || deviceCode }}</text>
				</view>
				<view class="info-item">
					<text class="info-label">用户手机号</text>
					<text class="info-value">{{ deviceInfo.phone || phone }}</text>
				</view>
				<view class="info-item">
					<text class="info-label">设备账号</text>
					<text class="info-value">{{ deviceInfo.account }}</text>
				</view>
				<view class="info-item">
					<text class="info-label">设备型号</text>
					<text class="info-value">{{ deviceInfo.model }}</text>
				</view>
				<view class="info-item">
					<text class="info-label">所属门店</text>
					<text class="info-value">{{ deviceInfo.store }}</text>
				</view>
				<view class="info-item">
					<text class="info-label">到期时间</text>
					<text class="info-value">{{ deviceInfo.expireDate }}</text>
				</view>
				<view class="info-item">
					<text class="info-label">固件版本</text>
					<text class="info-value">{{ deviceInfo.firmwareVersion }}</text>
				</view>
			</view>
			
			 <scale-calibration 
			      ref="scaleCalibration" 
			      :current-weight="scaleWeight" 
			      @calibration-complete="onCalibrationComplete"
			      @close="onCalibrationClose"
			    />

			<!-- 系统设置页面 -->
			<view v-if="activeTab === 2" class="section">
			  <view class="section-title">系统设置</view>
			  
			  <!-- 出料速度 -->
			  <view class="setting-item" @click="showSetting({ type: 'dischargeSpeed', title: '出料速度' })">
			    <text class="setting-label">出料速度</text>
			    <text class="setting-action">设置</text>
			  </view>
			  
			  <!-- 氛围灯颜色 -->
			  <view class="setting-item" @click="showSetting({ type: 'ambientLight', title: '氛围灯颜色' })">
			    <text class="setting-label">氛围灯颜色</text>
			    <text class="setting-action">设置</text>
			  </view>
			  
			  <!-- 获取设备ID -->
			  <view class="setting-item" @click="showSetting({ type: 'deviceId', title: '获取设备ID', action: 'get' })">
			    <text class="setting-label">获取设备ID</text>
			    <text class="setting-action">获取</text>
			  </view>
			  
			  <!-- 电子秤校准 -->
			  <view class="setting-item" @click="showSetting({ 
			    type: 'scaleCalibration', 
			    title: '电子秤校准', 
			    steps: ['空载校准', '标准砝码校准'] 
			  })">
			    <text class="setting-label">电子秤校准</text>
			    <text class="setting-action">校准</text>
			  </view>
			  
			  <!-- 测试电机 -->
			  <view class="setting-item" @click="showSetting({ type: 'motorTest', title: '测试电机' })">
			    <text class="setting-label">测试电机</text>
			    <text class="setting-action">测试</text>
			  </view>
			  
			  <!-- 清除校准数据 -->
			  <view class="setting-item" @click="showSetting({ 
			    type: 'clearCalibration', 
			    title: '清除校准数据',
			    confirmText: '确定要清除所有校准数据吗？此操作不可恢复！'
			  })">
			    <text class="setting-label">清除校准数据</text>
			    <text class="setting-action">清除</text>
			  </view>
			  
			  <!-- 连接WIFI -->
			  <view class="setting-item" @click="showSetting({ 
			    type: 'wifi', 
			    title: '连接WIFI',
			    fields: [{ name: 'ssid', label: '网络名称' }, { name: 'password', label: '密码', type: 'password' }]
			  })">
			    <text class="setting-label">连接WIFI</text>
			    <text class="setting-action">连接</text>
			  </view>
			  
			  <!-- APN -->
			  <view class="setting-item" @click="showSetting({ 
			    type: 'apn', 
			    title: 'APN设置',
			    fields: [
			      { name: 'apn', label: 'APN' },
			      { name: 'username', label: '用户名' },
			      { name: 'password', label: '密码', type: 'password' }
			    ]
			  })">
			    <text class="setting-label">APN</text>
			    <text class="setting-action">设置</text>
			  </view>
			</view>
		</view>

		<!-- 自定义弹窗 -->
		<uni-popup ref="popup" type="dialog">
			<uni-popup-dialog :type="popupType" :title="popupTitle" :content="popupContent" :duration="2000"
				:before-close="true" @close="popupClose" @confirm="popupConfirm">
				<view class="popup-content">
					<view class="color-display" :style="{ backgroundColor: selectedColorHex }"></view>
					<view class="color-details">
						<view class="detail-row">
							<text class="detail-label">颜色名称:</text>
							<text class="detail-value">{{ selectedColorName }}</text>
						</view>
						<view class="detail-row">
							<text class="detail-label">仓位编号:</text>
							<text class="detail-value">{{ selectedColorPosition }}</text>
						</view>
						<view class="detail-row">
							<text class="detail-label">剩余量:</text>
							<text class="detail-value"
								:style="{ color: remainingQuantity < 20 ? '#ff4d4f' : '#52c41a' }">
								{{ remainingQuantity }} {{ unit }}
							</text>
						</view>
						<view class="progress-container">
							<progress :percent="remainingQuantity" stroke-width="10"
								:activeColor="remainingQuantity < 20 ? '#ff4d4f' : '#52c41a'"
								backgroundColor="#f0f0f0" />
						</view>
						<view class="status-message" :style="{ color: remainingQuantity < 20 ? '#ff4d4f' : '#52c41a' }">
							{{ remainingQuantity < 20 ? '余量不足，请及时补充' : '余量充足' }}
						</view>
					</view>
				</view>
			</uni-popup-dialog>
		</uni-popup>
	</view>
</template>

<script>
	import {
		apiService
	} from '../../../utils/request'
	import ScaleCalibration from '@/components/ScaleCalibration.vue'

	export default {
		components: {
			ScaleCalibration
		},
		data() {
			return {
				// 电子秤相关操作
				scaleWeight: 0,
				tabs: ['操作设备', '设备信息', '系统设置'],
				activeTab: 0,
				deviceCode: '',
				deviceStatus: {
					isOnline: false,
					weight: 0
				},
				deviceInfo: {
					deviceNo: '',
					deviceId: '',
					account: '<EMAIL>',
					model: 'AISRD-X1',
					store: '芭佰邑旗舰店',
					expireDate: '2025-12-31',
					firmwareVersion: 'v1.2.3'
				},
				colorList: [{
						name: '0/11灰色/Grey',
						color_code: '0x30',
						hex: '#8B8B8B',
						value: 0
					},
					{
						name: '0/22绿色/Green',
						color_code: '0x31',
						hex: '#2E7D32',
						value: 0
					},
					{
						name: '0/33黄色/yellow',
						color_code: '0x32',
						hex: '#FFD700',
						value: 0
					},
					{
						name: '0/43橙色/Orange',
						color_code: '0x33',
						hex: '#FFA726',
						value: 0
					},
					{
						name: '0/45红色/Red',
						color_code: '0x34',
						hex: '#D32F2F',
						value: 0
					},
					{
						name: '0/66紫色/Purple',
						color_code: '0x35',
						hex: '#8E24AA',
						value: 0
					},
					{
						name: '0/77棕色/Brown',
						color_code: '0x36',
						hex: '#6D4C41',
						value: 0
					},
					{
						name: '0/88蓝色/Blue',
						color_code: '0x37',
						hex: '#1565C0',
						value: 0
					},
					{
						name: '2/0黑色/Black',
						color_code: '0x38',
						hex: '#000000',
						value: 0
					},
					{
						name: '0/00淡化剂/Faded',
						color_code: '0x39',
						hex: '#FAFAFA',
						value: 0
					},
					{
						name: '12/11极浅灰金色/Bleached',
						color_code: '0x40',
						hex: '#E0E0E0',
						value: 0
					},
					{
						name: '12/16极浅紫金/Silver',
						color_code: '0x41',
						hex: '#E1BEE7',
						value: 0
					},
					{
						name: '浅棕色/Gold',
						color_code: '0x42',
						hex: '#6D4C41',
						value: 0
					},
					{
						name: '深灰亚麻色/Linen',
						color_code: '0x43',
						hex: '#B0BEC5',
						value: 0
					},
					{
						name: '双氧乳3%',
						color_code: '0x0A',
						hex: '#FAFAFA',
						value: 0
					},
					{
						name: '双氧乳12%',
						color_code: '0x0B',
						hex: '#FAFAFA',
						value: 0
					},
				],
				selectedIndex: null,
				selectedColorName: '',
				selectedColorHex: '',
				selectedColorPosition: '',
				unit: 'g',
				remainingQuantity: 0,
				popupType: 'info',
				popupTitle: '',
				popupContent: ''
			}
		},

		onLoad() {
			const app = getApp()
			this.deviceCode = app.globalData.connectedDeviceId
			this.deviceStatus.isOnline = app.globalData.deviceStatus

			if (this.deviceCode && this.deviceStatus.isOnline) {
				this.initDeviceInfo()
			}
		},

		methods: {
			showCalibration() {
				this.$refs.scaleCalibration.open()
			},
			onCalibrationComplete(data) {
				console.log('校准完成', data)
				// uni.showToast({
				// 	title: '校准成功',
				// 	icon: 'success'
				// })
			},
			onCalibrationClose() {
				console.log('校准弹窗关闭')
			},
			async initDeviceInfo() {
				try {
					const response = await apiService.device.getDeviceInfo(this.deviceCode)
					if (response.code === 200) {
						this.deviceInfo = {
							...this.deviceInfo,
							...response.data
						}
					}
				} catch (error) {
					console.error('获取设备信息失败:', error)
				}
			},

			getDyePercentage(item) {
				// 这里可以根据实际业务逻辑计算百分比
				return item.value || 0
			},

			async selectColor(color, index) {
				this.selectedIndex = index
				this.selectedColorName = color.name
				this.selectedColorHex = color.hex
				this.selectedColorPosition = color.color_code

				await this.checkColorRemaining()
			},

			async checkColorRemaining() {
				if (this.selectedIndex === null) return

				try {
					uni.showLoading({
						title: '检测中...',
						mask: true
					})

					const selectedColor = this.colorList[this.selectedIndex]
					const response = await apiService.mall.dyeConsumption.checkRemaining({
						deviceCode: this.deviceCode,
						colorCode: selectedColor.color_code
					})

					if (response.code === 200) {
						this.remainingQuantity = response.data.remainingQuantity || 0
						this.unit = response.data.unit || 'g'
						this.popupType = this.remainingQuantity < 20 ? 'error' : 'success'
						this.popupTitle = '染膏余量检测'
						this.popupContent = `当前颜色余量: ${response.data.proportion}${this.unit}`

						this.$refs.popup.open()
					} else {
						throw new Error(response.message || '查询余量失败')
					}
				} catch (error) {
					console.error('检测余量失败:', error)
					uni.showToast({
						title: error.message || '检测余量失败',
						icon: 'none',
						duration: 2000
					})
				} finally {
					uni.hideLoading()
				}
			},

			switchTab(index) {
				this.activeTab = index
			},

			clearWeight() {
				this.deviceStatus.weight = 0
				uni.showToast({
					title: '重量已清零',
					icon: 'success',
					duration: 1500
				})
			},

			showSetting(config) {
			    // 根据不同的配置类型显示不同的弹窗或执行不同的操作
			    switch(config.type) {
			      case 'dischargeSpeed':
			        this.showDischargeSpeedSetting(config)
			        break
			      case 'ambientLight':
			        this.showAmbientLightSetting(config)
			        break
			      case 'deviceId':
			        this.getDeviceId(config)
			        break
			      case 'scaleCalibration':
			        this.$refs.scaleCalibration.open(config)
			        break
			      case 'motorTest':
			        this.testMotor(config)
			        break
			      case 'clearCalibration':
			        this.clearCalibrationData(config)
			        break
			      case 'wifi':
			        this.showWifiSetting(config)
			        break
			      case 'apn':
			        this.showApnSetting(config)
			        break
			      default:
			        console.warn('未知的设置类型:', config.type)
			    }
			  },
			  
			  showScaleCalibration(config) {
			      this.$refs.scaleCalibration.open(config)
			    },
			    
			    // 示例方法 - 获取设备ID
			    getDeviceId(config) {
			      uni.showLoading({ title: '获取中...' })
			      // 调用获取设备ID的API
			      getDeviceIdApi().then(res => {
			        uni.hideLoading()
			        uni.showModal({
			          title: config.title,
			          content: `设备ID: ${res.deviceId}`,
			          showCancel: false
			        })
			      }).catch(err => {
			        uni.hideLoading()
			        uni.showToast({ title: '获取失败', icon: 'none' })
			      })
			    },

			disconnectDevice() {
				uni.showModal({
					title: '确认断开',
					content: '确定要断开设备连接吗？',
					success: (res) => {
						if (res.confirm) {
							uni.showToast({
								title: '设备已断开',
								icon: 'success',
								duration: 1500
							})
							// 这里可以添加实际的断开逻辑
						}
					}
				})
			},

			goTo(page) {
				uni.showToast({
					title: `跳转到${page}`,
					icon: 'none',
					duration: 1500
				})
			},

			popupClose() {
				console.log('弹窗关闭')
			},

			popupConfirm() {
				this.$refs.popup.close()
			}
		}
	}
</script>

<style lang="scss">
	/* 保持原有的样式不变 */
	.device-management {
		width: 100%;
		display: flex;
		flex-direction: column;
		height: 100vh;
		background-color: #f8f8f8;
		font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
	}

	.header {
		background-color: #4285f4;
		color: white;
		padding: 20px;
		position: relative;

		.main-title {
			font-size: 20px;
			font-weight: bold;
			text-align: center;
			margin-bottom: 10px;
		}

		.sub-title {
			text-align: center;
			font-size: 14px;
			margin-bottom: 15px;

			.underline {
				text-decoration: underline;
				margin-left: 5px;
			}
		}

		.disconnect-btn-container {
			position: absolute;
			top: 20px;
			right: 20px;

			.disconnect-btn {
				background-color: rgba(255, 255, 255, 0.2);
				padding: 8px 16px;
				border-radius: 20px;
				font-size: 12px;
				border: 1px solid rgba(255, 255, 255, 0.3);
			}
		}

		.divider-line {
			height: 1px;
			background-color: rgba(255, 255, 255, 0.3);
			margin-top: 10px;
		}
	}

	.device-status-container {
		display: flex;
		justify-content: center;
		align-items: center;
		padding: 30px 0;
		background-color: white;
		position: relative;
		height: 200px; // 给一个固定高度确保有足够空间

		.circles-container {
			position: relative;
			width: 180px;
			height: 180px;
			display: flex;
			justify-content: center;
			align-items: center;

			.circle {
				position: absolute;
				border-radius: 50%;
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;
			}

			.circle-outer {
				width: 180px;
				height: 180px;
				background: linear-gradient(135deg, #e0e0e0, #b0b0b0);
				opacity: 0.6;
			}

			.circle-middle {
				width: 160px;
				height: 160px;
				background: linear-gradient(135deg, #d0d0d0, #a0a0a0);
				opacity: 0.7;
			}

			.circle-inner {
				width: 140px;
				height: 140px;
				background: linear-gradient(135deg, #c0c0c0, #909090);
				opacity: 0.8;
			}

			.circle-content {
				width: 120px;
				height: 120px;
				background-color: white;
				border: 2px solid #4285f4;
				z-index: 2;

				.wifi-icon {
					font-size: 18px;
					margin-bottom: 2px;
				}

				.status-text {
					font-size: 12px;
					color: #4285f4;
					font-weight: bold;
					margin-bottom: 2px;
				}

				.weight-text {
					font-size: 14px;
					font-weight: bold;
					color: #333;
					margin-bottom: 2px;
				}

				.clear-text {
					font-size: 10px;
					color: #666;
					text-decoration: underline;
				}
			}
		}
	}

	.tab-bar {
		display: flex;
		background-color: white;
		border-bottom: 1px solid #e0e0e0;

		.tab-item {
			flex: 1;
			text-align: center;
			padding: 15px 0;
			font-size: 16px;
			color: #666;
			border-bottom: 3px solid transparent;

			&.active {
				color: #4285f4;
				border-bottom-color: #4285f4;
				font-weight: bold;
			}
		}
	}

	.page-content {
		flex: 1;
		overflow-y: auto;
		background-color: white;
	}

	.section {
		padding: 20px;

		.section-title {
			font-size: 18px;
			font-weight: bold;
			color: #333;
			margin-bottom: 20px;
			position: relative;
		}

		.view-dye-btn-top {
			position: absolute;
			top: 0;
			right: 20px;
			background-color: #4285f4;
			color: white;
			padding: 8px 16px;
			border-radius: 20px;
			font-size: 12px;
		}
	}

	.dye-container {
		.dye-grid {
			display: flex;
			flex-wrap: wrap;
			gap: 20px;
			justify-content: space-between;

			.dye-item {
				width: calc(33.333% - 14px);
				display: flex;
				flex-direction: column;
				align-items: center;

				.dye-circle {
					width: 60px;
					height: 60px;
					border: 3px solid;
					border-radius: 50%;
					display: flex;
					align-items: center;
					justify-content: center;
					margin-bottom: 8px;

					.percentage {
						font-size: 12px;
						font-weight: bold;
						color: #333;
					}
				}

				.color-name {
					font-size: 12px;
					color: #666;
					text-align: center;
				}
			}
		}
	}

	.info-section {
		padding: 20px;

		.section-title {
			font-size: 18px;
			font-weight: bold;
			color: #333;
			margin-bottom: 20px;
		}

		.info-item {
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 15px 0;
			border-bottom: 1px solid #f0f0f0;

			.info-label {
				font-size: 14px;
				color: #666;
			}

			.info-value {
				font-size: 14px;
				color: #333;
				font-weight: 500;
			}
		}
	}

	.setting-item {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 15px 0;
		border-bottom: 1px solid #f0f0f0;

		.setting-label {
			font-size: 14px;
			color: #333;
		}

		.setting-action {
			font-size: 14px;
			color: #4285f4;
		}
	}

	/* 新增弹窗样式 */
	.popup-content {
		padding: 20rpx;
		width: 100%;
	}

	.color-display {
		width: 120rpx;
		height: 80rpx;
		margin: 0 auto 30rpx;
		border-radius: 8rpx;
		box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
	}

	.color-details {
		margin-top: 20rpx;
	}

	.detail-row {
		display: flex;
		justify-content: space-between;
		margin-bottom: 20rpx;
		font-size: 28rpx;
	}

	.detail-label {
		color: #666;
	}

	.detail-value {
		color: #333;
		font-weight: 500;
	}

	.progress-container {
		margin: 30rpx 0;
	}

	.status-message {
		text-align: center;
		font-size: 28rpx;
		font-weight: 500;
		margin-top: 20rpx;
	}

	/* 调整uni-popup默认样式 */
	.uni-popup__wrapper {
		border-radius: 20rpx;
		overflow: hidden;
	}

	.uni-popup__dialog {
		width: 80%;
		max-width: 600rpx;
	}
</style>