
	/* 保持原有的样式不变 */
.content {
		display: flex;
		flex-direction: column;
		align-items: stretch;
		height: 100vh;
		background-color: #ffffff;
		position: relative;
		width: 100%;
		box-sizing: border-box;
		overflow-x: hidden;
}
.header {
		background-color: #4285f4;
		color: white;
		position: relative;
		padding: 40px 15px 15px;
		display: flex;
		align-items: center;
		justify-content: center;
		width: 100%;
		box-sizing: border-box;
}
.title-center {
		font-size: 18px;
		font-weight: bold;
		text-align: center;
		flex: 1;
}
.device-list {
		flex: 1;
		padding: 0 20rpx;
		padding-bottom: 200rpx;
		/* 为固定按钮留出空间 */
		margin-bottom: 20rpx;
}
.device-item {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 25rpx 0;
		border-bottom: 1rpx solid #f0f0f0;
}
.device-info {
		flex: 1;
		display: flex;
		flex-direction: column;
}
.device-name {
		font-size: 30rpx;
		font-weight: bold;
		color: #333;
		margin-bottom: 8rpx;
}
.device-id {
		font-size: 22rpx;
		color: #999;
		word-break: break-all;
		margin-bottom: 8rpx;
}
.device-rssi {
		font-size: 22rpx;
		color: #666;
}
.device-actions {
		margin-left: 20rpx;
}
.connect-btn {
		background-color: #1aad19;
		color: white;
		padding: 10rpx 20rpx;
		border-radius: 8rpx;
		font-size: 24rpx;
}
.connect-btn.connected {
		background-color: #999;
}
.empty-tip {
		text-align: center;
		padding: 40rpx 0;
		font-size: 26rpx;
		color: #999;
}
.action-buttons {
		/* padding-bottom: calc(20rpx + constant(safe-area-inset-bottom));
		padding-bottom: calc(20rpx + env(safe-area-inset-bottom)); */
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		background: white;
		padding: 20rpx;
		box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.1);
		z-index: 100;
		display: flex;
		flex-direction: column;
}
.scan-btn {
		height: 50px;
		background-color: #4285f4;
		border-radius: 25px;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-bottom: 15px;
}
.scan-btn.scanning {
		background-color: #ff9900;
}
.disconnect-btn {
		height: 50px;
		background-color: #e64340;
		border-radius: 25px;
		display: flex;
		align-items: center;
		justify-content: center;
}
.scan-btn-text {
		color: white;
		font-size: 16px;
}
.usage-buttons {
		margin-bottom: 15px;
}
.start-usage-btn {
		height: 50px;
		background-color: #1aad19;
		border-radius: 25px;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-bottom: 10px;
}
.end-usage-btn {
		height: 50px;
		background-color: #e64340;
		border-radius: 25px;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-bottom: 10px;
}
.usage-btn-text {
		color: white;
		font-size: 16px;
}
.usage-info {
		text-align: center;
		padding: 10px;
		background-color: #f8f8f8;
		border-radius: 10px;
}
.usage-time {
		font-size: 14px;
		color: #333;
		font-weight: bold;
}
.log-container {
		padding: 20rpx;
		border-top: 1rpx solid #f0f0f0;
}
.section-title {
		font-size: 28rpx;
		font-weight: bold;
		color: #333;
		margin-bottom: 20rpx;
		display: block;
}
.log-list {
		max-height: 200rpx;
}
.log-item {
		font-size: 24rpx;
		color: #666;
		padding: 5rpx 0;
		border-bottom: 1rpx dashed #eee;
}


	/* 颜色变量 */
page {
		background-color: #F7F9FC;
		height: 100%;
}
.device-page {
		padding: 0 24rpx;
		height: 100vh;
		display: flex;
		flex-direction: column;
		background-color: #F7F9FC;
}
.wifi-property-btn {
		background-color: #1A8CFF;
		color: white;
		width: 90%;
		height: 90rpx;
		border-radius: 45rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 32rpx;
		margin-top: 30rpx;
		margin-bottom: 40rpx;
}

	/* 顶部标题 */
.page-header {
		padding: 40rpx 0 30rpx;
		text-align: center;
}
.header-title {
		font-size: 38rpx;
		font-weight: 600;
		color: #333;
}

	/* 连接方式选项卡 */
.connection-tabs {
		display: flex;
		justify-content: space-between;
		background-color: #FFFFFF;
		border-radius: 16rpx;
		padding: 0 40rpx;
		margin-bottom: 30rpx;
		box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.04);
}
.tab-item {
		padding: 24rpx 0;
		font-size: 30rpx;
		color: #666;
		position: relative;
		text-align: center;
		flex: 1;
}
.tab-item.active {
		color: #1A8CFF;
		font-weight: 500;
}
.tab-indicator {
		position: absolute;
		bottom: 0;
		left: 50%;
		transform: translateX(-50%);
		width: 60rpx;
		height: 6rpx;
		background-color: #1A8CFF;
		border-radius: 3rpx;
}

	/* 设备信息卡片 */
.device-info-card {
		background-color: #FFFFFF;
		border-radius: 16rpx;
		padding: 32rpx;
		margin-bottom: 40rpx;
		box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.04);
}
.info-item {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 32rpx;
}
.info-item:last-child {
		margin-bottom: 0;
}
.info-label {
		font-size: 30rpx;
		color: #666;
}
.info-value {
		font-size: 30rpx;
		color: #333;
		font-weight: 500;
}
.password-field {
		display: flex;
		align-items: center;
}
.show-password {
		font-size: 26rpx;
		color: #1A8CFF;
		margin-left: 20rpx;
}

	/* 连接按钮 */
.connect-btn {
		background: linear-gradient(90deg, #1A8CFF, #4DB8FF);
		color: #fff;
		border-radius: 50rpx;
		font-size: 34rpx;
		height: 96rpx;
		line-height: 96rpx;
		margin: 0 30rpx;
		border: none;
		box-shadow: 0 8rpx 24rpx rgba(26, 140, 255, 0.3);
}
.connect-btn::after {
		border: none;
}
.connect-btn:active {
		opacity: 0.9;
}

	/* 底部导航栏 */
.tab-bar {
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		display: flex;
		justify-content: space-around;
		background-color: #FFFFFF;
		padding: 16rpx 0 20rpx;
		border-top: 1rpx solid #EBEDF0;
		box-shadow: 0 -4rpx 12rpx rgba(0, 0, 0, 0.04);
}
.tab-bar-item {
		display: flex;
		flex-direction: column;
		align-items: center;
}
.tab-icon {
		width: 48rpx;
		height: 48rpx;
		margin-bottom: 8rpx;
}
.tab-text {
		font-size: 24rpx;
		color: #666;
}
.tab-bar-item.active .tab-text {
		color: #1A8CFF;
}

	/* wifi连接样式 */
.wifi-connection {
		padding: 40rpx;
}
.wifi-form {
		background-color: #FFFFFF;
		border-radius: 16rpx;
		padding: 32rpx;
		margin-bottom: 40rpx;
		box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.04);
}
.form-item {
		display: flex;
		align-items: center;
		margin-bottom: 32rpx;
		padding-bottom: 32rpx;
		border-bottom: 1rpx solid #f0f0f0;
}
.form-item:last-child {
		margin-bottom: 0;
		padding-bottom: 0;
		border-bottom: none;
}
.form-label {
		font-size: 30rpx;
		color: #666;
		width: 150rpx;
}
.form-input {
		flex: 1;
		font-size: 30rpx;
		color: #333;
		height: 60rpx;
		padding: 0 20rpx;
}
.show-password {
		font-size: 26rpx;
		color: #1A8CFF;
		margin-left: 20rpx;
}
.connect-btn {
		background: linear-gradient(90deg, #1A8CFF, #4DB8FF);
		color: #fff;
		border-radius: 50rpx;
		font-size: 34rpx;
		height: 96rpx;
		line-height: 96rpx;
		margin: 0 30rpx;
		border: none;
		box-shadow: 0 8rpx 24rpx rgba(26, 140, 255, 0.3);
}
.connect-btn::after {
		border: none;
}
.connect-btn:active {
		opacity: 0.9;
}

	/* 联网模块样式 */
.network-connection {
		padding: 30rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		height: 100%;
}
.connection-row {
		display: flex;
		justify-content: space-around;
		width: 100%;
		background-color: #FFFFFF;
		border-radius: 16rpx;
		padding: 30rpx 0;
		margin-bottom: 30rpx;
		box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.04);
}
.connection-item {
		display: flex;
		flex-direction: column;
		align-items: center;
		flex: 1;
}
.connection-label {
		font-size: 30rpx;
		color: #333;
		margin-bottom: 15rpx;
}
.connection-status {
		font-size: 28rpx;
		color: #999;
}
.connection-status.active {
		color: #1A8CFF;
}
.scan-device-btn {
		background-color: #1A8CFF;
		color: white;
		width: 90%;
		height: 90rpx;
		border-radius: 45rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 32rpx;
		margin-top: 30rpx;
		margin-bottom: 40rpx;
}

	/* 设备状态卡片样式 */
.device-status-card,
	.inventory-status-card {
		background-color: #ffffff;
		border-radius: 16rpx;
		margin: 20rpx;
		padding: 30rpx;
		box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
}
.status-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 20rpx;
}
.status-title {
		font-size: 32rpx;
		font-weight: bold;
		color: #333;
}
.status-indicator {
		padding: 8rpx 16rpx;
		border-radius: 20rpx;
		background-color: #f5f5f5;
		color: #999;
		font-size: 24rpx;
}
.status-indicator.online {
		background-color: #e8f5e8;
		color: #52c41a;
}
.status-details {
		display: flex;
		flex-direction: column;
		gap: 16rpx;
}
.status-item {
		display: flex;
		justify-content: space-between;
		align-items: center;
}
.status-label {
		font-size: 28rpx;
		color: #666;
}
.status-value {
		font-size: 28rpx;
		color: #333;
		font-weight: 500;
}
.refresh-btn {
		padding: 8rpx 16rpx;
		border-radius: 20rpx;
		background-color: #f0f9ff;
		color: #1890ff;
		font-size: 24rpx;
}

	/* 库存预警样式 */
.alert-section {
		margin-bottom: 20rpx;
}
.alert-title {
		font-size: 28rpx;
		font-weight: bold;
		color: #ff6b35;
		margin-bottom: 16rpx;
}
.alert-item {
		margin-bottom: 8rpx;
}
.alert-text {
		font-size: 24rpx;
		padding: 4rpx 12rpx;
		border-radius: 12rpx;
}
.alert-text.warning {
		background-color: #fff3cd;
		color: #856404;
}
.alert-text.critical {
		background-color: #f8d7da;
		color: #721c24;
}
.inventory-summary {
		display: flex;
		justify-content: space-between;
		padding-top: 20rpx;
		border-top: 1px solid #eee;
}
.summary-text {
		font-size: 24rpx;
		color: #666;
}



	/* 响应式适配 */
@media screen and (max-width: 390px) {
.content,
		.header {
			width: 100% !important;
}
}

	/* 安全区域适配 */
@supports (padding-bottom: constant(safe-area-inset-bottom)) or (padding-bottom: env(safe-area-inset-bottom)) {
.content {
			padding-bottom: calc(20px + constant(safe-area-inset-bottom));
			padding-bottom: calc(20px + env(safe-area-inset-bottom));
}
}
