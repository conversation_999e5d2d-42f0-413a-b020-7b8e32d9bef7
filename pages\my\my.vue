<template>
	<view class="content">
		<!-- 用户信息 -->

		<!-- <view class="follow-info">
			<text>0关注</text>
			<text>0粉丝</text>
		</view> -->
		<!-- 用户信息区域 -->
		<view class="user-info">
			<!-- 头像部分 -->
			<view class="avatar-container" @click="handleAvatarTap">
				<!-- 未登录状态 -->
				<template v-if="!isLogin" >
					<view class="avatar-placeholder"></view>
					<!-- <image v-if="tempAvatar" :src="tempAvatar" class="avatar"></image>
			  <view v-else class="avatar-placeholder">
				<view v-else class="avatar-placeholder"></view>
			    <image src="/static/default-avatar.png" class="default-avatar"></image>
			  </view> -->
				</template>

				<!-- 已登录状态 -->
				<template v-else>
					<image :src="userInfo.avatarUrl || '/static/logo.png'" class="avatar"></image>
				</template>
			</view>

			<!-- 用户名/登录提示 -->
			<view class="user-text">
				<text v-if="!isLogin">请登录</text>
				<text v-else>{{ userInfo.nickname || userInfo.phone || '微信用户' }}</text>
			</view>

			<!-- 关注信息（登录后显示） -->
			<view v-if="isLogin" class="follow-info">
				<text>{{ followCount ? followCount : 0}} 关注</text>
				<text>{{ fansCount ? fansCount : 0}} 粉丝</text>
			</view>
		</view>

		<!-- 数据统计 -->
		<view class="statistics">
			<view class="stat-item" @click="navigateToCollections">
				<text class="stat-num">{{ statistics.favoriteCount }}</text>
				<text class="stat-label">收藏</text>
			</view>
			<view class="stat-item" @click="navigateToWorks">
				<text class="stat-num">{{ statistics.worksCount }}</text>
				<text class="stat-label">作品</text>
			</view>
			<view class="stat-item" @click="navigateToColorRecord">
				<text class="stat-num">{{ statistics.colorRecordCount }}</text>
				<text class="stat-label">调色记录</text>
			</view>
			<view class="stat-item">
				<text class="stat-num">{{ statistics.formulaCount }}</text>
				<text class="stat-label">常用配方</text>
			</view>
		</view>

		<!-- 当前门店 -->
		<view class="current-store">
			<text class="store-label">当前门店:</text>
			<text class="store-name">{{ storeInfo.name }}</text>
			<text class="store-arrow">▼</text>
		</view>

		<!-- 功能菜单 -->
		<view class="function-menu">
			<view class="menu-row">
				<view class="menu-item" @click="navigateToStoreInfo">
					<!-- <view class="icon-placeholder"></view> -->
					<image class="icon-placeholder" src="/static/icons/store-icon.png"></image>
					<text>门店</text>
				</view>
				<view class="menu-item" @click="navigateToStaffManage">
					<image class="icon-placeholder" src="/static/icons/member-icon.png"></image>
					<text>成员</text>
				</view>
				<view class="menu-item" @click="navigateToDeviceList">
					<!-- <view class="icon-placeholder"></view> -->
					<image class="icon-placeholder" src="/static/icons/equipment-icon.png" />
					<text>设备</text>
				</view>
				<view class="menu-item" @click="navigateToStoreQrcode">
					<image class="icon-placeholder" src="/static/icons/qrcode-icon.png" />
					<text>门店二维码</text>
				</view>
			</view>

			<view class="menu-row">
				<view class="menu-item" @click="navigateToColorTest">
					<!-- <view class="icon-placeholder"></view> -->
					<image class="icon-placeholder" src="/static/icons/dye-icon.png" />
					<text>色膏检测</text>
				</view>
				<view class="menu-item" @click="navigateToColorRecord">
					<!-- <view class="icon-placeholder"></view>
					 -->
					 <image class="icon-placeholder" src="/static/icons/color-record-icon.png" />
					<text>调色记录</text>
				</view>
				<view class="menu-item" @click="navigateToUsageStats">
					<view class="icon-placeholder stats-icon">📊</view>
					<text>使用统计</text>
				</view>
				<view class="menu-item" @click="navigateToMyOrders">
					<image class="icon-placeholder" src="/static/icons/order-icon.png" />
					<text>订单管理</text>
				</view>
				
				<view class="menu-item" @click="navigateToMyDeviceManage">
					<image class="icon-placeholder" src="/static/icons/order-icon.png" />
					<text>设备权限</text>
				</view>
			</view>
		</view>

		<!-- 新手指引 -->
		<view class="guide-banner" @click="navigateToGuide">
			<image class="icon_f" src="/static/icons/xin_shou_ying_dao_f.png" />
			<text style="margin-left: 10rpx;">设备使用新手引导</text>
			<text class="check-now">立即查看 »</text>
		</view>

		<!-- 常用功能 -->
		<view class="common-functions">
			<view class="section-title">常用功能</view>
			<view class="function-grid">
				<view class="function-item" @click="navigateToRepair">
					<image class="icon-placeholder" src="/static/icons/bao_xiu.png" />
					<text>报修</text>
				</view>
				<view class="function-item" @click="navigateToPolicy">
					<image class="icon-placeholder" src="/static/icons/xie_yi_zheng_ce.png" />
					<text>协议政策</text>
				</view>
				<view class="function-item" @click="navigateToMessage">
					<image class="icon-placeholder" src="/static/icons/xiao_xi.png" />
					<text>消息</text>
				</view>
				<view class="function-item" @click="navigateToAbout">
					<image class="icon-placeholder" src="/static/icons/guan_yu.png" />
					<text>关于</text>
				</view>
				<view class="function-item" @click="navigateToManual">
					<image class="icon-placeholder" src="/static/icons/zi_liao_shou_ce.png" />
					<text>资料手册</text>
				</view>
				<view class="function-item">
					<image class="icon-placeholder" src="/static/icons/app_xia_zai.png" />
					<text>APP下载</text>
				</view>
				<view class="function-item" @click="navigateToGuide">
					<image class="icon-placeholder" src="/static/icons/xin_shou_ying_dao.png" />
					<text>新手引导</text>
				</view>
				<view class="function-item" @click="navigateToPartnership">
					<image class="icon-placeholder" src="/static/icons/he_zuo_jia_meng.png" />
					<text>合作加盟</text>
				</view>
			</view>
		</view>

		<!-- 底部空间 - 确保内容不被TabBar遮挡 -->
		<view class="tabbar-placeholder"></view>

		<!-- 客服悬浮按钮 -->
		<view class="customer-service" @click="showCustomerService">
			<view class="service-icon-placeholder">
				<text class="service-icon">📞</text>
			</view>
		</view>

		<!-- 自定义底部导航栏 -->
		<custom-tabbar v-if="useCustomTabbar" />
	</view>
</template>

<script>
	import CustomTabbar from '@/components/CustomTabbar.vue';
	import { apiService } from '@/utils/request.js';

	export default {
		components: {
			CustomTabbar
		},
		data() {
			return {
				useCustomTabbar: false, // 默认使用系统tabbar，需要时可以设置为true
				userInfo: {
					phone: '',
					nickname: '',
					avatar: ''
				},
				isLogin: false,
				// 统计数据
				statistics: {
					favoriteCount: 0,
					worksCount: 0,
					colorRecordCount: 0,
					formulaCount: 0
				},
				// 门店信息
				storeInfo: {
					name: 'Tony',
					id: null
				},
				// 关注信息
				followCount: 0,
				fansCount: 0
			};
		},
		onLoad() {
			// 在某些情况下可能需要显示自定义tabbar
			// this.useCustomTabbar = true;
		},
		onShow() {
			// 每次页面显示时检查登录状态
			this.checkLoginStatus();
			// 如果已登录，加载用户数据
			if (this.isLogin) {
				this.loadUserData();
			}
		},
		methods: {
			// 检查登录状态
			checkLoginStatus() {
				const token = uni.getStorageSync('token');
				const wasLogin = this.isLogin;
				this.isLogin = !!token;
				console.log("登录状态", this.isLogin)
				if (this.isLogin) {
					this.userInfo = uni.getStorageSync('userInfo') || this.userInfo;
					// 如果刚刚登录，加载数据
					if (!wasLogin) {
						this.loadUserData();
					}
				} else {
					// 如果退出登录，重置数据
					this.resetUserData();
				}
			},

			// 重置用户数据
			resetUserData() {
				this.statistics = {
					favoriteCount: 0,
					worksCount: 0,
					colorRecordCount: 0,
					formulaCount: 0
				};
				this.storeInfo = {
					name: 'Tony',
					id: null
				};
				this.followCount = 0;
				this.fansCount = 0;
			},

			// 加载用户数据
			async loadUserData() {
				try {
					// 并行加载多个数据
					await Promise.all([
						this.loadFavoriteCount(),
						this.loadUserStores()
					]);
				} catch (error) {
					console.error('加载用户数据失败:', error);
				}
			},

			// 加载收藏数量
			async loadFavoriteCount() {
				try {
					const response = await apiService.mall.favorites.list();
					if (response.code === 200 && response.data) {
						this.statistics.favoriteCount = response.data.length || 0;
					}
				} catch (error) {
					console.error('加载收藏数量失败:', error);
					// 静默失败，保持默认值0
				}
			},

			// 加载用户门店信息
			async loadUserStores() {
				try {
					const userInfo = uni.getStorageSync('userInfo');
					if (userInfo && userInfo.id) {
						const response = await apiService.mall.stores.userStores(userInfo.id);
						if (response.code === 200 && response.data && response.data.length > 0) {
							// 使用第一个门店作为当前门店
							this.storeInfo.name = response.data[0].name || 'Tony';
							this.storeInfo.id = response.data[0].id;
						}
					}
				} catch (error) {
					console.error('加载门店信息失败:', error);
					// 静默失败，保持默认值
				}
			},
			// 点击头像容器
			handleAvatarTap() {
				if (!this.isLogin) {
					uni.navigateTo({
						url: '/pages/login/login'
					});
					// 未登录状态不单独处理，由透明按钮触发授权
					return;
				}
				// 已登录状态可跳转个人中心等操作
				uni.navigateTo({
					url: '/pages/outLogin/outLogin'
				});
			},

			// 授权回调
			async handleUserInfo(e) {
				// console.log("开始登录 e的信息",e)
				if (e.detail.errMsg !== 'getUserInfo:ok') {
					uni.showToast({
						title: '授权取消',
						icon: 'none'
					});
					return;
				}

				try {
					// 1. 获取微信code
					const loginRes = await uni.login({
						provider: 'weixin'
					});
					console.log(loginRes.code)

					// 2. 发送用户信息到后端
					//         const res = await uni.request({
					//           url: 'https://your-api.com/login',
					//           method: 'POST',
					//           data: {
					//             code: loginRes.code,
					//             userInfo: e.detail.userInfo
					//           }
					//         });

					//         // 3. 登录成功处理
					//         if (res.statusCode === 200) {
					//           this.isLogin = true;
					//           this.userInfo = e.detail.userInfo;
					//           uni.setStorageSync('token', res.data.token);
					//           uni.showToast({ title: '登录成功' });
					//         }
				} catch (error) {
					// console.error('登录失败:', error);
					// uni.showToast({ title: '登录失败', icon: 'none' });
				}
			},
			navigateToCollections() {
				// 跳转到收藏页面
				uni.navigateTo({
					url: '/pages/my/collection',
					success: () => {
						console.log('跳转到收藏页面成功');
					},
					fail: (err) => {
						console.error('跳转到收藏页面失败：', err);

						// 如果页面不存在，提示用户
						uni.showToast({
							title: '收藏页面开发中',
							icon: 'none',
							duration: 2000
						});
					}
				});
			},
			navigateToWorks() {
				// 跳转到作品页面（使用相同的collection页面，但会通过参数显示作品标签）
				uni.navigateTo({
					url: '/pages/my/collection?tab=works',
					success: () => {
						console.log('跳转到作品页面成功');
					},
					fail: (err) => {
						console.error('跳转到作品页面失败：', err);

						// 如果页面不存在，提示用户
						uni.showToast({
							title: '作品页面开发中',
							icon: 'none',
							duration: 2000
						});
					}
				});
			},
			navigateToColorRecord() {
				// 跳转到调色记录页面
				uni.navigateTo({
					url: '/pages/my/color-record',
					success: () => {
						console.log('跳转到调色记录页面成功');
					},
					fail: (err) => {
						console.error('跳转到调色记录页面失败：', err);

						// 如果页面不存在，提示用户
						uni.showToast({
							title: '调色记录页面跳转失败',
							icon: 'none',
							duration: 2000
						});
					}
				});
			},
			navigateToStoreInfo() {
				// 跳转到门店信息页面
				uni.navigateTo({
					url: '/pages/my/store-info',
					success: () => {
						console.log('跳转到门店信息页面成功');
					},
					fail: (err) => {
						console.error('跳转到门店信息页面失败：', err);

						// 如果页面不存在，提示用户
						uni.showToast({
							title: '门店信息页面跳转失败',
							icon: 'none',
							duration: 2000
						});
					}
				});
			},
			navigateToStaffManage() {
				//
				uni.navigateTo({
					url: '/pages/my/staff-manage',
					success: () => {
						console.log('跳转到人员管理页面成功');
					},
					fail: (err) => {
						console.error('跳转到人员管理页面失败：', err);

						// 如果页面不存在，提示用户
						uni.showToast({
							title: '人员管理页面跳转失败',
							icon: 'none',
							duration: 2000
						});
					}
				});
			},
			navigateToDeviceList() {
				// 跳转到设备列表页面
				// uni.navigateTo({
				// 	url: '/pages/my/device-list',
				// 	success: () => {
				// 		console.log('跳转到设备列表页面成功');
				// 	},
				// 	fail: (err) => {
				// 		console.error('跳转到设备列表页面失败：', err);

				// 		// 如果页面不存在，提示用户
				// 		uni.showToast({
				// 			title: '设备列表页面跳转失败',
				// 			icon: 'none',
				// 			duration: 2000
				// 		});
				// 	}
				// });
				// 跳转到设备扫码付费页
				uni.navigateTo({
					url: '/pages/device/bind-device/bind-device',
					success: () => {
						console.log('跳转到设备绑定页面成功');
					},
					fail: (err) => {
						console.error('跳转到设备绑定页面失败：', err);
				
						// 如果页面不存在，提示用户
						uni.showToast({
							title: '设备绑定跳转失败',
							icon: 'none',
							duration: 2000
						});
					}
				});
			},
			navigateToStoreQrcode() {
				// 跳转到门店二维码页面
				uni.navigateTo({
					url: '/pages/my/store-qrcode',
					success: () => {
						console.log('跳转到门店二维码页面成功');
					},
					fail: (err) => {
						console.error('跳转到门店二维码页面失败：', err);

						// 如果页面不存在，提示用户
						uni.showToast({
							title: '门店二维码页面跳转失败',
							icon: 'none',
							duration: 2000
						});
					}
				});
			},
			navigateToColorTest() {
				// 跳转到色膏检测页面
				uni.navigateTo({
					url: '/pages/my/color-test',
					success: () => {
						console.log('跳转到色膏检测页面成功');
					},
					fail: (err) => {
						console.error('跳转到色膏检测页面失败：', err);

						// 如果页面不存在，提示用户
						uni.showToast({
							title: '色膏检测页面跳转失败',
							icon: 'none',
							duration: 2000
						});
					}
				});
			},
			navigateToUsageStats() {
				// 跳转到使用统计页面
				uni.navigateTo({
					url: '/pages/my/usage-stats',
					success: () => {
						console.log('跳转到使用统计页面成功');
					},
					fail: (err) => {
						console.error('跳转到使用统计页面失败：', err);

						// 如果页面不存在，提示用户
						uni.showToast({
							title: '使用统计页面跳转失败',
							icon: 'none',
							duration: 2000
						});
					}
				});
			},
			navigateToMyOrders() {
				// 跳转到我的订单页面
				uni.navigateTo({
					url: '/pages/my/order',
					success: () => {
						console.log('跳转到我的订单页面成功');
					},
					fail: (err) => {
						console.error('跳转到我的订单页面失败：', err);

						// 如果页面不存在，提示用户
						uni.showToast({
							title: '我的订单页面跳转失败',
							icon: 'none',
							duration: 2000
						});
					}
				});
			},
			navigateToMyDeviceManage() {
				uni.navigateTo({
					url: '/pages/device/dye-device-manager/dye-device-manager',
					success: () => {
						console.log('跳转到我的设备权限页面成功');
					},
					fail: (err) => {
						console.error('跳转到我的设备权限失败：', err);
				
						// 如果页面不存在，提示用户
						uni.showToast({
							title: '跳转到我的设备权限失败',
							icon: 'none',
							duration: 2000
						});
					}
				});
			},
			navigateToRepair() {
				// 跳转到报修页面
				uni.navigateTo({
					url: '/pages/my/repair',
					success: () => {
						console.log('跳转到报修页面成功');
					},
					fail: (err) => {
						console.error('跳转到报修页面失败：', err);

						// 如果页面不存在，提示用户
						uni.showToast({
							title: '报修页面跳转失败',
							icon: 'none',
							duration: 2000
						});
					}
				});
			},
			navigateToPolicy() {
				uni.navigateTo({
					url: '/pages/my/policy',
					success: function(res) {
						console.log('跳转成功');
					},
					fail: function(err) {
						console.log('跳转失败');
						uni.showToast({
							icon: 'none',
							title: '跳转失败'
						});
					}
				});
			},
			navigateToMessage() {
				// 跳转到消息页面
				uni.navigateTo({
					url: '/pages/my/message',
					success: () => {
						console.log('跳转到消息页面成功');
					},
					fail: (err) => {
						console.error('跳转到消息页面失败：', err);

						// 如果页面不存在，提示用户
						uni.showToast({
							title: '消息页面跳转失败',
							icon: 'none',
							duration: 2000
						});
					}
				});
			},
			navigateToAbout() {
				uni.navigateTo({
					url: '/pages/my/about',
					success: function(res) {
						console.log('跳转到关于我们成功');
					},
					fail: function(err) {
						console.log('跳转到关于我们失败', err);
						uni.showToast({
							icon: 'none',
							title: '关于我们页面跳转失败'
						});
					}
				});
			},
			navigateToManual() {
				uni.navigateTo({
					url: '/pages/my/manual',
					success: function(res) {
						console.log('跳转到资料手册成功');
					},
					fail: function(err) {
						console.log('跳转到资料手册失败', err);
						uni.showToast({
							icon: 'none',
							title: '资料手册页面跳转失败'
						});
					}
				});
			},
			navigateToGuide() {
				uni.navigateTo({
					url: '/pages/my/guide',
					success: function(res) {
						console.log('跳转到新手引导成功');
					},
					fail: function(err) {
						console.log('跳转到新手引导失败', err);
						uni.showToast({
							icon: 'none',
							title: '新手引导页面跳转失败'
						});
					}
				});
			},
			navigateToPartnership() {
				uni.navigateTo({
					url: '/pages/my/partnership',
					success: function(res) {
						console.log('跳转到合作加盟成功');
					},
					fail: function(err) {
						console.log('跳转到合作加盟失败', err);
						uni.showToast({
							icon: 'none',
							title: '合作加盟页面跳转失败'
						});
					}
				});
			},

			// 显示客服联系方式
			showCustomerService() {
				const phoneNumber = '13826059861';
				uni.showModal({
					title: '客服电话',
					content: `客服电话：${phoneNumber}\n\n可在手机端拨打`,
					confirmText: '复制号码',
					cancelText: '取消',
					success: (res) => {
						if (res.confirm) {
							// 用户点击了复制号码
							this.copyPhoneNumber(phoneNumber);
						}
					}
				});
			},

			// 复制电话号码到剪切板
			copyPhoneNumber(phoneNumber) {
				uni.setClipboardData({
					data: phoneNumber,
					success: () => {
						console.log('复制电话号码成功');
						uni.showToast({
							title: '电话号码已复制',
							icon: 'success',
							duration: 2000
						});
					},
					fail: (err) => {
						console.error('复制电话号码失败：', err);
						uni.showToast({
							title: '复制失败',
							icon: 'none',
							duration: 2000
						});
					}
				});
			},
			handleScan() {
				// 调用扫码API
				uni.scanCode({
					success: (res) => {
						console.log('扫码成功：', res);
						// 扫码成功后的处理
						uni.showToast({
							title: '扫码成功',
							icon: 'success',
							duration: 2000
						});

						// 根据扫码结果可以进行后续处理
						// 例如判断二维码类型并跳转至相应页面
						// 这里可以增加对不同类型二维码的处理逻辑
					},
					fail: (err) => {
						console.error('扫码失败：', err);
						uni.showToast({
							title: '扫码失败',
							icon: 'none',
							duration: 2000
						});
					}
				});
			}
		}
	};
</script>

<style lang="scss">
	// 点击头像登录的按键
	.auth-button {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		opacity: 0;
		/* 完全透明 */
		z-index: 2;
		/* 确保按钮在头像上层 */
	}

	.avatar {
		width: 100%;
		height: 100%;
	}

	.user-text {
		margin-top: 7px;
		/* 调整与上个元素的垂直间距 */
	}

	// 点击头像登录的按键
	.avatar-container {
		position: relative;
		z-index: 1;
		/* 头像在按钮下层 */
	}

	.content {
		display: flex;
		flex-direction: column;
		background-color: #f5f5f5;
		min-height: 100vh;
		position: relative;
		padding-bottom: 120rpx;
		/* 预留底部TabBar的空间 */
		width: 100%;
		box-sizing: border-box;
		overflow-x: hidden;
	}

	.top-header {
		display: flex;
		align-items: center;
		justify-content: center;
		width: 100%;
		height: 90rpx;
		background-color: #4285f4;
		padding: 0 20rpx;
		position: relative;
		box-sizing: border-box;
		flex-shrink: 0;
	}

	.header-title {
		color: #ffffff;
		font-size: 36rpx;
	}

	.top-right {
		display: flex;
		align-items: center;
		position: absolute;
		right: 20rpx;
	}

	.circle-btn {
		width: 60rpx;
		height: 60rpx;
		border-radius: 50%;
		background-color: rgba(255, 255, 255, 0.3);
		display: flex;
		justify-content: center;
		align-items: center;
		margin-left: 10rpx;
	}

	.circle-btn.active {
		background-color: rgba(255, 255, 255, 0.3);
	}

	.circle-inner {
		width: 30rpx;
		height: 30rpx;
		background-color: #fff;
		border-radius: 50%;
	}

	.dash-btn {
		width: 60rpx;
		height: 60rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		color: #fff;
		margin-left: 10rpx;
	}

	.more-dots {
		color: #fff;
		font-size: 36rpx;
		line-height: 20rpx;
	}

	.user-info {
		background-color: #fff;
		padding: 30rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		width: 100%;
		box-sizing: border-box;
	}

	.avatar-container {
		width: 120rpx;
		height: 120rpx;
		border-radius: 50%;
		overflow: hidden;
		background-color: #eee;
	}

	.avatar-placeholder {
		width: 100%;
		height: 100%;
		background-color: #ddd;
		border-radius: 50%;
	}

	.login-text {
		font-size: 32rpx;
		margin-top: 20rpx;
	}

	.follow-info {
		font-size: 24rpx;
		color: #999;
		margin-top: 10rpx;
	}

	.follow-info text {
		margin: 0 20rpx;
	}

	.scan-btn {
		margin-top: 20rpx;
		background-color: #4285f4;
		color: #fff;
		padding: 8rpx 40rpx;
		border-radius: 30rpx;
		font-size: 26rpx;
	}

	.statistics {
		display: flex;
		background-color: #fff;
		padding: 20rpx 0;
		border-top: 1rpx solid #eee;
		width: 100%;
		box-sizing: border-box;
	}

	.stat-item {
		flex: 1;
		display: flex;
		flex-direction: column;
		align-items: center;
	}

	.stat-num {
		font-size: 32rpx;
		font-weight: bold;
		color: #333;
	}

	.stat-label {
		font-size: 24rpx;
		color: #666;
		margin-top: 8rpx;
	}

	.current-store {
		display: flex;
		align-items: center;
		padding: 30rpx;
		background-color: #fff;
		margin-top: 20rpx;
		width: 100%;
		box-sizing: border-box;
	}

	.store-label {
		font-size: 28rpx;
		color: #666;
	}

	.store-name {
		font-size: 28rpx;
		margin-left: 10rpx;
		font-weight: bold;
	}

	.store-arrow {
		font-size: 24rpx;
		margin-left: 10rpx;
		color: #999;
	}

	.function-menu {
		background-color: #fff;
		padding: 20rpx;
		width: 100%;
		box-sizing: border-box;
	}

	.menu-row {
		display: flex;
		margin-bottom: 30rpx;
	}

	.menu-item {
		flex: 1;
		display: flex;
		flex-direction: column;
		align-items: center;
	}

	.menu-item .icon-placeholder {
		width: 60rpx;
		height: 60rpx;
		margin-bottom: 10rpx;
		border-radius: 8rpx;
	}
	
	.icon_f {
		width: 60rpx;
		height: 60rpx;
	}

	.menu-item text {
		font-size: 24rpx;
		// color: #333;
	}

	.guide-banner {
		margin-top: 20rpx;
		background-color: #ffb74d;
		padding: 20rpx 30rpx;
		display: flex;
		align-items: center;
		position: relative;
		border-radius: 8rpx;
		margin: 20rpx;
		width: calc(100% - 40rpx);
		box-sizing: border-box;
	}

	.guide-banner .banner-icon-placeholder {
		width: 80rpx;
		height: 80rpx;
		margin-right: 20rpx;
		// background-color: #ffcc80;
		border-radius: 8rpx;
	}

	.guide-banner text {
		font-size: 28rpx;
		color: #333;
		flex: 1;
	}

	.check-now {
		font-size: 24rpx;
		color: #666;
		position: absolute;
		right: 30rpx;
	}

	.common-functions {
		margin-top: 20rpx;
		background-color: #fff;
		padding: 20rpx;
		width: 100%;
		box-sizing: border-box;
	}

	.section-title {
		font-size: 28rpx;
		color: #333;
		font-weight: bold;
		margin-bottom: 20rpx;
	}

	.function-grid {
		display: flex;
		flex-wrap: wrap;
		width: 100%;
		margin-top: 20rpx;
	}

	.function-item {
		width: 25%;
		display: flex;
		flex-direction: column;
		align-items: center;
		margin-bottom: 30rpx;
	}

	.function-item .icon-placeholder {
		width: 50rpx;
		height: 50rpx;
		margin-bottom: 10rpx;
		// background-color: #eee;
		border-radius: 8rpx;
	}

	.function-item text {
		font-size: 24rpx;
		// color: #666;
	}

	.tabbar-placeholder {
		height: 100rpx;
		/* 与TabBar高度一致，确保内容不被遮挡 */
		width: 100%;
		box-sizing: border-box;
	}

	/* 底部导航自定义样式，确保与系统TabBar相匹配 */
	.custom-tabbar {
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		height: 100rpx;
		background-color: #fff;
		display: flex;
		box-shadow: 0 -2rpx 5rpx rgba(0, 0, 0, 0.1);
		z-index: 999;
	}

	.tabbar-item {
		flex: 1;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding: 10rpx 0;
	}

	.tabbar-icon {
		width: 40rpx;
		height: 40rpx;
		margin-bottom: 4rpx;
	}

	.tabbar-text {
		font-size: 20rpx;
		color: #999;
	}

	.tabbar-item.active .tabbar-text {
		color: #4285f4;
	}

	.customer-service {
		position: fixed;
		right: 30rpx;
		bottom: 130rpx;
		/* 调整位置，避免与TabBar重叠 */
		width: 100rpx;
		height: 100rpx;
		background-color: #4285f4;
		border-radius: 50%;
		display: flex;
		justify-content: center;
		align-items: center;
		z-index: 998;
	}

	.service-icon-placeholder {
		width: 60rpx;
		height: 60rpx;
		// background-color: #fff;
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.service-icon {
		font-size: 40rpx;
		color: #fff;
	}

	/* 统计图标样式 */
	.stats-icon {
		font-size: 40rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	/* 设备尺寸适配 */
	@media screen and (max-width: 390px) {

		.content,
		.top-header,
		.user-info,
		.statistics,
		.current-store,
		.function-menu,
		.guide-banner,
		.common-functions {
			width: 100% !important;
			box-sizing: border-box !important;
			margin-left: 0 !important;
			margin-right: 0 !important;
		}

		.guide-banner {
			margin: 20rpx 0;
			width: 100% !important;
			border-radius: 0;
		}

		.menu-item,
		.function-item {
			padding: 0 5rpx;
		}

		.function-item .icon-placeholder {
			width: 45rpx;
			height: 45rpx;
		}
	}

	/* iPhone X 及以上机型底部安全区域适配 */
	@supports (padding-bottom: constant(safe-area-inset-bottom)) or (padding-bottom: env(safe-area-inset-bottom)) {
		.content {
			padding-bottom: calc(120rpx + constant(safe-area-inset-bottom));
			padding-bottom: calc(120rpx + env(safe-area-inset-bottom));
		}

		.custom-tabbar,
		.uni-tabbar {
			padding-bottom: constant(safe-area-inset-bottom);
			padding-bottom: env(safe-area-inset-bottom);
		}
	}
</style>