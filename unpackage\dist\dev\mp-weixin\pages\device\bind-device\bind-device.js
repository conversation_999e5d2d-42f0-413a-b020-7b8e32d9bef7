"use strict";
const common_vendor = require("../../../common/vendor.js");
const common_assets = require("../../../common/assets.js");
const _sfc_main = {
  data() {
    return {
      activeYear: 0,
      originalPrice: 999,
      // 模拟原价数据
      discount: 601,
      // 模拟折扣金额
      yearOptions: [
        { year: "1", price: "398", originalPrice: "696" },
        { year: "2", price: "698", originalPrice: "1296" },
        { year: "3", price: "999", originalPrice: "1999" }
      ]
    };
  },
  onLoad(option) {
    setTimeout(() => {
    }, 200);
    common_vendor.index.navigateTo({
      url: "/pages/my/device-list",
      success: () => {
        common_vendor.index.__f__("log", "at pages/device/bind-device/bind-device.vue:83", "跳转到设备列表页面成功");
      },
      fail: (err) => {
        common_vendor.index.__f__("error", "at pages/device/bind-device/bind-device.vue:86", "跳转到设备列表页面失败：", err);
        common_vendor.index.showToast({
          title: "设备列表页面跳转失败",
          icon: "none",
          duration: 2e3
        });
      }
    });
  },
  computed: {
    selectedYear() {
      return this.yearOptions[this.activeYear].year;
    },
    finalPrice() {
      return this.yearOptions[this.activeYear].price;
    }
  },
  methods: {
    selectYear(index) {
      this.activeYear = index;
      this.originalPrice = [999, 1998, 2997][index];
      this.discount = [601, 1300, 1998][index];
    },
    handleScan() {
      common_vendor.index.scanCode({
        success: (res) => {
          common_vendor.index.__f__("log", "at pages/device/bind-device/bind-device.vue:115", "扫描结果:", res);
        },
        fail: (err) => {
          common_vendor.index.__f__("error", "at pages/device/bind-device/bind-device.vue:118", "扫描失败:", err);
        }
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_assets._imports_0$13,
    b: common_vendor.o((...args) => $options.handleScan && $options.handleScan(...args)),
    c: common_vendor.f($data.yearOptions, (option, index, i0) => {
      return {
        a: common_vendor.t(option.year),
        b: common_vendor.t(option.price),
        c: common_vendor.t(option.originalPrice),
        d: index,
        e: $data.activeYear === index ? 1 : "",
        f: common_vendor.o(($event) => $options.selectYear(index), index)
      };
    }),
    d: common_vendor.t($options.selectedYear),
    e: common_vendor.t($data.originalPrice),
    f: common_vendor.t($data.discount),
    g: common_vendor.t($options.finalPrice)
  };
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-d8916166"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/pages/device/bind-device/bind-device.js.map
