"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_request = require("../../utils/request.js");
const _sfc_main = {
  data() {
    return {
      partnershipInfo: null,
      loading: false,
      error: "",
      advantages: [],
      requirements: [],
      process: []
    };
  },
  onLoad() {
    this.loadPartnershipInfo();
  },
  methods: {
    goBack() {
      common_vendor.index.navigateBack({
        delta: 1
      });
    },
    async loadPartnershipInfo() {
      try {
        this.loading = true;
        this.error = "";
        const response = await utils_request.get("/api/partnership/info");
        if (response && response.code === 200) {
          this.partnershipInfo = response.data;
          this.parseJsonFields();
        } else {
          this.error = (response == null ? void 0 : response.message) || "获取合作加盟信息失败";
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/my/partnership.vue:138", "获取合作加盟信息失败:", error);
        this.error = "网络错误，请稍后重试";
      } finally {
        this.loading = false;
      }
    },
    parseJsonFields() {
      if (!this.partnershipInfo)
        return;
      try {
        this.advantages = this.partnershipInfo.advantages ? JSON.parse(this.partnershipInfo.advantages) : [];
      } catch (e) {
        this.advantages = [];
      }
      try {
        this.requirements = this.partnershipInfo.requirements ? JSON.parse(this.partnershipInfo.requirements) : [];
      } catch (e) {
        this.requirements = [];
      }
      try {
        this.process = this.partnershipInfo.process ? JSON.parse(this.partnershipInfo.process) : [];
      } catch (e) {
        this.process = [];
      }
    },
    formatContent(content) {
      if (!content)
        return "";
      return content.replace(/\n/g, "<br/>");
    },
    makeCall() {
      if (this.partnershipInfo.contactPhone) {
        common_vendor.index.makePhoneCall({
          phoneNumber: this.partnershipInfo.contactPhone
        });
      }
    },
    sendEmail() {
      if (this.partnershipInfo.contactEmail) {
        common_vendor.index.showToast({
          title: "请使用邮件客户端发送",
          icon: "none"
        });
      }
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.loading
  }, $data.loading ? {} : $data.error ? {
    c: common_vendor.t($data.error),
    d: common_vendor.o((...args) => $options.loadPartnershipInfo && $options.loadPartnershipInfo(...args))
  } : $data.partnershipInfo ? common_vendor.e({
    f: $data.partnershipInfo.imageUrl
  }, $data.partnershipInfo.imageUrl ? {
    g: $data.partnershipInfo.imageUrl
  } : {}, {
    h: common_vendor.t($data.partnershipInfo.title),
    i: common_vendor.t($data.partnershipInfo.description),
    j: $data.partnershipInfo.content
  }, $data.partnershipInfo.content ? {
    k: $options.formatContent($data.partnershipInfo.content)
  } : {}, {
    l: $data.advantages.length > 0
  }, $data.advantages.length > 0 ? {
    m: common_vendor.f($data.advantages, (item, index, i0) => {
      return {
        a: common_vendor.t(item),
        b: index
      };
    })
  } : {}, {
    n: $data.requirements.length > 0
  }, $data.requirements.length > 0 ? {
    o: common_vendor.f($data.requirements, (item, index, i0) => {
      return {
        a: common_vendor.t(index + 1),
        b: common_vendor.t(item),
        c: index
      };
    })
  } : {}, {
    p: $data.process.length > 0
  }, $data.process.length > 0 ? {
    q: common_vendor.f($data.process, (item, index, i0) => {
      return common_vendor.e({
        a: common_vendor.t(index + 1),
        b: index < $data.process.length - 1
      }, index < $data.process.length - 1 ? {} : {}, {
        c: common_vendor.t(item),
        d: index
      });
    })
  } : {}, {
    r: $data.partnershipInfo.contactPhone
  }, $data.partnershipInfo.contactPhone ? {
    s: common_vendor.t($data.partnershipInfo.contactPhone),
    t: common_vendor.o((...args) => $options.makeCall && $options.makeCall(...args))
  } : {}, {
    v: $data.partnershipInfo.contactEmail
  }, $data.partnershipInfo.contactEmail ? {
    w: common_vendor.t($data.partnershipInfo.contactEmail),
    x: common_vendor.o((...args) => $options.sendEmail && $options.sendEmail(...args))
  } : {}, {
    y: $data.partnershipInfo.contactWechat
  }, $data.partnershipInfo.contactWechat ? {
    z: common_vendor.t($data.partnershipInfo.contactWechat)
  } : {}, {
    A: $data.partnershipInfo.address
  }, $data.partnershipInfo.address ? {
    B: common_vendor.t($data.partnershipInfo.address)
  } : {}) : {}, {
    b: $data.error,
    e: $data.partnershipInfo
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/my/partnership.js.map
