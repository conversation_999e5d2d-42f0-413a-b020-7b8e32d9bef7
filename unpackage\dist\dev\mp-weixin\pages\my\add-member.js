"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_request = require("../../utils/request.js");
const _sfc_main = {
  data() {
    return {
      formData: {
        phone: "",
        name: "",
        password: "",
        confirmPassword: ""
      }
    };
  },
  methods: {
    goBack() {
      common_vendor.index.navigateBack();
    },
    // 验证表单
    validateForm() {
      if (!this.formData.phone) {
        common_vendor.index.showToast({
          title: "请输入手机号",
          icon: "none"
        });
        return false;
      }
      if (!/^1[3-9]\d{9}$/.test(this.formData.phone)) {
        common_vendor.index.showToast({
          title: "请输入正确的手机号",
          icon: "none"
        });
        return false;
      }
      if (!this.formData.password) {
        common_vendor.index.showToast({
          title: "请设置登录密码",
          icon: "none"
        });
        return false;
      }
      if (this.formData.password.length < 6) {
        common_vendor.index.showToast({
          title: "密码至少6位",
          icon: "none"
        });
        return false;
      }
      if (this.formData.password !== this.formData.confirmPassword) {
        common_vendor.index.showToast({
          title: "两次密码不一致",
          icon: "none"
        });
        return false;
      }
      return true;
    },
    // 添加成员
    async addMember() {
      if (!this.validateForm()) {
        return;
      }
      try {
        common_vendor.index.showLoading({
          title: "添加中..."
        });
        const response = await this.registerMember();
        common_vendor.index.hideLoading();
        if (response.success) {
          common_vendor.index.showToast({
            title: "添加成功",
            icon: "success"
          });
          setTimeout(() => {
            common_vendor.index.navigateBack();
          }, 1500);
        } else {
          common_vendor.index.showToast({
            title: response.message || "添加失败",
            icon: "none"
          });
        }
      } catch (error) {
        common_vendor.index.hideLoading();
        common_vendor.index.__f__("error", "at pages/my/add-member.vue:168", "添加成员失败:", error);
        common_vendor.index.showToast({
          title: "网络错误，请重试",
          icon: "none"
        });
      }
    },
    // 调用注册接口
    async registerMember() {
      try {
        const defaultName = this.formData.name || `用户${this.formData.phone.slice(-4)}`;
        const requestData = {
          phone: this.formData.phone,
          password: this.formData.password,
          nickname: defaultName,
          // 使用昵称字段保存姓名
          userType: "1"
          // 普通用户类型，注意这里改为字符串
        };
        const result = await utils_request.apiService.mall.stores.addStoreMember(requestData);
        if (result.code === 200) {
          return {
            success: true,
            data: result.data
          };
        } else {
          return {
            success: false,
            message: result.message || "注册失败"
          };
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/my/add-member.vue:204", "注册请求失败:", error);
        return {
          success: false,
          message: error.message || "网络请求失败"
        };
      }
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.o((...args) => $options.goBack && $options.goBack(...args)),
    b: $data.formData.phone,
    c: common_vendor.o(($event) => $data.formData.phone = $event.detail.value),
    d: $data.formData.name,
    e: common_vendor.o(($event) => $data.formData.name = $event.detail.value),
    f: $data.formData.password,
    g: common_vendor.o(($event) => $data.formData.password = $event.detail.value),
    h: $data.formData.confirmPassword,
    i: common_vendor.o(($event) => $data.formData.confirmPassword = $event.detail.value),
    j: common_vendor.o((...args) => $options.addMember && $options.addMember(...args))
  };
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-7eedd94a"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/my/add-member.js.map
