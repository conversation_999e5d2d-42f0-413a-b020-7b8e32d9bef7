"use strict";
const common_vendor = require("../../../common/vendor.js");
const utils_request = require("../../../utils/request.js");
const utils_imageUtils = require("../../../utils/imageUtils.js");
const _sfc_main = {
  data() {
    return {
      productId: null,
      productDetail: {},
      storeInfo: {},
      recommendations: [],
      loading: false,
      specifications: [],
      isFavorited: false,
      // 收藏状态
      favoriteLoading: false,
      // 收藏操作加载状态
      isInCart: false,
      // 商品是否在购物车中
      cartLoading: false,
      // 购物车操作加载状态
      isNavigating: false
      // 是否正在进行页面跳转
    };
  },
  computed: {
    productImages() {
      if (!this.productDetail.images)
        return [];
      let images = [];
      if (typeof this.productDetail.images === "string") {
        images = this.productDetail.images.split(",").filter((img) => img.trim());
      } else {
        images = this.productDetail.images || [];
      }
      return images.map((img) => utils_imageUtils.getFullImageUrl(img));
    }
  },
  onLoad(options) {
    if (options.id) {
      this.productId = options.id;
      this.loadProductDetail();
      this.loadRecommendations();
      this.checkFavoriteStatus();
      this.checkCartStatus();
    }
  },
  onShow() {
    if (this.productId) {
      if (this.isNavigating) {
        setTimeout(() => {
          this.isNavigating = false;
          this.checkCartStatus();
          this.checkFavoriteStatus();
        }, 300);
      } else {
        this.checkCartStatus();
        this.checkFavoriteStatus();
      }
    }
  },
  methods: {
    // 加载商品详情
    async loadProductDetail() {
      if (!this.productId)
        return;
      this.loading = true;
      try {
        const response = await utils_request.apiService.mall.products.detail(this.productId);
        if (response.code === 200) {
          this.productDetail = response.data.product || {};
          this.storeInfo = response.data.store || {};
          common_vendor.index.__f__("log", "at pages/mall/product/detail.vue:148", "商品详情数据:", this.productDetail);
          this.specifications = [
            { name: "商品ID", value: this.productDetail.id },
            { name: "商品名称", value: this.productDetail.productName || this.productDetail.name },
            { name: "价格", value: `¥${this.productDetail.price}` },
            { name: "库存", value: `${this.productDetail.stock}件` },
            { name: "状态", value: this.productDetail.status === 1 ? "上架" : "下架" }
          ];
          if (this.productDetail.specification) {
            this.specifications.push({ name: "规格", value: this.productDetail.specification });
          }
        } else {
          common_vendor.index.__f__("error", "at pages/mall/product/detail.vue:164", "获取商品详情失败:", response.message);
          common_vendor.index.showToast({
            title: "获取商品详情失败",
            icon: "none"
          });
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/mall/product/detail.vue:171", "获取商品详情异常:", error);
        common_vendor.index.showToast({
          title: "网络异常",
          icon: "none"
        });
      } finally {
        this.loading = false;
      }
    },
    // 加载推荐商品
    async loadRecommendations() {
      try {
        const response = await utils_request.apiService.mall.products.recommend(6);
        if (response.code === 200) {
          this.recommendations = response.data || [];
        } else {
          common_vendor.index.__f__("error", "at pages/mall/product/detail.vue:189", "获取推荐商品失败:", response.message);
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/mall/product/detail.vue:192", "获取推荐商品异常:", error);
      }
    },
    // 获取商品图片
    getProductImage(product) {
      if (!product.images)
        return "";
      if (typeof product.images === "string") {
        const imageArray = product.images.split(",");
        return imageArray[0] || "";
      }
      return product.images[0] || "";
    },
    // 检查收藏状态
    async checkFavoriteStatus() {
      const isLoggedIn = common_vendor.index.getStorageSync("isLoggedIn") || false;
      if (!isLoggedIn || !this.productId) {
        this.isFavorited = false;
        return;
      }
      try {
        const response = await utils_request.apiService.mall.favorites.check(this.productId);
        if (response.code === 200) {
          this.isFavorited = response.data || false;
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/mall/product/detail.vue:221", "检查收藏状态异常:", error);
        this.isFavorited = false;
      }
    },
    // 检查购物车状态
    async checkCartStatus() {
      const isLoggedIn = common_vendor.index.getStorageSync("isLoggedIn") || false;
      if (!isLoggedIn || !this.productId) {
        this.isInCart = false;
        return;
      }
      try {
        const response = await utils_request.apiService.mall.cart.list();
        if (response.code === 200) {
          const cartItems = response.data || [];
          this.isInCart = cartItems.some(
            (item) => item.productId == this.productId || item.product_id == this.productId
          );
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/mall/product/detail.vue:245", "检查购物车状态异常:", error);
        this.isInCart = false;
      }
    },
    goToStore() {
      common_vendor.index.navigateTo({
        url: "/pages/mall/store/store?id=1"
      });
    },
    goToCart() {
      this.isNavigating = true;
      const isLoggedIn = common_vendor.index.getStorageSync("isLoggedIn") || false;
      if (!isLoggedIn) {
        this.isNavigating = false;
        common_vendor.index.navigateTo({
          url: "/pages/login/login?redirect=/pages/cart/cart"
        });
      } else {
        common_vendor.index.navigateTo({
          url: "/pages/cart/cart",
          success: () => {
            common_vendor.index.__f__("log", "at pages/mall/product/detail.vue:275", "跳转购物车成功");
          },
          fail: (err) => {
            common_vendor.index.__f__("error", "at pages/mall/product/detail.vue:278", "跳转购物车失败:", err);
            this.isNavigating = false;
            common_vendor.index.showToast({
              title: "页面跳转失败",
              icon: "none"
            });
          }
        });
      }
    },
    goToProduct(id) {
      common_vendor.index.navigateTo({
        url: "/pages/mall/product/detail?id=" + id
      });
    },
    async addToCart() {
      const isLoggedIn = common_vendor.index.getStorageSync("isLoggedIn") || false;
      if (!isLoggedIn) {
        common_vendor.index.navigateTo({
          url: "/pages/login/login?redirect=/pages/mall/product/detail?id=" + this.productId
        });
        return;
      }
      if (!this.productDetail || !this.productDetail.id) {
        common_vendor.index.showToast({
          title: "商品信息异常",
          icon: "none"
        });
        return;
      }
      if (this.productDetail.stock <= 0) {
        common_vendor.index.showToast({
          title: "商品库存不足",
          icon: "none"
        });
        return;
      }
      try {
        const response = await utils_request.apiService.mall.cart.add({
          productId: this.productDetail.id,
          quantity: 1,
          skuId: null
          // 如果有规格选择，这里传入skuId
        });
        if (response.code === 200) {
          this.isInCart = true;
          common_vendor.index.showToast({
            title: "已加入购物车",
            icon: "success"
          });
        } else {
          common_vendor.index.showToast({
            title: response.message || "加入购物车失败",
            icon: "none"
          });
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/mall/product/detail.vue:346", "加入购物车异常:", error);
        common_vendor.index.showToast({
          title: "网络异常，请重试",
          icon: "none"
        });
      }
    },
    // 处理购物车按钮点击
    handleCartAction() {
      if (this.isInCart) {
        this.goToCart();
      } else {
        this.addToCart();
      }
    },
    buyNow() {
      const isLoggedIn = common_vendor.index.getStorageSync("isLoggedIn") || false;
      if (!isLoggedIn) {
        common_vendor.index.navigateTo({
          url: "/pages/login/login?redirect=/pages/mall/product/detail?id=" + this.productId
        });
        return;
      }
      if (!this.productDetail || !this.productDetail.id) {
        common_vendor.index.showToast({
          title: "商品信息异常",
          icon: "none"
        });
        return;
      }
      if (this.productDetail.stock <= 0) {
        common_vendor.index.showToast({
          title: "商品库存不足",
          icon: "none"
        });
        return;
      }
      const orderData = {
        type: "buyNow",
        products: [{
          id: this.productDetail.id,
          productName: this.productDetail.productName || this.productDetail.name,
          price: this.productDetail.price,
          quantity: 1,
          images: this.productDetail.images,
          skuId: null
        }]
      };
      common_vendor.index.navigateTo({
        url: "/pages/order/confirm?data=" + encodeURIComponent(JSON.stringify(orderData))
      });
    },
    async handleCollect() {
      const isLoggedIn = common_vendor.index.getStorageSync("isLoggedIn") || false;
      if (!isLoggedIn) {
        common_vendor.index.navigateTo({
          url: "/pages/login/login?redirect=/pages/mall/product/detail?id=" + this.productId
        });
        return;
      }
      if (!this.productId) {
        common_vendor.index.showToast({
          title: "商品信息异常",
          icon: "none"
        });
        return;
      }
      if (this.favoriteLoading) {
        return;
      }
      this.favoriteLoading = true;
      try {
        let response;
        if (this.isFavorited) {
          response = await utils_request.apiService.mall.favorites.remove(this.productId);
          if (response.code === 200) {
            this.isFavorited = false;
            common_vendor.index.showToast({
              title: "取消收藏成功",
              icon: "success"
            });
          } else {
            common_vendor.index.showToast({
              title: response.message || "取消收藏失败",
              icon: "none"
            });
          }
        } else {
          response = await utils_request.apiService.mall.favorites.add(this.productId);
          if (response.code === 200) {
            this.isFavorited = true;
            common_vendor.index.showToast({
              title: "收藏成功",
              icon: "success"
            });
            setTimeout(() => {
              common_vendor.index.showModal({
                title: "提示",
                content: "商品已收藏，是否查看我的收藏？",
                success: (res) => {
                  if (res.confirm) {
                    common_vendor.index.navigateTo({
                      url: "/pages/my/collection"
                    });
                  }
                }
              });
            }, 1500);
          } else {
            common_vendor.index.showToast({
              title: response.message || "收藏失败",
              icon: "none"
            });
          }
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/mall/product/detail.vue:489", "收藏操作异常:", error);
        common_vendor.index.showToast({
          title: "网络异常，请重试",
          icon: "none"
        });
      } finally {
        this.favoriteLoading = false;
      }
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.f($options.productImages, (img, index, i0) => {
      return {
        a: img,
        b: index
      };
    }),
    b: common_vendor.t($data.productDetail.productName || $data.productDetail.name),
    c: common_vendor.t($data.productDetail.description),
    d: common_vendor.t($data.productDetail.price),
    e: $data.productDetail.originalPrice
  }, $data.productDetail.originalPrice ? {
    f: common_vendor.t($data.productDetail.originalPrice)
  } : {}, {
    g: common_vendor.t($data.productDetail.stock),
    h: $data.specifications.length > 0
  }, $data.specifications.length > 0 ? {
    i: common_vendor.f($data.specifications, (spec, index, i0) => {
      return {
        a: common_vendor.t(spec.name),
        b: common_vendor.t(spec.value),
        c: index
      };
    })
  } : {}, {
    j: $data.productDetail.detail
  }, $data.productDetail.detail ? {
    k: $data.productDetail.detail
  } : {}, {
    l: $data.recommendations.length > 0
  }, $data.recommendations.length > 0 ? {
    m: common_vendor.f($data.recommendations, (item, index, i0) => {
      return {
        a: $options.getProductImage(item),
        b: common_vendor.t(item.productName || item.name),
        c: common_vendor.t(item.price),
        d: index,
        e: common_vendor.o(($event) => $options.goToProduct(item.id), index)
      };
    })
  } : {}, {
    n: common_vendor.o((...args) => $options.goToStore && $options.goToStore(...args)),
    o: common_vendor.o((...args) => $options.goToCart && $options.goToCart(...args)),
    p: common_vendor.t($data.isFavorited ? "❤️" : "🤍"),
    q: common_vendor.t($data.isFavorited ? "已收藏" : "收藏"),
    r: common_vendor.o((...args) => $options.handleCollect && $options.handleCollect(...args)),
    s: $data.isFavorited ? 1 : "",
    t: common_vendor.t($data.isInCart ? "查看购物车" : "加入购物车"),
    v: $data.isInCart ? 1 : "",
    w: common_vendor.o((...args) => $options.handleCartAction && $options.handleCartAction(...args)),
    x: common_vendor.o((...args) => $options.buyNow && $options.buyNow(...args))
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/pages/mall/product/detail.js.map
