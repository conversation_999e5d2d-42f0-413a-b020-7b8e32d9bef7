"use strict";
const common_vendor = require("../common/vendor.js");
const utils_bleCommandSet = require("./bleCommandSet.js");
const utils_request = require("./request.js");
const store_wifi = require("../store/wifi.js");
class HairDyeRecipeExecutor {
  constructor(store) {
    this.store = store;
  }
  /**
   * 从染膏配方对象生成指令队列
   * @param {Object} recipe - 染膏配方对象
   * @returns {Array} 指令步骤数组
   */
  generateStepsFromRecipe(recipe) {
    const steps = [];
    common_vendor.index.__f__("log", "at utils/hairDyeManager.js:43", "染膏", recipe);
    let hydrogenPeroxide3Complete = 0;
    let hydrogenPeroxide12Complete = 0;
    if (recipe.hydrogenPeroxide6Percent) {
      const dose = parseFloat((recipe.hydrogenPeroxide6Percent / 3).toFixed(1));
      const hydrogenPeroxide3 = dose * 2;
      const hydrogenPeroxide12 = dose * 1;
      hydrogenPeroxide3Complete += hydrogenPeroxide3;
      hydrogenPeroxide12Complete += hydrogenPeroxide12;
    }
    if (recipe.hydrogenPeroxide9Percent) {
      const dose = parseFloat((recipe.hydrogenPeroxide9Percent / 3).toFixed(1));
      const hydrogenPeroxide3 = dose * 1;
      const hydrogenPeroxide12 = dose * 2;
      hydrogenPeroxide3Complete += hydrogenPeroxide3;
      hydrogenPeroxide12Complete += hydrogenPeroxide12;
    }
    const resultHydrogenPeroxide12Complete = (recipe.hydrogenPeroxide12Percent || 0) + (hydrogenPeroxide12Complete || 0);
    if (resultHydrogenPeroxide12Complete > 0) {
      steps.push({
        type: "dispense",
        color: "HYDROGENPEROXIDE12PERCENT",
        grams: resultHydrogenPeroxide12Complete
      });
    }
    const resultHydrogenPeroxide3Complete = (recipe.hydrogenPeroxide3Percent || 0) + (hydrogenPeroxide3Complete || 0);
    if (resultHydrogenPeroxide3Complete > 0) {
      steps.push({
        type: "dispense",
        color: "HYDROGENPEROXIDE3PERCENT",
        grams: resultHydrogenPeroxide3Complete
      });
    }
    common_vendor.index.__f__("log", "at utils/hairDyeManager.js:104", `百分之3的双氧乳为${resultHydrogenPeroxide3Complete}g`);
    common_vendor.index.__f__("log", "at utils/hairDyeManager.js:105", `百分之12的双氧乳为${resultHydrogenPeroxide12Complete}g`);
    const colorOrder = [
      "faded_bleached",
      "black",
      "blue",
      "brown",
      "purple",
      "red",
      "orange",
      "yellow",
      "green",
      "gray"
    ];
    colorOrder.forEach((color) => {
      if (recipe[color] !== null && recipe[color] > 0) {
        const data = {
          type: "dispense",
          color: color.toUpperCase(),
          grams: recipe[color]
        };
        common_vendor.index.__f__("log", "at utils/hairDyeManager.js:128", "color.toUpperCase()转换码", data.color);
        common_vendor.index.__f__("log", "at utils/hairDyeManager.js:129", "指令步骤");
        steps.push(data);
      }
    });
    common_vendor.index.__f__("log", "at utils/hairDyeManager.js:140", "✖️ ❌当前指令有", steps);
    return steps;
  }
  /**
  * 执行染膏配方
  * @param {Object} recipe - 染膏配方对象
  * {
   
  * }
  * @param {Object} options - 选项
  * @param {boolean} [options.clearQueue=true] - 是否清空现有队列
  * @param {boolean} [options.startImmediately=true] - 是否立即开始执行
  */
  async executeRecipe(recipe, model, options = {
    clearQueue: true,
    startImmediately: true
  }) {
    try {
      common_vendor.index.__f__("log", "at utils/hairDyeManager.js:164", "设备在线", this.store.state.connectedDevice);
      const app = getApp();
      if (!app.globalData.deviceStatus) {
        return false;
      }
      if (options.clearQueue) {
        await this.store.dispatch("clearCommandQueue");
      }
      if (!recipe || typeof recipe !== "object") {
        throw new Error("无效的配方: recipe必须是对象");
      }
      const steps = this.generateStepsFromRecipe(recipe);
      if (!Array.isArray(steps) || steps.length === 0) {
        throw new Error("无法从配方生成有效步骤");
      }
      const commands = [];
      for (const [index, step] of steps.entries()) {
        try {
          if (!step || !step.type) {
            throw new Error(`步骤${index + 1}缺少type属性`);
          }
          let command;
          switch (step.type) {
            case "dispense":
              if (!step.color) {
                throw new Error(`步骤${index + 1}缺少color属性`);
              }
              if (typeof step.grams !== "number" || step.grams <= 0) {
                throw new Error(`步骤${index + 1}的grams必须是正数`);
              }
              const colorCode = utils_bleCommandSet.BLUETOOTH_COMMANDS.HAIRDYECOLOR[step.color];
              common_vendor.index.__f__("log", "at utils/hairDyeManager.js:212", "hairDyeManager颜色码", colorCode);
              if (!colorCode) {
                throw new Error(
                  `步骤${index + 1}: 未知颜色类型: ${step.color}`
                );
              }
              command = utils_bleCommandSet.getCommand(
                "startDispense",
                step.grams,
                colorCode,
                model
              );
              if (!command) {
                throw new Error(`步骤${index + 1}: 无法生成dispense命令`);
              }
              break;
            case "pause":
              command = utils_bleCommandSet.getCommand("pauseDispense");
              if (!command) {
                throw new Error(`步骤${index + 1}: 无法生成pause命令`);
              }
              break;
            case "stop":
              command = utils_bleCommandSet.getCommand("stopDispense");
              if (!command) {
                throw new Error(`步骤${index + 1}: 无法生成stop命令`);
              }
              break;
            default:
              throw new Error(
                `步骤${index + 1}: 未知配方步骤类型: ${step.type}`
              );
          }
          if (command) {
            command.id = Date.now() + Math.random();
            await this.store.dispatch("addToCommandQueue", {
              command,
              immediate: false
            });
            common_vendor.index.__f__("log", "at utils/hairDyeManager.js:259", "加入指令成功", command);
            commands.push(command);
          }
        } catch (stepError) {
          throw new Error(`处理步骤${index + 1}时出错: ${stepError.message}`);
        }
      }
      app.setExecutingQueue(true);
      app.setPausedStatus(false);
      common_vendor.index.__f__(
        "log",
        "at utils/hairDyeManager.js:312",
        "指令执行之前..................................................."
      );
      common_vendor.index.__f__("log", "at utils/hairDyeManager.js:315", "当前连接是通过", app.globalData.connectionMethod);
      if (app.isBluetoothConnection()) {
        await this.store.dispatch("executeCommandQueue");
      } else if (app.isWifiConnection()) {
        await store_wifi.executeNextCommand();
      }
      common_vendor.index.__f__(
        "log",
        "at utils/hairDyeManager.js:321",
        "指令执行之后完成..................................................."
      );
      common_vendor.index.__f__("log", "at utils/hairDyeManager.js:325", "指令生成之前", commands);
      return commands;
    } catch (error) {
      throw new Error(`执行配方失败: ${error.message}`);
    }
  }
  /**
   * 暂停当前出料
   */
  async pauseDispensing() {
    try {
      let result;
      const app = getApp();
      common_vendor.index.showLoading({
        title: "正在暂停...",
        mask: true
      });
      if (app.isBluetoothConnection()) {
        result = await this.store.dispatch("pauseExecution");
      } else if (app.isWifiConnection()) {
        const deviceCode = app.globalData.connectedDeviceId;
        const response = await utils_request.apiService.mall.command.sendPause(deviceCode);
        result = response && response.code === 200;
      }
      common_vendor.index.hideLoading();
      return result;
    } catch (error) {
      common_vendor.index.hideLoading();
      common_vendor.index.__f__("error", "at utils/hairDyeManager.js:367", "暂停出料失败:", error);
      common_vendor.index.showToast({
        title: "暂停失败",
        icon: "error"
      });
      throw new Error(`暂停出料失败: ${error.message}`);
    }
  }
  /**
   * 恢复当前出料
   */
  async resumeDispensing() {
    try {
      let result;
      const app = getApp();
      common_vendor.index.showLoading({
        title: "正在恢复...",
        mask: true
      });
      if (app.isBluetoothConnection()) {
        result = await this.store.dispatch("resumeExecution");
      } else if (app.isWifiConnection()) {
        const deviceCode = app.globalData.connectedDeviceId;
        const response = await utils_request.apiService.mall.command.sendContinue(deviceCode);
        result = response && response.code === 200;
      }
      common_vendor.index.hideLoading();
      return result;
    } catch (error) {
      common_vendor.index.hideLoading();
      common_vendor.index.__f__("error", "at utils/hairDyeManager.js:408", "恢复出料失败:", error);
      common_vendor.index.showToast({
        title: "恢复失败",
        icon: "error"
      });
      throw new Error(`恢复出料失败: ${error.message}`);
    }
  }
  /**
   * 取消出料
   */
  async canceling() {
    try {
      let result;
      const app = getApp();
      common_vendor.index.showLoading({
        title: "正在取消...",
        mask: true
      });
      common_vendor.index.__f__("log", "at utils/hairDyeManager.js:431", "取消出料");
      if (app.isBluetoothConnection()) {
        const command = utils_bleCommandSet.getCommand("stopDispense");
        if (command) {
          await this.store.dispatch("writeData", command.value);
          result = true;
        }
      } else if (app.isWifiConnection()) {
        const deviceCode = app.globalData.connectedDeviceId;
        const response = await utils_request.apiService.mall.command.sendCancel(deviceCode);
        result = response && response.code === 200;
      }
      common_vendor.index.hideLoading();
      common_vendor.index.__f__("log", "at utils/hairDyeManager.js:452", "取消指令已发送");
      return result;
    } catch (error) {
      common_vendor.index.hideLoading();
      common_vendor.index.__f__("error", "at utils/hairDyeManager.js:457", "取消出料失败:", error);
      common_vendor.index.showToast({
        title: "取消失败",
        icon: "error"
      });
      throw new Error(`取消出料失败: ${error.message}`);
    }
  }
  /**
   * 停止当前出料
   */
  async stopDispensing() {
    try {
      const app = getApp();
      if (app.isBluetoothConnection()) {
        const command = utils_bleCommandSet.getCommand("stopDispense");
        await this.store.dispatch("addToCommandQueue", {
          command,
          immediate: true
        });
      } else if (app.isWifiConnection()) {
        const deviceCode = app.globalData.connectedDeviceId;
        const response = await utils_request.apiService.mall.command.sendStop(deviceCode, {
          model: "smart"
        });
        if (!response || response.code !== 200) {
          return false;
        }
      }
      return true;
    } catch (error) {
      common_vendor.index.__f__("error", "at utils/hairDyeManager.js:491", "停止出料失败:", error);
      throw new Error(`停止出料失败: ${error.message}`);
    }
  }
  /**
   * 大师模式
   */
  async stopMaster() {
    try {
      const app = getApp();
      if (app.isBluetoothConnection()) {
        const command = utils_bleCommandSet.getCommand("masterStop");
        await this.store.dispatch("addToCommandQueue", {
          command,
          immediate: true
        });
        await this.store.dispatch("executeCommandQueue");
      } else if (app.isWifiConnection()) {
        const deviceCode = app.globalData.connectedDeviceId;
        const response = await utils_request.apiService.mall.command.sendStop(deviceCode, {
          model: "master"
        });
        if (!response || response.code !== 200) {
          throw new Error(`大师模式出料失败: 接口调用失败`);
        }
      }
      app.updateCommandStatus();
    } catch (err) {
      common_vendor.index.__f__("error", "at utils/hairDyeManager.js:530", "大师模式出料失败:", err);
      throw new Error(`大师模式出料失败: ${err.message}`);
    } finally {
    }
  }
  /**
   * 紧急停止所有操作
   */
  async emergencyStop() {
    try {
      await this.stopDispensing();
      await this.store.dispatch("clearCommandQueue");
      return true;
    } catch (error) {
      common_vendor.index.__f__("error", "at utils/hairDyeManager.js:549", "紧急停止失败:", error);
      throw new Error(`紧急停止失败: ${error.message}`);
    }
  }
}
exports.HairDyeRecipeExecutor = HairDyeRecipeExecutor;
//# sourceMappingURL=../../.sourcemap/mp-weixin/utils/hairDyeManager.js.map
