"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_request = require("../../utils/request.js");
const _sfc_main = {
  data() {
    return {
      guideList: [],
      loading: false,
      error: ""
    };
  },
  onLoad() {
    this.loadGuideList();
  },
  methods: {
    goBack() {
      common_vendor.index.navigateBack({
        delta: 1
      });
    },
    async loadGuideList() {
      try {
        this.loading = true;
        this.error = "";
        const response = await utils_request.get("/api/guide/list");
        if (response && response.code === 200) {
          this.guideList = response.data || [];
        } else {
          this.error = (response == null ? void 0 : response.message) || "获取引导列表失败";
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/my/guide.vue:89", "获取引导列表失败:", error);
        this.error = "网络错误，请稍后重试";
      } finally {
        this.loading = false;
      }
    },
    viewGuide(guide) {
      common_vendor.index.navigateTo({
        url: `/pages/my/guide-detail?id=${guide.id}&title=${encodeURIComponent(guide.title)}`,
        success: function() {
          common_vendor.index.__f__("log", "at pages/my/guide.vue:100", "跳转到引导详情成功");
        },
        fail: function() {
          common_vendor.index.showToast({
            title: "页面跳转失败",
            icon: "none"
          });
        }
      });
    },
    getGuideTypeText(type) {
      const typeMap = {
        "general": "通用",
        "device": "设备",
        "operation": "操作",
        "maintenance": "维护"
      };
      return typeMap[type] || "通用";
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.loading
  }, $data.loading ? {} : $data.error ? {
    c: common_vendor.t($data.error),
    d: common_vendor.o((...args) => $options.loadGuideList && $options.loadGuideList(...args))
  } : common_vendor.e({
    e: common_vendor.f($data.guideList, (item, index, i0) => {
      return {
        a: common_vendor.t(item.stepOrder || index + 1),
        b: common_vendor.t(item.title),
        c: common_vendor.t(item.description || "点击查看详细内容"),
        d: common_vendor.t($options.getGuideTypeText(item.guideType)),
        e: common_vendor.t(item.viewCount || 0),
        f: item.id,
        g: common_vendor.o(($event) => $options.viewGuide(item), item.id)
      };
    }),
    f: $data.guideList.length === 0
  }, $data.guideList.length === 0 ? {} : {}), {
    b: $data.error
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/my/guide.js.map
