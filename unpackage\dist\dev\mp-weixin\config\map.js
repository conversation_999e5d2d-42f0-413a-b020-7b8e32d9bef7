"use strict";
const mapConfig = {
  // 高德地图配置
  amap: {
    key: "48f35f240d10902e83fedb71a918e4f7",
    // 请替换为您的高德地图API key
    geocoderUrl: "https://restapi.amap.com/v3/geocode/regeo",
    poiSearchUrl: "https://restapi.amap.com/v3/place/text",
    enabled: true
    // 启用高德地图服务
  },
  // 默认配置
  default: {
    // 缓存过期时间（毫秒）
    cacheExpireTime: 30 * 60 * 1e3,
    // 30分钟
    // 位置精度（米）
    accuracy: 1e3,
    // 超时时间（毫秒）
    timeout: 1e4
  }
};
function getAvailableMapService() {
  {
    return "amap";
  }
}
function getMapServiceConfig(service) {
  return mapConfig[service] || null;
}
exports.getAvailableMapService = getAvailableMapService;
exports.getMapServiceConfig = getMapServiceConfig;
exports.mapConfig = mapConfig;
//# sourceMappingURL=../../.sourcemap/mp-weixin/config/map.js.map
