/* 水平间距 */
/* 水平间距 */
/* 全局样式修复 - 确保页面占满屏幕宽度 */
page {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
}

/* 通用容器样式 */
.container, .page-container {
  width: 100%;
  min-height: 100vh;
  box-sizing: border-box;
}

/* 修复小程序页面宽度问题 */
uni-page-body {
  width: 100% !important;
}

/* 确保所有view元素正确继承宽度 */
view {
  box-sizing: border-box;
}
.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f5f5;
}
.order-tabs {
  display: flex;
  background-color: #fff;
  padding: 0 20rpx;
  border-bottom: 1px solid #eee;
}
.tab-item {
  flex: 1;
  height: 80rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  font-size: 28rpx;
  color: #666;
}
.tab-item.active {
  color: #4285f4;
  font-weight: 500;
}
.active-line {
  position: absolute;
  bottom: 0;
  width: 40rpx;
  height: 4rpx;
  background-color: #4285f4;
  border-radius: 2rpx;
}
.empty-state {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 50rpx;
}
.empty-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
}
.empty-text {
  font-size: 30rpx;
  color: #999;
  margin-bottom: 40rpx;
}
.go-shopping-btn {
  width: 240rpx;
  height: 80rpx;
  background-color: #4285f4;
  color: #fff;
  border-radius: 40rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 30rpx;
}
.loading-state {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 28rpx;
  color: #999;
}
.order-list {
  flex: 1;
  padding: 20rpx;
}
.order-item {
  background-color: #fff;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}
.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  border-bottom: 1px solid #f0f0f0;
}
.order-no {
  font-size: 28rpx;
  color: #333;
}
.order-status {
  font-size: 28rpx;
  color: #4285f4;
  font-weight: 500;
}
.product-list {
  padding: 20rpx;
}
.product-item {
  display: flex;
  margin-bottom: 20rpx;
}
.product-item:last-child {
  margin-bottom: 0;
}
.product-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 8rpx;
  margin-right: 20rpx;
}
.product-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.product-name {
  font-size: 28rpx;
  color: #333;
  line-height: 1.4;
  margin-bottom: 10rpx;
}
.product-spec {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 10rpx;
}
.product-price-qty {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.product-price {
  font-size: 28rpx;
  color: #ff4757;
  font-weight: 500;
}
.product-qty {
  font-size: 24rpx;
  color: #999;
}
.order-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  border-top: 1px solid #f0f0f0;
  background-color: #fafafa;
}
.total-price {
  font-size: 28rpx;
  color: #333;
}
.order-actions {
  display: flex;
  gap: 20rpx;
}
.action-btn {
  padding: 12rpx 24rpx;
  border-radius: 20rpx;
  font-size: 26rpx;
  border: 1px solid #ddd;
  color: #666;
  background-color: #fff;
}
.action-btn.primary {
  background-color: #4285f4;
  color: #fff;
  border-color: #4285f4;
}