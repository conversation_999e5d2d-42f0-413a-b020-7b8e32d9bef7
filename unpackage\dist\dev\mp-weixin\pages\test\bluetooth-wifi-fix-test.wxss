/* 水平间距 */
/* 水平间距 */
/* 全局样式修复 - 确保页面占满屏幕宽度 */
page.data-v-9e44c880 {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
}

/* 通用容器样式 */
.container.data-v-9e44c880, .page-container.data-v-9e44c880 {
  width: 100%;
  min-height: 100vh;
  box-sizing: border-box;
}

/* 修复小程序页面宽度问题 */
uni-page-body.data-v-9e44c880 {
  width: 100% !important;
}

/* 确保所有view元素正确继承宽度 */
view.data-v-9e44c880 {
  box-sizing: border-box;
}
.container.data-v-9e44c880 {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}
.header.data-v-9e44c880 {
  text-align: center;
  margin-bottom: 30rpx;
}
.header .title.data-v-9e44c880 {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 10rpx;
}
.header .subtitle.data-v-9e44c880 {
  font-size: 28rpx;
  color: #666;
  display: block;
}
.section-title.data-v-9e44c880 {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}
.status-section.data-v-9e44c880, .validation-section.data-v-9e44c880, .test-section.data-v-9e44c880, .log-section.data-v-9e44c880 {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}
.status-item.data-v-9e44c880 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #eee;
}
.status-item.data-v-9e44c880:last-child {
  border-bottom: none;
}
.status-item .label.data-v-9e44c880 {
  font-size: 28rpx;
  color: #666;
}
.status-item .value.data-v-9e44c880 {
  font-size: 28rpx;
  font-weight: bold;
}
.status-item .value.bluetooth.data-v-9e44c880 {
  color: #007AFF;
}
.status-item .value.wifi.data-v-9e44c880 {
  color: #34C759;
}
.status-item .value.none.data-v-9e44c880 {
  color: #999;
}
.status-item .value.online.data-v-9e44c880 {
  color: #34C759;
}
.status-item .value.offline.data-v-9e44c880 {
  color: #FF3B30;
}
.btn.data-v-9e44c880 {
  padding: 20rpx 40rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  text-align: center;
  margin: 10rpx;
  border: none;
}
.btn.btn-primary.data-v-9e44c880 {
  background-color: #007AFF;
  color: white;
}
.btn.btn-secondary.data-v-9e44c880 {
  background-color: #34C759;
  color: white;
}
.btn.btn-test.data-v-9e44c880 {
  background-color: #FF9500;
  color: white;
}
.btn.btn-small.data-v-9e44c880 {
  padding: 15rpx 30rpx;
  font-size: 24rpx;
  display: inline-block;
  margin: 5rpx;
}
.validation-result.data-v-9e44c880 {
  margin-top: 20rpx;
}
.validation-result .validation-status.data-v-9e44c880 {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 15rpx;
}
.validation-result .validation-status.valid.data-v-9e44c880 {
  color: #34C759;
}
.validation-result .validation-status.invalid.data-v-9e44c880 {
  color: #FF3B30;
}
.validation-result .issues .issue.data-v-9e44c880 {
  background: #FFF3CD;
  border: 1rpx solid #FFEAA7;
  border-radius: 8rpx;
  padding: 15rpx;
  margin-bottom: 10rpx;
}
.validation-result .issues .issue .issue-type.data-v-9e44c880 {
  font-weight: bold;
  color: #856404;
  display: block;
  margin-bottom: 5rpx;
}
.validation-result .issues .issue .issue-message.data-v-9e44c880 {
  color: #856404;
  display: block;
  margin-bottom: 5rpx;
}
.validation-result .issues .issue .issue-severity.data-v-9e44c880 {
  font-size: 24rpx;
  padding: 4rpx 8rpx;
  border-radius: 4rpx;
}
.validation-result .issues .issue .issue-severity.high.data-v-9e44c880 {
  background: #FF3B30;
  color: white;
}
.validation-result .issues .issue .issue-severity.medium.data-v-9e44c880 {
  background: #FF9500;
  color: white;
}
.validation-result .issues .issue .issue-severity.low.data-v-9e44c880 {
  background: #34C759;
  color: white;
}
.log-container.data-v-9e44c880 {
  height: 600rpx;
  border: 1rpx solid #eee;
  border-radius: 8rpx;
  padding: 15rpx;
}
.log-item.data-v-9e44c880 {
  border-bottom: 1rpx solid #eee;
  padding: 15rpx 0;
}
.log-item.data-v-9e44c880:last-child {
  border-bottom: none;
}
.log-item .log-header.data-v-9e44c880 {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
}
.log-item .log-header .log-time.data-v-9e44c880 {
  font-size: 24rpx;
  color: #999;
  margin-right: 15rpx;
}
.log-item .log-header .log-action.data-v-9e44c880 {
  font-size: 26rpx;
  font-weight: bold;
  color: #333;
  margin-right: 15rpx;
}
.log-item .log-header .log-type.data-v-9e44c880 {
  font-size: 24rpx;
  padding: 4rpx 8rpx;
  border-radius: 4rpx;
}
.log-item .log-header .log-type.bluetooth.data-v-9e44c880 {
  background: #007AFF;
  color: white;
}
.log-item .log-header .log-type.wifi.data-v-9e44c880 {
  background: #34C759;
  color: white;
}
.log-item .log-header .log-type.unknown.data-v-9e44c880 {
  background: #999;
  color: white;
}
.log-item .log-details.data-v-9e44c880 {
  font-size: 24rpx;
  color: #666;
  background: #f8f8f8;
  padding: 10rpx;
  border-radius: 4rpx;
  margin-bottom: 10rpx;
  white-space: pre-wrap;
}
.log-item .log-validation.data-v-9e44c880 {
  font-size: 24rpx;
  color: #FF9500;
  font-weight: bold;
}