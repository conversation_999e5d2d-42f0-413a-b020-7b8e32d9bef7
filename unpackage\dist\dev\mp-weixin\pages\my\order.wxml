<view class="container"><view class="order-tabs"><view wx:for="{{a}}" wx:for-item="tab" wx:key="c" class="{{['tab-item', tab.d && 'active']}}" bindtap="{{tab.e}}"><text>{{tab.a}}</text><view wx:if="{{tab.b}}" class="active-line"></view></view></view><view wx:if="{{b}}" class="loading-state"><text>加载中...</text></view><view wx:elif="{{c}}" class="order-list"><view wx:for="{{d}}" wx:for-item="order" wx:key="l" class="order-item" bindtap="{{order.m}}"><view class="order-header"><text class="order-no">订单号：{{order.a}}</text><text class="order-status">{{order.b}}</text></view><view class="product-list"><view wx:for="{{order.c}}" wx:for-item="item" wx:key="f" class="product-item"><image class="product-image" src="{{item.a}}" mode="aspectFill"></image><view class="product-info"><text class="product-name">{{item.b}}</text><text class="product-spec">{{item.c}}</text><view class="product-price-qty"><text class="product-price">¥{{item.d}}</text><text class="product-qty">x{{item.e}}</text></view></view></view></view><view class="order-footer"><text class="total-price">共{{order.d}}件商品 合计：¥{{order.e}}</text><view class="order-actions"><view wx:if="{{order.f}}" class="action-btn primary" catchtap="{{order.g}}"><text>立即付款</text></view><view wx:if="{{order.h}}" class="action-btn" catchtap="{{order.i}}"><text>取消订单</text></view><view wx:if="{{order.j}}" class="action-btn primary" catchtap="{{order.k}}"><text>确认收货</text></view></view></view></view></view><view wx:else class="empty-state"><image class="empty-icon" src="{{e}}" mode="aspectFit"></image><text class="empty-text">暂无相关订单</text><view class="go-shopping-btn" bindtap="{{f}}"><text>去逛逛</text></view></view></view>