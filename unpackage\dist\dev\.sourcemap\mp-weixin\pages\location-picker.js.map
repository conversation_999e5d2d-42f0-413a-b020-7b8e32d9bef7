{"version": 3, "file": "location-picker.js", "sources": ["pages/location-picker.vue", "D:/edgexiazai/HBuilderX.4.66.2025051912/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvbG9jYXRpb24tcGlja2VyLnZ1ZQ"], "sourcesContent": ["<template>\n  <view class=\"location-picker\">\n    <!-- 头部 -->\n    <view class=\"header\">\n\t  <view class=\"header-btn\" @click=\"confirmLocation\">确定</view>\n      <view class=\"header-title\">{{selectedLocation.name || '广东省东莞市南城街道'}}</view>\n      <view class=\"header-btn\" @click=\"goBack\">取消</view>\n    </view>\n\n    <!-- 搜索框 -->\n    <view class=\"search-container\">\n      <view class=\"search-box\">\n        <view class=\"search-icon\">🔍</view>\n        <input \n          type=\"text\" \n          class=\"search-input\" \n          placeholder=\"搜索地点\" \n          v-model=\"searchKeyword\"\n          @input=\"onSearchInput\"\n        />\n      </view>\n    </view>\n\n    <!-- 当前位置 -->\n    <view class=\"current-location\">\n      <view class=\"location-icon\">📍</view>\n      <view class=\"current-location-text\">\n        <view class=\"current-location-name\">{{selectedLocation.name || '广东省东莞市南城街道'}}</view>\n        <view class=\"current-location-detail\">{{selectedLocation.address || '广东省东莞市南城街道'}}</view>\n      </view>\n      <view class=\"relocate-btn\" @click=\"getCurrentLocation\">重新定位</view>\n    </view>\n\n    <!-- 地图容器 -->\n    <view class=\"map-container\">\n      <map\n        class=\"map\"\n        :latitude=\"mapCenter.latitude\"\n        :longitude=\"mapCenter.longitude\"\n        :markers=\"markers\"\n        :scale=\"16\"\n        :show-location=\"false\"\n        :enable-scroll=\"true\"\n        :enable-zoom=\"true\"\n        :enable-rotate=\"false\"\n        @markertap=\"onMarkerTap\"\n        @tap=\"onMapTap\"\n        @regionchange=\"onRegionChange\"\n      >\n      </map>\n    </view>\n\n    <!-- 位置列表 -->\n    <scroll-view class=\"location-list\" scroll-y>\n      <view \n        v-for=\"(location, index) in locationList\" \n        :key=\"index\" \n        class=\"location-item\"\n        @click=\"selectLocation(location)\"\n      >\n        <view class=\"location-name\">{{location.name}}</view>\n        <view class=\"location-address\">{{location.address}}</view>\n      </view>\n    </scroll-view>\n  </view>\n</template>\n\n<script>\nexport default {\n  data() {\n    return {\n      selectedLocation: {\n        name: '广东省东莞市南城街道',\n        address: '广东省东莞市南城街道'\n      },\n      searchKeyword: '',\n      locationList: [\n        { name: '广东省东莞市南城街道', address: '广东省东莞市南城街道' },\n        { name: '广东省东莞市东城街道', address: '广东省东莞市东城街道' },\n        { name: '广东省东莞市万江街道', address: '广东省东莞市万江街道' },\n        { name: '广东省东莞市莞城街道', address: '广东省东莞市莞城街道' },\n        { name: '广东省东莞市石碣镇', address: '广东省东莞市石碣镇' }\n      ],\n      defaultLocationList: [],\n      mapCenter: {\n        latitude: 23.0489,\n        longitude: 113.7447\n      },\n      markers: [],\n      isLocationInitialized: false\n    };\n  },\n\n  onLoad() {\n    this.defaultLocationList = [...this.locationList];\n    this.getCurrentLocation();\n  },\n\n  methods: {\n    // 获取当前位置\n    getCurrentLocation() {\n      uni.showLoading({\n        title: '正在定位...'\n      });\n\n      // 使用微信原生定位\n      uni.getLocation({\n        type: 'gcj02',\n        success: (res) => {\n          // 使用默认地址而不是\"当前位置\"\n          const defaultAddress = '广东省东莞市南城街道';\n\n          this.selectedLocation = {\n            name: defaultAddress,\n            address: defaultAddress,\n            latitude: res.latitude,\n            longitude: res.longitude\n          };\n\n          // 更新地图中心和标记\n          this.updateMapLocation(res.latitude, res.longitude, defaultAddress);\n\n          console.log('位置获取成功:', res);\n          uni.hideLoading();\n        },\n        fail: (err) => {\n          console.error('定位失败:', err);\n          uni.hideLoading();\n          uni.showToast({\n            title: '定位失败',\n            icon: 'none'\n          });\n        }\n      });\n    },\n\n    // 更新地图位置\n    updateMapLocation(latitude, longitude, address) {\n      // 只在第一次获取位置时更新地图中心，避免地图跳动\n      if (!this.isLocationInitialized) {\n        this.mapCenter = {\n          latitude: latitude,\n          longitude: longitude\n        };\n        this.isLocationInitialized = true;\n      }\n\n      // 始终更新标记位置\n      this.markers = [\n        {\n          id: 1,\n          latitude: latitude,\n          longitude: longitude,\n          width: 30,\n          height: 30,\n          callout: {\n            content: address || '当前位置',\n            color: '#333',\n            fontSize: 14,\n            borderRadius: 4,\n            bgColor: '#fff',\n            padding: 8,\n            display: 'ALWAYS'\n          }\n        }\n      ];\n    },\n\n    // 地图标记点击事件\n    onMarkerTap(e) {\n      console.log('标记点击:', e);\n    },\n\n    // 地图点击事件\n    onMapTap(e) {\n      console.log('地图点击:', e);\n    },\n\n    // 地图区域变化事件\n    onRegionChange(e) {\n      console.log('地图区域变化:', e);\n    },\n\n    // 搜索输入事件\n    onSearchInput() {\n      if (this.searchKeyword.trim()) {\n        this.searchLocations(this.searchKeyword);\n      } else {\n        this.locationList = [...this.defaultLocationList];\n      }\n    },\n\n    // 搜索位置\n    searchLocations(query) {\n      // 使用模拟搜索数据\n      const mockResults = [\n        { name: `${query}附近`, address: `广东省东莞市${query}` },\n        { name: `${query}商圈`, address: `广东省东莞市南城街道${query}` },\n        { name: `${query}地铁站`, address: `广东省东莞市${query}地铁站` },\n        { name: `${query}购物中心`, address: `广东省东莞市南城街道${query}购物中心` },\n        { name: `${query}公园`, address: `广东省东莞市${query}公园` }\n      ];\n      this.locationList = mockResults;\n    },\n\n    // 选择位置\n    selectLocation(location) {\n      this.selectedLocation = location;\n      \n      // 如果有坐标，更新地图\n      if (location.latitude && location.longitude) {\n        this.updateMapLocation(location.latitude, location.longitude, location.name);\n      }\n    },\n\n    // 确认位置\n    confirmLocation() {\n      // 返回上一页并传递选中的位置\n      const pages = getCurrentPages();\n      const prevPage = pages[pages.length - 2];\n      \n      if (prevPage) {\n        prevPage.$vm.currentLocation = this.selectedLocation;\n      }\n      \n      uni.navigateBack();\n    },\n\n    // 返回\n    goBack() {\n      uni.navigateBack();\n    }\n  }\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.location-picker {\n  height: 100vh;\n  display: flex;\n  flex-direction: column;\n  background-color: #f5f5f5;\n}\n\n.header {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 20rpx 30rpx;\n  background-color: #fff;\n  border-bottom: 1rpx solid #eee;\n}\n\n.header-title {\n  font-size: 32rpx;\n  font-weight: 500;\n  color: #333;\n  flex: 1;\n  text-align: center;\n}\n\n.header-btn {\n  font-size: 28rpx;\n  color: #4285f4;\n  padding: 10rpx 20rpx;\n}\n\n.search-container {\n  padding: 20rpx 30rpx;\n  background-color: #fff;\n  border-bottom: 1rpx solid #eee;\n}\n\n.search-box {\n  display: flex;\n  align-items: center;\n  background-color: #f5f5f5;\n  border-radius: 20rpx;\n  padding: 20rpx;\n}\n\n.search-icon {\n  margin-right: 20rpx;\n  font-size: 28rpx;\n}\n\n.search-input {\n  flex: 1;\n  font-size: 28rpx;\n  color: #333;\n}\n\n.current-location {\n  display: flex;\n  align-items: center;\n  padding: 30rpx;\n  background-color: #fff;\n  border-bottom: 1rpx solid #eee;\n}\n\n.location-icon {\n  font-size: 32rpx;\n  margin-right: 20rpx;\n}\n\n.current-location-text {\n  flex: 1;\n}\n\n.current-location-name {\n  font-size: 30rpx;\n  color: #333;\n  font-weight: 500;\n}\n\n.current-location-detail {\n  font-size: 24rpx;\n  color: #999;\n  margin-top: 10rpx;\n}\n\n.relocate-btn {\n  font-size: 24rpx;\n  color: #4285f4;\n  padding: 10rpx 20rpx;\n  border: 1rpx solid #4285f4;\n  border-radius: 20rpx;\n}\n\n.map-container {\n  height: 400rpx;\n  background-color: #fff;\n  border-bottom: 1rpx solid #eee;\n}\n\n.map {\n  width: 100%;\n  height: 100%;\n}\n\n.location-list {\n  flex: 1;\n  background-color: #fff;\n}\n\n.location-item {\n  padding: 30rpx;\n  border-bottom: 1rpx solid #eee;\n}\n\n.location-name {\n  font-size: 30rpx;\n  color: #333;\n  font-weight: 500;\n}\n\n.location-address {\n  font-size: 24rpx;\n  color: #999;\n  margin-top: 10rpx;\n}\n</style>\n", "import MiniProgramPage from 'E:/tempCode/new_www_production/new_www_production/pages/location-picker.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;AAoEA,MAAK,YAAU;AAAA,EACb,OAAO;AACL,WAAO;AAAA,MACL,kBAAkB;AAAA,QAChB,MAAM;AAAA,QACN,SAAS;AAAA,MACV;AAAA,MACD,eAAe;AAAA,MACf,cAAc;AAAA,QACZ,EAAE,MAAM,cAAc,SAAS,aAAc;AAAA,QAC7C,EAAE,MAAM,cAAc,SAAS,aAAc;AAAA,QAC7C,EAAE,MAAM,cAAc,SAAS,aAAc;AAAA,QAC7C,EAAE,MAAM,cAAc,SAAS,aAAc;AAAA,QAC7C,EAAE,MAAM,aAAa,SAAS,YAAY;AAAA,MAC3C;AAAA,MACD,qBAAqB,CAAE;AAAA,MACvB,WAAW;AAAA,QACT,UAAU;AAAA,QACV,WAAW;AAAA,MACZ;AAAA,MACD,SAAS,CAAE;AAAA,MACX,uBAAuB;AAAA;EAE1B;AAAA,EAED,SAAS;AACP,SAAK,sBAAsB,CAAC,GAAG,KAAK,YAAY;AAChD,SAAK,mBAAkB;AAAA,EACxB;AAAA,EAED,SAAS;AAAA;AAAA,IAEP,qBAAqB;AACnBA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,MACT,CAAC;AAGDA,oBAAAA,MAAI,YAAY;AAAA,QACd,MAAM;AAAA,QACN,SAAS,CAAC,QAAQ;AAEhB,gBAAM,iBAAiB;AAEvB,eAAK,mBAAmB;AAAA,YACtB,MAAM;AAAA,YACN,SAAS;AAAA,YACT,UAAU,IAAI;AAAA,YACd,WAAW,IAAI;AAAA;AAIjB,eAAK,kBAAkB,IAAI,UAAU,IAAI,WAAW,cAAc;AAElEA,wBAAA,MAAA,MAAA,OAAA,oCAAY,WAAW,GAAG;AAC1BA,wBAAG,MAAC,YAAW;AAAA,QAChB;AAAA,QACD,MAAM,CAAC,QAAQ;AACbA,wBAAc,MAAA,MAAA,SAAA,oCAAA,SAAS,GAAG;AAC1BA,wBAAG,MAAC,YAAW;AACfA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UACR,CAAC;AAAA,QACH;AAAA,MACF,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,kBAAkB,UAAU,WAAW,SAAS;AAE9C,UAAI,CAAC,KAAK,uBAAuB;AAC/B,aAAK,YAAY;AAAA,UACf;AAAA,UACA;AAAA;AAEF,aAAK,wBAAwB;AAAA,MAC/B;AAGA,WAAK,UAAU;AAAA,QACb;AAAA,UACE,IAAI;AAAA,UACJ;AAAA,UACA;AAAA,UACA,OAAO;AAAA,UACP,QAAQ;AAAA,UACR,SAAS;AAAA,YACP,SAAS,WAAW;AAAA,YACpB,OAAO;AAAA,YACP,UAAU;AAAA,YACV,cAAc;AAAA,YACd,SAAS;AAAA,YACT,SAAS;AAAA,YACT,SAAS;AAAA,UACX;AAAA,QACF;AAAA;IAEH;AAAA;AAAA,IAGD,YAAY,GAAG;AACbA,2EAAY,SAAS,CAAC;AAAA,IACvB;AAAA;AAAA,IAGD,SAAS,GAAG;AACVA,2EAAY,SAAS,CAAC;AAAA,IACvB;AAAA;AAAA,IAGD,eAAe,GAAG;AAChBA,oBAAA,MAAA,MAAA,OAAA,oCAAY,WAAW,CAAC;AAAA,IACzB;AAAA;AAAA,IAGD,gBAAgB;AACd,UAAI,KAAK,cAAc,QAAQ;AAC7B,aAAK,gBAAgB,KAAK,aAAa;AAAA,aAClC;AACL,aAAK,eAAe,CAAC,GAAG,KAAK,mBAAmB;AAAA,MAClD;AAAA,IACD;AAAA;AAAA,IAGD,gBAAgB,OAAO;AAErB,YAAM,cAAc;AAAA,QAClB,EAAE,MAAM,GAAG,KAAK,MAAM,SAAS,SAAS,KAAK,GAAI;AAAA,QACjD,EAAE,MAAM,GAAG,KAAK,MAAM,SAAS,aAAa,KAAK,GAAI;AAAA,QACrD,EAAE,MAAM,GAAG,KAAK,OAAO,SAAS,SAAS,KAAK,MAAO;AAAA,QACrD,EAAE,MAAM,GAAG,KAAK,QAAQ,SAAS,aAAa,KAAK,OAAQ;AAAA,QAC3D,EAAE,MAAM,GAAG,KAAK,MAAM,SAAS,SAAS,KAAK,KAAK;AAAA;AAEpD,WAAK,eAAe;AAAA,IACrB;AAAA;AAAA,IAGD,eAAe,UAAU;AACvB,WAAK,mBAAmB;AAGxB,UAAI,SAAS,YAAY,SAAS,WAAW;AAC3C,aAAK,kBAAkB,SAAS,UAAU,SAAS,WAAW,SAAS,IAAI;AAAA,MAC7E;AAAA,IACD;AAAA;AAAA,IAGD,kBAAkB;AAEhB,YAAM,QAAQ;AACd,YAAM,WAAW,MAAM,MAAM,SAAS,CAAC;AAEvC,UAAI,UAAU;AACZ,iBAAS,IAAI,kBAAkB,KAAK;AAAA,MACtC;AAEAA,oBAAG,MAAC,aAAY;AAAA,IACjB;AAAA;AAAA,IAGD,SAAS;AACPA,oBAAG,MAAC,aAAY;AAAA,IAClB;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACxOA,GAAG,WAAW,eAAe;"}