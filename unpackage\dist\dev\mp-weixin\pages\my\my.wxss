/* 水平间距 */
/* 水平间距 */
/* 全局样式修复 - 确保页面占满屏幕宽度 */
page {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
}

/* 通用容器样式 */
.container, .page-container {
  width: 100%;
  min-height: 100vh;
  box-sizing: border-box;
}

/* 修复小程序页面宽度问题 */
uni-page-body {
  width: 100% !important;
}

/* 确保所有view元素正确继承宽度 */
view {
  box-sizing: border-box;
}
.auth-button {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  /* 完全透明 */
  z-index: 2;
  /* 确保按钮在头像上层 */
}
.avatar {
  width: 100%;
  height: 100%;
}
.user-text {
  margin-top: 7px;
  /* 调整与上个元素的垂直间距 */
}
.avatar-container {
  position: relative;
  z-index: 1;
  /* 头像在按钮下层 */
}
.content {
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
  min-height: 100vh;
  position: relative;
  padding-bottom: 120rpx;
  /* 预留底部TabBar的空间 */
  width: 100%;
  box-sizing: border-box;
  overflow-x: hidden;
}
.top-header {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 90rpx;
  background-color: #4285f4;
  padding: 0 20rpx;
  position: relative;
  box-sizing: border-box;
  flex-shrink: 0;
}
.header-title {
  color: #ffffff;
  font-size: 36rpx;
}
.top-right {
  display: flex;
  align-items: center;
  position: absolute;
  right: 20rpx;
}
.circle-btn {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.3);
  display: flex;
  justify-content: center;
  align-items: center;
  margin-left: 10rpx;
}
.circle-btn.active {
  background-color: rgba(255, 255, 255, 0.3);
}
.circle-inner {
  width: 30rpx;
  height: 30rpx;
  background-color: #fff;
  border-radius: 50%;
}
.dash-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #fff;
  margin-left: 10rpx;
}
.more-dots {
  color: #fff;
  font-size: 36rpx;
  line-height: 20rpx;
}
.user-info {
  background-color: #fff;
  padding: 30rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  box-sizing: border-box;
}
.avatar-container {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  overflow: hidden;
  background-color: #eee;
}
.avatar-placeholder {
  width: 100%;
  height: 100%;
  background-color: #ddd;
  border-radius: 50%;
}
.login-text {
  font-size: 32rpx;
  margin-top: 20rpx;
}
.follow-info {
  font-size: 24rpx;
  color: #999;
  margin-top: 10rpx;
}
.follow-info text {
  margin: 0 20rpx;
}
.scan-btn {
  margin-top: 20rpx;
  background-color: #4285f4;
  color: #fff;
  padding: 8rpx 40rpx;
  border-radius: 30rpx;
  font-size: 26rpx;
}
.statistics {
  display: flex;
  background-color: #fff;
  padding: 20rpx 0;
  border-top: 1rpx solid #eee;
  width: 100%;
  box-sizing: border-box;
}
.stat-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.stat-num {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}
.stat-label {
  font-size: 24rpx;
  color: #666;
  margin-top: 8rpx;
}
.current-store {
  display: flex;
  align-items: center;
  padding: 30rpx;
  background-color: #fff;
  margin-top: 20rpx;
  width: 100%;
  box-sizing: border-box;
}
.store-label {
  font-size: 28rpx;
  color: #666;
}
.store-name {
  font-size: 28rpx;
  margin-left: 10rpx;
  font-weight: bold;
}
.store-arrow {
  font-size: 24rpx;
  margin-left: 10rpx;
  color: #999;
}
.function-menu {
  background-color: #fff;
  padding: 20rpx;
  width: 100%;
  box-sizing: border-box;
}
.menu-row {
  display: flex;
  margin-bottom: 30rpx;
}
.menu-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.menu-item .icon-placeholder {
  width: 60rpx;
  height: 60rpx;
  margin-bottom: 10rpx;
  border-radius: 8rpx;
}
.icon_f {
  width: 60rpx;
  height: 60rpx;
}
.menu-item text {
  font-size: 24rpx;
}
.guide-banner {
  margin-top: 20rpx;
  background-color: #ffb74d;
  padding: 20rpx 30rpx;
  display: flex;
  align-items: center;
  position: relative;
  border-radius: 8rpx;
  margin: 20rpx;
  width: calc(100% - 40rpx);
  box-sizing: border-box;
}
.guide-banner .banner-icon-placeholder {
  width: 80rpx;
  height: 80rpx;
  margin-right: 20rpx;
  border-radius: 8rpx;
}
.guide-banner text {
  font-size: 28rpx;
  color: #333;
  flex: 1;
}
.check-now {
  font-size: 24rpx;
  color: #666;
  position: absolute;
  right: 30rpx;
}
.common-functions {
  margin-top: 20rpx;
  background-color: #fff;
  padding: 20rpx;
  width: 100%;
  box-sizing: border-box;
}
.section-title {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 20rpx;
}
.function-grid {
  display: flex;
  flex-wrap: wrap;
  width: 100%;
  margin-top: 20rpx;
}
.function-item {
  width: 25%;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 30rpx;
}
.function-item .icon-placeholder {
  width: 50rpx;
  height: 50rpx;
  margin-bottom: 10rpx;
  border-radius: 8rpx;
}
.function-item text {
  font-size: 24rpx;
}
.tabbar-placeholder {
  height: 100rpx;
  /* 与TabBar高度一致，确保内容不被遮挡 */
  width: 100%;
  box-sizing: border-box;
}

/* 底部导航自定义样式，确保与系统TabBar相匹配 */
.custom-tabbar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 100rpx;
  background-color: #fff;
  display: flex;
  box-shadow: 0 -2rpx 5rpx rgba(0, 0, 0, 0.1);
  z-index: 999;
}
.tabbar-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 10rpx 0;
}
.tabbar-icon {
  width: 40rpx;
  height: 40rpx;
  margin-bottom: 4rpx;
}
.tabbar-text {
  font-size: 20rpx;
  color: #999;
}
.tabbar-item.active .tabbar-text {
  color: #4285f4;
}
.customer-service {
  position: fixed;
  right: 30rpx;
  bottom: 130rpx;
  /* 调整位置，避免与TabBar重叠 */
  width: 100rpx;
  height: 100rpx;
  background-color: #4285f4;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 998;
}
.service-icon-placeholder {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.service-icon {
  font-size: 40rpx;
  color: #fff;
}

/* 统计图标样式 */
.stats-icon {
  font-size: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 设备尺寸适配 */
@media screen and (max-width: 390px) {
.content,
.top-header,
.user-info,
.statistics,
.current-store,
.function-menu,
.guide-banner,
.common-functions {
    width: 100% !important;
    box-sizing: border-box !important;
    margin-left: 0 !important;
    margin-right: 0 !important;
}
.guide-banner {
    margin: 20rpx 0;
    width: 100% !important;
    border-radius: 0;
}
.menu-item,
.function-item {
    padding: 0 5rpx;
}
.function-item .icon-placeholder {
    width: 45rpx;
    height: 45rpx;
}
}
/* iPhone X 及以上机型底部安全区域适配 */
@supports (padding-bottom: constant(safe-area-inset-bottom)) or (padding-bottom: env(safe-area-inset-bottom)) {
.content {
    padding-bottom: calc(120rpx + constant(safe-area-inset-bottom));
    padding-bottom: calc(120rpx + env(safe-area-inset-bottom));
}
.custom-tabbar,
.uni-tabbar {
    padding-bottom: constant(safe-area-inset-bottom);
    padding-bottom: env(safe-area-inset-bottom);
}
}