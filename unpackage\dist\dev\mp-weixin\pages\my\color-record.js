"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_request = require("../../utils/request.js");
const common_assets = require("../../common/assets.js");
const _sfc_main = {
  data() {
    return {
      records: [
        // {
        //   "id": 2,
        //   "userId": 9,
        //   "colorName": "橙色改棕灰色",
        //   "colorTypeId": 2,
        //   "colorNameId": 6,
        //   "colorComponents": "{\"red\": 6, \"blue\": null, \"gold\": null, \"gray\": null, \"black\": null, \"brown\": null, \"green\": null, \"linen\": null, \"orange\": 3, \"purple\": null, \"silver\": null, \"yellow\": 3, \"custom1\": null, \"custom2\": null, \"custom3\": null, \"custom4\": null, \"bleached\": null, \"faded_bleached\": null}",
        //   "developerComponents": "{\"hydrogenPeroxide3Percent\": 12, \"hydrogenPeroxide6Percent\": 15, \"hydrogenPeroxide9Percent\": 0, \"hydrogenPeroxide12Percent\": 0}",
        //   "beforeImageUrl": null,
        //   "afterImageUrl": null,
        //   "createTime": 1751948416000,
        //   "updateTime": 1751948416000,
        //   "deleted": 0,
        //   "formulaId": 14
        // }
      ],
      showRecipeModal: false,
      selectedRecipe: null,
      searchKeyword: "",
      showFilterModal: false,
      filterType: "",
      loading: false,
      statusOptions: [
        {
          value: "",
          label: "全部状态"
        },
        {
          value: "completed",
          label: "已完成"
        },
        {
          value: "processing",
          label: "进行中"
        },
        {
          value: "failed",
          label: "失败"
        }
      ],
      deviceOptions: [{
        value: "",
        label: "全部设备"
      }]
    };
  },
  onLoad() {
    this.initRecord();
  },
  methods: {
    getFilterOptions() {
      switch (this.filterType) {
        case "color":
          return this.colorOptions;
        case "status":
          return this.statusOptions;
        case "device":
          return this.deviceOptions;
        case "maker":
          return this.getMakerOptions();
        default:
          return [];
      }
    },
    initRecord() {
      utils_request.apiService.record.getRecords().then((res) => {
        this.records = res.data;
      });
    },
    // 格式化日期时间
    formatDateTime(timestamp) {
      if (!timestamp)
        return "";
      const date = new Date(timestamp);
      return date.toLocaleString();
    },
    // 根据key获取颜色值
    getColorHexByKey(key) {
      const colorMap = {
        1: "#FF0000",
        // 红色
        2: "#FFA500",
        // 橙色
        3: "#FFFF00",
        // 黄色
        4: "#008000",
        // 绿色
        5: "#0000FF",
        // 蓝色
        6: "#800080",
        // 紫色
        7: "#A52A2A",
        // 棕色
        8: "#000000",
        // 黑色
        9: "#808080",
        // 灰色
        10: "#FFD700",
        // 金色
        11: "#C0C0C0",
        // 银色
        12: "#FAF0E6"
        // 亚麻色
      };
      return colorMap[key] || "#CCCCCC";
    },
    // 解析颜色构造配比数据
    parseColorComponents(colorComponents) {
      if (!colorComponents)
        return [];
      try {
        const components = JSON.parse(colorComponents);
        const result = [];
        for (const [key, value] of Object.entries(components)) {
          if (value !== null && value !== 0) {
            result.push({
              name: this.getColorDisplayNameByKey(key),
              amount: value,
              color: this.getColorHexByKey(key)
            });
          }
        }
        return result;
      } catch (e) {
        common_vendor.index.__f__("warn", "at pages/my/color-record.vue:278", "解析配比数据失败:", e);
        return [];
      }
    },
    // 解析双氧水配比数据
    parseDeveloperComponents(developerComponents) {
      if (!developerComponents)
        return [];
      try {
        const components = JSON.parse(developerComponents);
        const result = [];
        const nameMap = {
          "hydrogenPeroxide3Percent": "3%双氧水",
          "hydrogenPeroxide6Percent": "6%双氧水",
          "hydrogenPeroxide9Percent": "9%双氧水",
          "hydrogenPeroxide12Percent": "12%双氧水"
        };
        for (const [key, value] of Object.entries(components)) {
          if (value !== null && value !== 0) {
            result.push({
              name: nameMap[key] || key,
              amount: value
            });
          }
        }
        return result;
      } catch (e) {
        common_vendor.index.__f__("warn", "at pages/my/color-record.vue:309", "解析双氧水数据失败:", e);
        return [];
      }
    },
    // 根据key获取颜色名称
    getColorDisplayNameByKey(key) {
      const nameMap = {
        "red": "红色",
        "blue": "蓝色",
        "gold": "金色",
        "gray": "灰色",
        "black": "黑色",
        "brown": "棕色",
        "green": "绿色",
        "linen": "亚麻色",
        "orange": "橙色",
        "purple": "紫色",
        "silver": "银色",
        "yellow": "黄色",
        "custom1": "自定义1",
        "custom2": "自定义2",
        "custom3": "自定义3",
        "custom4": "自定义4",
        "bleached": "漂白",
        "faded_bleached": "褪色漂白"
      };
      return nameMap[key] || key;
    },
    // 查看配方详情
    viewRecipeDetails(record) {
      this.selectedRecipe = record;
      this.showRecipeModal = true;
    },
    // 关闭配方详情
    closeRecipeModal() {
      this.showRecipeModal = false;
    },
    // 使用配方
    useRecipe() {
      common_vendor.index.showToast({
        title: "配方已应用",
        icon: "success"
      });
      this.closeRecipeModal();
    },
    // 分享记录
    shareRecord(record) {
      try {
        common_vendor.index.setStorageSync("shareRecordData", {
          ...record,
          // 添加一些额外的展示数据
          timestamp: Date.now()
        });
        common_vendor.index.navigateTo({
          url: "/pages/my/actual-effect",
          success: () => {
            common_vendor.index.__f__("log", "at pages/my/color-record.vue:373", "跳转到实际效果页面成功");
          },
          fail: (err) => {
            common_vendor.index.__f__("error", "at pages/my/color-record.vue:376", "跳转失败:", err);
            common_vendor.index.showToast({
              title: "跳转失败",
              icon: "none"
            });
          }
        });
      } catch (e) {
        common_vendor.index.__f__("error", "at pages/my/color-record.vue:384", "保存数据失败:", e);
        common_vendor.index.showToast({
          title: "数据保存失败",
          icon: "none"
        });
      }
    },
    // 搜索输入事件
    onSearchInput() {
      this.searchRecords();
    },
    // 搜索记录
    searchRecords() {
      if (!this.searchKeyword) {
        return;
      }
      const keyword = this.searchKeyword.toLowerCase();
      this.records = this.records.filter(
        (record) => record.colorName.toLowerCase().includes(keyword) || record.userId && record.userId.toString().includes(keyword)
      );
    },
    // 筛选相关方法
    showColorFilter() {
      this.filterType = "color";
      this.showFilterModal = true;
    },
    showStatusFilter() {
      this.filterType = "status";
      this.showFilterModal = true;
    },
    showTimeFilter() {
      this.showDatePicker = true;
      this.$nextTick(() => {
        const picker = this.$refs.datePicker;
        if (picker) {
          picker.show();
        }
      });
    },
    showDeviceFilter() {
      this.filterType = "device";
      this.showFilterModal = true;
    },
    showMakerFilter() {
      this.filterType = "maker";
      this.showFilterModal = true;
    },
    // 选择筛选项
    selectFilterOption(value) {
      if (this.filterType === "color") {
        this.filterOptions.colorCode = value;
      } else if (this.filterType === "status") {
        this.filterOptions.status = value;
      } else if (this.filterType === "device") {
        this.filterOptions.deviceCode = value;
      } else if (this.filterType === "maker") {
        this.filterOptions.author = value;
      }
      this.showFilterModal = false;
      if (this.filterType === "time") {
        this.loadColorRecords();
      } else {
        this.applyFilters();
      }
    },
    // 日期选择
    onDateChange(e) {
      this.selectedDate = e.detail.value;
      this.filterOptions.timeRange = e.detail.value;
      this.showDatePicker = false;
      this.loadColorRecords();
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.o([($event) => $data.searchKeyword = $event.detail.value, (...args) => $options.onSearchInput && $options.onSearchInput(...args)]),
    b: common_vendor.o((...args) => $options.searchRecords && $options.searchRecords(...args)),
    c: $data.searchKeyword,
    d: common_vendor.o((...args) => $options.searchRecords && $options.searchRecords(...args)),
    e: common_vendor.o((...args) => $options.showColorFilter && $options.showColorFilter(...args)),
    f: common_vendor.o((...args) => $options.showStatusFilter && $options.showStatusFilter(...args)),
    g: common_vendor.o((...args) => $options.showTimeFilter && $options.showTimeFilter(...args)),
    h: common_vendor.o((...args) => $options.showDeviceFilter && $options.showDeviceFilter(...args)),
    i: common_vendor.o((...args) => $options.showMakerFilter && $options.showMakerFilter(...args)),
    j: $data.records.length > 0
  }, $data.records.length > 0 ? {
    k: common_vendor.f($data.records, (record, index, i0) => {
      return {
        a: record.beforeImageUrl,
        b: common_vendor.t(record.colorName || "未命名配方"),
        c: common_vendor.t($options.formatDateTime(record.createTime)),
        d: common_vendor.f($options.parseColorComponents(record.colorComponents), (comp, i, i1) => {
          return {
            a: common_vendor.t(comp.name),
            b: common_vendor.t(comp.amount),
            c: i
          };
        }),
        e: common_vendor.o(($event) => $options.shareRecord(record), index),
        f: index,
        g: common_vendor.o(($event) => $options.viewRecipeDetails(record), index)
      };
    })
  } : {}, {
    l: $data.showFilterModal
  }, $data.showFilterModal ? {
    m: common_vendor.o(($event) => $data.showFilterModal = false),
    n: common_vendor.f($options.getFilterOptions(), (option, k0, i0) => {
      return {
        a: common_vendor.t(option.label),
        b: option.value,
        c: common_vendor.o(($event) => $options.selectFilterOption(option.value), option.value)
      };
    }),
    o: common_vendor.o(() => {
    }),
    p: common_vendor.o(($event) => $data.showFilterModal = false)
  } : {}, {
    q: _ctx.showDatePicker
  }, _ctx.showDatePicker ? {
    r: _ctx.selectedDate,
    s: common_vendor.o((...args) => $options.onDateChange && $options.onDateChange(...args)),
    t: common_vendor.o(($event) => _ctx.showDatePicker = false)
  } : {}, {
    v: $data.records.length === 0
  }, $data.records.length === 0 ? {
    w: common_assets._imports_0$7
  } : {}, {
    x: $data.showRecipeModal
  }, $data.showRecipeModal ? common_vendor.e({
    y: common_vendor.o((...args) => $options.closeRecipeModal && $options.closeRecipeModal(...args)),
    z: $data.selectedRecipe.beforeImageUrl,
    A: common_vendor.t($data.selectedRecipe.colorName || "未命名配方"),
    B: $data.selectedRecipe.colorComponents
  }, $data.selectedRecipe.colorComponents ? {
    C: common_vendor.f($options.parseColorComponents($data.selectedRecipe.colorComponents), (component, idx, i0) => {
      return {
        a: component.color,
        b: common_vendor.t(component.name),
        c: common_vendor.t(component.amount),
        d: idx
      };
    })
  } : {}, {
    D: $data.selectedRecipe.developerComponents
  }, $data.selectedRecipe.developerComponents ? {
    E: common_vendor.f($options.parseDeveloperComponents($data.selectedRecipe.developerComponents), (dev, idx, i0) => {
      return {
        a: common_vendor.t(dev.name),
        b: common_vendor.t(dev.amount),
        c: "dev" + idx
      };
    })
  } : {}, {
    F: common_vendor.t($data.selectedRecipe.formulaId),
    G: common_vendor.t($data.selectedRecipe.userId),
    H: common_vendor.t($options.formatDateTime($data.selectedRecipe.createTime)),
    I: common_vendor.o((...args) => $options.useRecipe && $options.useRecipe(...args)),
    J: common_vendor.o((...args) => $options.closeRecipeModal && $options.closeRecipeModal(...args))
  }) : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/my/color-record.js.map
