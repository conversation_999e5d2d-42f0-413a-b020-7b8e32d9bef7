"use strict";
const common_vendor = require("../../../common/vendor.js");
const utils_request = require("../../../utils/request.js");
const _sfc_main = {
  data() {
    return {
      storeId: null,
      isFollowed: false,
      loading: false,
      followLoading: false,
      storeInfo: {
        id: null,
        name: "",
        logo: "",
        description: "",
        productCount: 0,
        followerCount: 0,
        rating: {
          products: 0,
          service: 0,
          logistics: 0
        }
      },
      storeProducts: []
    };
  },
  onLoad(options) {
    if (options.id) {
      this.storeId = options.id;
      this.loadStoreInfo();
      this.loadStoreProducts();
      this.checkFollowStatus();
    }
  },
  onReady() {
    common_vendor.index.setNavigationBarColor({
      frontColor: "#ffffff",
      backgroundColor: "#c62828"
    });
    common_vendor.index.setNavigationBarTitle({
      title: ""
    });
  },
  methods: {
    async loadStoreInfo() {
      var _a, _b, _c;
      if (!this.storeId)
        return;
      this.loading = true;
      try {
        const response = await utils_request.apiService.mall.stores.detail(this.storeId);
        if (response.code === 200) {
          const storeData = response.data;
          if (!storeData) {
            common_vendor.index.__f__("error", "at pages/mall/store/store.vue:134", "店铺数据为空");
            common_vendor.index.showToast({
              title: "店铺不存在",
              icon: "none"
            });
            return;
          }
          this.storeInfo = {
            id: storeData.id || null,
            name: storeData.name || storeData.storeName || "",
            logo: storeData.logo || "/static/images/shop-logo.jpg",
            description: storeData.description || "",
            productCount: storeData.productCount || 0,
            followerCount: storeData.followerCount || 0,
            rating: {
              products: ((_a = storeData.rating) == null ? void 0 : _a.products) || 0,
              service: ((_b = storeData.rating) == null ? void 0 : _b.service) || 0,
              logistics: ((_c = storeData.rating) == null ? void 0 : _c.logistics) || 0
            }
          };
          common_vendor.index.__f__("log", "at pages/mall/store/store.vue:156", "店铺信息加载成功:", this.storeInfo);
        } else {
          common_vendor.index.__f__("error", "at pages/mall/store/store.vue:158", "获取店铺信息失败:", response.message);
          if (response.message && response.message.includes("店铺不存在")) {
            common_vendor.index.showModal({
              title: "提示",
              content: "该店铺不存在或已关闭，即将返回上一页",
              showCancel: false,
              success: () => {
                setTimeout(() => {
                  common_vendor.index.navigateBack({
                    fail: () => {
                      common_vendor.index.switchTab({
                        url: "/pages/index/index"
                      });
                    }
                  });
                }, 1500);
              }
            });
          } else {
            common_vendor.index.showToast({
              title: response.message || "获取店铺信息失败",
              icon: "none"
            });
          }
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/mall/store/store.vue:187", "获取店铺信息异常:", error);
        if (error.message && error.message.includes("店铺不存在")) {
          common_vendor.index.showModal({
            title: "提示",
            content: "该店铺不存在或已关闭，即将返回上一页",
            showCancel: false,
            success: () => {
              setTimeout(() => {
                common_vendor.index.navigateBack({
                  fail: () => {
                    common_vendor.index.switchTab({
                      url: "/pages/index/index"
                    });
                  }
                });
              }, 1500);
            }
          });
        } else {
          common_vendor.index.showToast({
            title: "网络异常，请稍后重试",
            icon: "none"
          });
        }
      } finally {
        this.loading = false;
      }
    },
    async loadStoreProducts() {
      if (!this.storeId)
        return;
      if (!this.loading) {
        this.loading = true;
      }
      try {
        const response = await utils_request.apiService.mall.products.list({
          storeId: this.storeId,
          page: 1,
          pageSize: 10
        });
        if (response.code === 200) {
          const products = response.data.products || response.data.records || response.data || [];
          this.storeProducts = products.map((product) => ({
            id: product.id,
            name: product.productName || product.name,
            price: product.price,
            soldCount: product.soldNum || product.sales || 0,
            image: this.getProductImage(product)
          }));
          if (this.storeInfo) {
            this.storeInfo.productCount = products.length;
          }
          common_vendor.index.__f__("log", "at pages/mall/store/store.vue:250", "店铺商品加载成功:", this.storeProducts);
        } else {
          common_vendor.index.__f__("error", "at pages/mall/store/store.vue:252", "获取店铺商品失败:", response.message);
          common_vendor.index.showToast({
            title: "获取商品失败",
            icon: "none"
          });
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/mall/store/store.vue:259", "获取店铺商品异常:", error);
        common_vendor.index.showToast({
          title: "网络异常",
          icon: "none"
        });
      } finally {
        this.loading = false;
      }
    },
    getProductImage(product) {
      if (!product.images)
        return "/static/images/product1.jpg";
      let imageUrl = "";
      if (typeof product.images === "string") {
        const imageArray = product.images.split(",");
        imageUrl = imageArray[0] || "/static/images/product1.jpg";
      } else {
        imageUrl = product.images[0] || "/static/images/product1.jpg";
      }
      return this.getFullImageUrl(imageUrl);
    },
    // 获取完整的图片URL
    getFullImageUrl(url) {
      if (!url)
        return "";
      if (url.startsWith("http")) {
        return url;
      }
      if (url.startsWith("/static") || url.startsWith("/uploads")) {
        return "https://www.narenqiqige.com" + url;
      }
      if (!url.startsWith("/")) {
        return "https://www.narenqiqige.com/api/uploads/" + url;
      }
      return url;
    },
    async checkFollowStatus() {
      if (!this.storeId)
        return;
      try {
        const response = await utils_request.apiService.mall.storeFollow.check(this.storeId);
        if (response.code === 200) {
          this.isFollowed = response.data;
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/mall/store/store.vue:307", "检查关注状态失败:", error);
      }
    },
    async followStore() {
      if (this.followLoading)
        return;
      this.followLoading = true;
      try {
        let response;
        if (this.isFollowed) {
          response = await utils_request.apiService.mall.storeFollow.unfollow(this.storeId);
        } else {
          response = await utils_request.apiService.mall.storeFollow.follow(this.storeId);
        }
        if (response.code === 200) {
          this.isFollowed = !this.isFollowed;
          if (this.isFollowed) {
            this.storeInfo.followerCount++;
          } else {
            this.storeInfo.followerCount--;
          }
          common_vendor.index.showToast({
            title: response.message || (this.isFollowed ? "关注成功" : "取消关注成功"),
            icon: "success"
          });
        } else {
          common_vendor.index.showToast({
            title: response.message || "操作失败",
            icon: "none"
          });
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/mall/store/store.vue:345", "关注操作失败:", error);
        common_vendor.index.showToast({
          title: "网络异常，请稍后重试",
          icon: "none"
        });
      } finally {
        this.followLoading = false;
      }
    },
    goToProduct(productId) {
      common_vendor.index.navigateTo({
        url: "/pages/mall/product/detail?id=" + productId
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.t($data.storeInfo.name),
    b: common_vendor.t(($data.storeInfo.rating.products || 4.1).toFixed(1)),
    c: common_vendor.t($data.storeInfo.description || "Official flagship store"),
    d: !$data.followLoading
  }, !$data.followLoading ? {
    e: common_vendor.t($data.isFollowed ? "已关注" : "+ 关注")
  } : {}, {
    f: $data.isFollowed ? 1 : "",
    g: $data.followLoading ? 1 : "",
    h: common_vendor.o((...args) => $options.followStore && $options.followStore(...args)),
    i: $data.loading
  }, $data.loading ? {} : $data.storeProducts.length > 0 ? {
    k: common_vendor.f($data.storeProducts, (product, index, i0) => {
      return {
        a: product.image,
        b: common_vendor.t(product.name),
        c: common_vendor.t(product.price),
        d: common_vendor.t(product.soldCount),
        e: index,
        f: common_vendor.o(($event) => $options.goToProduct(product.id), index)
      };
    })
  } : {}, {
    j: $data.storeProducts.length > 0
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/pages/mall/store/store.js.map
