"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_request = require("../../utils/request.js");
const utils_time_util = require("../../utils/time_util.js");
const store_wifi = require("../../store/wifi.js");
const common_assets = require("../../common/assets.js");
const _sfc_main = {
  data() {
    return {
      deviceStatus: false,
      connectionMethod: "",
      currentUseDevice: "",
      // 当前正在使用的设备
      isConnecting: false,
      // 是否正在连接中
      lastDisconnectTime: 0,
      // 最后断开连接的时间
      debounceDelay: 2e3,
      // 防抖延迟时间（毫秒）
      tabs: [
        {
          key: "offline",
          label: "离线",
          count: 1
        },
        {
          key: "online",
          label: "在线",
          count: 4
        },
        {
          key: "abnormal",
          label: "异常",
          count: 1
        }
      ],
      activeTab: "online",
      showBack: false,
      scrollLeft: 0,
      indicatorStyle: {
        width: "60px",
        transform: "translateX(0px)"
      },
      refreshing: false,
      loadingMore: false,
      hasMore: true,
      pageSize: 10,
      currentPage: 1,
      devices: [
        // {
        //   id: 'IOT-KT-001',
        //   name: '客厅空调',
        //   status: 'online',
        //   lastActive: new Date().toISOString(),
        //   type: 'air_conditioner',
        //   // brand: '美的',
        //   // model: 'KFR-35GW',
        //   // temperature: 26,
        //   // mode: 'cool',
        //   // powerConsumption: '1.2kW/h'
        // },
        // {
        //   id: 'IOT-SD-002',
        //   name: '电动窗帘',
        //   status: 'online',
        //   lastActive: new Date(Date.now() - 3600000).toISOString(),
        //   type: 'curtain',
        //   brand: '小米',
        //   model: 'MJZNCL01LM',
        //   position: '80%',
        //   battery: 65
        // },
        // {
        //   id: 'IOT-LT-003',
        //   name: '餐厅吊灯',
        //   status: 'offline',
        //   lastActive: new Date(Date.now() - 86400000).toISOString(),
        //   type: 'light',
        //   brand: '飞利浦',
        //   model: 'Hue White',
        //   brightness: 0,
        //   colorTemp: 4000
        // },
        // {
        //   id: 'IOT-SF-004',
        //   name: '智能插座',
        //   status: 'abnormal',
        //   lastActive: new Date(Date.now() - 1800000).toISOString(),
        //   type: 'outlet',
        //   brand: '涂鸦',
        //   model: 'SR01',
        //   power: '220V',
        //   current: '0A',
        //   alert: '电流异常'
        // },
        // {
        //   id: 'IOT-CM-005',
        //   name: '门口摄像头',
        //   status: 'online',
        //   lastActive: new Date().toISOString(),
        //   type: 'camera',
        //   brand: '萤石',
        //   model: 'C6CN',
        //   resolution: '1080p',
        //   isRecording: true
        // },
        // {
        //   id: 'IOT-TH-006',
        //   name: '温湿度计',
        //   status: 'online',
        //   lastActive: new Date().toISOString(),
        //   type: 'sensor',
        //   brand: '米家',
        //   model: 'LYWSD03MMC',
        //   temperature: 24.5,
        //   humidity: 56,
        //   battery: 85
        // }
      ],
      offlineCount: 0,
      onlineCount: 0,
      abnormalCount: 0
      // 监听一下设备的连接状态等信息
    };
  },
  computed: {
    filteredDevices() {
      return this.devices.filter((device) => device.status === this.activeTab);
    }
  },
  onLoad(options) {
    this.statusWatcher = setInterval(() => {
      const app = getApp();
      const deviceCode = app.globalData.connectedDeviceId;
      const deviceStatus = app.globalData.deviceStatus;
      const connectionMethod = app.globalData.connectionMethod;
      if (deviceCode !== this.currentUseDevice || deviceStatus != this.deviceStatus || connectionMethod != this.connectionMethod) {
        this.currentUseDevice = deviceCode;
        this.deviceStatus = deviceStatus;
        this.connectionMethod = connectionMethod;
        this.$forceUpdate();
      }
    }, 500);
    utils_request.apiService.device.getDevicePermissionlist().then((res) => {
      this.devices = res.data;
    });
    this.calculateCounts();
    this.loadDevices();
    this.showBack = !!options.from;
  },
  onShow() {
    this.syncDeviceStatus();
  },
  onReady() {
    this.updateIndicatorPosition();
  },
  onUnload() {
    if (this.statusWatcher) {
      clearInterval(this.statusWatcher);
      this.statusWatcher = null;
    }
  },
  // 下拉刷新
  onPullDownRefresh() {
    common_vendor.index.__f__("log", "at pages/my/device-list.vue:262", "下拉刷新");
    this.loadDevice();
  },
  methods: {
    async unbind(device) {
      common_vendor.index.__f__("log", "at pages/my/device-list.vue:270", "需要解绑的设备", device);
      const confirmResult = await new Promise((resolve) => {
        common_vendor.index.showModal({
          title: "确认解除绑定码",
          content: `确定要解除绑定设备 ${device.name} 吗？`,
          success: (res) => {
            resolve(res.confirm);
          }
        });
      });
      if (!confirmResult) {
        return;
      }
      const response = await utils_request.apiService.device.unbindDevice(device.id);
      if (response.code >= 200 && response.code <= 300) {
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({
          title: "设备已解除绑定",
          icon: "success",
          duration: 2e3
        });
        this.loadDevice();
      } else {
        common_vendor.index.showToast({
          title: "设备解除绑定失败,请重试",
          icon: "none",
          // 注意：uni.showToast 没有 'fail' 图标，应该用 'none'
          duration: 2e3
        });
      }
    },
    async disconnectDevice(device) {
      common_vendor.index.__f__("log", "at pages/my/device-list.vue:311", "需要断开的设备", device);
      try {
        const confirmResult = await new Promise((resolve) => {
          common_vendor.index.showModal({
            title: "确认断开",
            content: `确定要断开设备 ${device.name} 吗？`,
            success: (res) => {
              resolve(res.confirm);
            }
          });
        });
        if (!confirmResult) {
          return;
        }
        common_vendor.index.showLoading({
          title: "断开中...",
          mask: true
        });
        const response = await utils_request.post(`/api/device/monitor/disconnect/${device.id}`);
        store_wifi.stopPollingWifi();
        store_wifi.disconnectSSE();
        const app = getApp();
        app.globalData.connectedDeviceId = "";
        app.globalData.deviceStatus = false;
        app.globalData.connectionMethod = "";
        app.globalData.prepareToConnectDeviceCode = "";
        if (app.stopDeviceServices) {
          app.stopDeviceServices();
        }
        this.currentUseDevice = "";
        this.deviceStatus = false;
        this.connectionMethod = "";
        this.lastDisconnectTime = Date.now();
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({
          title: "设备已断开",
          icon: "success",
          duration: 2e3
        });
        common_vendor.index.__f__("log", "at pages/my/device-list.vue:372", "🔌 设备断开成功:", device.id);
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/my/device-list.vue:375", "❌ 断开设备失败:", error);
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({
          title: "断开失败",
          icon: "error",
          duration: 2e3
        });
      }
    },
    getDeviceIcon(type) {
      const icons = {
        "air_conditioner": "/static/icons/air-conditioner.png",
        "curtain": "/static/icons/curtain.png",
        "light": "/static/icons/light.png",
        "outlet": "/static/icons/outlet.png",
        "camera": "/static/icons/camera.png",
        "sensor": "/static/icons/sensor.png"
      };
      return icons[type] || "/static/icons/device.png";
    },
    async useDevice(device) {
      const app = getApp();
      try {
        if (app.isBluetoothConnection() && app.globalData.deviceStatus) {
          common_vendor.index.showToast({
            title: "设备已通过蓝牙连接,请先断开蓝牙连接",
            icon: "none",
            duration: 2e3
          });
          return;
        }
        if (this.isConnecting) {
          common_vendor.index.__f__("log", "at pages/my/device-list.vue:409", "正在连接中，请勿重复点击");
          return;
        }
        const timeSinceDisconnect = Date.now() - this.lastDisconnectTime;
        if (this.lastDisconnectTime > 0 && timeSinceDisconnect < this.debounceDelay) {
          const remainingTime = Math.ceil((this.debounceDelay - timeSinceDisconnect) / 1e3);
          common_vendor.index.showToast({
            title: `请等待 ${remainingTime} 秒后再试`,
            icon: "none",
            duration: 2e3
          });
          common_vendor.index.__f__("log", "at pages/my/device-list.vue:422", `防抖检查：距离上次断开连接仅 ${timeSinceDisconnect}ms，需要等待 ${remainingTime} 秒`);
          return;
        }
        this.isConnecting = true;
        common_vendor.index.showLoading({
          title: "连接中...",
          mask: true
        });
        app.globalData.connectedDeviceId = device.id;
        app.globalData.prepareToConnectDeviceCode = device.id;
        app.globalData.connectionMethod = "wifi";
        if (app.startDeviceServices) {
          app.startDeviceServices(device.id);
        }
        this.currentUseDevice = device.id;
        this.connectionMethod = "wifi";
        store_wifi.startPollingWifi(device.id);
        let connectionEstablished = false;
        const maxWaitTime = 15e3;
        const checkInterval = 500;
        let waitTime = 0;
        const checkConnection = () => {
          waitTime += checkInterval;
          let isDeviceOnline = false;
          let isDeviceOffline = false;
          if (app.globalData.connectionMethod === "wifi") {
            const deviceStatusDetail = app.globalData.deviceStatusDetail;
            const globalDeviceStatus = app.globalData.deviceStatus;
            common_vendor.index.__f__("log", "at pages/my/device-list.vue:471", "WiFi连接状态检查:", {
              deviceStatusDetail,
              globalDeviceStatus,
              isOnline: deviceStatusDetail == null ? void 0 : deviceStatusDetail.isOnline
            });
            isDeviceOnline = (deviceStatusDetail == null ? void 0 : deviceStatusDetail.isOnline) === true || globalDeviceStatus === true;
            isDeviceOffline = globalDeviceStatus === false && (deviceStatusDetail == null ? void 0 : deviceStatusDetail.isOnline) === false;
          } else {
            isDeviceOnline = app.globalData.deviceStatus === true;
            isDeviceOffline = app.globalData.deviceStatus === false;
          }
          if (isDeviceOnline) {
            connectionEstablished = true;
            this.deviceStatus = true;
            this.isConnecting = false;
            common_vendor.index.hideLoading();
            common_vendor.index.showToast({
              title: "连接成功",
              icon: "success",
              duration: 2e3
            });
            common_vendor.index.__f__("log", "at pages/my/device-list.vue:501", "🔗 设备连接成功:", device.id);
          } else if (isDeviceOffline) {
            this.isConnecting = false;
            common_vendor.index.hideLoading();
            common_vendor.index.showToast({
              title: "设备离线，请检查设备状态",
              icon: "none",
              duration: 3e3
            });
            common_vendor.index.__f__("warn", "at pages/my/device-list.vue:512", "设备离线:", device.id);
            this.resetConnectionState();
          } else if (waitTime >= maxWaitTime) {
            this.isConnecting = false;
            common_vendor.index.hideLoading();
            common_vendor.index.showToast({
              title: "连接超时，请检查设备状态",
              icon: "none",
              duration: 3e3
            });
            common_vendor.index.__f__("warn", "at pages/my/device-list.vue:526", "连接超时:", device.id);
            this.resetConnectionState();
          } else {
            setTimeout(checkConnection, checkInterval);
          }
        };
        setTimeout(checkConnection, 1e3);
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/my/device-list.vue:541", "❌ 连接设备失败:", error);
        this.isConnecting = false;
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({
          title: "连接失败",
          icon: "error",
          duration: 2e3
        });
        this.resetConnectionState();
      }
    },
    // 重置连接状态的方法
    resetConnectionState() {
      this.currentUseDevice = "";
      this.deviceStatus = false;
      this.connectionMethod = "";
      this.isConnecting = false;
      const app = getApp();
      app.globalData.deviceStatus = false;
      app.globalData.connectedDeviceId = "";
      app.globalData.connectionMethod = "";
    },
    // 同步设备状态的方法
    syncDeviceStatus() {
      const app = getApp();
      const deviceCode = app.globalData.connectedDeviceId;
      const deviceStatus = app.globalData.deviceStatus;
      const connectionMethod = app.globalData.connectionMethod;
      common_vendor.index.__f__("log", "at pages/my/device-list.vue:575", "同步设备状态:", {
        deviceCode,
        deviceStatus,
        connectionMethod,
        currentLocal: {
          currentUseDevice: this.currentUseDevice,
          deviceStatus: this.deviceStatus,
          connectionMethod: this.connectionMethod
        }
      });
      if (deviceCode !== this.currentUseDevice || deviceStatus !== this.deviceStatus || connectionMethod !== this.connectionMethod) {
        this.currentUseDevice = deviceCode;
        this.deviceStatus = deviceStatus;
        this.connectionMethod = connectionMethod;
        this.$forceUpdate();
        common_vendor.index.__f__("log", "at pages/my/device-list.vue:596", "设备状态已同步更新");
      }
    },
    formatTime: utils_time_util.formatTime,
    goBack() {
      common_vendor.index.navigateBack();
    },
    loadDevice() {
      utils_request.apiService.device.getDevicePermissionlist().then((res) => {
        this.devices = res.data;
        this.calculateCounts();
      });
      this.calculateCounts();
      this.loadDevices();
    },
    setActiveTab(tab) {
      this.activeTab = tab;
      this.currentPage = 1;
      this.hasMore = true;
      this.$nextTick(() => {
        this.updateIndicatorPosition();
      });
    },
    updateIndicatorPosition() {
      const query = common_vendor.index.createSelectorQuery().in(this);
      query.select(`#tab-${this.activeTab}`).boundingClientRect();
      query.select(".tab-container").boundingClientRect();
      query.exec((res) => {
        if (res[0] && res[1]) {
          const tab = res[0];
          const container = res[1];
          const left = tab.left - container.left;
          this.indicatorStyle = {
            width: `${tab.width}px`,
            transform: `translateX(${left}px)`,
            transition: "transform 0.3s ease"
          };
          const scrollLeft = left - (container.width - tab.width) / 2;
          this.scrollLeft = Math.max(0, scrollLeft);
        }
      });
    },
    async loadDevices() {
      try {
        this.refreshing = true;
        await new Promise((resolve) => setTimeout(resolve, 800));
        this.calculateCounts();
      } catch (e) {
        common_vendor.index.showToast({
          title: "加载失败",
          icon: "none"
        });
      } finally {
        this.refreshing = false;
        this.loadingMore = false;
        common_vendor.index.stopPullDownRefresh();
      }
    },
    onRefresh() {
      this.loadDevice();
      this.refreshing = true;
      this.currentPage = 1;
      this.hasMore = true;
      this.loadDevices();
    },
    loadMore() {
      if (this.loadingMore || !this.hasMore)
        return;
      this.loadingMore = true;
      this.currentPage += 1;
      this.loadDevices();
    },
    calculateCounts() {
      this.offlineCount = this.devices.filter((device) => device.status === "offline").length;
      this.onlineCount = this.devices.filter((device) => device.status === "online").length;
      this.abnormalCount = this.devices.filter((device) => device.status === "abnormal").length;
    },
    getStatusText(status) {
      const statusMap = {
        "offline": "离线",
        "online": "在线",
        "abnormal": "异常"
      };
      return statusMap[status] || status;
    },
    addDevice() {
      common_vendor.index.navigateTo({
        url: "/pages/device/add"
      });
    },
    // viewDeviceDetail(device) {
    // 	uni.navigateTo({
    // 		url: `/pages/device/detail?id=${device.id}`
    // 	});
    // },
    updateIndicatorPosition() {
      const query = common_vendor.index.createSelectorQuery().in(this);
      query.select(`#tab-${this.activeTab}`).boundingClientRect();
      query.select(".tab-container").boundingClientRect();
      query.exec((res) => {
        if (res[0] && res[1]) {
          const tab = res[0];
          const container = res[1];
          const left = tab.left - container.left;
          this.indicatorStyle = {
            width: `${tab.width}px`,
            transform: `translateX(${left}px)`,
            transition: "transform 0.3s ease"
          };
          const scrollLeft = left - (container.width - tab.width) / 2;
          this.scrollLeft = Math.max(0, scrollLeft);
        }
      });
    },
    setActiveTab(tab) {
      this.activeTab = tab;
      this.$nextTick(() => {
        this.updateIndicatorPosition();
      });
    }
  }
};
if (!Array) {
  const _easycom_uni_icons2 = common_vendor.resolveComponent("uni-icons");
  _easycom_uni_icons2();
}
const _easycom_uni_icons = () => "../../uni_modules/uni-icons/components/uni-icons/uni-icons.js";
if (!Math) {
  _easycom_uni_icons();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.f($data.tabs, (tab, k0, i0) => {
      return {
        a: common_vendor.t(tab.label),
        b: tab.key,
        c: $data.activeTab === tab.key ? 1 : "",
        d: common_vendor.o(($event) => $options.setActiveTab(tab.key), tab.key),
        e: "tab-" + tab.key
      };
    }),
    b: $data.scrollLeft,
    c: common_vendor.s($data.indicatorStyle),
    d: $options.filteredDevices.length === 0
  }, $options.filteredDevices.length === 0 ? {
    e: common_assets._imports_0$7
  } : {}, {
    f: common_vendor.f($options.filteredDevices, (device, index, i0) => {
      return common_vendor.e({
        a: $options.getDeviceIcon(device.type),
        b: common_vendor.t(device.name),
        c: common_vendor.t($options.getStatusText(device.status)),
        d: common_vendor.n(device.status),
        e: common_vendor.t(device.id),
        f: device.lastActive
      }, device.lastActive ? {
        g: "defba7c2-0-" + i0,
        h: common_vendor.p({
          type: "calendar",
          size: "14",
          color: "#999"
        }),
        i: common_vendor.t($options.formatTime(device.lastActive))
      } : {}, {
        j: device.status === "online" && device.permission >= 2
      }, device.status === "online" && device.permission >= 2 ? common_vendor.e({
        k: device.id === $data.currentUseDevice && $data.deviceStatus && $data.connectionMethod === "wifi"
      }, device.id === $data.currentUseDevice && $data.deviceStatus && $data.connectionMethod === "wifi" ? {
        l: common_vendor.o(($event) => $options.disconnectDevice(device), device.id)
      } : {
        m: common_vendor.o(($event) => $options.useDevice(device), device.id)
      }, {
        n: common_vendor.o(() => {
        }, device.id)
      }) : {}, {
        o: device.status === "offline" && device.permission > 0
      }, device.status === "offline" && device.permission > 0 ? {
        p: common_vendor.o(($event) => $options.unbind(device), device.id),
        q: common_vendor.o(() => {
        }, device.id)
      } : {}, {
        r: device.id
      });
    }),
    g: $data.loadingMore
  }, $data.loadingMore ? {
    h: common_vendor.p({
      type: "spinner-cycle",
      size: "20",
      color: "#999"
    })
  } : {}, {
    i: !$data.hasMore && $options.filteredDevices.length > 5
  }, !$data.hasMore && $options.filteredDevices.length > 5 ? {} : {}, {
    j: $data.refreshing,
    k: common_vendor.o((...args) => $options.onRefresh && $options.onRefresh(...args)),
    l: common_vendor.o((...args) => $options.loadMore && $options.loadMore(...args))
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/my/device-list.js.map
