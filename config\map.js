/**
 * 地图服务配置
 * 请在这里配置您的地图API密钥
 */

export const mapConfig = {
  // 高德地图配置
  amap: {
    key: '48f35f240d10902e83fedb71a918e4f7', // 请替换为您的高德地图API key
    geocoderUrl: 'https://restapi.amap.com/v3/geocode/regeo',
    poiSearchUrl: 'https://restapi.amap.com/v3/place/text',
    enabled: true // 启用高德地图服务
  },

  // 默认配置
  default: {
    // 缓存过期时间（毫秒）
    cacheExpireTime: 30 * 60 * 1000, // 30分钟
    // 位置精度（米）
    accuracy: 1000,
    // 超时时间（毫秒）
    timeout: 10000
  }
};

/**
 * 获取可用的地图服务
 * 只使用高德地图
 */
export function getAvailableMapService() {
  if (mapConfig.amap.enabled && mapConfig.amap.key !== 'YOUR_AMAP_KEY') {
    return 'amap';
  }

  return null;
}

/**
 * 获取地图服务配置
 */
export function getMapServiceConfig(service) {
  return mapConfig[service] || null;
}
