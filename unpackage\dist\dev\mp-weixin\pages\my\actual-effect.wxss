/* 水平间距 */
/* 水平间距 */
/* 全局样式修复 - 确保页面占满屏幕宽度 */
page {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
}

/* 通用容器样式 */
.container, .page-container {
  width: 100%;
  min-height: 100vh;
  box-sizing: border-box;
}

/* 修复小程序页面宽度问题 */
uni-page-body {
  width: 100% !important;
}

/* 确保所有view元素正确继承宽度 */
view {
  box-sizing: border-box;
}
.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f5f5;
}
.device-info {
  display: flex;
  justify-content: space-between;
  /* 左右两端对齐 */
  align-items: center;
  /* 垂直居中 */
  width: 100%;
  /* 确保容器占满宽度 */
}
.device-name {
  font-size: 32rpx;
  color: #333;
  white-space: nowrap;
  /* 不换行 */
  overflow: hidden;
  /* 超出部分隐藏 */
  text-overflow: ellipsis;
  /* 显示省略号 */
  max-width: 70%;
  /* 限制最大宽度，防止挤压状态 */
}
.device-status {
  font-size: 26rpx;
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
  white-space: nowrap;
  /* 确保状态不换行 */
  flex-shrink: 0;
  /* 防止被压缩 */
  margin-right: 20px;
}
.status-online {
  color: #07C160;
  background-color: rgba(7, 193, 96, 0.1);
}
.status-offline {
  color: #FA5151;
  background-color: rgba(250, 81, 81, 0.1);
}
.comparison-section {
  background-color: #fff;
  padding: 30rpx;
  margin-bottom: 20rpx;
}
.image-container {
  display: flex;
  justify-content: space-between;
  margin-bottom: 30rpx;
}
.before-image,
.after-image {
  width: 45%;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.hair-image {
  width: 100%;
  height: 400rpx;
  border-radius: 20rpx;
  margin-bottom: 20rpx;
}
.placeholder-image {
  width: 100%;
  height: 400rpx;
  border: 2rpx dashed #4285f4;
  border-radius: 20rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-bottom: 20rpx;
  background-color: #f8f9ff;
}
.add-icon {
  font-size: 60rpx;
  color: #4285f4;
  margin-bottom: 10rpx;
}
.placeholder-text {
  font-size: 28rpx;
  color: #4285f4;
  margin-bottom: 5rpx;
}
.placeholder-subtext {
  font-size: 24rpx;
  color: #999;
}
.image-label {
  font-size: 28rpx;
  color: #333;
  text-align: center;
}
.color-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  text-align: center;
}
.color-structure {
  background-color: #fff;
  padding: 30rpx;
  margin-bottom: 20rpx;
}
.structure-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
}
.color-composition {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}
.color-item {
  display: flex;
  align-items: center;
  padding: 15rpx;
  background-color: #f8f9fa;
  border-radius: 12rpx;
}
.color-block {
  width: 80rpx;
  height: 80rpx;
  border-radius: 12rpx;
  margin-right: 20rpx;
  border: 2rpx solid #eee;
}
.color-info {
  display: flex;
  flex-direction: column;
}
.color-name {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 5rpx;
}
.color-amount {
  font-size: 24rpx;
  color: #666;
}
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #fff;
  padding: 20rpx 30rpx;
  border-top: 1rpx solid #eee;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.1);
}
.action-icon {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-right: 30rpx;
}
.icon {
  font-size: 40rpx;
  color: #666;
  margin-bottom: 5rpx;
}
.count {
  font-size: 24rpx;
  color: #999;
}
.generate-btn {
  background-color: #4285f4;
  color: #fff;
  padding: 20rpx 40rpx;
  border-radius: 30rpx;
  font-size: 32rpx;
  font-weight: 500;
}