
.container {
		/* padding: 20px; */
		background-color: #f5f5f5;
		padding: 10rpx;
}
.header {
		background-color: #3b99fc;
		padding: 10px;
		text-align: center;
}
.online {
		color: blue;
		font-size: 20rpx;
		margin-left: 20rpx;
}
.offline {
		color: #999999;
		font-size: 20rpx;
		margin-left: 20rpx;
		margin-top: 10rpx;
}
.title {
		color: #ffffff;
		font-size: 18px;
}
.device-status {
		display: flex;
		  justify-content: space-between; /* 左右两端对齐 */
		  align-items: center; /* 垂直居中 */
		  width: 100%; /* 确保占满宽度 */
		  padding: 20rpx; /* 调整内边距 */
		  box-sizing: border-box; /* 防止 padding 影响宽度 */
}
.categoryName {
		font-size: 32rpx;
		  color: #333;
		  white-space: nowrap; /* 防止设备名称换行 */
		  overflow: hidden; /* 超出部分隐藏 */
		  text-overflow: ellipsis; /* 显示省略号 */
		  flex: 1; /* 允许名称占据剩余空间 */
		  min-width: 0; /* 防止 flex 布局溢出 */
		  margin-right: 20rpx; /* 和状态标签保持间距 */
}
.device-state {
		 font-size: 26rpx;
		  padding: 4rpx 12rpx;
		  border-radius: 20rpx;
		  white-space: nowrap; /* 防止状态标签换行 */
		  flex-shrink: 0; /* 防止状态标签被压缩 */
}
.status-online {
		color: #07C160;
		background-color: rgba(7, 193, 96, 0.1);
}
.status-offline {
		color: #FA5151;
		background-color: rgba(250, 81, 81, 0.1);
}
.card {
		background-color: #ffffff;
		border-radius: 10px;
		margin-top: 10rpx;
		padding: 20px;
		width: 100%;
		justify-content: center;
		/* 水平居中 */
		margin-bottom: 20px;
		box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
.card-title {
		display: block;
		font-size: 16px;
		margin-bottom: 10px;
		text-align: center;
}
.grading {
		font-weight: 100;
		font-size: 45rpx;
		display: block;
		/* 确保text元素变成块级元素 */
		text-align: center;
		/* 文本居中 */
		width: 100%;
		/* 占据全部宽度 */
}
.bowl-icon {
		display: block;
		width: 110px;
		height: 60px;
		margin: 0 auto;

		/* 优化过渡效果 */
		transition:
			opacity 0.6s cubic-bezier(0.4, 0, 0.2, 1),
			filter 0.6s cubic-bezier(0.4, 0, 0.2, 1),
			transform 0.4s ease;
		transform-origin: center;
}

	/* 非激活状态 */
.bowl-icon:not(.active) {
		opacity: 0.7;
		filter: grayscale(30%) brightness(0.9);
}

	/* 激活状态 */
.bowl-icon.active {
		opacity: 1;
		filter: drop-shadow(0 0 15rpx rgba(59, 153, 252, 0.4));
		transform: scale(1.05);
		animation: pulse 3s infinite;
}

	/* 添加状态切换时的中间态 */
.bowl-icon.active-add,
	.bowl-icon.active-remove {
		transition:
			opacity 0.8s ease-out,
			filter 0.8s ease-out,
			transform 0.5s cubic-bezier(0.68, -0.55, 0.27, 1.55);
}
.bowl-icon.active {
		opacity: 1;
		filter: drop-shadow(0 0 15rpx rgba(59, 153, 252, 0.4));
		transform: scale(1.05);
		animation: pulse 3s infinite;
}
@keyframes pulse {
0% {
			filter: drop-shadow(0 0 15rpx rgba(59, 153, 252, 0.4));
}
50% {
			filter: drop-shadow(0 0 25rpx rgba(59, 153, 252, 0.6));
}
100% {
			filter: drop-shadow(0 0 15rpx rgba(59, 153, 252, 0.4));
}
}
.bowl-color {
		display: block;
		text-align: center;
		font-size: 26rpx;
		color: #666;
		margin: 15rpx 0;
		transition: all 0.3s ease;
}


	/* 原有的 .button-group 样式 */
.button-group {
		display: flex;
		justify-content: center;
		gap: 10rpx;
		/* 使用gap替代margin-right */
		flex-wrap: wrap;
		/* 允许换行 */
		max-width: 80%;
		/* 限制最大宽度 */
		margin: 0 auto;
}


	/* 统一按钮样式 */
.button-group button {
		flex: 1;
		/* height: 80rpx; */
		border-radius: 12rpx;
		font-size: 28rpx;
		font-weight: 500;
		display: flex;
		align-items: center;
		justify-content: center;
		transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
		border: none;
		outline: none;
		position: relative;
		overflow: hidden;
}
.button-group button:active {
		transform: scale(0.98);
}

	/* 按钮水波纹效果 */
.button-group button:after {
		content: "";
		position: absolute;
		top: 50%;
		left: 50%;
		width: 5rpx;
		height: 5rpx;
		background: rgba(255, 255, 255, 0.5);
		opacity: 0;
		border-radius: 100%;
		transform: scale(1, 1) translate(-50%);
		transform-origin: 50% 50%;
}
.button-group button:focus:not(:active)::after {
		animation: ripple 0.6s ease-out;
}
@keyframes ripple {
0% {
			transform: scale(0, 0);
			opacity: 0.5;
}
100% {
			transform: scale(20, 20);
			opacity: 0;
}
}

	/* 各按钮特定样式 */
.start-button {
		background-color: #3b99fc;
		color: white;
		box-shadow: 0 4rpx 12rpx rgba(59, 153, 252, 0.3);
}
.stop-button {
		background-color: #52c41a;
		color: white;
		box-shadow: 0 4rpx 12rpx rgba(82, 196, 26, 0.3);
}
.resume-button {
		background-color: #faad14;
		color: white;
		box-shadow: 0 4rpx 12rpx rgba(250, 173, 20, 0.3);
}
.cancel-button {
		background-color: #f5f5f5;
		color: #666;
		border: 1rpx solid #e8e8e8;
}
.stop-master-button {
		background-color: #ff4d4f;
		color: white;
		box-shadow: 0 4rpx 12rpx rgba(255, 77, 79, 0.3);
}
.color-item {
		display: flex;
		align-items: center;
		margin: 10px 0;
}
.color-indicator {
		width: 20px;
		height: 20px;
		border-radius: 50%;
		margin-right: 10px;
}

	/* 过渡动画 */
.fade-enter-active,
	.fade-leave-active {
		transition: opacity 0.5s ease;
}
.fade-enter,
	.fade-leave-to {
		opacity: 0;
}
.slide-fade-enter-active {
		transition: all 0.3s ease-out;
}
.slide-fade-leave-active {
		transition: all 0.3s cubic-bezier(1, 0.5, 0.8, 1);
}
.slide-fade-enter,
	.slide-fade-leave-to {
		transform: translateY(20rpx);
		opacity: 0;
}

	/* 加载指示器 */
.loading-overlay {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: rgba(255, 255, 255, 0.7);
		display: flex;
		justify-content: center;
		align-items: center;
		z-index: 999;
}
.loading-spinner {
		width: 80rpx;
		height: 80rpx;
		border: 6rpx solid #f3f3f3;
		border-top: 6rpx solid #3b99fc;
		border-radius: 50%;
		animation: spin 1s linear infinite;
}
@keyframes spin {
0% {
			transform: rotate(0deg);
}
100% {
			transform: rotate(360deg);
}
}
.status-text {
		display: inline-block;
		transition:
			color 0.6s cubic-bezier(0.25, 0.1, 0.25, 1),
			transform 0.4s ease;
}
.status-text.command-active {
		animation: gentleScale 2s infinite;
}
@keyframes gentleScale {
0% {
			transform: scale(1);
}
50% {
			transform: scale(1.25);
			color: #5a4fcf;
			/* 稍亮的SlateBlue */
}
100% {
			transform: scale(1);
}
}
.amount {
		margin-left: auto;
}

	/* 进度条样式 */
.progress-container {
		margin: 20rpx 0;
		display: flex;
		align-items: center;
		gap: 20rpx;
}
.progress-bar {
		flex: 1;
		height: 8px;
		background-color: #f0f0f0;
		border-radius: 4px;
		overflow: hidden;
}
.progress-fill {
		height: 100%;
		background-color: #3b99fc;
		transition: width 0.3s ease;
}
.progress-text {
		font-size: 12px;
		color: #666;
		min-width: 35px;
}

	/* 库存预警样式 */
.alert-section {
		margin-bottom: 15px;
}
.alert-title {
		font-size: 14px;
		font-weight: bold;
		color: #ff6b35;
		margin-bottom: 8px;
		display: block;
}
.alert-item {
		margin-bottom: 5px;
}
.alert-text {
		font-size: 12px;
		padding: 2px 6px;
		border-radius: 3px;
}
.alert-text.warning {
		background-color: #fff3cd;
		color: #856404;
}
.alert-text.critical {
		background-color: #f8d7da;
		color: #721c24;
}
.inventory-summary {
		display: flex;
		justify-content: space-between;
		padding-top: 10px;
		border-top: 1px solid #eee;
}
.summary-text {
		font-size: 12px;
		color: #666;
}
