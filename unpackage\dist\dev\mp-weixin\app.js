"use strict";
Object.defineProperty(exports, Symbol.toStringTag, { value: "Module" });
const common_vendor = require("./common/vendor.js");
const utils_messageHandler = require("./utils/messageHandler.js");
const utils_dataSync = require("./utils/dataSync.js");
const utils_dyeConsumptionManager = require("./utils/dyeConsumptionManager.js");
const utils_request = require("./utils/request.js");
const store_wifi = require("./store/wifi.js");
const utils_connectionStateValidator = require("./utils/connectionStateValidator.js");
const store_bluetooth = require("./store/bluetooth.js");
if (!Math) {
  "./pages/index/index.js";
  "./pages/mall/formula-making/formula-making.js";
  "./pages/device/device.js";
  "./pages/device/index.js";
  "./pages/device/device-manage/device-manage.js";
  "./pages/my/my.js";
  "./pages/mall/mall.js";
  "./pages/mall/category/category.js";
  "./pages/mall/product.js";
  "./pages/mall/store/store.js";
  "./pages/login/login.js";
  "./pages/mall/product/index.js";
  "./pages/mall/product/detail.js";
  "./pages/mall/test-navigation.js";
  "./pages/cart/cart.js";
  "./pages/order/confirm.js";
  "./pages/order/success.js";
  "./pages/address/list.js";
  "./pages/address/add.js";
  "./pages/address/region-picker.js";
  "./pages/address/edit.js";
  "./pages/mall/search/search.js";
  "./pages/mall/color/detail.js";
  "./pages/mall/color/confirm.js";
  "./pages/my/collection.js";
  "./pages/my/color-record.js";
  "./pages/my/card-detail.js";
  "./pages/my/actual-effect.js";
  "./pages/my/store-info.js";
  "./pages/my/staff-manage.js";
  "./pages/my/add-member.js";
  "./pages/my/device-list.js";
  "./pages/my/store-qrcode.js";
  "./pages/my/color-test.js";
  "./pages/my/usage-stats.js";
  "./pages/my/order.js";
  "./pages/my/order-detail.js";
  "./pages/my/repair.js";
  "./pages/my/policy.js";
  "./pages/my/message.js";
  "./pages/my/about.js";
  "./pages/my/manual.js";
  "./pages/my/user-agreement.js";
  "./pages/my/privacy-policy.js";
  "./pages/my/guide.js";
  "./pages/my/guide-detail.js";
  "./pages/my/partnership.js";
  "./pages/outLogin/outLogin.js";
  "./pages/test/debounce-demo.js";
  "./pages/test/connection-state-test.js";
  "./pages/test/bluetooth-wifi-fix-test.js";
  "./pages/location-picker.js";
  "./pages/my/content-detail.js";
  "./pages/device/bind-device/bind-device.js";
  "./pages/device/dye-device-manager/dye-device-manager.js";
  "./pages/device/dye-device-manager/bind-modal.js";
  "./pages/device/dye-device-manager/assign-modal.js";
  "./pages/device/dye-device-manager/log.js";
  "./pages/device/dye-device-manager/detail.js";
}
const _sfc_main = {
  globalData: {
    deviceStatus: false,
    // 设备是否在线
    connectedDeviceId: "",
    // 当前需要连接
    deviceList: /* @__PURE__ */ new Set(),
    // 相关联设备id列表
    commandStatus: false,
    // 指令执行状态
    connectionMethod: "",
    // 连接设备方式 'bluetooth' | 'wifi'
    prepareToConnectDeviceCode: "",
    // 准备连接的设备编码
    // 以下是wifi设置
    currentWifi: {},
    // 指令队列相关状态 (从蓝牙store迁移过来)
    commandQueue: [],
    // 指令队列
    isExecutingQueue: false,
    // 是否正在执行队列
    currentCommand: null,
    // 当前正在执行的指令
    isPaused: false,
    // 是否暂停状态
    pausedQueueState: null,
    // 暂停时的队列状态
    // 任务完成缓冲期 - 防止任务完成后误判断线
    taskCompletionBuffer: false,
    // 是否在任务完成缓冲期
    taskCompletionBufferTimer: null,
    // 缓冲期定时器
    // 染膏消耗相关状态
    dyeConsumption: {
      currentUsage: {},
      // 当前使用量
      totalConsumption: 0,
      // 总消耗量
      lastRecordTime: null
      // 最后记录时间
    },
    // 设备状态详细信息
    deviceStatusDetail: {
      isOnline: false,
      // 设备在线状态
      connectionQuality: 0,
      // 连接质量 (0-100)
      lastHeartbeat: null,
      // 最后心跳时间
      firmwareVersion: "",
      // 固件版本
      batteryLevel: 100
      // 电池电量 (如果适用)
    },
    // 指令执行详细状态
    commandExecutionDetail: {
      executionId: null,
      // 执行ID
      progress: 0,
      // 执行进度 (0-100)
      currentStep: "",
      // 当前步骤
      totalSteps: 0,
      // 总步骤数
      startTime: null,
      // 开始时间
      estimatedEndTime: null,
      // 预计结束时间
      errors: []
      // 错误列表
    },
    // 染膏库存状态
    dyeInventory: {
      lastUpdateTime: null,
      // 最后更新时间
      totalColors: 0,
      // 总颜色数
      lowStockCount: 0,
      // 低库存数量
      alerts: []
      // 库存预警列表
    },
    // 错误状态管理
    errorState: {
      hasError: false,
      // 是否有错误
      errorType: "",
      // 错误类型
      errorMessage: "",
      // 错误消息
      errorCode: null,
      // 错误代码
      timestamp: null
      // 错误时间
    },
    // 碗的颜色
    wan_color: false,
    // false 代表灰色 true 代表绿色
    // 全局的wifi下发分片状态
    // 全局传输状态
    bleTransfer: {
      currentChunks: null,
      // 当前传输的分片数组
      currentIndex: 0,
      // 当前分片索引
      retryCount: 0,
      // 当前重试次数
      maxRetries: 3,
      // 最大重试次数
      isTransferring: false
      // 是否正在传输
    }
  },
  onLaunch() {
    common_vendor.index.__f__("log", "at App.vue:104", "应用初始化");
    this.initializeServices();
  },
  onShow() {
    common_vendor.index.__f__("log", "at App.vue:109", "当前连接方式是", this.globalData.connectionMethod);
    common_vendor.index.__f__("log", "at App.vue:110", "全局app.vue文件中的onShow函数触发");
    if (this.globalData.connectionMethod !== "bluetooth") {
      this.restoreDeviceConnectionState();
    } else {
      common_vendor.index.__f__("log", "at App.vue:117", "当前为蓝牙连接，跳过状态恢复以避免与WiFi逻辑冲突");
    }
    common_vendor.index.__f__("log", "at App.vue:120", "全局：小程序从后台进入前台");
    this.resumeServices();
  },
  onHide() {
    common_vendor.index.__f__("log", "at App.vue:126", "当前连接方式是", this.globalData.connectionMethod);
    common_vendor.index.__f__("log", "at App.vue:127", "全局app.vue文件中的onHide函数触发");
    this.pauseServices();
  },
  methods: {
    // 检查用户登录状态
    checkLoginStatus() {
      const token = common_vendor.index.getStorageSync("token");
      return !!token;
    },
    // 清空用户登录状态函数
    clearLoginState() {
      common_vendor.index.removeStorageSync("token");
      common_vendor.index.removeStorageSync("userInfo");
      common_vendor.index.removeStorageSync("isLoggedIn");
      common_vendor.index.switchTab({
        url: "/pages/index/index"
      });
    },
    async getDeviceConnectId() {
      const response = await utils_request.apiService.device.getDeviceCodeList();
      this.globalData.deviceList = /* @__PURE__ */ new Set([...this.globalData.deviceList, ...response.data]);
    },
    hasDeviceList(deviceCode) {
      return this.globalData.deviceList.has(deviceCode) || this.globalData.connectedDeviceId && this.globalData.connectedDeviceId === deviceCode;
    },
    addDeviceList(deviceCode) {
      this.globalData.deviceList = /* @__PURE__ */ new Set([...this.globalData.deviceList, deviceCode]);
    },
    updateWanColor(parm) {
      this.globalData.wan_color = parm;
    },
    // 初始化服务
    initializeServices() {
      try {
        this.setupMessageHandlers();
        this.restoreDeviceConnectionState();
        if (this.globalData.connectedDeviceId) {
          utils_dataSync.dataSyncService.start(this.globalData.connectedDeviceId);
        }
        common_vendor.index.__f__("log", "at App.vue:188", "服务初始化完成");
      } catch (error) {
        common_vendor.index.__f__("error", "at App.vue:190", "服务初始化失败:", error);
        this.setErrorState({
          errorType: "INITIALIZATION_ERROR",
          errorMessage: `服务初始化失败: ${error.message}`,
          errorCode: null
        });
      }
    },
    // 恢复设备连接状态
    restoreDeviceConnectionState() {
      try {
        setTimeout(() => {
        }, 300);
        const deviceStatus = this.globalData.deviceStatus;
        const connectedDeviceId = this.globalData.connectedDeviceId;
        const connectionMethod = this.globalData.connectionMethod;
        common_vendor.index.__f__("log", "at App.vue:207", `restoreDeviceConnectionState() 设备连接状态${deviceStatus} 设备连接id${connectedDeviceId} 设备连接方式${connectionMethod}`);
        if (deviceStatus && connectedDeviceId && connectionMethod) {
          common_vendor.index.__f__("log", "at App.vue:211", "检测到之前的连接记录:", connectedDeviceId);
          if (connectionMethod) {
            common_vendor.index.__f__("log", "at App.vue:220", "恢复连接类型:", connectionMethod);
            utils_connectionStateValidator.connectionStateValidator.logConnectionChange("CONNECTION_STATE_RESTORED", connectionMethod, {
              deviceId: this.globalData.connectedDeviceId,
              connectionType: connectionMethod
            });
          } else {
            common_vendor.index.__f__("warn", "at App.vue:229", "连接设备缺少connectionMethod，清除可能损坏的连接记录");
            utils_connectionStateValidator.connectionStateValidator.logConnectionChange("CORRUPTED_STATE_CLEARED", "unknown", {
              reason: "MISSING_CONNECTION_TYPE",
              deviceId: this.globalData.connectedDeviceId
            });
            store.commit("CLEAR_CONNECTED_DEVICE");
            this.globalData.connectionMethod = "";
            this.globalData.connectedDeviceId = "";
            this.globalData.deviceStatus = false;
            common_vendor.index.__f__("log", "at App.vue:241", "已清除损坏的连接记录，等待用户重新连接");
            return;
          }
          if (this.isWifiConnection()) {
            common_vendor.index.__f__("log", "at App.vue:247", "检测到WiFi设备连接记录，设备ID:", this.globalData.connectedDeviceId);
            if (this.isBluetoothConnection()) {
              common_vendor.index.__f__("warn", "at App.vue:251", "当前为蓝牙连接，跳过WiFi自动重连以避免冲突");
              return;
            }
            const connectionStatus = store_wifi.getConnectionStatus();
            common_vendor.index.__f__("log", "at App.vue:257", "当前WiFi连接状态:", connectionStatus);
            if (connectionStatus.connectionPersistent === false) {
              common_vendor.index.__f__("log", "at App.vue:261", "检测到用户之前主动断开连接，跳过自动重连");
              return;
            }
            if (!connectionStatus.isPolling) {
              common_vendor.index.__f__("log", "at App.vue:266", "开始重新连接WiFi设备...");
              store_wifi.setConnectionPersistent(true);
              setTimeout(() => {
                store_wifi.startPollingWifi(this.globalData.connectedDeviceId);
              }, 1e3);
            } else {
              common_vendor.index.__f__("log", "at App.vue:275", "WiFi连接已在进行中，跳过重连");
            }
          }
          this.globalData.deviceStatus = false;
          common_vendor.index.__f__("log", "at App.vue:282", "设备连接状态恢复完成，等待连接确认:", {
            deviceId: this.globalData.connectedDeviceId,
            connectionMethod: this.globalData.connectionMethod,
            deviceStatus: this.globalData.deviceStatus
          });
        } else {
          common_vendor.index.__f__("log", "at App.vue:288", "没有找到之前的连接记录");
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at App.vue:291", "恢复设备连接状态失败:", error);
      }
    },
    // 设置消息处理器
    setupMessageHandlers() {
      utils_messageHandler.messageHandler.registerListener("DISPENSE_COMPLETE", (message) => {
        common_vendor.index.__f__("log", "at App.vue:299", "收到出料完成消息:", message);
        this.updateCommandExecutionDetail({
          progress: 100,
          currentStep: "出料完成"
        });
      });
      utils_messageHandler.messageHandler.registerListener("BOWL_PLACED", (message) => {
        common_vendor.index.__f__("log", "at App.vue:307", "收到已放碗消息:", message);
        this.updateCommandExecutionDetail({
          currentStep: "已放碗",
          progress: 10
        });
      });
      utils_messageHandler.messageHandler.registerListener("DEVICE_STATUS_UPDATE", (message) => {
        common_vendor.index.__f__("log", "at App.vue:315", "收到设备状态更新:", message);
        this.updateDeviceStatusDetail({
          isOnline: message.isOnline,
          lastHeartbeat: message.timestamp
        });
      });
    },
    // 恢复服务（优化：检查SSE连接状态，只在需要时重连）
    resumeServices() {
      common_vendor.index.__f__("log", "at App.vue:325", "应用切换回前台连接设备id", this.globalData.connectedDeviceId);
      common_vendor.index.__f__("log", "at App.vue:326", "当前连接方式", this.globalData.connectionMethod);
      common_vendor.index.__f__("log", "at App.vue:327", "切换回前台缓存中的数据:", utils_dataSync.dataSyncService.getSyncStatus().syncInProgress);
      const shouldEnterIf = this.globalData.connectedDeviceId && !utils_dataSync.dataSyncService.getSyncStatus().syncInProgress;
      common_vendor.index.__f__("log", "at App.vue:331", "是否应进入if块:", shouldEnterIf);
      if (this.globalData.connectedDeviceId && !utils_dataSync.dataSyncService.getSyncStatus().syncInProgress) {
        utils_dataSync.dataSyncService.start(this.globalData.connectedDeviceId);
        common_vendor.index.__f__("log", "at App.vue:336", "进入前台连接设备id", this.globalData.connectedDeviceId);
        common_vendor.index.__f__("log", "at App.vue:337", "进入前台连接设备连接方式", this.globalData.connectionMethod);
        if (this.isWifiConnection() && this.globalData.connectionMethod === "wifi") {
          const status = store_wifi.getConnectionStatus();
          common_vendor.index.__f__("log", "at App.vue:342", "应用回到前台，SSE连接状态:", status);
          if (!status.isPolling) {
            common_vendor.index.__f__("log", "at App.vue:344", "SSE连接已断开，重新建立连接");
            wifiStore.startPollingWifi(this.globalData.connectedDeviceId);
          } else {
            common_vendor.index.__f__("log", "at App.vue:347", "SSE连接正常，无需重连");
          }
        }
      }
    },
    // 暂停服务（优化：保持SSE连接，只暂停数据同步）
    pauseServices() {
      utils_dataSync.dataSyncService.stop();
      store_wifi.setIsPolling(false);
      common_vendor.index.__f__("log", "at App.vue:376", "应用进入后台，保持SSE连接，暂停数据同步");
    },
    // 启动设备相关服务
    startDeviceServices(deviceCode) {
      if (!deviceCode) {
        common_vendor.index.__f__("warn", "at App.vue:382", "设备编码为空，无法启动设备服务");
        return;
      }
      this.globalData.connectedDeviceId = deviceCode;
      utils_dataSync.dataSyncService.start(deviceCode);
      utils_dyeConsumptionManager.dyeConsumptionManager.initialize(deviceCode);
      common_vendor.index.__f__("log", "at App.vue:394", `设备服务已启动: ${deviceCode}`);
    },
    // 停止设备相关服务
    stopDeviceServices() {
      utils_dataSync.dataSyncService.stop();
      this.globalData.connectedDeviceId = "";
      common_vendor.index.__f__("log", "at App.vue:401", "设备服务已停止");
    },
    // 改变指令执行状态
    updateCommandStatus(commandStatus) {
      this.globalData.commandStatus = commandStatus;
    },
    // 设置连接方式
    setConnectionMethod(method) {
      this.globalData.connectionMethod = method;
      common_vendor.index.__f__("log", "at App.vue:411", "连接方式已设置为:", method);
    },
    // 判断是否为蓝牙连接
    isBluetoothConnection() {
      return this.globalData.connectionMethod === "bluetooth";
    },
    // 判断是否为WiFi连接
    isWifiConnection() {
      return this.globalData.connectionMethod === "wifi";
    },
    // 指令队列管理方法
    addToCommandQueue(command) {
      this.globalData.commandQueue.unshift(command);
      common_vendor.index.__f__("log", "at App.vue:427", "指令已添加到全局队列:", command.description);
    },
    removeFromCommandQueue() {
      return this.globalData.commandQueue.shift();
    },
    clearCommandQueue() {
      this.globalData.commandQueue = [];
      this.globalData.currentCommand = null;
      this.globalData.isExecutingQueue = false;
      this.globalData.isPaused = false;
      this.globalData.pausedQueueState = null;
      this.globalData.wan_color = false;
      this.clearTaskCompletionBuffer();
      common_vendor.index.__f__("log", "at App.vue:443", "全局指令队列已清空");
    },
    setExecutingQueue(executing) {
      this.globalData.isExecutingQueue = executing;
    },
    setCurrentCommand(command) {
      this.globalData.currentCommand = command;
    },
    setPaused(isPaused) {
      this.globalData.isPaused = isPaused;
    },
    setPausedQueueState(queueState) {
      this.globalData.pausedQueueState = queueState;
    },
    // 更新染膏消耗状态
    updateDyeConsumption(consumptionData) {
      this.globalData.dyeConsumption = {
        ...this.globalData.dyeConsumption,
        ...consumptionData,
        lastRecordTime: (/* @__PURE__ */ new Date()).toISOString()
      };
    },
    // 更新设备状态详情
    updateDeviceStatusDetail(statusData) {
      this.globalData.deviceStatusDetail = {
        ...this.globalData.deviceStatusDetail,
        ...statusData
      };
    },
    // 更新指令执行详情
    updateCommandExecutionDetail(executionData) {
      this.globalData.commandExecutionDetail = {
        ...this.globalData.commandExecutionDetail,
        ...executionData
      };
    },
    // 更新染膏库存状态
    updateDyeInventory(inventoryData) {
      this.globalData.dyeInventory = {
        ...this.globalData.dyeInventory,
        ...inventoryData,
        lastUpdateTime: (/* @__PURE__ */ new Date()).toISOString()
      };
    },
    // 设置错误状态
    setErrorState(errorData) {
      this.globalData.errorState = {
        hasError: true,
        timestamp: (/* @__PURE__ */ new Date()).toISOString(),
        ...errorData
      };
    },
    // 设置取消状态
    setCanceling(commandStatus) {
      this.globalData.commandStatus = false;
      this.clearCommandQueue();
    },
    // 设置执行队列状态
    setExecutingQueue(status) {
      this.globalData.isExecutingQueue = status;
      common_vendor.index.__f__("log", "at App.vue:517", "全局执行状态已更新:", status);
    },
    // 设置暂停状态
    setPausedStatus(status) {
      this.globalData.isPaused = status;
      common_vendor.index.__f__("log", "at App.vue:523", "全局暂停状态已更新:", status);
    },
    // 清除错误状态
    clearErrorState() {
      this.globalData.errorState = {
        hasError: false,
        errorType: "",
        errorMessage: "",
        errorCode: null,
        timestamp: null
      };
    },
    // 重置所有状态
    resetAllStates() {
      this.globalData.dyeConsumption = {
        currentUsage: {},
        totalConsumption: 0,
        lastRecordTime: null
      };
      this.globalData.deviceStatusDetail = {
        isOnline: false,
        connectionQuality: 0,
        lastHeartbeat: null,
        firmwareVersion: "",
        batteryLevel: 100
      };
      this.globalData.commandExecutionDetail = {
        executionId: null,
        progress: 0,
        currentStep: "",
        totalSteps: 0,
        startTime: null,
        estimatedEndTime: null,
        errors: []
      };
      this.globalData.dyeInventory = {
        lastUpdateTime: null,
        totalColors: 0,
        lowStockCount: 0,
        alerts: []
      };
      this.clearErrorState();
      this.clearTaskCompletionBuffer();
    },
    // 处理蓝牙消息
    handleBluetoothMessage(message) {
      utils_messageHandler.messageHandler.handleBluetoothMessage(message);
    },
    // 处理WiFi消息
    handleWifiMessage(message) {
      utils_messageHandler.messageHandler.handleWifiMessage(message);
    },
    // 强制同步数据
    async forceSyncData(dataType = "all") {
      if (!this.globalData.connectedDeviceId) {
        common_vendor.index.__f__("warn", "at App.vue:584", "设备未连接，无法同步数据");
        return false;
      }
      return await utils_dataSync.dataSyncService.forceSync(dataType);
    },
    // 获取同步状态
    getSyncStatus() {
      return utils_dataSync.dataSyncService.getSyncStatus();
    },
    // 获取消息历史
    getMessageHistory(limit = 50) {
      return utils_messageHandler.messageHandler.getMessageHistory(limit);
    },
    // 清除消息历史
    clearMessageHistory() {
      utils_messageHandler.messageHandler.clearHistory();
    },
    // 当全部出料完成之后改变一些状态
    allExecutionCompleted() {
      this.updateCommandExecutionDetail({
        progress: 100,
        currentStep: "出料完成",
        estimatedEndTime: (/* @__PURE__ */ new Date()).toISOString()
      });
      this.globalData.currentCommand = null;
      this.globalData.isExecutingQueue = false;
      this.globalData.isPaused = false;
      this.globalData.pausedQueueState = null;
      this.globalData.wan_color = false;
      this.globalData.commandStatus = false;
      this.startTaskCompletionBuffer();
    },
    // 启动任务完成缓冲期
    startTaskCompletionBuffer() {
      common_vendor.index.__f__("log", "at App.vue:626", "启动任务完成缓冲期，防止误判断线");
      if (this.globalData.taskCompletionBufferTimer) {
        clearTimeout(this.globalData.taskCompletionBufferTimer);
      }
      this.globalData.taskCompletionBuffer = true;
      this.globalData.taskCompletionBufferTimer = setTimeout(() => {
        common_vendor.index.__f__("log", "at App.vue:638", "任务完成缓冲期结束，恢复正常连接检测");
        this.globalData.taskCompletionBuffer = false;
        this.globalData.taskCompletionBufferTimer = null;
      }, 5e3);
    },
    // 清除任务完成缓冲期
    clearTaskCompletionBuffer() {
      if (this.globalData.taskCompletionBufferTimer) {
        clearTimeout(this.globalData.taskCompletionBufferTimer);
        this.globalData.taskCompletionBufferTimer = null;
      }
      this.globalData.taskCompletionBuffer = false;
    }
  }
};
if (!Array) {
  const _component_router_view = common_vendor.resolveComponent("router-view");
  _component_router_view();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {};
}
const App = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
function createApp() {
  const app = common_vendor.createSSRApp(App);
  app.use(store_bluetooth.store);
  return {
    app
  };
}
createApp().app.mount("#app");
exports.createApp = createApp;
//# sourceMappingURL=../.sourcemap/mp-weixin/app.js.map
