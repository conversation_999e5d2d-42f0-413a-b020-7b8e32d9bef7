/* 水平间距 */
/* 水平间距 */
/* 全局样式修复 - 确保页面占满屏幕宽度 */
page.data-v-d67fbbdc {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
}

/* 通用容器样式 */
.container.data-v-d67fbbdc, .page-container.data-v-d67fbbdc {
  width: 100%;
  min-height: 100vh;
  box-sizing: border-box;
}

/* 修复小程序页面宽度问题 */
uni-page-body.data-v-d67fbbdc {
  width: 100% !important;
}

/* 确保所有view元素正确继承宽度 */
view.data-v-d67fbbdc {
  box-sizing: border-box;
}
.bind-popup .popup-container.data-v-d67fbbdc {
  width: 90vw;
  max-width: 500px;
  background-color: #fff;
  border-radius: 12px;
  overflow: hidden;
}
.bind-popup .popup-header.data-v-d67fbbdc {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #f5f5f5;
}
.bind-popup .popup-header .popup-title.data-v-d67fbbdc {
  font-size: 18px;
  font-weight: bold;
  color: #333;
}
.bind-popup .popup-body.data-v-d67fbbdc {
  padding: 20px;
}
.bind-popup .popup-body .step-indicator.data-v-d67fbbdc {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 24px;
}
.bind-popup .popup-body .step-indicator .step.data-v-d67fbbdc {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.bind-popup .popup-body .step-indicator .step .step-number.data-v-d67fbbdc {
  width: 24px;
  height: 24px;
  line-height: 24px;
  text-align: center;
  border-radius: 50%;
  background-color: #eee;
  color: #999;
  font-size: 14px;
  margin-bottom: 4px;
}
.bind-popup .popup-body .step-indicator .step .step-text.data-v-d67fbbdc {
  font-size: 12px;
  color: #999;
}
.bind-popup .popup-body .step-indicator .step.active .step-number.data-v-d67fbbdc {
  background-color: #2979ff;
  color: #fff;
}
.bind-popup .popup-body .step-indicator .step.active .step-text.data-v-d67fbbdc {
  color: #2979ff;
  font-weight: bold;
}
.bind-popup .popup-body .step-indicator .step-line.data-v-d67fbbdc {
  width: 40px;
  height: 1px;
  background-color: #eee;
  margin: 0 8px;
}
.bind-popup .popup-body .step-content .input-group.data-v-d67fbbdc {
  position: relative;
  margin-bottom: 16px;
}
.bind-popup .popup-body .step-content .input-group .input-label.data-v-d67fbbdc {
  display: block;
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
}
.bind-popup .popup-body .step-content .input-group .input-field.data-v-d67fbbdc {
  height: 48px;
  padding: 0 100px 0 15px;
  border: 1px solid #eee;
  border-radius: 8px;
  font-size: 16px;
  background-color: #fafafa;
}
.bind-popup .popup-body .step-content .input-group .scan-btn.data-v-d67fbbdc {
  position: absolute;
  right: 10px;
  top: 30px;
  display: flex;
  align-items: center;
  padding: 4px 8px;
  background-color: #f0f7ff;
  border-radius: 4px;
  color: #2979ff;
  font-size: 12px;
}
.bind-popup .popup-body .step-content .input-group .scan-btn text.data-v-d67fbbdc {
  margin-left: 4px;
}
.bind-popup .popup-body .step-content .tips.data-v-d67fbbdc {
  display: flex;
  align-items: center;
  margin-top: 8px;
}
.bind-popup .popup-body .step-content .tips .tip-text.data-v-d67fbbdc {
  font-size: 12px;
  color: #999;
  margin-left: 4px;
}
.bind-popup .popup-body .step-content .device-info-card.data-v-d67fbbdc {
  display: flex;
  padding: 12px;
  margin-bottom: 20px;
  border: 1px solid #eee;
  border-radius: 8px;
  background-color: #fafafa;
}
.bind-popup .popup-body .step-content .device-info-card .device-image.data-v-d67fbbdc {
  width: 80px;
  height: 80px;
  margin-right: 12px;
}
.bind-popup .popup-body .step-content .device-info-card .device-details.data-v-d67fbbdc {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}
.bind-popup .popup-body .step-content .device-info-card .device-details .device-name.data-v-d67fbbdc {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 4px;
}
.bind-popup .popup-body .step-content .device-info-card .device-details .device-sn.data-v-d67fbbdc,
.bind-popup .popup-body .step-content .device-info-card .device-details .device-type.data-v-d67fbbdc {
  font-size: 12px;
  color: #666;
  margin-bottom: 2px;
}
.bind-popup .popup-body .step-content .form-item.data-v-d67fbbdc {
  margin-bottom: 16px;
}
.bind-popup .popup-body .step-content .form-item .label.data-v-d67fbbdc {
  display: block;
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
}
.bind-popup .popup-body .step-content .form-item .picker.data-v-d67fbbdc {
  padding: 12px 15px;
  border: 1px solid #eee;
  border-radius: 8px;
  font-size: 15px;
  color: #333;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.bind-popup .popup-footer.data-v-d67fbbdc {
  display: flex;
  padding: 12px 20px;
  border-top: 1px solid #f5f5f5;
}
.bind-popup .popup-footer .footer-btn.data-v-d67fbbdc {
  flex: 1;
  height: 44px;
  line-height: 44px;
  border-radius: 8px;
  font-size: 16px;
  margin: 0 8px;
}
.bind-popup .popup-footer .footer-btn.cancel-btn.data-v-d67fbbdc {
  background-color: #f8f8f8;
  color: #666;
}
.bind-popup .popup-footer .footer-btn.confirm-btn.data-v-d67fbbdc {
  background-color: #2979ff;
  color: #fff;
}
.bind-popup .popup-footer .footer-btn[disabled].data-v-d67fbbdc {
  opacity: 0.6;
}
.bind-popup .loading-mask.data-v-d67fbbdc {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}