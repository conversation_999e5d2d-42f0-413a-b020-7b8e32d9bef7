<template>
	<view class="content">
		<!-- 连接方式选项卡 -->
		<view class="connection-tabs">
			<view class="tab-item" :class="{active: activeTab === 'network'}" @click="switchTab('network')">
				<text>连网</text>
				<view class="tab-indicator" v-if="activeTab === 'network'"></view>
			</view>
			<view class="tab-item" :class="{active: activeTab === 'bluetooth'}" @click="switchTab('bluetooth')">
				<text>蓝牙</text>
				<view class="tab-indicator" v-if="activeTab === 'bluetooth'"></view>
			</view>
			<view class="tab-item" :class="{active: activeTab === 'wifi'}" @click="switchTab('wifi')">
				<text>WIFI</text>
				<view class="tab-indicator" v-if="activeTab === 'wifi'"></view>
			</view>
		</view>

		<!-- 蓝牙连接部分 -->
		<view v-if="activeTab === 'bluetooth'">
			<!-- 设备状态详情卡片 -->
			<!-- <view v-if="connectedDevice" class="device-status-card"> -->
			<!-- <view class="status-header">
					<text class="status-title">设备状态</text>
					<view class="status-indicator" :class="{ online: deviceStatusDetail.isOnline }">
						<text>{{ deviceStatusDetail.isOnline ? '在线' : '离线' }}</text>
					</view>
				</view> -->
			<!-- <view class="status-details">
					<view class="status-item">
						<text class="status-label">连接质量:</text>
						<text class="status-value">{{ deviceStatusDetail.connectionQuality }}%</text>
					</view>
					<view class="status-item" v-if="deviceStatusDetail.lastHeartbeat">
						<text class="status-label">最后心跳:</text>
						<text class="status-value">{{ formatTime(deviceStatusDetail.lastHeartbeat) }}</text>
					</view>
					<view class="status-item" v-if="deviceStatusDetail.firmwareVersion">
						<text class="status-label">固件版本:</text>
						<text class="status-value">{{ deviceStatusDetail.firmwareVersion }}</text>
					</view>
				</view> -->
			<!-- </view> -->

			<!-- 染膏库存状态卡片 -->
			<!-- <view v-if="connectedDevice && inventoryStatus" class="inventory-status-card">
				<view class="status-header">
					<text class="status-title">染膏库存</text>
					<view class="refresh-btn" @click="refreshInventoryStatus">
						<text>刷新</text>
					</view>
				</view>
				<view v-if="inventoryStatus.alerts && inventoryStatus.alerts.length > 0" class="alert-section">
					<text class="alert-title">⚠️ 库存预警</text>
					<view v-for="alert in inventoryStatus.alerts" :key="alert.colorCode" class="alert-item">
						<text :class="['alert-text', alert.level]">
							{{ alert.colorName }}: {{ alert.percentage }}%
						</text>
					</view>
				</view>
				<view class="inventory-summary">
					<text class="summary-text">总颜色数: {{ inventoryStatus.totalColors }}</text>
					<text class="summary-text">低库存: {{ inventoryStatus.lowStockCount }}</text>
				</view>
			</view> -->

			<!-- 设备列表 -->
			<scroll-view class="device-list" scroll-y>
				<view v-for="(item, index) in deviceList" :key="index" class="device-item" @click="connectDevice(item)">
					<view class="device-info">
						<text class="device-name">{{ item.name || '未知设备' }}</text>
						<text class="device-id">{{ item.deviceId }}</text>
						<text class="device-rssi">信号强度: {{ item.RSSI || '未知' }}dBm</text>
					</view>
					<view class="device-actions">
						<text class="connect-btn"
							:class="{ connected: connectedDevice && connectedDevice.deviceId === item.deviceId }">
							{{ connectedDevice && connectedDevice.deviceId === item.deviceId ? '已连接' : '连接' }}
						</text>
					</view>
				</view>
				<view v-if="deviceList.length === 0 && isScanning" class="empty-tip">
					<text>正在搜索设备...</text>
				</view>
				<view v-else-if="deviceList.length === 0" class="empty-tip">
					<text>未发现设备，请点击"搜索蓝牙"按钮</text>
				</view>
			</scroll-view>

			<!-- 操作按钮区域 -->
			<view class="action-buttons"
				style="position: fixed; bottom: 0; left: 0; right: 0; background: white; padding: 20rpx;  z-index: 100;">
				<view class="scan-btn" :class="{ scanning: isScanning }" @click="toggleScan">
					<text class="scan-btn-text">
						{{ isScanning ? '停止搜索' : '搜索蓝牙' }}
					</text>
				</view>
				<!-- 设备使用记录按钮 -->
				<!-- <view v-if="connectedDevice" class="usage-buttons">
					<view v-if="!isUsingDevice" class="start-usage-btn" @click="startUsage">
						<text class="usage-btn-text">开始使用</text>
					</view>
					<view v-else class="end-usage-btn" @click="endUsage">
						<text class="usage-btn-text">结束使用</text>
					</view>
					<view class="usage-info" v-if="isUsingDevice">
						<text class="usage-time">使用时长: {{ usageDuration }}</text>
					</view>
				</view> -->
				<view v-if="connectedDevice" class="disconnect-btn" @click="disconnectDevice">
					<text class="scan-btn-text">断开连接</text>
				</view>
			</view>
			<!-- 日志区域 -->
			<!-- <view v-if="logs.length > 0" class="log-container">
				<view class="section-title">操作日志</view>
				<scroll-view class="log-list" scroll-y>
					<view v-for="(log, index) in logs" :key="index" class="log-item">
						<text>{{ log }}</text>
					</view>
				</scroll-view>
			</view> -->
		</view>

		<!-- 网络连接部分 -->
		<view v-if="activeTab === 'network'" class="network-connection">
			<view class="connection-row">
				<view class="connection-item" @click="toggleConnection('network')">
					<text class="connection-label">连网</text>
					<text class="connection-status" :class="{active: networkStatus}">
						{{ networkStatus ? '已开启' : '已关闭' }}
					</text>
				</view>

				<view class="connection-item" @click="toggleConnection('bluetooth')">
					<text class="connection-label">蓝牙</text>
					<text class="connection-status" :class="{active: bluetoothStatus}">
						{{ bluetoothStatus ? '已开启' : '已关闭' }}
					</text>
				</view>

				<view class="connection-item" @click="toggleConnection('wifi')">
					<text class="connection-label">WIFI</text>
					<text class="connection-status" :class="{active: wifiStatus}">
						{{ wifiStatus ? '已开启' : '已关闭' }}
					</text>
				</view>
			</view>

			<view class="scan-device-btn" @click="scanDevices">
				<text>扫描设备</text>
			</view>
		</view>

		<!-- WiFi连接部分 -->
		<view v-if="activeTab === 'wifi'" class="wifi-connection">
			<view class="wifi-form">
				<view class="form-item">
					<text class="form-label">设备编号</text>
					<input class="form-input" type="text" v-model="wifiInfo.deviceId" placeholder="请输入设备编号" />
				</view>

				<view class="form-item">
					<text class="form-label">wifi名称</text>
					<input class="form-input" type="text" v-model="wifiInfo.SSID" placeholder="请输入wifi名" />
				</view>

				<view class="form-item">
					<text class="form-label">密码</text>
					<input class="form-input" type="password" v-model="wifiInfo.password" placeholder="请输入密码" />
					<!-- <text class="show-password" @click="togglePasswordVisibility">
						{{ showPassword ? '隐藏' : '显示' }}
					</text> -->
				</view>
			</view>
			<button class="wifi-property-btn" @click="connectWifi">配置设备wifi</button>
		</view>

	</view>
</template>

<script>
	import {
		apiService
	} from '@/utils/request.js';
	import {
		getCommand,
		BLE_CONSTANTS,
		BLUETOOTH_COMMANDS
	} from '../../utils/bleCommandSet';
	import {
		startPollingWifi
	} from '../../store/wifi';
	import dyeConsumptionManager from '../../utils/dyeConsumptionManager.js';
	import {
		writeDataChunked,
		transferManager,
		TransferState
	} from '@/utils/writeDataChunked'

	export default {
		data() {
			return {
				activeTab: 'bluetooth', // 当前选中的选项卡
				// 联网相关数据
				networkStatus: true,
				bluetoothStatus: true,
				wifiStatus: true,
				// 以下是wifi模块
				// wifiInfo : {

				// },
				wifiInfo: {
					deviceId: '',
					password: '',
					SSID: ''
				},
				showPassword: false,
				// 蓝牙相关数据
				isScanning: false,
				deviceList: [],
				connectedDevice: null,
				logs: [],
				isUsingDevice: false,
				usageStartTime: null,
				usageDuration: '00:00:00',
				usageTimer: null,
				// 设备状态详情
				deviceStatusDetail: {
					isOnline: false,
					connectionQuality: 0,
					lastHeartbeat: null,
					firmwareVersion: '',
					batteryLevel: 100
				},
				// 染膏库存状态
				inventoryStatus: null,
				transferState: TransferState.IDLE,
				lastTransferState: TransferState.IDLE,
				transferStateButtom: true
			}
		},

		onLoad() {
			try {
				this.initBluetooth()
			} catch (err) {
				console.log("蓝牙初始化失败", err)
			}
			try {
				this.initWifi()
			} catch (err) {
				console.log("wifi初始化失败")
			}

		},

		onUnload() {
			this.stopScanning()
			this.stopUsageTimer()
			this.startStatusWatcher()
		},

		onShow() {
			// 页面显示时更新状态
			this.updatePageState()
			this.updateDeviceStatusFromGlobal()
			this.loadInventoryStatus()
		},

		beforeDestroy() {
			// 清理定时器
			if (this.statusWatcher) {
				clearInterval(this.statusWatcher);
			}
		},
		created() {
			// 启动状态监听器
			this.startStatusWatcher();
		},

		methods: {
			startStatusWatcher() {
				this.lastTransferState = TransferState.IDLE

				this.statusWatcher = setInterval(() => {
					const currentTransferState = transferManager.transferState
					// console.log("最新的分片状态",currentTransferState)
					// console.log("旧值是",this.lastTransferState)
					if (this.lastTransferState != currentTransferState) {
						console.log("监听到分片变化值为", currentTransferState)
						switch (currentTransferState) {
							// case TransferState.IDLE:
							// 	this.transferStateButtom = true;
							// 	break;
							case TransferState.TRANSFERRING:
								// 发送配置中将配置按钮设为不可用
								// this.transferStateButtom = false;
								uni.showLoading({
									title: '配置中请稍等...',
									mask: true // 添加遮罩层，防止用户点击
								});
								break;
							case TransferState.SUCCESS:
								// 发送成功,将按钮设置可用
								// this.transferStateButtom =true;
								uni.hideLoading();
								uni.showToast({
									title: '配置设备wifi成功',
									mask: true, // 是否显示透明蒙层（防止触摸穿透）
									icon: 'none'
								});
								// this.transferStateButtom = true
								// 断开蓝牙
								// this.$store.dispatch('disconnectDevice');
								break;
							case TransferState.FAILED:
							case TransferState.TIMEOUT:
								// console.log("当前状态",currentTransferState)
								// console.log("当前按钮状态",this.transferStateButtom)
								// 发送失败提示,将按钮设置为可用
								// this.transferStateButtom =true;
								uni.hideLoading();
								uni.showToast({
									title: '配置设备wifi失败,请重试',
									mask: true, // 是否显示透明蒙层（防止触摸穿透）
									icon: 'none'
								});
								// 断开蓝牙
								// this.$store.dispatch('disconnectDevice');
								break;
						}
						// 改变现在的值
						this.lastTransferState = currentTransferState
					}
				}, 500)
			},
			// 格式化时间
			formatTime(timestamp) {
				if (!timestamp) return '';
				const date = new Date(timestamp);
				return date.toLocaleString();
			},

			// 从全局状态更新设备状态
			updateDeviceStatusFromGlobal() {
				const app = getApp();
				this.deviceStatusDetail = {
					...app.globalData.deviceStatusDetail
				};
			},

			// 加载库存状态
			async loadInventoryStatus() {
				try {
					const app = getApp();
					const deviceCode = app.globalData.connectedDeviceId;
					if (deviceCode && this.connectedDevice) {
						this.inventoryStatus = await dyeConsumptionManager.getInventoryStatus(deviceCode);
					}
				} catch (error) {
					console.error('加载库存状态失败:', error);
				}
			},

			// 刷新库存状态
			async refreshInventoryStatus() {
				try {
					const app = getApp();
					const deviceCode = app.globalData.connectedDeviceId;
					if (deviceCode) {
						uni.showLoading({
							title: '刷新中...'
						});
						this.inventoryStatus = await dyeConsumptionManager.getInventoryStatus(deviceCode, false);
						uni.hideLoading();
						uni.showToast({
							title: '刷新成功',
							icon: 'success'
						});
					}
				} catch (error) {
					uni.hideLoading();
					console.error('刷新库存状态失败:', error);
					uni.showToast({
						title: '刷新失败',
						icon: 'none'
					});
				}
			},
			scanDevices() {
				const app = getApp();
				if (!app.checkLoginStatus()) {
					uni.showToast({
						title: '未登录请先登录',
						icon: 'none',
						duration: 1500
					});
					return;
				}

				uni.scanCode({
					success: async (res) => {
						try {
							console.log('扫码结果:', res.result);
							const device = JSON.parse(res.result);

							if (!device.deviceId) {
								uni.showToast({
									title: '不是有效设备编号',
									icon: 'none',
									duration: 1500
								});
								return;
							}

							// 只发送一次请求
							const response = await apiService.device.bindDevice(device.deviceId);
							console.log("返回结果", response);
							
							// 给出提示框
							switch (response.code) {
								case 200:
									uni.showToast({
										title: response.message,
										icon: 'none',
										duration: 2000
									});
									setTimeout(() => {
										this.activeTab = 'bluetooth';
									}, 4000);
									this.wifiInfo.deviceId = device.deviceId;
									// this.startScanning();
									break;
								case 401:
									// getApp().clearLoginState();
									break;
								case 500:
									uni.showToast({
										title: response.message || '设备已被绑定,请重新',
										icon: 'none',
										duration: 1500
									});
									break;
								default:
									uni.showToast({
										title: response.message || '未知错误',
										icon: 'none',
										duration: 1500
									});
							}

							// app.globalData.connectedDeviceId = device.deviceId;
							app.globalData.deviceList = new Set([...app.globalData.deviceList, device
								.deviceId
							]);
							console.log("绑定机器的设备编号的设备编号", app.globalData.connectedDeviceId);

						} catch (e) {
							console.error('扫码绑定设备出错:', e);
							uni.showToast({
								title: e.message || '网络错误请检查网络后,重新扫码',
								icon: 'none',
								duration: 1500
							});
						}
					},
					fail: (err) => {
						uni.showToast({
							title: '扫码失败: ' + (err.errMsg || '未知错误'),
							icon: 'none',
							duration: 1500
						});
					}
				});
			},

			async initWifi() {
				console.log("WiFi模块开始初始化");
				try {
					// 初始化WiFi模块（微信原生API）
					uni.startWifi({
						success: () => {
							console.log("WiFi模块初始化成功");
							// 获取当前连接的wifi
							uni.getConnectedWifi({
								success: (res) => {
									const app = getApp();
									console.log('当前连接的Wi-Fi信息:', res.wifi);
									this.wifiInfo.SSID = res.wifi.SSID;
									// 安全地设置全局数据
									// const app = getApp();
									// if (!app.store) {
									//   app.store = {}; // 如果 store 不存在则初始化
									// }
									app.globalData.currentWifi = res.wifi;
								},
								fail: (err) => {
									console.error('获取Wi-Fi信息失败:', err);
									// 可以在这里处理错误，比如提示用户检查权限设置							
								}

							});
						},
						fail: (err) => {
							console.error("WiFi模块初始化失败", err);
						}
					});
				} catch (err) {
					console.error("调用WiFi接口异常", err);
				}
			},

			// 获取WiFi列表
			getWifiList() {
				console.log("获取位置等信息")
				uni.getSetting({
					success(res) {
						console.log("配置列表", res)
						if (!res.authSetting['scope.userLocation']) {
							uni.authorize({
								scope: 'scope.userLocation',
								success() {
									console.log('位置权限授权成功');
									this.requestWifiList();
								},
								fail() {
									console.log('位置权限授权失败');
								}
							});
						} else {
							this.requestWifiList();
						}
					}
				});
			},
			// connectWifi() {
			// 	this.$store.dispatch('writeDataChunked', {
			// 						wifiInfo: this.wifiInfo,
			// 						// len
			// 					}).then(() => {
			// 						// uni.hideLoading();
			// 						// uni.showToast({
			// 						// 	title: 'wifi配置成功',
			// 						// 	icon: 'success'
			// 						// });
			// 						//断开蓝牙
			// 						// this.$store.dispatch('disconnectDevice');
			// 					}).catch(err => {
			// 						console.log("配置失败请检查", err);
			// 						uni.hideLoading();
			// 						uni.showToast({
			// 							title: '连接网络错误,请确保蓝牙和wifi打开后重试',
			// 							icon: 'none',
			// 							duration: 2500
			// 						});
			// 						//断开蓝牙
			// 						this.$store.dispatch('disconnectDevice');
			// 					});
			// },

			connectWifi() {
				const isLogin = uni.getStorageSync('isLoggedIn');
				
				if (!isLogin) {
					uni.showModal({
						title: '未登录请先登录'
					});
					return;
				}
				
				if (!this.wifiInfo.deviceId) {
					uni.showToast({
						title: '请输入设备编号',
						icon: 'none'
					});
					return;
				}

				if (!this.wifiInfo.SSID) {
					uni.showToast({
						title: '请输入wifi账号',
						icon: 'none'
					});
					return;
				}

				if (!this.wifiInfo.password) {
					uni.showToast({
						title: '请输入wifi密码',
						icon: 'none'
					});
					return;
				}

				if (transferManager.transferState !== TransferState.IDLE) {
					uni.showToast({
						title: '配置中请稍后重试',
						mask: true, // 是否显示透明蒙层（防止触摸穿透）
						icon: 'none'
					});
					return
				}

				const app = getApp()

				const startTime = Date.now();
				const timeout = 60000; // 1分钟超时
				let retryCount = 0;
				const maxRetryCount = 5; // 最大重试次数

				const tryConnect = () => {
					const currentTime = Date.now();
					if (currentTime - startTime >= timeout) {
						uni.hideLoading();
						uni.showToast({
							title: '连接超时，未找到设备',
							icon: 'none'
						});
						return;
					}

					//断开蓝牙
					this.$store.dispatch('disconnectDevice');

					try {
						// 先搜索蓝牙
						this.$store.dispatch('startScan');

						// 根据wifiinfo的deviceid这个也表示蓝牙名称,从discoveredNameDevices找出设备
						const deviceList = this.$store.getters.deviceList;

						app.addDeviceList(this.wifiInfo.deviceId)

						console.log("所有设备", deviceList);
						const connectDevice = deviceList.find(device => device.name === this.wifiInfo.deviceId) ||
							null;
						console.warn("需要连接的设备信息", connectDevice);

						if (!connectDevice) {
							retryCount++;
							if (retryCount <= maxRetryCount) {
								console.log(`未找到设备，正在第${retryCount}次重试...`);
								setTimeout(tryConnect, 3000); // 3秒后重试
							} else {
								uni.hideLoading();
								uni.showToast({
									title: '未找到设备，请检查设备编号',
									icon: 'none'
								});
							}
							return;
						}

						// 连接蓝牙
						this.$store.dispatch('connectDevice', connectDevice)
							.then(() => {
								// 执行指令,通过蓝牙下发给wifi
								this.$store.dispatch('writeDataChunked', {
									wifiInfo: this.wifiInfo,
									// len
								}).then(() => {
									// uni.hideLoading();
									// uni.showToast({
									// 	title: 'wifi配置成功',
									// 	icon: 'success'
									// });
									//断开蓝牙
									// this.$store.dispatch('disconnectDevice');
								}).catch(err => {
									console.log("配置失败请检查", err);
									uni.hideLoading();
									uni.showToast({
										title: '连接网络错误,请确保蓝牙和wifi打开后重试',
										icon: 'none',
										duration: 2500
									});
									//断开蓝牙
									this.$store.dispatch('disconnectDevice');
								});
							})
							.catch(err => {
								console.log("配置失败请检查", err);
								uni.hideLoading();
								uni.showToast({
									title: '连接网络错误,请确保蓝牙和wifi打开后重试',
									icon: 'none',
									duration: 2500
								});
								//断开蓝牙
								this.$store.dispatch('disconnectDevice');
							});

					} catch (err) {
						console.log("蓝牙连接错误", err);
						retryCount++;
						if (retryCount <= maxRetryCount) {
							console.log(`发生异常，正在第${retryCount}次重试...`);
							setTimeout(tryConnect, 3000); // 3秒后重试
						} else {
							uni.hideLoading();
							uni.showToast({
								title: '连接过程中发生错误',
								icon: 'none'
							});
						}
					}
				}
				// 开始尝试连接
				tryConnect();
			},

			// 切换选项卡
			switchTab(tab) {
				this.activeTab = tab
			},

			/**
			 * 更新页面状态
			 */
			updatePageState() {
				this.isScanning = this.$store.state.isScanning
				this.deviceList = this.$store.state.deviceList
				this.connectedDevice = this.$store.state.connectedDevice ? {
					...this.$store.state.connectedDevice
				} : null
				this.logs = [...this.$store.state.logs]
			},

			/**
			 * 初始化蓝牙
			 */
			async initBluetooth() {
				try {
					uni.showLoading({
						title: '初始化蓝牙...',
						mask: true
					})

					const result = await this.$store.dispatch('initBluetooth')
					this.updatePageState()

					if (result) {
						uni.showToast({
							title: '蓝牙初始化成功',
							icon: 'success',
							duration: 1500
						})
					}
				} catch (error) {
					console.error('初始化蓝牙失败:', error)
					uni.showToast({
						title: '蓝牙初始化失败',
						icon: 'error',
						duration: 2000
					})
				} finally {
					uni.hideLoading()
				}
			},

			/**
			 * 切换搜索状态
			 */
			toggleScan() {
				if (this.isScanning) {
					this.stopScanning()
				} else {
					this.startScanning()
				}
			},

			/**
			 * 开始扫描蓝牙设备
			 */
			async startScanning() {
				try {
					const isLogin = uni.getStorageSync('isLoggedIn');
					
					if (!isLogin) {
						uni.showModal({
							title: '未登录请先登录'
						});
						return;
					}
					
					uni.showLoading({
						title: '开始扫描...',
						mask: true
					})

					// 搜索设备之前调用app的函数将设备列表更新一下
					const app = getApp()
					await app.getDeviceConnectId()

					const result = await this.$store.dispatch('startScan')
					this.updatePageState()

					if (result) {
						uni.showToast({
							title: '开始扫描设备',
							icon: 'success',
							duration: 1500
						})

						// 定时更新设备列表
						const updateInterval = setInterval(() => {
							if (!this.$store.state.isScanning) {
								clearInterval(updateInterval)
								return
							}
							this.deviceList = [...this.$store.state.deviceList]
							if(!this.$store.state.connectedDevice) {
								this.connectedDevice = null;
							}
						}, 1000)
					}
				} catch (error) {
					console.error('开始扫描失败:', error)
					this.updatePageState()
				} finally {
					uni.hideLoading()
				}
			},

			/**
			 * 停止扫描蓝牙设备
			 */
			async stopScanning() {
				try {
					await this.$store.dispatch('stopScan')
					this.updatePageState()

					uni.showToast({
						title: '已停止扫描',
						icon: 'success',
						duration: 1500
					})
				} catch (error) {
					console.error('停止扫描失败:', error)
					this.updatePageState()
				}
			},

			/**
			 * 连接选定的蓝牙设备
			 */
			async connectDevice(device) {
				const app = getApp()
				if(app.isWifiConnection() && app.globalData.deviceStatus) {
					uni.showToast({
						title: '设备已通过wifi连接,请先断开wifi连接',
						icon: 'none',
						duration: 2000
					})
					return;
				}

				if (this.connectedDevice && this.connectedDevice.deviceId === device.deviceId) {
					uni.showToast({
						title: '设备已连接',
						icon: 'none',
						duration: 1500
					})
					return
				}

				try {
					await this.$store.dispatch('connectDevice', device)
					this.updatePageState()

					uni.showToast({
						title: '连接成功',
						icon: 'success',
						duration: 2000
					})
				} catch (error) {
					console.error('连接设备失败:', error)
					this.updatePageState()

					uni.showToast({
						title: '连接失败',
						icon: 'error',
						duration: 2000
					})
				}
			},

			/**
			 * 断开当前已连接的设备
			 */
			async disconnectDevice() {
				try {
					// 如果正在使用设备，先结束使用
					if (this.isUsingDevice) {
						await this.endUsage();
					}

					await this.$store.dispatch('disconnectDevice')
					this.updatePageState()

					uni.showToast({
						title: '已断开连接',
						icon: 'success',
						duration: 1500
					})
				} catch (error) {
					console.error('断开连接失败:', error)
					this.updatePageState()

					uni.showToast({
						title: '断开失败',
						icon: 'error',
						duration: 2000
					})
				}
			},

			/**
			 * 开始使用设备
			 */
			async startUsage() {
				if (!this.connectedDevice) {
					uni.showToast({
						title: '请先连接设备',
						icon: 'none'
					});
					return;
				}

				try {
					// 模拟设备ID，实际应该从connectedDevice获取
					const deviceId = 1; // 这里应该是真实的设备ID

					const response = await apiService.mall.deviceUsage.startUsage(
						deviceId);
					if (response.code === 200) {
						this.isUsingDevice = true;
						this.usageStartTime = new Date();
						this.startUsageTimer();

						uni.showToast({
							title: '开始使用记录',
							icon: 'success'
						});
					} else {
						uni.showToast({
							title: response.message || '开始使用失败',
							icon: 'none'
						});
					}
				} catch (error) {
					console.error('开始使用失败:', error);
					uni.showToast({
						title: '开始使用失败',
						icon: 'none'
					});
				}
			},

			/**
			 * 结束使用设备
			 */
			async endUsage() {
				if (!this.isUsingDevice) {
					return;
				}

				try {
					// 模拟设备ID，实际应该从connectedDevice获取
					const deviceId = 1; // 这里应该是真实的设备ID

					const response = await apiService.mall.deviceUsage.endUsage(
						deviceId);
					if (response.code === 200) {
						this.isUsingDevice = false;
						this.stopUsageTimer();

						uni.showToast({
							title: '使用记录已保存',
							icon: 'success'
						});
					} else {
						uni.showToast({
							title: response.message || '结束使用失败',
							icon: 'none'
						});
					}
				} catch (error) {
					console.error('结束使用失败:', error);
					uni.showToast({
						title: '结束使用失败',
						icon: 'none'
					});
				}
			},

			/**
			 * 开始使用时长计时器
			 */
			startUsageTimer() {
				this.usageTimer = setInterval(() => {
					if (this.usageStartTime) {
						const now = new Date();
						const diff = now - this.usageStartTime;
						this.usageDuration = this.formatDuration(diff);
					}
				}, 1000);
			},

			/**
			 * 停止使用时长计时器
			 */
			stopUsageTimer() {
				if (this.usageTimer) {
					clearInterval(this.usageTimer);
					this.usageTimer = null;
				}
				this.usageDuration = '00:00:00';
				this.usageStartTime = null;
			},

			/**
			 * 格式化使用时长
			 */
			formatDuration(milliseconds) {
				const seconds = Math.floor(milliseconds / 1000);
				const hours = Math.floor(seconds / 3600);
				const minutes = Math.floor((seconds % 3600) / 60);
				const secs = seconds % 60;

				return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
			}
		}
	}
</script>

<style>
	/* 保持原有的样式不变 */
	.content {
		display: flex;
		flex-direction: column;
		align-items: stretch;
		height: 100vh;
		background-color: #ffffff;
		position: relative;
		width: 100%;
		box-sizing: border-box;
		overflow-x: hidden;
	}

	.header {
		background-color: #4285f4;
		color: white;
		position: relative;
		padding: 40px 15px 15px;
		display: flex;
		align-items: center;
		justify-content: center;
		width: 100%;
		box-sizing: border-box;
	}

	.title-center {
		font-size: 18px;
		font-weight: bold;
		text-align: center;
		flex: 1;
	}

	.device-list {
		flex: 1;
		padding: 0 20rpx;
		padding-bottom: 200rpx;
		/* 为固定按钮留出空间 */
		margin-bottom: 20rpx;
	}


	.device-item {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 25rpx 0;
		border-bottom: 1rpx solid #f0f0f0;
	}

	.device-info {
		flex: 1;
		display: flex;
		flex-direction: column;
	}

	.device-name {
		font-size: 30rpx;
		font-weight: bold;
		color: #333;
		margin-bottom: 8rpx;
	}

	.device-id {
		font-size: 22rpx;
		color: #999;
		word-break: break-all;
		margin-bottom: 8rpx;
	}

	.device-rssi {
		font-size: 22rpx;
		color: #666;
	}

	.device-actions {
		margin-left: 20rpx;
	}

	.connect-btn {
		background-color: #1aad19;
		color: white;
		padding: 10rpx 20rpx;
		border-radius: 8rpx;
		font-size: 24rpx;
	}

	.connect-btn.connected {
		background-color: #999;
	}

	.empty-tip {
		text-align: center;
		padding: 40rpx 0;
		font-size: 26rpx;
		color: #999;
	}

	.action-buttons {
		/* padding-bottom: calc(20rpx + constant(safe-area-inset-bottom));
		padding-bottom: calc(20rpx + env(safe-area-inset-bottom)); */
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		background: white;
		padding: 20rpx;
		box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.1);
		z-index: 100;
		display: flex;
		flex-direction: column;
	}

	.scan-btn {
		height: 50px;
		background-color: #4285f4;
		border-radius: 25px;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-bottom: 15px;
	}

	.scan-btn.scanning {
		background-color: #ff9900;
	}

	.disconnect-btn {
		height: 50px;
		background-color: #e64340;
		border-radius: 25px;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.scan-btn-text {
		color: white;
		font-size: 16px;
	}

	.usage-buttons {
		margin-bottom: 15px;
	}

	.start-usage-btn {
		height: 50px;
		background-color: #1aad19;
		border-radius: 25px;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-bottom: 10px;
	}

	.end-usage-btn {
		height: 50px;
		background-color: #e64340;
		border-radius: 25px;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-bottom: 10px;
	}

	.usage-btn-text {
		color: white;
		font-size: 16px;
	}

	.usage-info {
		text-align: center;
		padding: 10px;
		background-color: #f8f8f8;
		border-radius: 10px;
	}

	.usage-time {
		font-size: 14px;
		color: #333;
		font-weight: bold;
	}

	.log-container {
		padding: 20rpx;
		border-top: 1rpx solid #f0f0f0;
	}

	.section-title {
		font-size: 28rpx;
		font-weight: bold;
		color: #333;
		margin-bottom: 20rpx;
		display: block;
	}

	.log-list {
		max-height: 200rpx;
	}

	.log-item {
		font-size: 24rpx;
		color: #666;
		padding: 5rpx 0;
		border-bottom: 1rpx dashed #eee;
	}


	/* 颜色变量 */
	page {
		background-color: #F7F9FC;
		height: 100%;
	}

	.device-page {
		padding: 0 24rpx;
		height: 100vh;
		display: flex;
		flex-direction: column;
		background-color: #F7F9FC;
	}

	.wifi-property-btn {
		background-color: #1A8CFF;
		color: white;
		width: 90%;
		height: 90rpx;
		border-radius: 45rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 32rpx;
		margin-top: 30rpx;
		margin-bottom: 40rpx;
	}

	/* 顶部标题 */
	.page-header {
		padding: 40rpx 0 30rpx;
		text-align: center;
	}

	.header-title {
		font-size: 38rpx;
		font-weight: 600;
		color: #333;
	}

	/* 连接方式选项卡 */
	.connection-tabs {
		display: flex;
		justify-content: space-between;
		background-color: #FFFFFF;
		border-radius: 16rpx;
		padding: 0 40rpx;
		margin-bottom: 30rpx;
		box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.04);
	}

	.tab-item {
		padding: 24rpx 0;
		font-size: 30rpx;
		color: #666;
		position: relative;
		text-align: center;
		flex: 1;
	}

	.tab-item.active {
		color: #1A8CFF;
		font-weight: 500;
	}

	.tab-indicator {
		position: absolute;
		bottom: 0;
		left: 50%;
		transform: translateX(-50%);
		width: 60rpx;
		height: 6rpx;
		background-color: #1A8CFF;
		border-radius: 3rpx;
	}

	/* 设备信息卡片 */
	.device-info-card {
		background-color: #FFFFFF;
		border-radius: 16rpx;
		padding: 32rpx;
		margin-bottom: 40rpx;
		box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.04);
	}

	.info-item {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 32rpx;
	}

	.info-item:last-child {
		margin-bottom: 0;
	}

	.info-label {
		font-size: 30rpx;
		color: #666;
	}

	.info-value {
		font-size: 30rpx;
		color: #333;
		font-weight: 500;
	}

	.password-field {
		display: flex;
		align-items: center;
	}

	.show-password {
		font-size: 26rpx;
		color: #1A8CFF;
		margin-left: 20rpx;
	}

	/* 连接按钮 */
	.connect-btn {
		background: linear-gradient(90deg, #1A8CFF, #4DB8FF);
		color: #fff;
		border-radius: 50rpx;
		font-size: 34rpx;
		height: 96rpx;
		line-height: 96rpx;
		margin: 0 30rpx;
		border: none;
		box-shadow: 0 8rpx 24rpx rgba(26, 140, 255, 0.3);
	}

	.connect-btn::after {
		border: none;
	}

	.connect-btn:active {
		opacity: 0.9;
	}

	/* 底部导航栏 */
	.tab-bar {
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		display: flex;
		justify-content: space-around;
		background-color: #FFFFFF;
		padding: 16rpx 0 20rpx;
		border-top: 1rpx solid #EBEDF0;
		box-shadow: 0 -4rpx 12rpx rgba(0, 0, 0, 0.04);
	}

	.tab-bar-item {
		display: flex;
		flex-direction: column;
		align-items: center;
	}

	.tab-icon {
		width: 48rpx;
		height: 48rpx;
		margin-bottom: 8rpx;
	}

	.tab-text {
		font-size: 24rpx;
		color: #666;
	}

	.tab-bar-item.active .tab-text {
		color: #1A8CFF;
	}

	/* wifi连接样式 */
	.wifi-connection {
		padding: 40rpx;
	}

	.wifi-form {
		background-color: #FFFFFF;
		border-radius: 16rpx;
		padding: 32rpx;
		margin-bottom: 40rpx;
		box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.04);
	}

	.form-item {
		display: flex;
		align-items: center;
		margin-bottom: 32rpx;
		padding-bottom: 32rpx;
		border-bottom: 1rpx solid #f0f0f0;
	}

	.form-item:last-child {
		margin-bottom: 0;
		padding-bottom: 0;
		border-bottom: none;
	}

	.form-label {
		font-size: 30rpx;
		color: #666;
		width: 150rpx;
	}

	.form-input {
		flex: 1;
		font-size: 30rpx;
		color: #333;
		height: 60rpx;
		padding: 0 20rpx;
	}

	.show-password {
		font-size: 26rpx;
		color: #1A8CFF;
		margin-left: 20rpx;
	}

	.connect-btn {
		background: linear-gradient(90deg, #1A8CFF, #4DB8FF);
		color: #fff;
		border-radius: 50rpx;
		font-size: 34rpx;
		height: 96rpx;
		line-height: 96rpx;
		margin: 0 30rpx;
		border: none;
		box-shadow: 0 8rpx 24rpx rgba(26, 140, 255, 0.3);
	}

	.connect-btn::after {
		border: none;
	}

	.connect-btn:active {
		opacity: 0.9;
	}

	/* 联网模块样式 */
	.network-connection {
		padding: 30rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		height: 100%;
	}

	.connection-row {
		display: flex;
		justify-content: space-around;
		width: 100%;
		background-color: #FFFFFF;
		border-radius: 16rpx;
		padding: 30rpx 0;
		margin-bottom: 30rpx;
		box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.04);
	}

	.connection-item {
		display: flex;
		flex-direction: column;
		align-items: center;
		flex: 1;
	}

	.connection-label {
		font-size: 30rpx;
		color: #333;
		margin-bottom: 15rpx;
	}

	.connection-status {
		font-size: 28rpx;
		color: #999;
	}

	.connection-status.active {
		color: #1A8CFF;
	}

	.scan-device-btn {
		background-color: #1A8CFF;
		color: white;
		width: 90%;
		height: 90rpx;
		border-radius: 45rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 32rpx;
		margin-top: 30rpx;
		margin-bottom: 40rpx;
	}

	/* 设备状态卡片样式 */
	.device-status-card,
	.inventory-status-card {
		background-color: #ffffff;
		border-radius: 16rpx;
		margin: 20rpx;
		padding: 30rpx;
		box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
	}

	.status-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 20rpx;
	}

	.status-title {
		font-size: 32rpx;
		font-weight: bold;
		color: #333;
	}

	.status-indicator {
		padding: 8rpx 16rpx;
		border-radius: 20rpx;
		background-color: #f5f5f5;
		color: #999;
		font-size: 24rpx;
	}

	.status-indicator.online {
		background-color: #e8f5e8;
		color: #52c41a;
	}

	.status-details {
		display: flex;
		flex-direction: column;
		gap: 16rpx;
	}

	.status-item {
		display: flex;
		justify-content: space-between;
		align-items: center;
	}

	.status-label {
		font-size: 28rpx;
		color: #666;
	}

	.status-value {
		font-size: 28rpx;
		color: #333;
		font-weight: 500;
	}

	.refresh-btn {
		padding: 8rpx 16rpx;
		border-radius: 20rpx;
		background-color: #f0f9ff;
		color: #1890ff;
		font-size: 24rpx;
	}

	/* 库存预警样式 */
	.alert-section {
		margin-bottom: 20rpx;
	}

	.alert-title {
		font-size: 28rpx;
		font-weight: bold;
		color: #ff6b35;
		margin-bottom: 16rpx;
	}

	.alert-item {
		margin-bottom: 8rpx;
	}

	.alert-text {
		font-size: 24rpx;
		padding: 4rpx 12rpx;
		border-radius: 12rpx;
	}

	.alert-text.warning {
		background-color: #fff3cd;
		color: #856404;
	}

	.alert-text.critical {
		background-color: #f8d7da;
		color: #721c24;
	}

	.inventory-summary {
		display: flex;
		justify-content: space-between;
		padding-top: 20rpx;
		border-top: 1px solid #eee;
	}

	.summary-text {
		font-size: 24rpx;
		color: #666;
	}



	/* 响应式适配 */
	@media screen and (max-width: 390px) {

		.content,
		.header {
			width: 100% !important;
		}
	}

	/* 安全区域适配 */
	@supports (padding-bottom: constant(safe-area-inset-bottom)) or (padding-bottom: env(safe-area-inset-bottom)) {
		.content {
			padding-bottom: calc(20px + constant(safe-area-inset-bottom));
			padding-bottom: calc(20px + env(safe-area-inset-bottom));
		}
	}
</style>