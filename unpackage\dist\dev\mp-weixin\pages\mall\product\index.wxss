
.content {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
}
.header {
  display: flex;
  align-items: center;
  width: 100%;
  margin-bottom: 20px;
}
.back-btn {
  margin-right: 20px;
}
.back-icon {
  font-size: 24px;
}
.title {
  font-size: 18px;
  font-weight: bold;
}
.product-images {
  width: 100%;
  height: 300px;
  margin-bottom: 30px;
}
.swiper-box {
  width: 100%;
  height: 100%;
}
.swiper-box image {
  width: 100%;
  height: 100%;
}

/* 新增商品详情样式 */
.product-detail {
  width: 100%;
  padding: 15px;
  background-color: #fff;
  border-radius: 10px;
  margin-bottom: 20px;
}
.product-name {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 10px;
}
.product-price {
  font-size: 20px;
  color: #f44336;
  font-weight: bold;
  margin-bottom: 10px;
}
.product-desc {
  font-size: 14px;
  color: #666;
  line-height: 1.5;
}
.btn-container {
  margin-top: 20px;
  width: 100%;
}
.btn {
  background-color: #4285f4;
  color: white;
  padding: 10px;
  border-radius: 5px;
  text-align: center;
}
