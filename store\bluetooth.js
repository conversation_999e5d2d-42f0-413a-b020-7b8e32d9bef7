// store/bluetooth.js

// #ifndef VUE3
/******************** Vue 2 版本 ********************/
import Vue from 'vue'
import Vuex from 'vuex'
import {
	getCommand,
	BLUETOOTH_COMMANDS
} from '../utils/bleCommandSet.js';

import {
	startPollingWifi
} from './wifi.js';

import {
	writeDataChunked,
	transferManager
} from '@/utils/writeDataChunked';

Vue.use(Vuex)
// #endif

// #ifdef VUE3
/******************** Vue 3 版本 ********************/
import {
	createStore
} from 'vuex';

import {
	getCommand,
	BLUETOOTH_COMMANDS
} from '../utils/bleCommandSet.js';


import {
	startPollingWifi
} from './wifi.js';
import dyeConsumptionManager from '../utils/dyeConsumptionManager.js';
import {
	from
} from 'form-data'


import {
	writeDataChunked,
	transferManager
} from '@/utils/writeDataChunked'
import connectionStateValidator from '@/utils/connectionStateValidator'
// #endif

// 蓝牙服务常量	(两种环境共用)
const BLUETOOTH_SERVICE_UUID = '55535343-fe7d-4ae5-8fa9-9fafd205e455'
const WRITE_CHARACTERISTIC_UUID = '*************-43f4-a8d4-ecbe34729bb3'
const NOTIFY_CHARACTERISTIC_UUID = '49535343-1e4d-4bd9-ba61-23c647249616'

// 工具函数（两种环境共用）
const arrayBufferToHex = (buffer) => {
	if (!buffer) return ''
	const hexArr = Array.prototype.map.call(
		new Uint8Array(buffer),
		bit => ('00' + bit.toString(16)).slice(-2)
	)
	return hexArr.join('')
}

function arrayToArrayBuffer(arr) {
	const buffer = new ArrayBuffer(arr.length);
	const dataView = new Uint8Array(buffer);
	for (let i = 0; i < arr.length; i++) {
		dataView[i] = arr[i] || 0; // 处理 undefined（默认填 0）
	}
	return buffer;
}

// 状态持久化键名
const STORE_PERSISTENCE_KEY = 'vuex_store_state';

// 从本地存储恢复状态
function restorePersistedState() {
	try {
		const persistedState = uni.getStorageSync(STORE_PERSISTENCE_KEY);
		if (persistedState) {
			console.log('恢复持久化状态:', persistedState);
			return {
				connectedDevice: persistedState.connectedDevice || null,
				// 其他需要持久化的状态可以在这里添加
			};
		}
	} catch (error) {
		console.error('恢复持久化状态失败:', error);
	}
	return {};
}

// 保存状态到本地存储
function saveStateToStorage(state) {
	try {
		const stateToSave = {
			connectedDevice: state.connectedDevice,
			// 其他需要持久化的状态可以在这里添加
		};
		uni.setStorageSync(STORE_PERSISTENCE_KEY, stateToSave);
		console.log('状态已保存到本地存储:', stateToSave);
	} catch (error) {
		console.error('保存状态到本地存储失败:', error);
	}
}

// 获取初始状态
function getInitialState() {
	const persistedState = restorePersistedState();
	return {
		isScanning: false,
		deviceList: [],
		// connectedDevice: persistedState.connectedDevice || null,
		connectedDevice:  null,
		logs: [],
		discoveredDevices: new Map(),
		discoveredNameDevices: new Map(),
		bluetoothAdapterReady: false,
		error: null,
		discharging: false
	};
}

// #ifndef VUE3
export default new Vuex.Store({
			// #endif

			// #ifdef VUE3
			export default createStore({
				// #endif
				state: getInitialState(),

				mutations: {
					SET_SCANNING(state, isScanning) {
						state.isScanning = isScanning
					},

					//映射蓝牙id和设备参数
					ADD_DEVICE(state, device) {
						if (!state.discoveredDevices.has(device.deviceId)) {
							state.discoveredDevices.set(device.deviceId, device)
							state.deviceList = Array.from(state.discoveredDevices.values())
						}
					},

					// 映射蓝牙名和设备参数
					ADD_MAP_DEVICENAME(state, device) {
						if (!state.discoveredNameDevices.has(device.name)) {
							state.discoveredNameDevices.set(device.name, device)
							state.deviceList = Array.from(state.discoveredNameDevices.values())
						}
					},

					CLEAR_DEVICES(state) {
						state.deviceList = []
						state.discoveredDevices.clear()
						state.discoveredNameDevices.clear()
					},

					SET_CONNECTED_DEVICE(state, device) {
						state.connectedDevice = device;
						// state.connectedDevice = 'bluetooth'
						// 保存状态到本地存储
						saveStateToStorage(state);
					},

					SET_MOCK_CONNECTED_DEVICE(state, device) {
						state.connectedDevice = device
					},

					CLEAR_CONNECTED_DEVICE(state) {
						state.connectedDevice = null;
						// 保存状态到本地存储
						saveStateToStorage(state);
					},

					ADD_LOG(state, message) {
						console.log("日志", message)
						const timestamp = new Date().toLocaleTimeString()
						state.logs.unshift(`[${timestamp}] ${message}`)
						if (state.logs.length > 50) state.logs.pop()
						// console.log("输出日志成功")
					},

					SET_BLUETOOTH_ADAPTER_READY(state, ready) {
						state.bluetoothAdapterReady = ready
					},

					SET_ERROR(state, error) {
						state.error = error
					},


				},

				actions: {
					/**
					 * 模拟连接设备
					 */
					async mockConnect({
						commit,
						state
					}) {
						try {
							console.log('mockConnect action 被调用')
							const mockDevice = {
								deviceId: 'mock_connected_device',
								name: '芭佰邑-智能染发机',
								serviceId: 'mock_service_id',
								writeCharId: 'mock_write_char',
								notifyCharId: 'mock_notify_char',
								RSSI: -45
							}

							console.log('准备设置模拟设备:', mockDevice)
							commit('SET_MOCK_CONNECTED_DEVICE', mockDevice)
							commit('ADD_LOG', '模拟设备连接成功')
							console.log('模拟设备设置完成，当前状态:', state.connectedDevice)

							return true
						} catch (error) {
							console.error('mockConnect 错误:', error)
							commit('ADD_LOG', `模拟连接失败: ${error.message}`)
							throw error
						}
					},

					/**
					 * 添加指令到队列
					 * @param {Object} context - Vuex上下文
					 * @param {Object} payload - 参数对象
					 * @param {Object} payload.command - 指令对象
					 * @param {boolean} [payload.immediate=false] - 是否立即执行
					 */
					async addToCommandQueue(context, {
						command,
						immediate = false
					}) {
						const {
							commit,
							dispatch
						} = context;

						if (!command) {
							throw new Error('无效的指令对象');
						}

						const app = getApp();
						app.addToCommandQueue(command);
						commit('ADD_LOG', `指令已添加到队列: ${command.description || '无描述指令'}`);

						if (immediate && !app.globalData.isExecutingQueue) {
							await dispatch('executeCommandQueue');
						}
						console.log("指令以添加到队列")
					},

					/**
					 * 清空指令队列
					 */
					clearCommandQueue({
						commit
					}) {
						console.log("清空队列中")
						const app = getApp();
						app.clearCommandQueue();
						commit('ADD_LOG', '指令队列已清空');
						console.log("队列已经清空")
					},

					/**
					 * 暂停执行
					 */
					async pauseExecution({
						commit,
						dispatch
					}) {
						try {
							const app = getApp();

							if (!app.globalData.isExecutingQueue) {
								commit('ADD_LOG', '当前没有正在执行的队列');
								return false;
							}

							if (app.globalData.isPaused) {
								commit('ADD_LOG', '已经处于暂停状态');
								return true;
							}

							// 保存当前队列状态
							app.setPausedQueueState({
								commandQueue: [...app.globalData.commandQueue],
								currentCommand: app.globalData.currentCommand,
								isExecutingQueue: app.globalData.isExecutingQueue
							});

							// 发送暂停指令
							const pauseCommand = getCommand('pauseDispense');
							if (pauseCommand) {
								commit('ADD_LOG', '正在发送暂停指令...');
								await dispatch('writeData', pauseCommand.value);
								commit('ADD_LOG', '暂停指令已发送，等待设备确认');
							}

							// 注意：不再立即设置暂停状态，等待设备确认响应
							// 状态将在收到设备确认响应时更新

							return true;
						} catch (error) {
							commit('ADD_LOG', `暂停执行失败: ${error.message}`);
							throw error;
						}
					},

					/**
					 * 恢复执行
					 */
					async resumeExecution({
						commit,
						dispatch
					}) {
						try {
							const app = getApp();

							if (!app.globalData.isPaused) {
								commit('ADD_LOG', '当前不在暂停状态');
								return false;
							}

							if (!app.globalData.pausedQueueState) {
								commit('ADD_LOG', '没有保存的队列状态，无法恢复');
								return false;
							}

							commit('ADD_LOG', '正在发送继续指令...');

							// 发送恢复出料指令
							const continuedischarging = getCommand('continue_discharging');
							console.log("继续指令", continuedischarging)

							await dispatch('writeData', continuedischarging.value)
							commit('ADD_LOG', '继续指令已发送，等待设备确认');

							// 注意：不再立即设置执行状态，等待设备确认响应
							// 状态将在收到设备确认响应时更新

							return true;
						} catch (error) {
							commit('ADD_LOG', `恢复执行失败: ${error.message}`);
							throw error;
						}
					},

					/**
					 * 执行指令队列
					 */
					async executeCommandQueue({
						state,
						commit,
						dispatch
					}) {
						try {
							const app = getApp();

							// 先判断有没有连接
							if (!state.connectedDevice) {
								return;
							}

							// 检查是否处于暂停状态
							if (app.globalData.isPaused) {
								commit('ADD_LOG', '当前处于暂停状态，无法执行队列');
								return;
							}

							if (app.globalData.commandQueue.length === 0) {
								// 清空按钮之类的状态
								app.allExecutionCompleted()

								// 出料完成代码,给设备发送
								console.log('全部出料完成');
								const allclear = getCommand('dischargingCompleted');
								dispatch('writeData', allclear.value)
								return;
							}

							app.setExecutingQueue(true);

							try {
								const command = app.globalData.commandQueue[0];
								console.log("当前执行的指令")
								// 明确检查 END_DISPENSE 指令（假设 opcode 在 command.value[2]）
								// if (command.value?.[2] === BLUETOOTH_COMMANDS.OPCODES.END_DISPENSE) {
								// 	console.log("收到全部完成指令，清空队列");
								// 	dispatch('clearCommandQueue', command);
								// 	return;
								// }

								app.setCurrentCommand(command);
								// commit('SET_EXECUTING_QUEUE',true)
								console.log("指令", command.value)
								commit('ADD_LOG', `开始执行指令: ${command.description}`);
								// 发送指令
								try {
									await dispatch('writeData', command.value);
									// 将app里面的当前执行指令放入
									app.globalData.currentCommand = command;
								} catch (error) {
									console.error('执行命令队列失败:', error);
									// 在这里处理错误
									return
								}
								commit('ADD_LOG', `指令发送成功: ${command.description}`);


								// 指令执行成功，从队列移除
								app.removeFromCommandQueue();
								// 注意：不要立即清空 currentCommand，保留到出料完成时再清空

							} catch (error) {
								commit('ADD_LOG', `指令执行失败: ${command.description} - ${error.message}`);
								// 可以根据需要决定是否继续执行队列
							}
						} finally {
							// commit('SET_EXECUTING_QUEUE', false);
							commit('ADD_LOG', '指令队列执行完成');
						}
					},

					// 检查运行环境
					checkEnvironment() {
						// #ifdef MP-WEIXIN
						const systemInfo = uni.getSystemInfoSync()
						const isDevTools = systemInfo.platform === 'devtools'
						return {
							isDevTools,
							platform: systemInfo.platform,
							system: systemInfo.system
						}
						// #endif

						// #ifndef MP-WEIXIN
						return {
							isDevTools: false,
							platform: 'unknown',
							system: 'unknown'
						}
						// #endif
					},

					// 检查蓝牙权限
					async checkBluetoothPermission({
						commit,
						dispatch
					}) {
						try {
							const env = dispatch('checkEnvironment')

							// 如果是开发者工具，给出提示
							if (env.isDevTools) {
								commit('ADD_LOG', '检测到开发者工具环境，蓝牙功能需要在真机上测试')
								return await new Promise((resolve) => {
									uni.showModal({
										title: '开发者工具提示',
										content: '蓝牙功能在开发者工具中无法正常使用，请在真机上测试。是否继续尝试初始化？',
										confirmText: '继续',
										cancelText: '取消',
										success: (res) => {
											resolve(res.confirm)
										}
									})
								})
							}

							// #ifdef MP-WEIXIN
							const setting = await new Promise((resolve, reject) => {
								uni.getSetting({
									success: resolve,
									fail: reject
								})
							})

							commit('ADD_LOG', `当前权限状态: ${JSON.stringify(setting.authSetting)}`)

							if (setting.authSetting['scope.bluetooth'] === false) {
								// 用户之前拒绝过权限，需要引导到设置页面
								return await new Promise((resolve) => {
									uni.showModal({
										title: '需要蓝牙权限',
										content: '请在设置中开启蓝牙权限，以便扫描和连接蓝牙设备',
										confirmText: '去设置',
										cancelText: '取消',
										success: (res) => {
											if (res.confirm) {
												uni.openSetting({
													success: (
														settingRes) => {
														if (settingRes
															.authSetting[
																'scope.bluetooth'
															]) {
															commit('ADD_LOG',
																'蓝牙权限已开启'
															)
															resolve(
																true)
														} else {
															commit('ADD_LOG',
																'蓝牙权限仍未开启'
															)
															resolve(
																false)
														}
													},
													fail: () => {
														commit('ADD_LOG',
															'打开设置页面失败'
														)
														resolve(
															false)
													}
												})
											} else {
												commit('ADD_LOG', '用户取消开启蓝牙权限')
												resolve(false)
											}
										}
									})
								})
							} else if (setting.authSetting['scope.bluetooth'] === undefined) {
								// 尝试请求权限
								try {
									await uni.authorize({
										scope: 'scope.bluetooth'
									})
									commit('ADD_LOG', '蓝牙权限请求成功')
									return true
								} catch (authErr) {
									commit('ADD_LOG', `蓝牙权限请求失败: ${authErr.errMsg}`)
									return false
								}
							}
							// #endif

							return true
						} catch (err) {
							console.error('检查蓝牙权限失败:', err)
							commit('ADD_LOG', `权限检查异常: ${err.errMsg || err.message}`)
							return false
						}
					},

					// 初始化蓝牙适配器
					async initBluetooth({
						commit,
						dispatch
					}) {
						try {
							// 检查运行环境
							const env = dispatch('checkEnvironment')
							commit('ADD_LOG', `运行环境: ${env.platform}, 系统: ${env.system}`)

							// 先检查权限
							const hasPermission = await dispatch('checkBluetoothPermission')
							if (!hasPermission) {
								throw new Error('用户拒绝蓝牙权限')
							}

							// 如果是开发者工具，提供模拟模式
							if (env.isDevTools) {
								commit('ADD_LOG', '开发者工具环境，启用模拟模式')
								commit('SET_BLUETOOTH_ADAPTER_READY', true)

								// 添加一些模拟设备
								// const mockDevices = [{
								// 	deviceId: 'mock_device_1',
								// 	name: '模拟蓝牙设备1',
								// 	RSSI: -50
								// },
								// {
								// 	deviceId: 'mock_device_2',
								// 	name: '模拟蓝牙设备2',
								// 	RSSI: -60
								// }
								// ]

								// setTimeout(() => {
								// 	mockDevices.forEach(device => {
								// 		commit('ADD_DEVICE', device)
								// 		commit('ADD_LOG',
								// 			`发现模拟设备: ${device.name}`)
								// 	})
								// }, 1000)

								commit('ADD_LOG', '模拟蓝牙环境初始化完成，请在真机上测试实际功能')
								return true
							}

							// 真机环境下的正常初始化
							const res = await new Promise((resolve, reject) => {
								uni.openBluetoothAdapter({
									success: resolve,
									fail: reject
								})
							})

							console.log("初始化蓝牙结果", res)
							commit('ADD_LOG', '蓝牙适配器初始化成功')
							commit('SET_BLUETOOTH_ADAPTER_READY', true)

							// 监听蓝牙适配器状态变化
							uni.onBluetoothAdapterStateChange((res) => {
								commit('ADD_LOG', `蓝牙状态变化: ${JSON.stringify(res)}`)
								if (!res.available) {
									commit('SET_SCANNING', false)
									commit('SET_BLUETOOTH_ADAPTER_READY', false)
									dispatch('resetBluetoothState')
								}
							})
							return true
						} catch (err) {
							console.error('蓝牙初始化失败:', err)

							// 根据不同的错误码提供不同的处理方案
							let title = '蓝牙初始化失败'
							let content = '请检查蓝牙设置'
							let showRetry = true

							// 检查是否是开发者工具环境的错误
							const env = dispatch('checkEnvironment')
							if (env.isDevTools || err.errMsg?.includes('Mac') || err.errMsg
								?.includes('仅支持')) {
								title = '开发者工具环境'
								content = '蓝牙功能在开发者工具中无法正常使用，已启用模拟模式。请在真机上测试完整功能。'
								showRetry = false

								// 在开发者工具中启用模拟模式
								commit('SET_BLUETOOTH_ADAPTER_READY', true)
								commit('ADD_LOG', '开发者工具模拟模式已启用')

								// 添加模拟设备
								const mockDevices = [{
										deviceId: 'mock_device_1',
										name: '模拟蓝牙设备1',
										RSSI: -50
									},
									{
										deviceId: 'mock_device_2',
										name: '模拟蓝牙设备2',
										RSSI: -60
									}
								]

								setTimeout(() => {
									mockDevices.forEach(device => {
										commit('ADD_DEVICE', device)
										commit('ADD_LOG',
											`发现模拟设备: ${device.name}`)
									})
								}, 1000)

								uni.showModal({
									title: title,
									content: content,
									showCancel: false,
									confirmText: '确定'
								})

								return true
							}

							if (err.errCode === 10001) {
								title = '蓝牙未开启'
								content = '请先开启手机蓝牙功能，然后重试'
							} else if (err.errCode === 10004) {
								title = '蓝牙权限被拒绝'
								content = '请在设置中开启蓝牙权限'
								showRetry = false
							} else if (err.errCode === 10005) {
								title = '系统版本过低'
								content = '当前系统版本不支持蓝牙功能'
								showRetry = false
							} else if (err.errCode === 10009) {
								title = '蓝牙适配器不可用'
								content = '请检查设备是否支持蓝牙功能'
								showRetry = false
							} else {
								content = err.errMsg || err.message || '未知错误，请重试'
							}

							commit('ADD_LOG', `蓝牙初始化失败: ${title} - ${content}`)

							return await new Promise((resolve) => {
								uni.showModal({
									title: title,
									content: content,
									confirmText: showRetry ? '重试' : '确定',
									cancelText: showRetry ? '取消' : '',
									showCancel: showRetry,
									success: async (res) => {
										if (res.confirm && showRetry) {
											// 用户选择重试
											try {
												const result =
													await dispatch(
														'initBluetooth')
												resolve(result)
											} catch (retryErr) {
												resolve(false)
											}
										} else {
											resolve(false)
										}
									}
								})
							})
						}
					},
					// 重置蓝牙状态
					resetBluetoothState({
						commit
					}) {
						commit('CLEAR_CONNECTED_DEVICE')
						commit('CLEAR_DEVICES')
						commit('SET_BLUETOOTH_ADAPTER_READY', false)
					},

					// 开始扫描设备
					async startScan({
						state,
						commit,
						dispatch
					}, bluetoothName) {
						if (state.isScanning) {
							commit('ADD_LOG', '扫描已在进行中')
							return
						}

						// 确保蓝牙适配器已准备好
						if (!state.bluetoothAdapterReady) {
							try {
								const initResult = await dispatch('initBluetooth')
								if (!initResult) {
									commit('ADD_LOG', '蓝牙初始化失败，无法开始扫描')
									return false
								}
							} catch (err) {
								commit('ADD_LOG', '蓝牙初始化异常，无法开始扫描')
								return false
							}
						}

						commit('CLEAR_DEVICES')
						commit('SET_SCANNING', true)
						commit('ADD_LOG', '开始扫描蓝牙设备...')

						// 检查运行环境
						const env = dispatch('checkEnvironment')

						// 如果是开发者工具，使用模拟扫描
						if (env.isDevTools) {
							commit('ADD_LOG', '开发者工具环境，使用模拟扫描')

							// 模拟扫描过程
							const mockDevices = [{
									deviceId: 'mock_device_1',
									name: '模拟蓝牙设备1',
									RSSI: -50
								},
								{
									deviceId: 'mock_device_2',
									name: '模拟蓝牙设备2',
									RSSI: -60
								},
								{
									deviceId: 'mock_device_3',
									name: '模拟蓝牙设备3',
									RSSI: -70
								}
							]

							// 模拟逐个发现设备
							mockDevices.forEach((device, index) => {
								setTimeout(() => {
									commit('ADD_DEVICE', device)
									commit('ADD_LOG',
										`发现模拟设备: ${device.name} (${device.deviceId})`
									)
								}, (index + 1) * 1000)
							})

							// 5秒后自动停止模拟扫描
							setTimeout(() => {
								commit('SET_SCANNING', false)
								commit('ADD_LOG', '模拟扫描完成')
							}, 5000)

							return true
						}

						try {
							// 启动蓝牙设备扫描
							await new Promise((resolve, reject) => {
								uni.startBluetoothDevicesDiscovery({
									allowDuplicatesKey: true,
									interval: 0,
									success: resolve,
									fail: reject
								})
							})

							commit('ADD_LOG', '扫描启动成功，正在搜索设备...')

							// 监听发现新设备事件
							uni.onBluetoothDeviceFound((res) => {
								if (res.devices && res.devices.length > 0) {
									res.devices.forEach(device => {
										const app = getApp();

										// 判断是否在当前列表
										if (app.hasDeviceList(device.name)) {
											const deviceInfo = {
												name: device.name || device
													.localName || '未知设备',
												deviceId: device.deviceId,
												RSSI: device.RSSI || 0
											}


											commit('ADD_DEVICE', deviceInfo)
											commit('ADD_MAP_DEVICENAME', deviceInfo)

											commit('ADD_LOG',
												`发现设备: ${deviceInfo.name} (${deviceInfo.deviceId})`
											)
										}
									})
								}
							})

							// 设置扫描超时（30秒后自动停止）
							setTimeout(async () => {
								if (state.isScanning) {
									await dispatch('stopScan')
									commit('ADD_LOG', '扫描超时，已自动停止')

									if (state.deviceList.length === 0) {
										uni.showToast({
											title: '未发现设备',
											icon: 'none',
											duration: 2000
										})
									}
								}
							}, 30000)

							return true
						} catch (err) {
							commit('SET_SCANNING', false)
							commit('ADD_LOG', `扫描失败: ${err.errMsg || err.message}`)

							// 根据错误类型给出不同提示
							let errorMsg = '扫描失败'
							if (err.errCode === 10001) {
								errorMsg = '蓝牙未开启，请开启蓝牙后重试'
							} else if (err.errCode === 10004) {
								errorMsg = '没有蓝牙权限，请授权后重试'
							} else if (err.errCode === 10012) {
								errorMsg = '连接超时，请重试'
							}

							uni.showToast({
								title: errorMsg,
								icon: 'none',
								duration: 3000
							})

							throw err
						}
					},

					// 停止扫描
					async stopScan({
						state,
						commit
					}) {
						if (!this.state.isScanning) return

						try {
							await new Promise((resolve, reject) => {
								uni.stopBluetoothDevicesDiscovery({
									success: resolve,
									fail: reject
								})
							})
							commit('SET_SCANNING', false)
							commit('ADD_LOG', '扫描已停止')
						} catch (err) {
							commit('ADD_LOG', `停止扫描失败: ${err.errMsg}`)
							throw err
						}
					},

					// 连接设备
					async connectDevice({
						state,
						commit,
						dispatch
					}, device) {
						// 检查是否已连接
						if (state.connectedDevice?.deviceId === device.deviceId) {
							throw new Error('设备已连接');
						}

						uni.showLoading({
							title: '连接中...',
							mask: true
						});

						try {
							// 1. 停止扫描
							await dispatch('stopScan');
							commit('ADD_LOG', '已停止扫描');

							// 2. 建立连接
							const connectionResult = await new Promise((resolve, reject) => {
								uni.createBLEConnection({
									deviceId: device.deviceId,
									success: (res) => {
										// 将连接状态改变一下
										const app = getApp();
										app.globalData.deviceStatus = true;
										app.setConnectionMethod('bluetooth');
										// 将app的globalData的连接上的id改为connectedDeviceId
										app.globalData.connectedDeviceId = device.name;
										console.log("当前设备编号为", app.globalData
											.connectedDeviceId)

										// ✅ 记录蓝牙连接成功状态变化
										connectionStateValidator.logConnectionChange('BLUETOOTH_CONNECT_SUCCESS', 'bluetooth', {
											deviceId: device.deviceId,
											deviceName: device.name
										});

										commit('ADD_LOG', `连接成功: ${device.deviceId}`);
										resolve(res);
									},
									fail: (err) => {
										commit('ADD_LOG', `连接失败: ${err.errMsg}`);
										reject(new Error(`连接失败: ${err.errMsg}`));
									}
								});
							});

							// 监听连接状态变化
							uni.onBLEConnectionStateChange((res) => {
								if (!res.connected) {
									const app = getApp();

									// 检查是否在任务完成缓冲期内
									if (app.globalData.taskCompletionBuffer) {
										console.log('当前在任务完成缓冲期内，忽略蓝牙断开连接');
										commit('ADD_LOG', `设备在任务完成缓冲期内断开，这是正常现象`);
										return;
									}

									// ✅ 记录蓝牙断开状态变化
									connectionStateValidator.logConnectionChange('BLUETOOTH_DISCONNECT', 'bluetooth', {
										deviceId: res.deviceId,
										reason: 'CONNECTION_LOST'
									});

									app.globalData.deviceStatus = false;
									// 将全局连接上的id清除
									app.globalData.connectedDeviceId = ''
									app.setConnectionMethod('');

									commit('ADD_LOG', `设备断开: ${res.deviceId}`);
									commit('CLEAR_CONNECTED_DEVICE');
									dispatch('resetBluetoothState');
								} else {
									commit('ADD_LOG', `设备保持连接: ${res.deviceId}`);
								}
							});

							// 3. 获取服务列表
							const services = await new Promise((resolve, reject) => {
								uni.getBLEDeviceServices({
									deviceId: device.deviceId,
									success: (res) => {
										console.log("所有服务", res.services)
										commit('ADD_LOG', `获取到${res.services.length}个服务`);
										resolve(res);
									},
									fail: (err) => {
										commit('ADD_LOG', `获取服务失败: ${err.errMsg}`);
										reject(new Error(`获取服务失败: ${err.errMsg}`));
									}
								});
							});

							// 查找目标服务
							const service = services.services.find(s =>
								s.uuid.toLowerCase() === BLUETOOTH_SERVICE_UUID.toLowerCase()
							);
							if (!service) {
								throw new Error(`未找到服务: ${BLUETOOTH_SERVICE_UUID}`);
							}
							commit('ADD_LOG', `找到目标服务: ${service.uuid}`);

							// 4. 获取特征值
							const characteristics = await new Promise((resolve, reject) => {
								uni.getBLEDeviceCharacteristics({
									deviceId: device.deviceId,
									serviceId: service.uuid,
									success: (res) => {
										console.log("所有特征", res.characteristics)
										commit('ADD_LOG',
											`获取到${res.characteristics.length}个特征值`);
										resolve(res);
									},
									fail: (err) => {
										commit('ADD_LOG', `获取特征值失败: ${err.errMsg}`);
										reject(new Error(`获取特征值失败: ${err.errMsg}`));
									}
								});
							});

							console.log("获取到的特征", characteristics)

							// 查找必要的特征值
							const writeChar = characteristics.characteristics.find(
								c => c.uuid.toLowerCase() === WRITE_CHARACTERISTIC_UUID.toLowerCase() &&
								c.properties.write
							);
							const notifyChar = characteristics.characteristics.find(
								c => c.uuid.toLowerCase() === NOTIFY_CHARACTERISTIC_UUID.toLowerCase() &&
								(c.properties.notify || c.properties.indicate)
							);

							if (!writeChar) {
								throw new Error(`未找到可写特征: ${WRITE_CHARACTERISTIC_UUID}`);
							}
							if (!notifyChar) {
								throw new Error(`未找到可通知特征: ${NOTIFY_CHARACTERISTIC_UUID}`);
							}

							commit('ADD_LOG', `找到写特征: ${writeChar.uuid}`);
							commit('ADD_LOG', `找到通知特征: ${notifyChar.uuid}`);

							// 5. 启用通知（必须先于写入操作）
							await new Promise((resolve, reject) => {
								uni.notifyBLECharacteristicValueChange({
									deviceId: device.deviceId,
									serviceId: service.uuid,
									characteristicId: notifyChar.uuid,
									state: true,
									success: () => {
										commit('ADD_LOG', '通知特征启用成功');
										resolve();
									},
									fail: (err) => {
										commit('ADD_LOG', `启用通知失败: ${err.errMsg}`);
										reject(new Error(`启用通知失败: ${err.errMsg}`));
									}
								});
							});

							// 6. 设置数据接收监听（必须在启用通知之后）
							uni.onBLECharacteristicValueChange(async (res) => {
								try {
									console.log("原始数据", res.value)
									const value = arrayBufferToHex(res.value);
									commit('ADD_LOG', `收到数据: ${value}`);

									const parsedData = BLUETOOTH_COMMANDS.utils.parseResponse(res
										.value);
									// console.log("处理过的返回", parsedData)

									console.log("收到设备操作码", parsedData.opcode?.toString(16))

									// 更新设备状态
									const app = getApp();
									app.updateDeviceStatusDetail({
										isOnline: true,
										lastHeartbeat: new Date().toISOString(),
										connectionQuality: 100
									});

									// 根据操作码来做不同的操作?
									switch (parsedData.opcode) {
										case BLUETOOTH_COMMANDS.OPCODES.PRE_END_DISPENSE:
											console.log("继续出料指令返回出料情况", parsedData);
											// 记录染膏消耗
											try {
												// 拿到正在执行的指令
												const currentCommand = app.globalData.currentCommand
												await dispatch('recordDyeConsumption',
													currentCommand);
											} catch (error) {
												console.error('记录染膏消耗失败:', error);
												commit('ADD_LOG', `记录染膏消耗失败: ${error.message}`);
											}
											// 继续执行指令队列
											dispatch('executeCommandQueue');
											break;

										case BLUETOOTH_COMMANDS.OPCODES.END_DISPENSE:
											console.log("出料完成", parsedData);
											// 更新指令执行状态
											app.updateCommandExecutionDetail({
												progress: 100,
												currentStep: '出料完成',
												estimatedEndTime: new Date().toISOString()
											});
											// 全部出料完成时清空当前指令
											app.setCurrentCommand(null);
											app.setExecutingQueue(false);
											break;

											// 设备返回0x05码就将已放碗,返回的图标改为绿色
										case BLUETOOTH_COMMANDS.OPCODES.BOWL_ALREADY_PLACED:
											app.updateCommandStatus(true);
											app.updateWanColor(true);
											app.updateCommandExecutionDetail({
												startTime: new Date().toISOString(),
												progress: 0,
												currentStep: '执行中',
											});
											break;

											// 设备返回0x26 , 表示蓝牙下发成功
										case BLUETOOTH_COMMANDS.OPCODES.BLUETOOTH_SENDS_COMMANDS:
											// 以下这个业务流程是错误的,设备连接上tcp服务器之后会给蓝牙返回一个0x26的代码,目前没用
											break;

											// 设备暂停确认响应
										case BLUETOOTH_COMMANDS.OPCODES.PAUSE_DISPENSE:
											console.log("设备暂停确认", parsedData);
											app.setPaused(true);
											app.setExecutingQueue(false);
											uni.showToast({
												title: '设备已暂停',
												icon: 'success'
											});
											commit('ADD_LOG', '设备暂停确认');
											break;

											// 设备继续确认响应
										case BLUETOOTH_COMMANDS.OPCODES.CONTINUE_DISCHARGING:
											console.log("设备继续确认", parsedData);
											app.setPaused(false);
											app.setExecutingQueue(true);
											// 清除暂停状态保存
											app.setPausedQueueState(null);
											uni.showToast({
												title: '设备已恢复',
												icon: 'success'
											});
											commit('ADD_LOG', '设备继续确认');
											break;

											// 设备取消确认响应
										case BLUETOOTH_COMMANDS.OPCODES.CANCEL_DISCHARGE:
											console.log("设备取消确认", parsedData);
											app.setExecutingQueue(false);
											app.setPaused(false);
											// 清空指令队列
											app.globalData.commandQueue = [];
											uni.showToast({
												title: '操作已取消',
												icon: 'success'
											});
											commit('ADD_LOG', '设备取消确认');
											break;

										case BLUETOOTH_COMMANDS.OPCODES.CHUNK_SUCCESS:
											// 分片传输成功继续 
											setTimeout(() => {
												transferManager.handleSuccess()
											}, 200)
											break;
										

										case BLUETOOTH_COMMANDS.OPCODES.CHUNK_ERROR:
											commit('ADD_LOG', '设备报告分片数据错误');
											// 分片传输重试
											setTimeout(() => {
												transferManager.handleError()
											}, 200)
											break;
										
										// 设备电子秤已放平0x15
										case BLUETOOTH_COMMANDS.OPCODES.ALREADY_FLAT:
											console.log("收到电子秤校准响应 (0x15):", parsedData);
											commit('ADD_LOG', `电子秤校准响应: ${JSON.stringify(parsedData)}`);

											// 通知应用层处理校准响应
											if (app.handleBluetoothMessage) {
												app.handleBluetoothMessage({
													type: 'CALIBRATION_RESPONSE',
													opcode: parsedData.opcode,
													data: parsedData.data,
													timestamp: new Date().toISOString()
												});
											}

											// 触发校准完成事件
											uni.$emit('calibration-response', {
												success: true,
												opcode: parsedData.opcode,
												data: parsedData.data
											});


											break;

										// 电子秤校准响应 (0x14)
										case BLUETOOTH_COMMANDS.OPCODES.CALIBRATION_RESPONSE:
											console.log("收到电子秤校准响应 (0x14):", parsedData);
											commit('ADD_LOG', `电子秤校准响应: ${JSON.stringify(parsedData)}`);

											// 通知应用层处理校准响应
											if (app.handleBluetoothMessage) {
												app.handleBluetoothMessage({
													type: 'CALIBRATION_RESPONSE',
													opcode: parsedData.opcode,
													data: parsedData.data,
													timestamp: new Date().toISOString()
												});
											}

											// 触发校准完成事件
											uni.$emit('calibration-response', {
												success: true,
												opcode: parsedData.opcode,
												data: parsedData.data
											});
											break;

										default:
											console.warn("未知指令:", parsedData.opcode);
											app.setErrorState({
												errorType: 'UNKNOWN_COMMAND',
												errorMessage: `未知指令: ${parsedData.opcode}`,
												errorCode: parsedData.opcode
											});
									}
								} catch (err) {
									commit('ADD_LOG', `数据处理错误: ${err.message}`);
								}
							});

							// 保存连接信息
							commit('SET_CONNECTED_DEVICE', {
								...device,
								serviceId: service.uuid,
								writeCharId: writeChar.uuid,
								notifyCharId: notifyChar.uuid,
								connectionType: 'bluetooth'  // ✅ 添加连接类型标识，防止与WiFi连接混淆
							});

							// ✅ 记录蓝牙设备信息保存状态变化
							connectionStateValidator.logConnectionChange('BLUETOOTH_DEVICE_SAVED', 'bluetooth', {
								deviceId: device.deviceId,
								deviceName: device.name,
								hasConnectionType: true
							});

							uni.showToast({
								title: '连接成功',
								icon: 'success'
							});
							
							// 发送连接成功代码
							try {
								const command = getCommand('bluetoothConnectSuccess');
								await dispatch('writeData', command.value);
							} catch(err) {
								console.log("发送语音指令失败",err)
							}

						} catch (err) {
							commit('ADD_LOG', `连接过程出错: ${err.message}`);
							await dispatch('resetBluetoothState');
							throw err; // 继续向上抛出错误
						} finally {
							uni.hideLoading();
						}
					},

					// 断开连接
					async disconnectDevice({
						state,
						commit
					}) {
						if (!state.connectedDevice) return

						uni.showLoading({
							title: '断开中...',
							mask: true
						})

						try {
							// 关闭连接
							await new Promise((resolve, reject) => {
								uni.closeBLEConnection({
									deviceId: state
										.connectedDevice
										.deviceId,
									success: resolve,
									fail: reject
								})
							})

							const app = getApp();
							app.globalData.deviceStatus = false;
							app.setConnectionMethod('');

							// 将当前连接的id去掉
							app.globalData.connectedDeviceId = ''

							commit('ADD_LOG', '设备已断开')
							commit('CLEAR_CONNECTED_DEVICE')
							uni.showToast({
								title: '已断开',
								icon: 'success'
							})
						} catch (err) {
							commit('ADD_LOG', `断开失败: ${err.errMsg}`)
							throw err
						} finally {
							uni.hideLoading()
						}
					},

					// 写入数据
					async writeData({
						state,
						commit
					}, data) {
						// console.log("写入服务")

						if (!state.connectedDevice) {
							console.log("设备未连接")
							throw new Error('未连接设备')
						}

						// console.log("连接特征 服务id", state.connectedDevice.serviceId)
						// console.log("连接特征 设备id", state.connectedDevice.deviceId)
						// console.log("连接特征 写特征", state.connectedDevice.writeCharId)
						// console.log("发生的数据", data)

						try {
							const res = await new Promise((resolve,
								reject) => {
								uni.writeBLECharacteristicValue({
									deviceId: state
										.connectedDevice
										.deviceId,
									serviceId: state
										.connectedDevice
										.serviceId,
									characteristicId: state
										.connectedDevice
										.writeCharId,
									value: arrayToArrayBuffer(data),
									success: resolve,
									fail: reject
								})
							})

							console.log("发送数据返回的结果", res)
							commit('ADD_LOG', `数据发送成功: ${arrayToArrayBuffer(data)}`)
							console.log("发送成功", arrayToArrayBuffer(data))
							return res
						} catch (err) {
							console.log("指令发送失败", err)
							commit('ADD_LOG', `发送失败: ${err.errMsg}`)
							throw err
						}
					},
					async writeDataChunked({
						state,
						commit,
						dispatch
					}, data) {
						// 延迟函数
						function delay(ms) {
							return new Promise(resolve => setTimeout(resolve, ms));
						}

						// 等待连接状态稳定
						await delay(500);

						// 等待设备准备接收数据
						// await delay(500);

						writeDataChunked(data)

						// 测试等待第一条的发送
						// await delay(500);

						// transferManager.handleSuccess()
						// transferManager.handleSuccess()
						// transferManager.handleSuccess()
						// transferManager.handleSuccess()
						// transferManager.handleSuccess()
						// transferManager.handleSuccess()
						// transferManager.handleSuccess()
						// transferManager.handleSuccess()

						// setTimeout(() => {transferManager.handleError({
						// 	message : "设备返回错误"
						// })},5000)
						// console.log("收到08")
						// setTimeout(() => {transferManager.handleError()},5000)
						// setTimeout(() => {transferManager.handleError()},500)
						// setTimeout(() => {transferManager.handleError()},500)
						// setTimeout(() => {transferManager.handleError()},500)
						// setTimeout(() => {transferManager.handleError()},500)
						// console.log("收到07")
						// setTimeout(() => {transferManager.handleSuccess()},5000)
						// console.log("收到07")
						// setTimeout(() => {transferManager.handleSuccess()},600)
						// console.log("收到07")
						// setTimeout(() => {transferManager.handleSuccess()},700)
						// console.log("收到07")
						// setTimeout(() => {transferManager.handleSuccess()},500)
						// setTimeout(() => {transferManager.handleSuccess()},600)
						// setTimeout(() => {transferManager.handleSuccess()},700)
						// setTimeout(() => {transferManager.handleSuccess()},800)
					},


					// async writeDataChunked({
					// 	state,
					// 	commit,
					// 	dispatch
					// }, data) {
					// 	try {
					// 		// 延迟函数
					// 		function delay(ms) {
					// 			return new Promise(resolve => setTimeout(resolve, ms));
					// 		}

					// 		// 1. 首先发送命令头
					// 		await dispatch('writeData', data.command);
					// 		commit('ADD_LOG', `命令头发送成功: ${JSON.stringify(data.command)}`);

					// 		// 等待设备准备接收数据
					// 		await delay(300);

					// 		// 2. 构造要发送的字符串
					// 		const payloadString =
					// 			`ssid:${data.wifiInfo.SSID}password:${data.wifiInfo.password}`;
					// 		commit('ADD_LOG', `准备发送的WiFi配置字符串: ${payloadString}`);


					// 		// 3.将字符串转换为十六进制
					// 		function stringToHex(str) {
					// 			let hex = '';
					// 			for (let i = 0; i < str.length; i++) {
					// 				// 获取字符的Unicode编码，然后转换为16进制
					// 				const code = str.charCodeAt(i).toString(16);
					// 				// 确保是两位表示，不足补0
					// 				hex += code.padStart(2, '0');
					// 			}
					// 			return hex;
					// 		}

					// 		// 十六进制字符串
					// 		const payloadStringHex =stringToHex(payloadString)


					// 		// 4.将十六进制字符串转换为十进制数组
					// 		function hexToIntArray(hex) {
					// 			const intArray = [];
					// 			// 每两位处理一次
					// 			for (let i = 0; i < hex.length; i += 2) {
					// 			  const byte = hex.substr(i, 2);
					// 			  // 将16进制字符串转换为10进制整数
					// 			  intArray.push(parseInt(byte, 16));
					// 			}
					// 			return intArray;
					// 		}

					// 		const uint8Array = hexToIntArray(payloadStringHex);
					// 		commit('ADD_LOG', `转换为字节数组: ${Array.from(uint8Array).join(',')}`);

					// 		// 4. 分片发送（调整为20字节/片）
					// 		const CHUNK_SIZE = 20;
					// 		const totalChunks = Math.ceil(uint8Array.length / CHUNK_SIZE);

					// 		for (let i = 0; i < totalChunks; i++) {
					// 			const start = i * CHUNK_SIZE;
					// 			const end = Math.min(start + CHUNK_SIZE, uint8Array.length);
					// 			const chunk = uint8Array.slice(start, end);

					// 			// 转换为普通数组
					// 			const chunkArray = Array.from(chunk);

					// 			commit('ADD_LOG', `发送分片 ${i+1}/${totalChunks}: ${chunkArray.join(',')}`);
					// 			await dispatch('writeData', chunkArray);

					// 			// 如果不是最后一片，等待更长时间（500ms）
					// 			if (i < totalChunks - 1) {
					// 				await delay(500);
					// 			}
					// 		}

					// 		commit('ADD_LOG', "全部分片发送完成");
					// 		return {
					// 			success: true
					// 		};

					// 	} catch (err) {
					// 		commit('ADD_LOG', `分片发送失败: ${err.message}`);
					// 		console.error("分片发送失败:", err);
					// 		throw err;
					// 	}
					// },


					// async writeDataChunked({
					// 	state,
					// 	commit,
					// 	dispatch
					// }, data) {
					// 	try {
					// 		// 先发送 0x06 作为分片开始（假设 data.command 是 0x06 的 int 数组形式）
					// 		await dispatch('writeData', data.command);

					// 		// 等待500ms再继续
					// 		await delay(500);

					// 		// 构造要发送的字符串
					// 		const payloadString =
					// 		`ssid:${data.wifiInfo.SSID}password:${data.wifiInfo.password}`;

					// 		// 将字符串转换为 Uint8Array（即 int 数组）
					// 		function stringToUint8Array(str) {
					// 			const arr = new Uint8Array(str.length);
					// 			for (let i = 0; i < str.length; i++) {
					// 				arr[i] = str.charCodeAt(i); // 获取字符的 ASCII 码（0-255）
					// 			}
					// 			return arr;
					// 		}

					// 		// 定义 delay 函数（方法1）
					// 		function delay(ms) {
					// 		    return new Promise(resolve => setTimeout(resolve, ms));
					// 		}

					// 		const uint8Array = stringToUint8Array(payloadString);

					// 		// 分片发送（假设 CHUNK_SIZE = 20 字节）
					// 		const CHUNK_SIZE = 13; // 每个分片 20 字节（可调整）
					// 		const totalChunks = Math.ceil(uint8Array.length / CHUNK_SIZE);

					// 		for (let i = 0; i < totalChunks; i++) {
					// 			const start = i * CHUNK_SIZE;
					// 			const end = Math.min(start + CHUNK_SIZE, uint8Array.length);
					// 			const chunk = uint8Array.slice(start, end);

					// 			// 转换为普通数组（因为 arrayToArrayBuffer 接收的是 number[]）
					// 			const chunkArray = Array.from(chunk);

					// 			console.log('Sending data:', Array.from(new Uint8Array(arrayToArrayBuffer(data))))
					// 			await dispatch("writeData", chunkArray);
					// 			commit("ADD_LOG", `分片 ${i+1}/${totalChunks} 发送成功 (字节 ${start}-${end-1})`);

					// 			if (i < totalChunks - 1) {
					// 				await delay(500);
					// 			}
					// 		}

					// 		commit("ADD_LOG", "全部分片发送完成");
					// 		return {
					// 			success: true
					// 		};
					// 	} catch (err) {
					// 		console.error("分片发送失败:", err);
					// 		throw err;
					// 	}
					// },


					// 记录染膏消耗
					async recordDyeConsumption({
						state,
						commit
					}, responseData) {
						try {
							const app = getApp();
							const deviceCode = app.globalData.connectedDeviceId;

							if (!deviceCode) {
								console.warn('设备编码为空，无法记录染膏消耗');
								return;
							}

							console.log("记录染膏耗量数据", responseData)

							// 解析响应数据中的染膏使用信息
							// if (responseData.data && responseData.data.length >= 2) {
							if (responseData.colorCode && responseData.weight) {
								const colorCode = responseData.colorCode; // 颜色代码
								const weight = responseData.weight; // 重量

								// 获取当前执行的指令的后端ID
								const currentCommand = app.globalData.currentCommand;
								const commandId = currentCommand ? currentCommand.backendId : null;

								// 构造消耗数据
								const consumptionData = {
									deviceCode,
									colorCode: `0x${colorCode.toString(16).padStart(2, '0').toUpperCase()}`,
									usageAmount: weight,
									timestamp: new Date().toISOString()
								};

								// 使用染膏消耗管理器记录
								const success = await dyeConsumptionManager.recordConsumption(
									deviceCode, {
										consumption: {
											[colorCode]: {
												amount: weight,
												colorCode: consumptionData.colorCode
											}
										}
									},
									commandId
								);

								if (success) {
									// 更新全局状态
									app.updateDyeConsumption({
										currentUsage: {
											[consumptionData.colorCode]: weight
										},
										totalConsumption: app.globalData.dyeConsumption
											.totalConsumption + weight
									});

									commit('ADD_LOG',
										`✅ 蓝牙染膏消耗记录成功: 颜色${consumptionData.colorCode}, 用量${weight}g, 后端指令ID: ${commandId}`
									);
								} else {
									commit('ADD_LOG', `蓝牙染膏消耗记录失败`);
								}
							}
						} catch (error) {
							console.error('记录染膏消耗失败:', error);
							commit('ADD_LOG', `记录染膏消耗失败: ${error.message}`);
						}
					}
				},

				getters: {
					// #ifndef VUE3
					// Vue 2 的 getters 写法
					isConnected: state => !!state.connectedDevice,
					deviceCount: state => state.deviceList.length,
					recentLogs: state => state.logs.slice(0, 10),
					deviceNameMap: state => state.discoveredNameDevices,
					deviceList: state => state.deviceList,
					// #endif

					// #ifdef VUE3
					// Vue 3 的 computed 写法（在组件中使用 computed）
					// 这里保持与 Vue 2 相同的 getters 以兼容两种模式
					isConnected: state => !!state.connectedDevice,
					deviceCount: state => state.deviceList.length,
					recentLogs: state => state.logs.slice(0, 10),
					deviceNameMap: state => state.discoveredNameDevices,
					deviceList: state => state.deviceList,

					// #endif
				}
			})