<view class="cart-container"><block wx:if="{{a}}"><view class="manage-section"><view class="manage-btn" bindtap="{{c}}">{{b}}</view></view><view class="cart-list"><view wx:for="{{d}}" wx:for-item="item" wx:key="l" class="cart-item"><view class="checkbox"><checkbox checked="{{item.a}}" bindtap="{{item.b}}" color="#4285f4"></checkbox></view><image class="product-image" src="{{item.c}}" mode="aspectFill"></image><view class="product-info"><text class="product-name">{{item.d}}</text><text class="product-spec">{{item.e}}</text><text wx:if="{{item.f}}" class="stock-info">库存：{{item.g}}件</text><view class="bottom-row"><text class="product-price">¥{{item.h}}</text><view class="quantity-selector"><view class="quantity-btn minus" bindtap="{{item.i}}">-</view><input type="number" value="{{item.j}}" class="quantity-input" disabled/><view class="quantity-btn plus" bindtap="{{item.k}}">+</view></view></view></view></view></view><view class="bottom-bar"><view class="left-section"><view class="select-all"><checkbox checked="{{e}}" bindtap="{{f}}" color="#4285f4"></checkbox><text>全选</text></view><view wx:if="{{g}}" class="price-info"><text>合计：</text><text class="total-price">¥{{h}}</text></view></view><view class="right-section"><view wx:if="{{i}}" class="checkout-btn" bindtap="{{k}}"> 结算({{j}}) </view><view wx:else class="delete-btn" bindtap="{{l}}"> 删除 </view></view></view></block><view wx:else class="empty-cart"><view class="empty-content"><view class="empty-icon"><image src="{{m}}" mode="aspectFit"></image></view><view class="empty-text"><text class="empty-title">购物车还是空的</text><text class="empty-subtitle">快去挑选心仪的商品吧~</text></view><button class="go-shopping" bindtap="{{n}}">去逛逛</button></view><view class="empty-decoration"><view class="decoration-circle circle-1"></view><view class="decoration-circle circle-2"></view><view class="decoration-circle circle-3"></view></view></view></view>