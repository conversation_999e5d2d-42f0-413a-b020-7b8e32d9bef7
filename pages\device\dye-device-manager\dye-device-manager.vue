<template>
	<view class="container">
		<!-- 新增的绑定按钮 -->
		    <view class="header-actions">
		      <button class="bind-btn" type="primary" @click="openBindModal">
		        <uni-icons type="plus" size="16"></uni-icons>
		        绑定新设备
		      </button>
		    </view>
		<!-- 设备列表 -->
		<scroll-view scroll-y class="device-list" @scrolltolower="loadMore" :style="{height: scrollHeight + 'px'}">
			<uni-list>
				<uni-list-item v-for="(item, index) in filteredDevices" :key="item.id"
					:title="item.device_name || item.name || item.deviceCode"
					:note="`编号: ${item.deviceCode || item.sn} | 类型: ${item.type}`"
					:right-text="getStatusText(item.status)" :right-text-style="{color: getStatusColor(item.status)}"
					@click="showDetail(item)" clickable>
					<template v-slot:footer>
						<view class="action-buttons">
							<!-- <button class="action-btn mini-btn" type="primary" size="mini"
								@click.stop="bindManager(item)" v-if="item.status !== 1">
								绑定
							</button> -->
							<button class="action-btn mini-btn" type="warn" size="mini"
								@click.stop="unbindManager(item)"
								v-if="item.status === 1 && isCurrentUserManager(item.managers)">
								解除绑定
							</button>
							<button class="action-btn mini-btn" type="default" size="mini"
								@click.stop="assignPermission(item)"
								v-if="item.status === 1 && isCurrentUserManager(item.managers)">
								分配权限
							</button>
						</view>
					</template>
				</uni-list-item>

			</uni-list>

			<!-- 加载状态 -->
			<!-- <uni-load-more :status="loadingStatus" :icon-size="16" :content-text="contentText"></uni-load-more> -->

			<!-- 空状态提示 -->
			<view class="empty-tip" v-if="filteredDevices.length === 0 && !loading">
				<image src="/static/empty.png" mode="aspectFit"></image>
				<text>暂无设备数据</text>
			</view>
		</scroll-view>

		<!-- 绑定和解绑弹窗 -->
		<bind-modal ref="bindModal" @success="bindSuccess"></bind-modal>

		<!-- 分配权限弹窗 -->
		<uni-popup ref="assignPopup" type="bottom" :mask-click="false">
			<view class="popup-content">
				<view class="popup-header">
					<text class="popup-title">分配权限</text>
					<uni-icons type="closeempty" size="24" color="#999" @click="closeAssignPopup"></uni-icons>
				</view>

				<view class="popup-body">
					<view class="form-item">
						<text class="label">设备名称</text>
						<text class="value">{{ assignForm.deviceName }}</text>
					</view>

					<view class="form-item">
						<text class="label">当前管理员</text>
						<text class="value">{{ assignForm.currentManager }}</text>
					</view>

					<view class="form-item">
						<text class="label">选择员工</text>
						<picker mode="selector" :range="staffList" range-key="name" @change="onStaffChange">
							<view class="picker">
								{{ assignForm.selectedStaff.name || '请选择员工' }}
							</view>
						</picker>
					</view>

					<view class="form-item">
						<text class="label">权限类型</text>
						<uni-data-checkbox v-model="assignForm.permissionType"
							:localdata="permissionTypes"></uni-data-checkbox>
					</view>
				</view>

				<view class="popup-footer">
					<button class="footer-btn cancel-btn" @click="closeAssignPopup">
						取消
					</button>
					<button class="footer-btn confirm-btn" type="primary" @click="confirmAssign"
						:disabled="!assignForm.selectedStaff.id">
						确认分配
					</button>
				</view>
			</view>
		</uni-popup>
	</view>
</template>

<script>
	import {
		apiService
	} from '../../../utils/request'
	import BindModal from './bind-modal.vue'

	export default {
		components: {
			BindModal
		},
		data() {
			return {
				currentTab: 0,
				tabs: ['全部设备', '已绑定', '未绑定'],
				deviceList: [],
				loadingStatus: 'more',
				searchKeyword: '',
				filterStatus: 0,
				page: 1,
				pageSize: 10,
				total: 0,
				loading: false,
				scrollHeight: 0,
				contentText: {
					// contentdown: '上拉加载更多',
					// contentrefresh: '加载中',
					// contentnomore: '没有更多数据了'
				},
				userInfo: {// 实际应从缓存获取
				
				},
				// 分配权限相关数据
				assignForm: {
					deviceId: null,
					deviceName: '',
					currentManager: '',
					selectedStaff: {},
					permissionType: 1
				},
				staffList: [], // 员工列表从后端API获取
				permissionTypes: [{
						value: 0,
						text: "回收权限"
					},
					{
						value: 1,
						text: '普通权限'
					},
					{
						value: 2,
						text: '管理员权限'
					}
				]
			}
		},
		computed: {
			filteredDevices() {
				return this.deviceList.filter(item => {
					// 搜索关键词过滤
					const keyword = this.searchKeyword.toLowerCase()
					const matchKeyword = !keyword ||
						(item.name && item.name.toLowerCase().includes(keyword)) ||
						(item.sn && item.sn.toLowerCase().includes(keyword)) ||
						(item.device_name && item.device_name.toLowerCase().includes(keyword)) ||
						(item.device_code && item.device_code.toLowerCase().includes(keyword)) ||
						(item.manager && typeof item.manager === 'object' &&
							item.manager.name && item.manager.name.toLowerCase().includes(keyword)) ||
						(item.manager && typeof item.manager === 'string' &&
							item.manager.toLowerCase().includes(keyword))

					// 状态过滤
					const matchStatus = this.filterStatus === 0 ||
						(this.filterStatus === 1 && item.status === 1) ||
						(this.filterStatus === 2 && item.status === 0)

					return matchKeyword && matchStatus
				})
			}
		},
		onLoad() {
			this.calculateScrollHeight()
			this.checkLoginAndLoadData()
			this.getStaffList()
		},
		onShow() {
			this.calculateScrollHeight()
		},
		onPullDownRefresh() {
			this.page = 1
			this.loadDeviceList(true)
		},
		methods: {
			    openBindModal() {
			      this.$refs.bindModal.open()
			    },
				    bindSuccess() {
				      this.page = 1
				      this.loadDeviceList()
				      uni.showToast({
				        title: '绑定成功',
				        icon: 'success'
				      })
				    },
			async getStaffList() {
				try {
					const response = await apiService.mall.stores.getStaffList() // 替换为实际API
					this.staffList = response.data
				} catch (e) {
					console.error('获取员工列表失败:', e)
					uni.showToast({
						title: '获取员工列表失败',
						icon: 'none'
					})
				}
			},
			// 检查登录状态并加载数据
			checkLoginAndLoadData() {
				const isLoggedIn = uni.getStorageSync('isLoggedIn');
				const token = uni.getStorageSync('token');

				if (!isLoggedIn || !token) {
					// 未登录，跳转到登录页面
					uni.showModal({
						title: '提示',
						content: '请先登录后再访问设备管理',
						showCancel: false,
						success: () => {
							uni.navigateTo({
								url: '/pages/login/login'
							});
						}
					});
					return;
				}

				// 已登录，初始化用户信息并加载数据
				this.initUserInfo();
				this.loadDeviceList();
			},

			initUserInfo() {
				const token = uni.getStorageSync('token');
				const wasLogin = this.isLogin;
				this.isLogin = !!token;
				console.log("登录状态", this.isLogin)
				if (this.isLogin) {
					this.userInfo = uni.getStorageSync('userInfo') || this.userInfo;
				}
			},
			// 判断当前用户是否是设备管理员
			isCurrentUserManager(managers) {
				// console.log("当前的管理员", managers)
				if (!managers || !Array.isArray(managers)) return false
				return managers.some(manager => manager.id == this.userInfo.id)
			},
			calculateScrollHeight() {
				const systemInfo = uni.getSystemInfoSync()
				this.scrollHeight = systemInfo.windowHeight - 44 // 44是导航栏高度
			},

			getStatusText(status) {
				return status === 1 ? '已绑定' : '未绑定'
			},

			getStatusColor(status) {
				return status === 1 ? '#4cd964' : '#dd524d'
			},

			async loadDeviceList(isRefresh = false) {
				if (this.loading) return

				this.loading = true
				this.loadingStatus = isRefresh ? 'refresh' : 'loading'

				try {
					await new Promise(resolve => setTimeout(resolve, 800))
					// const mockData = this.getMockData()
					const response = await apiService.mall.stores.getStoreDeviceMapping();

					if (isRefresh || this.page === 1) {
						// this.deviceList = mockData
						this.deviceList = response.data
						// console.log("当前数据", this.deviceList)
					} else {
						this.deviceList = [...this.deviceList, ...response.data]
					}

					// 根据返回数据判断是否还有更多数据
					// 如果有分页信息，可以根据response.total和当前数据量比较
					// 这里假设如果返回数据不为空就认为还有数据
					this.loadingStatus = response.data.length > 0 ? 'more' : 'noMore'
					this.page++
				} catch (e) {
					console.error('加载失败:', e)
					this.loadingStatus = 'more'
					uni.showToast({
						title: '加载失败',
						icon: 'none'
					})
				} finally {
					this.loading = false
					if (isRefresh) {
						uni.stopPullDownRefresh()
					}
				}
			},

			getMockData() {
				return [{
						id: 1,
						name: '染膏机-01',
						device_name: '染膏机-01',
						sn: 'RG20230001',
						device_code: 'RG20230001',
						type: '专业染膏机',
						status: 1,
						managers: [{
								id: 1,
								name: '张店长',
								permissionType: 2
							}, // 管理员权限
							{
								id: 3,
								name: '王五',
								permissionType: 1
							}
						],
						storeName: '朝阳门店',
						purchaseDate: '2023-01-15',
						lastMaintenance: '2023-06-20'
					},
					{
						id: 2,
						name: '染膏机-02',
						device_name: '染膏机-02',
						sn: 'RG20230002',
						device_code: 'RG20230002',
						type: '快速染膏机',
						status: 1,
						managers: [{
							id: 2,
							name: '李经理',
							permissionType: 2
						}],
						storeName: '海淀门店',
						purchaseDate: '2023-02-10',
						lastMaintenance: '2023-07-05'
					},
					{
						id: 3,
						name: '染膏调配仪-01',
						device_name: '染膏调配仪-01',
						sn: 'RG20230003',
						device_code: 'RG20230003',
						type: '智能调配仪',
						status: 0,
						managers: [],
						storeName: '',
						purchaseDate: '2023-03-20',
						lastMaintenance: ''
					}
				]
			},

			loadMore() {
				if (this.loadingStatus !== 'more') return
				this.loadDeviceList()
			},

			onClickItem(e) {
				if (this.currentTab === e.currentIndex) return

				this.currentTab = e.currentIndex
				this.filterStatus = e.currentIndex
				this.page = 1
				this.loadDeviceList()
			},

			search() {
				this.page = 1
				this.loadDeviceList()
			},

			clearSearch() {
				this.searchKeyword = ''
				this.page = 1
				this.loadDeviceList()
			},

			bindManager(item) {
				this.$refs.bindModal.open(item)
			},

			async unbindManager(item) {
				console.log("解除绑定信息", item)
				uni.showModal({
					title: '确认解绑',
					content: `确定要解绑设备 ${item.name || item.device_name || item.deviceId} 吗？`,
					success: async (res) => {
						if (res.confirm) {
							uni.showLoading({
								title: '处理中...',
								mask: true
							})
							try {
								const data = {
									deviceId: item.id,
									storeId: item.storeId
								}
								// await new Promise(resolve => setTimeout(resolve, 800))
								const response = await apiService.mall.stores.storeUnbindDevice(data)
								if (response.code === 200) {
									uni.showToast({
										title: '解绑成功',
										icon: 'success'
									})
									this.page = 1
									this.loadDeviceList()
								} else {
									uni.showToast({
										title: '解绑失败请重试',
										icon: 'fail',
										duration: 2500
									})
								}

							} catch (e) {
								uni.showToast({
									title: e.message || '解绑失败',
									icon: 'none'
								})
							} finally {
								uni.hideLoading()
							}
						}
					}
				})
			},

			// 打开分配权限弹窗
			assignPermission(item) {
				this.assignForm = {
					deviceId: item.id,
					deviceName: item.device_name || item.name,
					currentManager: item.managers && item.managers.length ?
						item.managers.map(m => m.name).join('、') : '无',
					selectedStaff: {},
					permissionType: 1
				}
				this.$refs.assignPopup.open()
			},

			// 员工选择变化
			onStaffChange(e) {
				const index = e.detail.value
				this.assignForm.selectedStaff = this.staffList[index]
			},

			// 确认分配
			confirmAssign() {
				// console.log("assignForm", this.assignForm)

				// Integer deviceId = (Integer) param.get("deviceId");
				//       Integer employeeId : selectedStaff = (Integer) param.get("employeeId");
				//       Integer permissionType = (Integer) param.get("permissionType");

				// console.log("分配的信息",this.assignForm.selectedStaff)
				// console.log("选择的权限",this.assignForm.permissionType)
				// console.log("当前")
				if (!this.assignForm.selectedStaff.id) {
					uni.showToast({
						title: '请选择员工',
						icon: 'none'
					})
					return
				}

				uni.showLoading({
					title: '分配中...',
					mask: true
				})


				const data = {
					deviceId: this.assignForm.deviceId,
					permissionType: this.assignForm.permissionType,
					employeeId: this.assignForm.selectedStaff.employeeId
				}

				// 修改用户权限

				apiService.mall.stores.assignPermission(data)
					.then(res => {
						if (res.code === 200) {
							uni.hideLoading()
							this.$refs.assignPopup.close()
							uni.showToast({
								title: '分配成功',
								icon: 'success'
							})

							// 刷新列表
							this.page = 1
							this.loadDeviceList()
						}
					}).catch(err => {
						uni.hideLoading()

						uni.showToast({
							title: '分配失败,请重试',
							icon: 'fail'
						})

						// 刷新列表
						this.page = 1
						this.loadDeviceList()
					})


				// 模拟API请求
				// setTimeout(() => {
				// 	uni.hideLoading()
				// 	this.$refs.assignPopup.close()
				// 	uni.showToast({
				// 		title: '分配成功',
				// 		icon: 'success'
				// 	})

				// 	// 刷新列表
				// 	this.page = 1
				// 	this.loadDeviceList()
				// }, 1000)
			},

			// 关闭分配弹窗
			closeAssignPopup() {
				this.$refs.assignPopup.close()
			},

			showDetail(item) {
				// uni.navigateTo({
				// 	url: `/pages/device/dye-device-manager/detail?id=${item.id}`
				// })
			},

			bindSuccess() {
				this.page = 1
				this.loadDeviceList()
			},

			back() {
				uni.navigateBack()
			}
		}
	}
</script>

<style lang="scss">
	.container {
		display: flex;
		flex-direction: column;
		height: 100vh;
		background-color: #f8f8f8;
	}

	.device-list {
		flex: 1;
		overflow: hidden;
		padding: 10px;

		:deep(.uni-list-item__content) {
			padding-right: 15px;
		}
	}

	.action-buttons {
		display: flex;
		justify-content: flex-end;
		align-items: center;
		gap: 8px;
		margin-top: 8px;

		.action-btn {
			margin: 0;
			padding: 0 12px;
			height: 28px;
			line-height: 28px;
			font-size: 12px;
			border-radius: 4px;

			&.mini-btn {
				min-width: 60px;
			}
		}
	}

	.empty-tip {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding: 60px 0;
		color: #999;
		font-size: 14px;

		image {
			width: 120px;
			height: 120px;
			margin-bottom: 20px;
			opacity: 0.6;
		}
	}

	/* 分配权限弹窗样式 */
	.popup-content {
		background-color: #fff;
		border-radius: 16px 16px 0 0;
		padding: 20px;
	}

	.popup-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 20px;

		.popup-title {
			font-size: 18px;
			font-weight: bold;
			color: #333;
		}
	}

	.popup-body {
		padding: 10px 0;

		.form-item {
			margin-bottom: 20px;

			.label {
				display: block;
				font-size: 14px;
				color: #666;
				margin-bottom: 8px;
			}

			.value {
				display: block;
				font-size: 15px;
				color: #333;
				padding: 8px 0;
			}

			.picker {
				padding: 10px;
				border: 1px solid #eee;
				border-radius: 4px;
				font-size: 15px;
				color: #333;
			}
		}
	}

	.popup-footer {
		display: flex;
		justify-content: space-between;
		margin-top: 20px;

		.footer-btn {
			flex: 1;
			height: 44px;
			line-height: 44px;
			border-radius: 22px;
			font-size: 16px;

			&.cancel-btn {
				background-color: #f8f8f8;
				color: #666;
				margin-right: 15px;
			}

			&.confirm-btn {
				background-color: #2979ff;
				color: #fff;
			}

			&[disabled] {
				opacity: 0.6;
			}
		}
	}
	
	/* 新增的绑定按钮样式 */
	.header-actions {
	  padding: 10px 15px;
	  background-color: #fff;
	  border-bottom: 1px solid #f5f5f5;
	  
	  .bind-btn {
	    display: flex;
	    align-items: center;
	    justify-content: center;
	    height: 36px;
	    font-size: 14px;
	    
	    .uni-icons {
	      margin-right: 5px;
	    }
	  }
	}
</style>