<template>
  <!-- 主弹窗 -->
  <uni-popup ref="popup" type="center" :mask-click="false">
    <view class="calibration-modal">
      <!-- 头部 -->
      <view class="modal-header">
        <text class="modal-title">电子秤校准</text>
        <uni-icons type="close" size="24" color="#999" @click="closeModal" />
      </view>

      <!-- 步骤1: 空载校准 -->
      <view class="step-content" v-if="step === 1">
        <view class="step-header">
          <text class="step-number">1</text>
          <text class="step-title">空载校准</text>
        </view>
        <text class="step-desc">请确保秤盘上没有放置任何物品</text>
        <!-- <view class="weight-display">
          <text>当前重量:</text>
          <text class="weight-value">{{ currentWeight }}</text>
          <text>g</text>
        </view> -->
        <button class="primary-btn" @click="startZeroCalibration" :disabled="isCalibrating">
          {{ isCalibrating ? '校准中...' : '开始空载校准' }}
        </button>
      </view>

      <!-- 步骤2: 标准砝码校准 -->
      <view class="step-content" v-else-if="step === 2">
        <view class="step-header">
          <text class="step-number">2</text>
          <text class="step-title">标准砝码校准</text>
        </view>
        <text class="step-desc">请在秤盘上放置已知重量的标准砝码</text>

        <view class="input-row">
          <text class="input-label">砝码重量 (g):</text>
          <input class="input-field" type="number" v-model="standardWeight" placeholder="请输入" :disabled="isCalibrating" />
        </view>

        <view class="weight-display">
          <text>当前重量:</text>
          <text class="weight-value">{{ currentWeight }}</text>
          <text>g</text>
        </view>

        <button class="primary-btn" @click="startStandardCalibration" :disabled="!standardWeight || isCalibrating">
          {{ isCalibrating ? '校准中...' : '开始砝码校准' }}
        </button>
      </view>

      <!-- 步骤3: 校准完成 -->
      <view class="step-content" v-else-if="step === 3">
        <view class="step-header">
          <uni-icons type="checkmarkempty" size="24" color="#07C160" />
          <text class="step-title">校准完成</text>
        </view>

        <view class="result-card">
          <text class="result-text">电子秤校准成功!</text>
          <view class="result-data">
            <text>零点值: {{ calibrationData.zeroPoint }}</text>
            <text>灵敏度: {{ calibrationData.sensitivity }}</text>
          </view>
        </view>

        <button class="primary-btn" @click="closeModal">完成</button>
      </view>

      <!-- 错误提示 -->
      <view class="error-tip" v-if="errorMessage">
        <uni-icons type="info" size="16" color="#FF4D4F" />
        <text>{{ errorMessage }}</text>
      </view>
    </view>
  </uni-popup>
</template>

<script>
import { getCommand } from '@/utils/bleCommandSet.js'
export default {
  name: "ScaleCalibration",
  props: {
    // 当前重量值，由父组件通过传感器获取
    currentWeight: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      step: 1, // 1:空载, 2:标准砝码, 3:完成
      standardWeight: '', // 标准砝码重量
      isCalibrating: false, // 是否正在校准
      errorMessage: '', // 错误信息
      calibrationData: { // 校准数据
        zeroPoint: 0,
        sensitivity: 0
      }
    }
  },
  watch: {
    currentWeight(newVal) {
      // 可以在这里添加重量变化的处理逻辑
    }
  },
  methods: {
    // 打开弹窗
    open() {
      this.resetCalibration()
      this.$refs.popup.open()
    },

    // 关闭弹窗
    closeModal() {
      this.$refs.popup.close()
      this.$emit('close')
    },

    // 重置校准状态
    resetCalibration() {
      this.step = 1
      this.standardWeight = ''
      this.isCalibrating = false
      this.errorMessage = ''
      this.calibrationData = {
        zeroPoint: 0,
        sensitivity: 0
      }
    },

    // 开始空载校准
    async startZeroCalibration() {
      this.isCalibrating = true
      this.errorMessage = ''

      //这里发送0x15开始空值校验指令
      const startZeroCalibration = getCommand('startZeroCalibration')


       // 发送校准指令 (0x12)
       await store.dispatch('writeData', startZeroCalibration.value)

      // 模拟API调用，实际项目中替换为真实硬件接口
      setTimeout(() => {
        // 这里应该是与硬件通信的代码
        // 假设成功获取零点值
        // this.calibrationData.zeroPoint = this.currentWeight
        // this.isCalibrating = false
        // this.step = 2 // 进入下一步
      }, 1500)
    },

    startZeroCalibrationResponseListener() {
      // 监听校准响应事件
      const startZeroCalibrationResponse = (response) => {
        console.log('收到电子秤校准响应 (0x15):', response)

        if (response.success && response.opcode === 0x15) {
          // 校准成功处理
          this.calibrationData.zeroPoint = this.currentWeight
          this.isCalibrating = false
          this.step = 2 // 进入下一步

          // 通知父组件校准完成
          this.$emit('calibration-complete', this.calibrationData)

          // 移除监听器
          uni.$off('startZeroCalibration-response', startZeroCalibrationResponse)
        } else {
          this.errorMessage = '校准失败，请重试'
          this.isCalibrating = false
          uni.$off('startZeroCalibration-response', startZeroCalibrationResponse)
        }
      }

      // 添加事件监听器
      uni.$on('startZeroCalibration-response', startZeroCalibrationResponse)

      // 设置超时处理
      setTimeout(() => {
        if (this.isCalibrating) {
          this.errorMessage = '校准超时，请重试'
          this.isCalibrating = false
          uni.$off('startZeroCalibration-response', startZeroCalibrationResponse)
        }
      }, 10000) // 10秒超时
    },



    // 开始标准砝码校准
    async startStandardCalibration() {
      if (!this.standardWeight || isNaN(this.standardWeight)) {
        this.errorMessage = '请输入有效的砝码重量'
        return
      }

      this.isCalibrating = true
      this.errorMessage = ''

      try {
        // 获取蓝牙store实例
        const store = this.$store || getApp().store

        // 检查蓝牙连接状态
        if (!store.state.connectedDevice) {
          throw new Error('设备未连接，请先连接蓝牙设备')
        }

        // 导入蓝牙指令
        // const { getCommand } = await import('../utils/bleCommandSet.js')

        // 生成电子秤校准指令
        const calibrationCommand = getCommand('electronicScaleCalibration', parseInt(this.standardWeight))

        // 发送校准指令 (0x12)
        await store.dispatch('writeData', calibrationCommand.value)

        // 设置响应监听器，等待0x14响应
        this.setupCalibrationResponseListener()

        console.log(`发送电子秤校准指令: 砝码重量${this.standardWeight}g`)

      } catch (error) {
        console.error('发送校准指令失败:', error)
        this.errorMessage = error.message || '发送校准指令失败'
        this.isCalibrating = false
      }
    },

    // 设置校准响应监听器
    setupCalibrationResponseListener() {
      // 监听校准响应事件
      const handleCalibrationResponse = (response) => {
        console.log('收到电子秤校准响应 (0x14):', response)

        if (response.success && response.opcode === 0x14) {
          // 校准成功处理
          this.calibrationData.zeroPoint = this.currentWeight
          this.calibrationData.sensitivity = parseFloat(this.standardWeight)
          this.isCalibrating = false
          this.step = 3 // 进入完成步骤

          // 通知父组件校准完成
          this.$emit('calibration-complete', this.calibrationData)

          // 移除监听器
          uni.$off('calibration-response', handleCalibrationResponse)
        } else {
          this.errorMessage = '校准失败，请重试'
          this.isCalibrating = false
          uni.$off('calibration-response', handleCalibrationResponse)
        }
      }

      // 添加事件监听器
      uni.$on('calibration-response', handleCalibrationResponse)

      // 设置超时处理
      setTimeout(() => {
        if (this.isCalibrating) {
          this.errorMessage = '校准超时，请重试'
          this.isCalibrating = false
          uni.$off('calibration-response', handleCalibrationResponse)
        }
      }, 10000) // 10秒超时
    }
  }
}
</script>

<style scoped>
.calibration-modal {
  width: 80vw;
  max-width: 400px;
  background-color: #fff;
  border-radius: 12px;
  padding: 20px;
  box-sizing: border-box;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.modal-title {
  font-size: 18px;
  font-weight: bold;
  color: #333;
}

.step-content {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.step-header {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.step-number {
  width: 24px;
  height: 24px;
  background-color: #1890FF;
  color: #fff;
  border-radius: 50%;
  text-align: center;
  line-height: 24px;
  margin-right: 10px;
  font-size: 14px;
}

.step-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
}

.step-desc {
  font-size: 18px;
  color: #666;
  margin-bottom: 20px;
  text-align: center;
}

.weight-display {
  display: flex;
  align-items: baseline;
  margin: 15px 0;
  font-size: 16px;
}

.weight-value {
  font-size: 24px;
  font-weight: bold;
  margin: 0 5px;
  color: #1890FF;
}

.input-row {
  width: 100%;
  display: flex;
  align-items: center;
  margin: 15px 0;
}

.input-label {
  font-size: 14px;
  color: #333;
  margin-right: 10px;
  width: 100px;
}

.input-field {
  flex: 1;
  height: 36px;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 0 10px;
  font-size: 14px;
}

.primary-btn {
  width: 100%;
  height: 44px;
  background-color: #1890FF;
  color: #fff;
  border-radius: 4px;
  font-size: 16px;
  margin-top: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.primary-btn[disabled] {
  opacity: 0.6;
}

.result-card {
  width: 100%;
  background-color: #F6FFED;
  border: 1px solid #B7EB8F;
  border-radius: 4px;
  padding: 15px;
  margin: 15px 0;
}

.result-text {
  font-size: 16px;
  color: #52C41A;
  font-weight: bold;
  margin-bottom: 10px;
  display: block;
}

.result-data {
  font-size: 14px;
  color: #333;
}

.result-data text {
  display: block;
  margin: 5px 0;
}

.error-tip {
  width: 100%;
  background-color: #FFF2F0;
  border: 1px solid #FFCCC7;
  border-radius: 4px;
  padding: 10px;
  margin-top: 15px;
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #FF4D4F;
}

.error-tip uni-icons {
  margin-right: 5px;
}
</style>