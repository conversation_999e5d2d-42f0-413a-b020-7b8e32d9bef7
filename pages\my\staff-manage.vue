<template>
  <view class="container">    
    <!-- 成员列表标题 -->
    <view class="staff-section-title">
      <text>共{{totalMemberCount}}位成员</text>
    </view>

    <!-- 成员列表 -->
    <view class="staff-list">
      <view v-if="allMembersList.length === 0" class="empty-staff">
        <text class="empty-text">暂无成员</text>
      </view>

      <view v-for="(staff, index) in allMembersList" :key="index" class="staff-item">
        <view class="staff-avatar">
          <view class="avatar-placeholder"></view>
        </view>
        <view class="staff-info">
          <view class="staff-name">{{staff.name}}</view>
          <view class="staff-phone">{{staff.phone}}</view>
        </view>
        <view class="staff-role-tag">
          <text class="role-text">{{getPosition(staff.position)}}</text>
        </view>
      </view>
    </view>
    
    <!-- 底部添加人员按钮 -->
    <view class="add-btn" @click="addStaff">
      <text>添加人员</text>
    </view>
  </view>
</template>

<script>
import { apiService } from '../../utils/request';

export default {
  data() {
    return {
      staffList: [],
      currentUser: null,
      allMembersList: [],
      totalMemberCount: 0
    };
  },
  onLoad() {
    // 加载员工列表
    this.loadStaffList();
    this.loadCurrentUser();
  },

  onShow() {
    // 页面显示时重新加载数据，用于从添加成员页面返回时刷新
    this.loadStaffList();
  },
  methods: {
	getPosition(positionValue) {
		if(positionValue == 0) {
			return '店长'
		} else if(positionValue == 2) {
			return '管理员'
		} else if(positionValue == 3) {
			return '店员'
		}
		return '未知成员'
	},
    goBack() {
      uni.navigateBack();
    },
    async loadCurrentUser() {
      // 获取当前登录用户信息
	  const response = await apiService.mall.stores.listStoreMember()
	  
	  this.allMembersList = response.data
      // const userInfo = uni.getStorageSync('userInfo');
      // if (userInfo) {
      //   this.currentUser = {
      //     id: userInfo.id,
      //     name: userInfo.nickname || userInfo.username || '微信用户',
      //     phone: userInfo.phone || '未绑定手机号',
      //     role: 'position',
      //     isCurrentUser: true
      //   };
      //   this.updateMembersList();
      // }
    },
    loadStaffList() {
      // 模拟从本地或服务器获取员工列表
      const staffData = uni.getStorageSync('staffList');
      if (staffData) {
        this.staffList = JSON.parse(staffData);
      } else {
        // 初始化为空列表
        this.staffList = [];
      }
      this.updateMembersList();
    },
    updateMembersList() {
      // 合并当前用户和其他员工列表
      this.allMembersList = [];

      // 添加当前用户（如果存在）
      if (this.currentUser) {
        this.allMembersList.push(this.currentUser);
      }

      // 添加其他员工
      this.staffList.forEach(staff => {
        this.allMembersList.push({
          ...staff,
          isCurrentUser: false
        });
      });

      this.totalMemberCount = this.allMembersList.length;
    },
    addStaff() {
      // 跳转到添加成员页面
      uni.navigateTo({
        url: '/pages/my/add-member'
      });
    },
    editStaff(staff) {
      // 编辑员工信息
      uni.showToast({
        title: '编辑功能开发中',
        icon: 'none'
      });
    },
    deleteStaff(staff) {
      // 删除功能暂不实现
      uni.showToast({
        title: '删除功能开发中',
        icon: 'none'
      });
    }
  }
};
</script>

<style lang="scss">
.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f5f5;
}

.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx;
  background-color: #4285f4;
  color: #fff;
}

.back-btn {
  font-size: 40rpx;
  width: 60rpx;
  display: flex;
  align-items: center;
}

.title {
  font-size: 36rpx;
  font-weight: 500;
}

.header-icons {
  display: flex;
  align-items: center;
}

.header-icon {
  margin-left: 15rpx;
  font-size: 24rpx;
}

.staff-section-title {
  padding: 30rpx 20rpx;
  font-size: 30rpx;
  color: #333;
  font-weight: 500;
}

.staff-list {
  background-color: #fff;
  flex: 1;
}

.empty-staff {
  padding: 100rpx 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

.staff-item {
  padding: 25rpx 20rpx;
  display: flex;
  align-items: center;
  border-bottom: 1rpx solid #f0f0f0;
  background-color: #fff;
}

.staff-avatar {
  width: 90rpx;
  height: 90rpx;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 25rpx;
  flex-shrink: 0;
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  background-color: #d0d0d0;
  border-radius: 50%;
}

.staff-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.staff-name {
  font-size: 30rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 8rpx;
  line-height: 1.2;
}

.staff-phone {
  font-size: 26rpx;
  color: #666;
  line-height: 1.2;
}

.staff-role-tag {
  padding: 6rpx 12rpx;
  background-color: transparent;
  border-radius: 0;
  border: none;
  flex-shrink: 0;
}

.role-text {
  font-size: 26rpx;
  color: #666;
  font-weight: 400;
}

.staff-actions {
  display: flex;
}

.action-btn {
  margin-left: 20rpx;
  padding: 8rpx 20rpx;
  font-size: 24rpx;
  border-radius: 30rpx;
}

.action-btn.edit {
  background-color: #e3f2fd;
  color: #2196f3;
}

.action-btn.delete {
  background-color: #ffebee;
  color: #f44336;
}

.add-btn {
  position: fixed;
  bottom: 40rpx;
  left: 40rpx;
  right: 40rpx;
  height: 90rpx;
  background-color: #4285f4;
  border-radius: 45rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #fff;
  font-size: 32rpx;
}
</style> 