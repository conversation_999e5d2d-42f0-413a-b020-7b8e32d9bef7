"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const _sfc_main = {
  data() {
    return {
      storeInfo: {
        name: "",
        bindCode: "",
        qrCode: ""
      },
      isMember: false
    };
  },
  onLoad() {
    this.loadStoreInfo();
  },
  methods: {
    goBack() {
      common_vendor.index.navigateBack();
    },
    loadStoreInfo() {
      const storeData = common_vendor.index.getStorageSync("storeInfo");
      if (storeData) {
        this.storeInfo = JSON.parse(storeData);
        this.isMember = !!this.storeInfo.bindCode && this.storeInfo.bindCode !== "空";
      }
    },
    shareStore() {
      if (!this.isMember) {
        common_vendor.index.showToast({
          title: "请先加入门店",
          icon: "none"
        });
        return;
      }
      common_vendor.index.showModal({
        title: "分享",
        content: "分享功能开发中",
        showCancel: false
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_assets._imports_0$9,
    b: common_vendor.o((...args) => $options.shareStore && $options.shareStore(...args))
  };
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/my/store-qrcode.js.map
