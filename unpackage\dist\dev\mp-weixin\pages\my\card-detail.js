"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const _sfc_main = {
  data() {
    return {
      gridItems: [
        "我的余额",
        "套餐订购",
        "我的套餐",
        "订购记录",
        "实名认证",
        "设备重启",
        "切换网络",
        "显示 Wifi",
        "关闭设备",
        "恢复出厂设置",
        "问题投诉",
        "企业微信"
      ],
      activeIndex: -1,
      popupType: "success",
      popupMessage: "",
      refreshing: false
    };
  },
  methods: {
    handleGridClick(item) {
      this.popupMessage = `即将跳转到${item}`;
      this.popupType = "info";
      this.$refs.popup.open();
      setTimeout(() => {
        common_vendor.index.showToast({
          title: `正在打开${item}`,
          icon: "none"
        });
      }, 500);
    },
    touchStart(index) {
      this.activeIndex = index;
    },
    touchEnd() {
      this.activeIndex = -1;
    },
    refreshStatus() {
      if (this.refreshing)
        return;
      this.refreshing = true;
      this.popupMessage = "正在刷新状态...";
      this.popupType = "loading";
      this.$refs.popup.open();
      setTimeout(() => {
        this.refreshing = false;
        this.popupMessage = "状态已刷新";
        this.popupType = "success";
        this.$refs.popup.open();
      }, 1500);
    },
    switchAccount() {
      common_vendor.index.navigateTo({
        url: "/pages/account/switch"
      });
    },
    changeWifiPassword() {
      common_vendor.index.navigateTo({
        url: "/pages/wifi/setting"
      });
    },
    renewPackage() {
      common_vendor.index.navigateTo({
        url: "/pages/package/renew"
      });
    },
    goToUserCenter() {
      common_vendor.index.navigateTo({
        url: "/pages/user/center"
      });
    },
    navigateTo(page) {
      let url = "";
      switch (page) {
        case "activate":
          url = "/pages/card/activate";
          break;
        case "service":
          url = "/pages/service/index";
          break;
        case "user":
          url = "/pages/user/center";
          break;
      }
      common_vendor.index.navigateTo({ url });
    }
  }
};
if (!Array) {
  const _easycom_uni_popup_message2 = common_vendor.resolveComponent("uni-popup-message");
  const _easycom_uni_popup2 = common_vendor.resolveComponent("uni-popup");
  (_easycom_uni_popup_message2 + _easycom_uni_popup2)();
}
const _easycom_uni_popup_message = () => "../../uni_modules/uni-popup/components/uni-popup-message/uni-popup-message.js";
const _easycom_uni_popup = () => "../../uni_modules/uni-popup/components/uni-popup/uni-popup.js";
if (!Math) {
  (_easycom_uni_popup_message + _easycom_uni_popup)();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_assets._imports_0$8,
    b: common_vendor.o((...args) => $options.refreshStatus && $options.refreshStatus(...args)),
    c: common_vendor.o((...args) => $options.switchAccount && $options.switchAccount(...args)),
    d: common_vendor.o((...args) => $options.changeWifiPassword && $options.changeWifiPassword(...args)),
    e: common_vendor.o((...args) => $options.renewPackage && $options.renewPackage(...args)),
    f: common_assets._imports_1$2,
    g: common_vendor.f($data.gridItems, (item, index, i0) => {
      return {
        a: "/static/icons/grid-" + index + ".png",
        b: common_vendor.t(item),
        c: index,
        d: common_vendor.o(($event) => $options.handleGridClick(item), index),
        e: common_vendor.o(($event) => $options.touchStart(index), index),
        f: common_vendor.o(($event) => $options.touchEnd(index), index),
        g: $data.activeIndex === index ? 1 : ""
      };
    }),
    h: common_assets._imports_2$1,
    i: common_vendor.o(($event) => $options.navigateTo("activate")),
    j: common_assets._imports_3$1,
    k: common_vendor.o(($event) => $options.navigateTo("service")),
    l: common_assets._imports_4$1,
    m: common_vendor.o(($event) => $options.navigateTo("user")),
    n: common_vendor.p({
      type: $data.popupType,
      message: $data.popupMessage,
      duration: 2e3
    }),
    o: common_vendor.sr("popup", "6772c398-0"),
    p: common_vendor.p({
      type: "message"
    })
  };
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/my/card-detail.js.map
