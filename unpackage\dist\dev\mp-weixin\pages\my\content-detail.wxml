<view class="content-detail"><swiper class="image-swiper" indicator-dots autoplay circular><swiper-item wx:for="{{a}}" wx:for-item="image" wx:key="b"><image class="swiper-image" src="{{image.a}}" mode="aspectFill"></image></swiper-item></swiper><view class="content-box"><text class="content-text">{{b}}</text></view><view class="meta-info"><view class="user-info"><image class="user-avatar" src="{{c}}"></image><text class="meta-user">{{d}}</text></view><text class="meta-time">{{e}}</text></view><view class="interaction-stats"><view class="stat-item"><text class="stat-num">{{f}}</text><text class="stat-label">评论</text></view><view class="stat-item"><text class="stat-num">{{g}}</text><text class="stat-label">点赞</text></view><view class="stat-item"><text class="stat-num">{{h}}</text><text class="stat-label">浏览</text></view></view><view class="comment-section"><view class="comment-title">评论</view><view wx:if="{{i}}" class="no-comment">暂无评论</view><view wx:else class="comment-list"><view wx:for="{{j}}" wx:for-item="comment" wx:key="d" class="comment-item"><view class="comment-user-info"><text class="comment-user">{{comment.a}}</text><text class="comment-time">{{comment.b}}</text></view><text class="comment-text">{{comment.c}}</text></view></view></view><view class="comment-input"><input placeholder="文明城市,友善交流..." class="input-box" value="{{k}}" bindinput="{{l}}"/><button class="send-btn" bindtap="{{m}}">发送</button></view></view>