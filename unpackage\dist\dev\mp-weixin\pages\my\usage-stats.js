"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_request = require("../../utils/request.js");
const utils_dyeConsumptionManager = require("../../utils/dyeConsumptionManager.js");
const common_assets = require("../../common/assets.js");
const _sfc_main = {
  data() {
    return {
      usageStats: {
        oneMonthUsage: 0,
        threeMonthsUsage: 0,
        sixMonthsUsage: 0,
        totalUsage: 0,
        usageDays: 0,
        dyeConsumption: 0
      },
      // 染膏消耗统计
      dyeConsumptionStats: {
        totalConsumption: 0,
        colorBreakdown: [],
        recentUsage: []
      },
      // 库存状态
      inventoryStatus: null,
      activeTimeRange: "7days",
      activeCompletionRange: "7days",
      hasCompletionData: false,
      loading: false,
      chartData: [],
      chartLabels: []
    };
  },
  onLoad() {
    this.loadUsageStats();
    this.loadDyeConsumptionStats();
    this.loadInventoryStatus();
    this.changeTimeRange(this.activeTimeRange);
  },
  methods: {
    goBack() {
      common_vendor.index.navigateBack();
    },
    // 加载使用统计数据
    async loadUsageStats() {
      try {
        this.loading = true;
        const response = await utils_request.apiService.mall.deviceUsage.getStats();
        if (response.code === 200) {
          this.usageStats = response.data;
        } else {
          common_vendor.index.__f__("error", "at pages/my/usage-stats.vue:209", "获取使用统计失败:", response.message);
          common_vendor.index.showToast({
            title: "获取数据失败",
            icon: "none"
          });
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/my/usage-stats.vue:216", "获取使用统计异常:", error);
        common_vendor.index.showToast({
          title: "网络异常",
          icon: "none"
        });
      } finally {
        this.loading = false;
      }
    },
    // 加载染膏消耗统计
    async loadDyeConsumptionStats() {
      try {
        const app = getApp();
        const deviceCode = app.globalData.connectedDeviceId;
        if (!deviceCode) {
          common_vendor.index.__f__("warn", "at pages/my/usage-stats.vue:232", "设备编码为空，无法加载染膏消耗统计");
          return;
        }
        const statsResponse = await utils_request.apiService.mall.dyeConsumption.getUsageStatistics(deviceCode, {
          timeRange: "30days"
        });
        if (statsResponse.code === 200) {
          this.dyeConsumptionStats = {
            totalConsumption: statsResponse.data.totalConsumption || 0,
            colorBreakdown: statsResponse.data.colorBreakdown || [],
            recentUsage: statsResponse.data.recentUsage || []
          };
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/my/usage-stats.vue:249", "加载染膏消耗统计失败:", error);
      }
    },
    // 加载库存状态
    async loadInventoryStatus() {
      try {
        const app = getApp();
        const deviceCode = app.globalData.connectedDeviceId;
        if (deviceCode) {
          this.inventoryStatus = await utils_dyeConsumptionManager.dyeConsumptionManager.getInventoryStatus(deviceCode);
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/my/usage-stats.vue:262", "加载库存状态失败:", error);
      }
    },
    // 刷新染膏消耗数据
    async refreshDyeConsumption() {
      try {
        common_vendor.index.showLoading({ title: "刷新中..." });
        await Promise.all([
          this.loadDyeConsumptionStats(),
          this.loadInventoryStatus()
        ]);
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({
          title: "刷新成功",
          icon: "success"
        });
      } catch (error) {
        common_vendor.index.hideLoading();
        common_vendor.index.__f__("error", "at pages/my/usage-stats.vue:281", "刷新染膏消耗数据失败:", error);
        common_vendor.index.showToast({
          title: "刷新失败",
          icon: "none"
        });
      }
    },
    // 切换时间范围
    async changeTimeRange(timeRange) {
      this.activeTimeRange = timeRange;
      try {
        const response = await utils_request.apiService.mall.deviceUsage.getChartData(timeRange);
        if (response.code === 200 && response.data) {
          this.updateChartData(response.data);
        } else {
          common_vendor.index.__f__("error", "at pages/my/usage-stats.vue:298", "获取时间范围数据失败:", response.message);
          this.chartData = [];
          this.chartLabels = [];
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/my/usage-stats.vue:303", "获取时间范围数据失败:", error);
        this.chartData = [];
        this.chartLabels = [];
      }
    },
    // 更新图表数据
    updateChartData(data) {
      if (!data || !data.chartData || data.chartData.length === 0) {
        this.chartData = [];
        this.chartLabels = [];
        return;
      }
      const maxValue = Math.max(...data.chartData.map((item) => item.value));
      this.chartData = data.chartData.map((item, index) => ({
        value: maxValue > 0 ? item.value / maxValue * 80 : 0,
        // 转换为百分比高度
        position: index / (data.chartData.length - 1) * 90 + 5
        // 计算水平位置
      }));
      this.chartLabels = data.labels || [];
    },
    // 切换完成率时间范围
    changeCompletionRange(timeRange) {
      this.activeCompletionRange = timeRange;
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.t($data.usageStats.oneMonthUsage || 0),
    b: common_vendor.t($data.usageStats.threeMonthsUsage || 0),
    c: common_vendor.t($data.usageStats.sixMonthsUsage || 0),
    d: common_vendor.t($data.usageStats.totalUsage || 0),
    e: common_vendor.t($data.usageStats.usageDays || 0),
    f: common_vendor.t($data.dyeConsumptionStats.totalConsumption || 0),
    g: common_vendor.o((...args) => $options.refreshDyeConsumption && $options.refreshDyeConsumption(...args)),
    h: $data.inventoryStatus
  }, $data.inventoryStatus ? common_vendor.e({
    i: common_vendor.t($data.inventoryStatus.totalColors),
    j: $data.inventoryStatus.alerts && $data.inventoryStatus.alerts.length > 0
  }, $data.inventoryStatus.alerts && $data.inventoryStatus.alerts.length > 0 ? common_vendor.e({
    k: common_vendor.t($data.inventoryStatus.lowStockCount),
    l: common_vendor.f($data.inventoryStatus.alerts.slice(0, 3), (alert, k0, i0) => {
      return {
        a: common_vendor.t(alert.colorName),
        b: common_vendor.t(alert.percentage),
        c: common_vendor.n(alert.level),
        d: alert.colorCode
      };
    }),
    m: $data.inventoryStatus.alerts.length > 3
  }, $data.inventoryStatus.alerts.length > 3 ? {
    n: common_vendor.t($data.inventoryStatus.alerts.length - 3)
  } : {}) : {}) : {}, {
    o: $data.dyeConsumptionStats.colorBreakdown && $data.dyeConsumptionStats.colorBreakdown.length > 0
  }, $data.dyeConsumptionStats.colorBreakdown && $data.dyeConsumptionStats.colorBreakdown.length > 0 ? {
    p: common_vendor.f($data.dyeConsumptionStats.colorBreakdown.slice(0, 5), (color, index, i0) => {
      return {
        a: common_vendor.t(index + 1),
        b: common_vendor.t(color.colorName),
        c: common_vendor.t(color.totalUsage),
        d: color.totalUsage / $data.dyeConsumptionStats.colorBreakdown[0].totalUsage * 100 + "%",
        e: color.colorCode
      };
    })
  } : {}, {
    q: $data.activeTimeRange === "7days" ? 1 : "",
    r: common_vendor.o(($event) => $options.changeTimeRange("7days")),
    s: $data.activeTimeRange === "30days" ? 1 : "",
    t: common_vendor.o(($event) => $options.changeTimeRange("30days")),
    v: $data.activeTimeRange === "6months" ? 1 : "",
    w: common_vendor.o(($event) => $options.changeTimeRange("6months")),
    x: $data.activeTimeRange === "1year" ? 1 : "",
    y: common_vendor.o(($event) => $options.changeTimeRange("1year")),
    z: $data.chartData.length > 0
  }, $data.chartData.length > 0 ? {
    A: common_vendor.f($data.chartData, (point, index, i0) => {
      return {
        a: index,
        b: point.value + "%",
        c: point.position + "%"
      };
    }),
    B: common_vendor.f($data.chartLabels, (label, index, i0) => {
      return {
        a: common_vendor.t(label),
        b: index
      };
    })
  } : {
    C: common_assets._imports_0$10
  }, {
    D: $data.activeCompletionRange === "7days" ? 1 : "",
    E: common_vendor.o(($event) => $options.changeCompletionRange("7days")),
    F: $data.activeCompletionRange === "30days" ? 1 : "",
    G: common_vendor.o(($event) => $options.changeCompletionRange("30days")),
    H: $data.activeCompletionRange === "6months" ? 1 : "",
    I: common_vendor.o(($event) => $options.changeCompletionRange("6months")),
    J: $data.activeCompletionRange === "1year" ? 1 : "",
    K: common_vendor.o(($event) => $options.changeCompletionRange("1year")),
    L: !$data.hasCompletionData
  }, !$data.hasCompletionData ? {
    M: common_assets._imports_0$10
  } : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/my/usage-stats.js.map
