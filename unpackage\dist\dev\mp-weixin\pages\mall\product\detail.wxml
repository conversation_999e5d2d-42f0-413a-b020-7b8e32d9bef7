<view class="detail-container"><scroll-view scroll-y class="detail-scroll"><swiper class="detail-swiper" indicator-dots autoplay circular><swiper-item wx:for="{{a}}" wx:for-item="img" wx:key="b"><image src="{{img.a}}" mode="aspectFill"></image></swiper-item></swiper><view class="product-title-section"><view class="product-title">{{b}}</view><view class="product-desc">{{c}}</view><view class="product-price-section"><text class="current-price">¥{{d}}</text><text wx:if="{{e}}" class="original-price">¥{{f}}</text></view><view class="product-stock">库存：{{g}}件</view></view><view wx:if="{{h}}" class="specs-section"><view class="section-title">商品参数</view><view class="specs-list"><view wx:for="{{i}}" wx:for-item="spec" wx:key="c" class="specs-item"><text class="specs-label">{{spec.a}}</text><text class="specs-value">{{spec.b}}</text></view></view></view><view wx:if="{{j}}" class="detail-content"><view class="section-title">商品详情</view><rich-text nodes="{{k}}"></rich-text></view><view wx:if="{{l}}" class="recommend-section"><view class="section-title">相关推荐</view><view class="recommend-list"><view wx:for="{{m}}" wx:for-item="item" wx:key="d" class="recommend-item" bindtap="{{item.e}}"><image src="{{item.a}}" mode="aspectFill"></image><text class="recommend-name">{{item.b}}</text><text class="recommend-price">¥{{item.c}}</text></view></view></view></scroll-view><view class="bottom-actions"><view class="action-item store" bindtap="{{n}}"><view class="action-icon">🏪</view><text class="action-text">店铺</text></view><view class="action-item cart" bindtap="{{o}}"><view class="action-icon">🛒</view><text class="action-text">购物车</text></view><view bindtap="{{r}}" class="{{['action-item', 'fav', s && 'favorited']}}"><view class="action-icon">{{p}}</view><text class="action-text">{{q}}</text></view><view class="{{['add-cart-btn', v && 'in-cart']}}" bindtap="{{w}}">{{t}}</view><view class="buy-now-btn" bindtap="{{x}}">立即购买</view></view></view>