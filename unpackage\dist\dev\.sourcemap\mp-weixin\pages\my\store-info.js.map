{"version": 3, "file": "store-info.js", "sources": ["pages/my/store-info.vue", "D:/edgexiazai/HBuilderX.4.66.2025051912/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvbXkvc3RvcmUtaW5mby52dWU"], "sourcesContent": ["<template>\n  <view class=\"container\">\n    <!-- 加载状态 -->\n    <view v-if=\"loading\" class=\"loading-container\">\n      <text class=\"loading-text\">加载中...</text>\n    </view>\n\n    <!-- 店铺信息表单 -->\n    <view v-else class=\"form-container\">\n      <view class=\"form-item\">\n        <text class=\"form-label\">店铺名称</text>\n        <input class=\"form-input\" type=\"text\" placeholder=\"请输入店铺或店长名称\" v-model=\"storeInfo.name\" />\n      </view>\n      \n      <view class=\"form-item\">\n        <text class=\"form-label\">联系手机</text>\n        <input class=\"form-input\" type=\"number\" placeholder=\"请填写真实手机号码\" v-model=\"storeInfo.phone\" />\n      </view>\n      \n      <view class=\"form-item\">\n        <text class=\"form-label\">绑定码</text>\n        <view class=\"form-input-container\">\n          <input\n            class=\"form-input-field\"\n            type=\"text\"\n            placeholder=\"请输入管理端提供的绑定码\"\n            v-model=\"storeInfo.bindCode\"\n            :disabled=\"isBindCodeDisabled\"\n            @blur=\"validateBindCode\"\n          />\n          <view v-if=\"isBindCodeDisabled\" class=\"bind-status\">已绑定</view>\n        </view>\n      </view>\n      \n      <view class=\"form-item\">\n        <text class=\"form-label\">所属类型</text>\n        <view class=\"form-input selector\" @click=\"showTypeSelector\">\n          <text>{{ getStoreTypeLabel() }}</text>\n          <text class=\"dropdown-icon\">▼</text>\n        </view>\n      </view>\n      \n      <view class=\"form-item\">\n        <text class=\"form-label\">企业地址</text>\n        <!-- <view class=\"form-input address\" @click=\"selectAddress\">\n          <text>{{ storeInfo.address || '请输入企业地址' }}</text>\n          <text class=\"location-icon\">◎</text>\n        </view> -->\n\t\t<view class=\"form-item\">\n\t\t  <!-- <text class=\"form-label\">企业地址</text> -->\n\t\t  <input class=\"form-input\" type=\"text\" placeholder=\"请输入企业地址\" v-model=\"storeInfo.address\" />\n\t\t</view>\n      </view>\n      \n      <view class=\"form-detail-address\">\n        <input class=\"detail-input\" type=\"text\" placeholder=\"详细地址(如街道、小区、门牌号)\" v-model=\"storeInfo.detailAddress\" />\n      </view>\n    </view>\n    \n    <!-- 底部保存按钮 -->\n    <view class=\"save-btn\" :class=\"{ disabled: saving }\" @click=\"saveStoreInfo\">\n      <text>{{ saving ? '保存中...' : '保存' }}</text>\n    </view>\n  </view>\n</template>\n\n<script>\nimport { apiService } from '@/utils/request.js';\n\nexport default {\n  data() {\n    return {\n      storeInfo: {\n        id: null,\n        name: '',\n        phone: '',\n        bindCode: '',\n        storeType: 1, // 1-门店，2-供应商\n        address: '',\n        detailAddress: '',\n        province: '',\n        city: '',\n        district: '',\n        latitude: null,\n        longitude: null,\n        description: ''\n      },\n      storeTypeOptions: [\n        { value: 1, label: '门店/Store' },\n        { value: 2, label: '工作室/Studio' },\n        { value: 3, label: '美发沙龙/Hair Salon' }\n      ],\n      saving: false,\n      loading: false,\n      isBindCodeValid: false,\n      bindCodeValidating: false,\n      isStoreAlreadyBound: false\n    };\n  },\n  computed: {\n    isBindCodeDisabled() {\n      // 如果已经有店铺ID，说明已经绑定了，禁用输入\n      return this.isStoreAlreadyBound || (this.storeInfo.id && this.storeInfo.id !== null);\n    }\n  },\n  onLoad() {\n    // 加载已有的门店信息\n    this.loadStoreInfo();\n    // 设置位置选择监听器\n    this.setupLocationListener();\n  },\n  onUnload() {\n    // 移除事件监听器\n    uni.$off('locationSelected');\n  },\n  methods: {\n    async loadStoreInfo() {\n      this.loading = true;\n      try {\n        const userInfo = uni.getStorageSync('userInfo');\n        if (userInfo && userInfo.id) {\n          const response = await apiService.mall.stores.userStores(userInfo.id);\n          if (response.code === 200 && response.data && response.data.length > 0) {\n            // 使用第一个门店作为当前门店\n            const store = response.data[0];\n            this.storeInfo = {\n              id: store.id,\n              name: store.name || '',\n              phone: store.phone || '',\n              bindCode: store.bindCode || store.bind_code || '',\n              storeType: store.storeType || store.store_type || 1,\n              address: store.address || '',\n              detailAddress: '', // 可以从address中解析\n              province: store.province || '',\n              city: store.city || '',\n              district: store.district || '',\n              latitude: store.latitude || null,\n              longitude: store.longitude || null,\n              description: store.description || ''\n            };\n\n            // 如果有店铺ID，说明已经绑定了店铺\n            if (store.id) {\n              this.isStoreAlreadyBound = true;\n              this.isBindCodeValid = true;\n            }\n          }\n        }\n      } catch (error) {\n        console.error('加载门店信息失败:', error);\n        // 静默失败，使用默认值\n      } finally {\n        this.loading = false;\n      }\n    },\n    showTypeSelector() {\n      // 显示门店类型选择器\n      const itemList = this.storeTypeOptions.map(option => option.label);\n      uni.showActionSheet({\n        itemList,\n        success: (res) => {\n          this.storeInfo.storeType = this.storeTypeOptions[res.tapIndex].value;\n        }\n      });\n    },\n    getStoreTypeLabel() {\n      const option = this.storeTypeOptions.find(opt => opt.value === this.storeInfo.storeType);\n      return option ? option.label : '门店/Store';\n    },\n    // 验证绑定码\n    async validateBindCode() {\n      const bindCode = this.storeInfo.bindCode.trim();\n\n      // 如果绑定码为空或已经绑定，不需要验证\n      if (!bindCode || this.isStoreAlreadyBound) {\n        return;\n      }\n\n      // 如果绑定码长度不符合要求\n      if (bindCode.length < 6) {\n        this.isBindCodeValid = false;\n        return;\n      }\n\n      this.bindCodeValidating = true;\n\n      try {\n        const response = await apiService.mall.stores.validateBindCode(bindCode);\n\n        if (response.code === 200 && response.data) {\n          this.isBindCodeValid = true;\n          // 可以显示店铺信息预览\n          uni.showToast({\n            title: '绑定码有效',\n            icon: 'success'\n          });\n        } else {\n          this.isBindCodeValid = false;\n          uni.showToast({\n            title: '绑定码无效或已被使用',\n            icon: 'none'\n          });\n        }\n      } catch (error) {\n        console.error('验证绑定码失败:', error);\n        this.isBindCodeValid = false;\n        uni.showToast({\n          title: '验证绑定码失败',\n          icon: 'none'\n        });\n      } finally {\n        this.bindCodeValidating = false;\n      }\n    },\n    selectAddress() {\n      // 打开位置选择器\n      // uni.navigateTo({\n      //   url: '/pages/location-picker'\n      // });\n    },\n\n    // 设置位置选择监听器\n    setupLocationListener() {\n      // 监听从位置选择页面返回的数据\n      uni.$on('locationSelected', (location) => {\n        this.updateStoreLocation(location);\n      });\n    },\n\n    // 更新店铺位置信息\n    updateStoreLocation(location) {\n      this.storeInfo.address = location.address || location.name;\n      this.storeInfo.province = this.extractProvince(location.address);\n      this.storeInfo.city = this.extractCity(location.address);\n      this.storeInfo.district = this.extractDistrict(location.address);\n\n      // 如果有经纬度信息，也保存\n      if (location.latitude && location.longitude) {\n        this.storeInfo.latitude = location.latitude;\n        this.storeInfo.longitude = location.longitude;\n      }\n\n      uni.showToast({\n        title: '地址已更新',\n        icon: 'success'\n      });\n    },\n\n    // 从地址中提取省份\n    extractProvince(address) {\n      if (!address) return '';\n      const match = address.match(/^([^省]+省|[^自治区]+自治区|[^市]+市)/);\n      return match ? match[1] : '';\n    },\n\n    // 从地址中提取城市\n    extractCity(address) {\n      if (!address) return '';\n      const match = address.match(/省([^市]+市)|自治区([^市]+市)|^([^市]+市)/);\n      return match ? (match[1] || match[2] || match[3]) : '';\n    },\n\n    // 从地址中提取区县\n    extractDistrict(address) {\n      if (!address) return '';\n      const match = address.match(/市([^区县]+[区县])/);\n      return match ? match[1] : '';\n    },\n    async saveStoreInfo() {\n      // 防抖处理 - 如果正在保存中，直接返回\n      if (this.saving) {\n        return;\n      }\n\n      // 验证表单\n      if (!this.storeInfo.name.trim()) {\n        uni.showToast({\n          title: '请输入店铺名称',\n          icon: 'none'\n        });\n        return;\n      }\n\n      if (!this.storeInfo.phone.trim()) {\n        uni.showToast({\n          title: '请输入联系手机',\n          icon: 'none'\n        });\n        return;\n      }\n\n      // 如果没有绑定店铺，需要验证绑定码\n      if (!this.isStoreAlreadyBound) {\n        if (!this.storeInfo.bindCode.trim()) {\n          uni.showToast({\n            title: '请输入绑定码',\n            icon: 'none'\n          });\n          return;\n        }\n\n        if (!this.isBindCodeValid) {\n          uni.showToast({\n            title: '请输入有效的绑定码',\n            icon: 'none'\n          });\n          return;\n        }\n      }\n\n      // 验证手机号格式\n      const phoneRegex = /^1[3-9]\\d{9}$/;\n      if (!phoneRegex.test(this.storeInfo.phone)) {\n        uni.showToast({\n          title: '请输入正确的手机号',\n          icon: 'none'\n        });\n        return;\n      }\n\n      this.saving = true;\n\n      try {\n        const userInfo = uni.getStorageSync('userInfo');\n\t\tconsole.log(\"用户信息\",userInfo)\n        if (!userInfo || !userInfo.id) {\n          throw new Error('用户信息不存在，请重新登录');\n        }\n\n        // 准备保存的数据\n        // 拼接完整地址：定位地址 + 详细地址\n        const baseAddress = [\n          this.storeInfo.province || '',\n          this.storeInfo.city || '',\n          this.storeInfo.district || '',\n          this.storeInfo.address || ''\n        ].filter(item => item.trim()).join('');\n\n        const fullAddress = baseAddress + (this.storeInfo.detailAddress ? this.storeInfo.detailAddress.trim() : '');\n\n        const storeData = {\n          name: this.storeInfo.name.trim(),\n          phone: this.storeInfo.phone.trim(),\n          storeType: this.storeInfo.storeType,\n          address: fullAddress,\n          province: this.storeInfo.province || '',\n          city: this.storeInfo.city || '',\n          district: this.storeInfo.district || '',\n          latitude: this.storeInfo.latitude,\n          longitude: this.storeInfo.longitude,\n          description: this.storeInfo.description || ''\n        };\n\n        let response;\n        if (this.storeInfo.id) {\n          // 更新现有门店\n          response = await apiService.mall.stores.update(this.storeInfo.id, storeData);\n        } else if (this.storeInfo.bindCode.trim()) {\n          // 通过绑定码绑定店铺\n          response = await apiService.mall.stores.bindStore(this.storeInfo.bindCode.trim(), userInfo.id);\n\n          if (response.code === 200) {\n            // 绑定成功后，立即更新店铺信息\n            this.storeInfo.id = response.data.id;\n            this.isStoreAlreadyBound = true;\n\n            // 绑定成功后，立即更新店铺的详细信息\n            const updateResponse = await apiService.mall.stores.update(this.storeInfo.id, storeData);\n            if (updateResponse.code !== 200) {\n              throw new Error(updateResponse.message || '更新店铺信息失败');\n            }\n            response = updateResponse; // 使用更新的响应\n          }\n        } else {\n          // 创建新门店\n          response = await apiService.mall.stores.create(storeData, userInfo.id);\n        }\n\n        if (response.code === 200) {\n          // 保存成功，更新本地存储\n          uni.setStorageSync('storeInfo', JSON.stringify(this.storeInfo));\n\n          uni.showToast({\n            title: '保存成功',\n            icon: 'success',\n            duration: 2000\n          });\n\n          // 延迟返回上一页\n          setTimeout(() => {\n            uni.navigateBack();\n          }, 2000);\n        } else {\n          throw new Error(response.message || '保存失败');\n        }\n      } catch (error) {\n        console.error('保存门店信息失败:', error);\n        uni.showToast({\n          title: error.message || '保存失败，请重试',\n          icon: 'none',\n          duration: 3000\n        });\n      } finally {\n        this.saving = false;\n      }\n    }\n  }\n};\n</script>\n\n<style lang=\"scss\">\n.container {\n  display: flex;\n  flex-direction: column;\n  min-height: 100vh;\n  background-color: #f5f5f5;\n}\n\n.loading-container {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  height: 400rpx;\n}\n\n.loading-text {\n  font-size: 30rpx;\n  color: #999;\n}\n\n.form-container {\n  background-color: #fff;\n  padding: 0 20rpx;\n}\n\n.form-item {\n  display: flex;\n  align-items: center;\n  padding: 30rpx 0;\n  border-bottom: 1rpx solid #eee;\n}\n\n.form-label {\n  width: 180rpx;\n  font-size: 30rpx;\n  color: #663c00;\n  font-weight: 500;\n}\n\n.form-input {\n  flex: 1;\n  font-size: 30rpx;\n  color: #999;\n}\n\n.form-input.disabled {\n  color: #999;\n  background-color: #f8f8f8;\n  padding: 10rpx;\n  border-radius: 8rpx;\n  font-style: italic;\n}\n\n.form-input-container {\n  flex: 1;\n  display: flex;\n  height: 50rpx;\n  align-items: center;\n  position: relative;\n}\n\n.form-input-field {\n  flex: 1;\n  font-size: 30rpx;\n  height: 70rpx;\n  color: #333;\n  padding: 10rpx;\n  border: 1rpx solid #ddd;\n  border-radius: 8rpx;\n  background-color: #fff;\n}\n\n.form-input-field:disabled {\n  background-color: #f8f8f8;\n  color: #999;\n}\n\n.bind-status {\n  position: absolute;\n  right: 10rpx;\n  font-size: 24rpx;\n  color: #4285f4;\n  background-color: #e3f2fd;\n  padding: 4rpx 8rpx;\n  border-radius: 4rpx;\n}\n\n.form-input.selector, .form-input.address {\n  display: flex;\n  justify-content: space-between;\n}\n\n.dropdown-icon, .location-icon {\n  color: #999;\n  font-size: 24rpx;\n}\n\n.form-detail-address {\n  background-color: #fff;\n  padding: 20rpx 0 30rpx 180rpx;\n  border-bottom: 1rpx solid #eee;\n}\n\n.detail-input {\n  font-size: 28rpx;\n  color: #999;\n}\n\n.save-btn {\n  position: fixed;\n  bottom: 40rpx;\n  left: 40rpx;\n  right: 40rpx;\n  height: 90rpx;\n  background-color: #4285f4;\n  border-radius: 45rpx;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  color: #fff;\n  font-size: 32rpx;\n\n  &.disabled {\n    background-color: #ccc;\n    color: #999;\n  }\n}\n</style> ", "import MiniProgramPage from 'E:/tempCode/new_www_production/new_www_production/pages/my/store-info.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni", "apiService"], "mappings": ";;;AAqEA,MAAK,YAAU;AAAA,EACb,OAAO;AACL,WAAO;AAAA,MACL,WAAW;AAAA,QACT,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,OAAO;AAAA,QACP,UAAU;AAAA,QACV,WAAW;AAAA;AAAA,QACX,SAAS;AAAA,QACT,eAAe;AAAA,QACf,UAAU;AAAA,QACV,MAAM;AAAA,QACN,UAAU;AAAA,QACV,UAAU;AAAA,QACV,WAAW;AAAA,QACX,aAAa;AAAA,MACd;AAAA,MACD,kBAAkB;AAAA,QAChB,EAAE,OAAO,GAAG,OAAO,WAAY;AAAA,QAC/B,EAAE,OAAO,GAAG,OAAO,aAAc;AAAA,QACjC,EAAE,OAAO,GAAG,OAAO,kBAAkB;AAAA,MACtC;AAAA,MACD,QAAQ;AAAA,MACR,SAAS;AAAA,MACT,iBAAiB;AAAA,MACjB,oBAAoB;AAAA,MACpB,qBAAqB;AAAA;EAExB;AAAA,EACD,UAAU;AAAA,IACR,qBAAqB;AAEnB,aAAO,KAAK,uBAAwB,KAAK,UAAU,MAAM,KAAK,UAAU,OAAO;AAAA,IACjF;AAAA,EACD;AAAA,EACD,SAAS;AAEP,SAAK,cAAa;AAElB,SAAK,sBAAqB;AAAA,EAC3B;AAAA,EACD,WAAW;AAETA,wBAAI,KAAK,kBAAkB;AAAA,EAC5B;AAAA,EACD,SAAS;AAAA,IACP,MAAM,gBAAgB;AACpB,WAAK,UAAU;AACf,UAAI;AACF,cAAM,WAAWA,cAAAA,MAAI,eAAe,UAAU;AAC9C,YAAI,YAAY,SAAS,IAAI;AAC3B,gBAAM,WAAW,MAAMC,cAAAA,WAAW,KAAK,OAAO,WAAW,SAAS,EAAE;AACpE,cAAI,SAAS,SAAS,OAAO,SAAS,QAAQ,SAAS,KAAK,SAAS,GAAG;AAEtE,kBAAM,QAAQ,SAAS,KAAK,CAAC;AAC7B,iBAAK,YAAY;AAAA,cACf,IAAI,MAAM;AAAA,cACV,MAAM,MAAM,QAAQ;AAAA,cACpB,OAAO,MAAM,SAAS;AAAA,cACtB,UAAU,MAAM,YAAY,MAAM,aAAa;AAAA,cAC/C,WAAW,MAAM,aAAa,MAAM,cAAc;AAAA,cAClD,SAAS,MAAM,WAAW;AAAA,cAC1B,eAAe;AAAA;AAAA,cACf,UAAU,MAAM,YAAY;AAAA,cAC5B,MAAM,MAAM,QAAQ;AAAA,cACpB,UAAU,MAAM,YAAY;AAAA,cAC5B,UAAU,MAAM,YAAY;AAAA,cAC5B,WAAW,MAAM,aAAa;AAAA,cAC9B,aAAa,MAAM,eAAe;AAAA;AAIpC,gBAAI,MAAM,IAAI;AACZ,mBAAK,sBAAsB;AAC3B,mBAAK,kBAAkB;AAAA,YACzB;AAAA,UACF;AAAA,QACF;AAAA,MACA,SAAO,OAAO;AACdD,sBAAc,MAAA,MAAA,SAAA,kCAAA,aAAa,KAAK;AAAA,MAElC,UAAU;AACR,aAAK,UAAU;AAAA,MACjB;AAAA,IACD;AAAA,IACD,mBAAmB;AAEjB,YAAM,WAAW,KAAK,iBAAiB,IAAI,YAAU,OAAO,KAAK;AACjEA,oBAAAA,MAAI,gBAAgB;AAAA,QAClB;AAAA,QACA,SAAS,CAAC,QAAQ;AAChB,eAAK,UAAU,YAAY,KAAK,iBAAiB,IAAI,QAAQ,EAAE;AAAA,QACjE;AAAA,MACF,CAAC;AAAA,IACF;AAAA,IACD,oBAAoB;AAClB,YAAM,SAAS,KAAK,iBAAiB,KAAK,SAAO,IAAI,UAAU,KAAK,UAAU,SAAS;AACvF,aAAO,SAAS,OAAO,QAAQ;AAAA,IAChC;AAAA;AAAA,IAED,MAAM,mBAAmB;AACvB,YAAM,WAAW,KAAK,UAAU,SAAS,KAAI;AAG7C,UAAI,CAAC,YAAY,KAAK,qBAAqB;AACzC;AAAA,MACF;AAGA,UAAI,SAAS,SAAS,GAAG;AACvB,aAAK,kBAAkB;AACvB;AAAA,MACF;AAEA,WAAK,qBAAqB;AAE1B,UAAI;AACF,cAAM,WAAW,MAAMC,yBAAW,KAAK,OAAO,iBAAiB,QAAQ;AAEvE,YAAI,SAAS,SAAS,OAAO,SAAS,MAAM;AAC1C,eAAK,kBAAkB;AAEvBD,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UACR,CAAC;AAAA,eACI;AACL,eAAK,kBAAkB;AACvBA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UACR,CAAC;AAAA,QACH;AAAA,MACA,SAAO,OAAO;AACdA,sBAAc,MAAA,MAAA,SAAA,kCAAA,YAAY,KAAK;AAC/B,aAAK,kBAAkB;AACvBA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AAAA,MACH,UAAU;AACR,aAAK,qBAAqB;AAAA,MAC5B;AAAA,IACD;AAAA,IACD,gBAAgB;AAAA,IAKf;AAAA;AAAA,IAGD,wBAAwB;AAEtBA,oBAAAA,MAAI,IAAI,oBAAoB,CAAC,aAAa;AACxC,aAAK,oBAAoB,QAAQ;AAAA,MACnC,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,oBAAoB,UAAU;AAC5B,WAAK,UAAU,UAAU,SAAS,WAAW,SAAS;AACtD,WAAK,UAAU,WAAW,KAAK,gBAAgB,SAAS,OAAO;AAC/D,WAAK,UAAU,OAAO,KAAK,YAAY,SAAS,OAAO;AACvD,WAAK,UAAU,WAAW,KAAK,gBAAgB,SAAS,OAAO;AAG/D,UAAI,SAAS,YAAY,SAAS,WAAW;AAC3C,aAAK,UAAU,WAAW,SAAS;AACnC,aAAK,UAAU,YAAY,SAAS;AAAA,MACtC;AAEAA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACR,CAAC;AAAA,IACF;AAAA;AAAA,IAGD,gBAAgB,SAAS;AACvB,UAAI,CAAC;AAAS,eAAO;AACrB,YAAM,QAAQ,QAAQ,MAAM,6BAA6B;AACzD,aAAO,QAAQ,MAAM,CAAC,IAAI;AAAA,IAC3B;AAAA;AAAA,IAGD,YAAY,SAAS;AACnB,UAAI,CAAC;AAAS,eAAO;AACrB,YAAM,QAAQ,QAAQ,MAAM,iCAAiC;AAC7D,aAAO,QAAS,MAAM,CAAC,KAAK,MAAM,CAAC,KAAK,MAAM,CAAC,IAAK;AAAA,IACrD;AAAA;AAAA,IAGD,gBAAgB,SAAS;AACvB,UAAI,CAAC;AAAS,eAAO;AACrB,YAAM,QAAQ,QAAQ,MAAM,eAAe;AAC3C,aAAO,QAAQ,MAAM,CAAC,IAAI;AAAA,IAC3B;AAAA,IACD,MAAM,gBAAgB;AAEpB,UAAI,KAAK,QAAQ;AACf;AAAA,MACF;AAGA,UAAI,CAAC,KAAK,UAAU,KAAK,KAAI,GAAI;AAC/BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AACD;AAAA,MACF;AAEA,UAAI,CAAC,KAAK,UAAU,MAAM,KAAI,GAAI;AAChCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AACD;AAAA,MACF;AAGA,UAAI,CAAC,KAAK,qBAAqB;AAC7B,YAAI,CAAC,KAAK,UAAU,SAAS,KAAI,GAAI;AACnCA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UACR,CAAC;AACD;AAAA,QACF;AAEA,YAAI,CAAC,KAAK,iBAAiB;AACzBA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UACR,CAAC;AACD;AAAA,QACF;AAAA,MACF;AAGA,YAAM,aAAa;AACnB,UAAI,CAAC,WAAW,KAAK,KAAK,UAAU,KAAK,GAAG;AAC1CA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACR,CAAC;AACD;AAAA,MACF;AAEA,WAAK,SAAS;AAEd,UAAI;AACF,cAAM,WAAWA,cAAAA,MAAI,eAAe,UAAU;AACpDA,sBAAAA,MAAY,MAAA,OAAA,kCAAA,QAAO,QAAQ;AACrB,YAAI,CAAC,YAAY,CAAC,SAAS,IAAI;AAC7B,gBAAM,IAAI,MAAM,eAAe;AAAA,QACjC;AAIA,cAAM,cAAc;AAAA,UAClB,KAAK,UAAU,YAAY;AAAA,UAC3B,KAAK,UAAU,QAAQ;AAAA,UACvB,KAAK,UAAU,YAAY;AAAA,UAC3B,KAAK,UAAU,WAAW;AAAA,QAC5B,EAAE,OAAO,UAAQ,KAAK,KAAI,CAAE,EAAE,KAAK,EAAE;AAErC,cAAM,cAAc,eAAe,KAAK,UAAU,gBAAgB,KAAK,UAAU,cAAc,KAAO,IAAE;AAExG,cAAM,YAAY;AAAA,UAChB,MAAM,KAAK,UAAU,KAAK,KAAM;AAAA,UAChC,OAAO,KAAK,UAAU,MAAM,KAAM;AAAA,UAClC,WAAW,KAAK,UAAU;AAAA,UAC1B,SAAS;AAAA,UACT,UAAU,KAAK,UAAU,YAAY;AAAA,UACrC,MAAM,KAAK,UAAU,QAAQ;AAAA,UAC7B,UAAU,KAAK,UAAU,YAAY;AAAA,UACrC,UAAU,KAAK,UAAU;AAAA,UACzB,WAAW,KAAK,UAAU;AAAA,UAC1B,aAAa,KAAK,UAAU,eAAe;AAAA;AAG7C,YAAI;AACJ,YAAI,KAAK,UAAU,IAAI;AAErB,qBAAW,MAAMC,yBAAW,KAAK,OAAO,OAAO,KAAK,UAAU,IAAI,SAAS;AAAA,QAC7E,WAAW,KAAK,UAAU,SAAS,KAAI,GAAI;AAEzC,qBAAW,MAAMA,cAAU,WAAC,KAAK,OAAO,UAAU,KAAK,UAAU,SAAS,KAAI,GAAI,SAAS,EAAE;AAE7F,cAAI,SAAS,SAAS,KAAK;AAEzB,iBAAK,UAAU,KAAK,SAAS,KAAK;AAClC,iBAAK,sBAAsB;AAG3B,kBAAM,iBAAiB,MAAMA,yBAAW,KAAK,OAAO,OAAO,KAAK,UAAU,IAAI,SAAS;AACvF,gBAAI,eAAe,SAAS,KAAK;AAC/B,oBAAM,IAAI,MAAM,eAAe,WAAW,UAAU;AAAA,YACtD;AACA,uBAAW;AAAA,UACb;AAAA,eACK;AAEL,qBAAW,MAAMA,cAAU,WAAC,KAAK,OAAO,OAAO,WAAW,SAAS,EAAE;AAAA,QACvE;AAEA,YAAI,SAAS,SAAS,KAAK;AAEzBD,wBAAG,MAAC,eAAe,aAAa,KAAK,UAAU,KAAK,SAAS,CAAC;AAE9DA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,YACN,UAAU;AAAA,UACZ,CAAC;AAGD,qBAAW,MAAM;AACfA,0BAAG,MAAC,aAAY;AAAA,UACjB,GAAE,GAAI;AAAA,eACF;AACL,gBAAM,IAAI,MAAM,SAAS,WAAW,MAAM;AAAA,QAC5C;AAAA,MACA,SAAO,OAAO;AACdA,sBAAc,MAAA,MAAA,SAAA,kCAAA,aAAa,KAAK;AAChCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO,MAAM,WAAW;AAAA,UACxB,MAAM;AAAA,UACN,UAAU;AAAA,QACZ,CAAC;AAAA,MACH,UAAU;AACR,aAAK,SAAS;AAAA,MAChB;AAAA,IACF;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACtZA,GAAG,WAAW,eAAe;"}