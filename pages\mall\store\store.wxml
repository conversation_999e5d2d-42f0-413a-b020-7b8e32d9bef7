<view class="store-container">
	<!-- 顶部导航栏 -->
	<view class="top-bar">
		<view class="back-btn" bindtap="goBack">
			<text class="iconfont icon-back">←</text>
		</view>
		<view class="title">{{storeInfo.name}}</view>
		<view class="more-btn">•••</view>
	</view>
	
	<!-- 店铺头部信息 -->
	<view class="store-header">
		<view class="store-bg">
			<image src="/static/images/store-bg.jpg" mode="aspectFill"></image>
		</view>
		<view class="store-info">
			<view class="store-main">
				<view class="store-name-row">
					<text class="store-name">{{storeInfo.name}}</text>
					<view class="rating-badge">
						<text class="rating-score">{{storeInfo.rating.products}}</text>
						<text class="rating-text">分</text>
					</view>
				</view>
				<view class="business-license">
					<text>营业执照：江西省瑞金市药妆设备有限公司</text>
				</view>
			</view>
			<view class="follow-btn" bindtap="followStore">
				<text>{{isFollowed ? '已关注' : '+ 关注'}}</text>
			</view>
		</view>
	</view>
	
	<!-- 分类导航 -->
	<view class="category-nav">
		<view class="nav-item active">全部商品</view>
		<view class="nav-item">新品上架</view>
		<view class="nav-item">热销商品</view>
		<view class="nav-item">优惠活动</view>
	</view>
	
	<!-- 筛选栏 -->
	<view class="filter-bar">
		<view class="filter-item">
			<text>综合</text>
			<text class="arrow">▼</text>
		</view>
		<view class="filter-item">
			<text>销量</text>
			<text class="arrow">▼</text>
		</view>
		<view class="filter-item">
			<text>价格</text>
			<text class="arrow">▼</text>
		</view>
	</view>
	
	<!-- 商品列表 -->
	<view class="product-list">
		<view class="product-item" wx:for="{{storeProducts}}" wx:key="index" bindtap="goToProduct" data-id="{{item.id}}">
			<image class="product-image" src="{{item.image}}" mode="aspectFill"></image>
			<view class="product-info">
				<text class="product-name">{{item.name}}</text>
				<view class="product-price-row">
					<text class="product-price">¥{{item.price}}</text>
					<text class="product-sold">已售{{item.soldCount}}</text>
				</view>
			</view>
		</view>
	</view>
</view> 