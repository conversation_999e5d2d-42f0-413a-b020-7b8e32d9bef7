"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_request = require("../../utils/request.js");
const common_assets = require("../../common/assets.js");
const _sfc_main = {
  data() {
    return {
      currentTime: this.getCurrentTime()
    };
  },
  methods: {
    getCurrentTime() {
      const now = /* @__PURE__ */ new Date();
      const hours = now.getHours().toString().padStart(2, "0");
      const minutes = now.getMinutes().toString().padStart(2, "0");
      return `${hours}:${minutes}`;
    },
    navigateToProfile() {
      common_vendor.index.navigateTo({
        url: "/pages/profile/index"
      });
    },
    navigateToAccountSettings() {
      common_vendor.index.navigateTo({
        url: "/pages/account/settings"
      });
    },
    handleLogout() {
      common_vendor.index.showModal({
        title: "提示",
        content: "确定要退出登录吗？",
        success: (res) => {
          if (res.confirm) {
            utils_request.clearUserStatus();
            common_vendor.index.switchTab({
              url: "/pages/index/index"
            });
          }
        }
      });
    },
    handleDeleteAccount() {
      common_vendor.index.showModal({
        title: "警告",
        content: "注销账号将永久删除您的所有数据，确定要继续吗？",
        confirmText: "确定注销",
        confirmColor: "#FF0000",
        success: (res) => {
          if (res.confirm) {
            this.deleteAccount();
          }
        }
      });
    },
    async deleteAccount() {
      try {
        await common_vendor.index.request({
          url: "/api/user/delete",
          method: "POST",
          header: {
            "Authorization": "Bearer " + common_vendor.index.getStorageSync("token")
          }
        });
        common_vendor.index.showToast({
          title: "账号已注销",
          icon: "success"
        });
        common_vendor.index.reLaunch({
          url: "/pages/login/index"
        });
      } catch (error) {
        common_vendor.index.showToast({
          title: "注销失败",
          icon: "none"
        });
      }
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_assets._imports_0$12,
    b: common_vendor.o((...args) => $options.navigateToProfile && $options.navigateToProfile(...args)),
    c: common_assets._imports_0$12,
    d: common_vendor.o((...args) => $options.navigateToAccountSettings && $options.navigateToAccountSettings(...args)),
    e: common_vendor.o((...args) => $options.handleLogout && $options.handleLogout(...args)),
    f: common_vendor.o((...args) => $options.handleDeleteAccount && $options.handleDeleteAccount(...args))
  };
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/outLogin/outLogin.js.map
