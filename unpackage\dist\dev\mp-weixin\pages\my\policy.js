"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  data() {
    return {};
  },
  methods: {
    goBack() {
      common_vendor.index.navigateBack();
    },
    navigateToUserAgreement() {
      common_vendor.index.navigateTo({
        url: "/pages/my/user-agreement"
      });
    },
    navigateToPrivacyPolicy() {
      common_vendor.index.navigateTo({
        url: "/pages/my/privacy-policy"
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.o((...args) => $options.navigateToUserAgreement && $options.navigateToUserAgreement(...args)),
    b: common_vendor.o((...args) => $options.navigateToPrivacyPolicy && $options.navigateToPrivacyPolicy(...args))
  };
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/my/policy.js.map
