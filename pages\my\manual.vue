<template>
  <view class="manual-container">
    <!-- <view class="header">
      <view class="back-btn" @click="goBack">
        <text class="iconfont icon-arrow-left"></text>
      </view>
      <view class="title">资料手册</view>
    </view> -->
    
    <view class="content">
      <!-- 加载状态 -->
      <view v-if="loading" class="loading-container">
        <text>加载中...</text>
      </view>

      <!-- 错误状态 -->
      <view v-else-if="error" class="error-container">
        <text>{{ error }}</text>
        <button @click="loadManualList" class="retry-btn">重试</button>
      </view>

      <view v-else>
        <view class="category-tabs">
          <view
            v-for="(tab, index) in tabs"
            :key="index"
            class="tab-item"
            :class="{ active: currentTab === index }"
            @click="switchTab(index)"
          >
            <text>{{ tab.name }}</text>
          </view>
        </view>
      
      <!-- PDF文档列表 -->
      <view v-if="currentTab === 0" class="resource-list">
        <view 
          v-for="(item, index) in pdfList" 
          :key="index" 
          class="resource-item"
          @click="openResource(item)"
        >
          <view class="resource-icon">
            <text class="iconfont icon-pdf"></text>
          </view>
          <view class="resource-info">
            <text class="resource-name">{{ item.name }}</text>
            <text class="resource-date">{{ item.date }}</text>
          </view>
          <view class="resource-action">
            <text class="iconfont icon-download"></text>
          </view>
        </view>
      </view>
      
      <!-- 图片列表 -->
      <view v-if="currentTab === 1" class="resource-list image-list">
        <view 
          v-for="(item, index) in imageList" 
          :key="index" 
          class="image-item"
          @click="previewImage(item)"
        >
          <image class="thumbnail" :src="item.url" mode="aspectFill"></image>
          <text class="image-name">{{ item.name }}</text>
        </view>
      </view>
      
      <!-- 视频列表 -->
      <view v-if="currentTab === 2" class="resource-list">
        <view 
          v-for="(item, index) in videoList" 
          :key="index" 
          class="resource-item"
          @click="playVideo(item)"
        >
          <view class="resource-icon video-icon">
            <text class="iconfont icon-video"></text>
          </view>
          <view class="resource-info">
            <text class="resource-name">{{ item.name }}</text>
            <text class="resource-date">{{ item.date }}</text>
          </view>
          <view class="resource-action">
            <text class="iconfont icon-play"></text>
          </view>
        </view>
      </view>
      </view>
    </view>
  </view>
</template>

<script>
import { get } from '@/utils/request.js'

export default {
  data() {
    return {
      currentTab: 0,
      tabs: [
        { name: 'PDF文档' },
        { name: '图片' },
        { name: '视频' }
      ],
      manualList: [],
      pdfList: [],
      imageList: [],
      videoList: [],
      loading: false,
      error: ''
    }
  },
  onLoad() {
    this.loadManualList()
  },
  methods: {
    goBack() {
      uni.navigateBack({
        delta: 1
      });
    },
    switchTab(index) {
      this.currentTab = index;
    },

    async loadManualList() {
      try {
        this.loading = true
        this.error = ''

        const response = await get('/api/manual/list')

        if (response && response.code === 200) {
          this.manualList = response.data || []
          this.categorizeManuals()
        } else {
          this.error = response?.message || '获取资料手册失败'
          this.loadDefaultData() // 加载默认数据作为备用
        }
      } catch (error) {
        console.error('获取资料手册失败:', error)
        this.error = '网络错误，请稍后重试'
        this.loadDefaultData() // 加载默认数据作为备用
      } finally {
        this.loading = false
      }
    },

    categorizeManuals() {
      // 根据文件URL后缀分类
      this.pdfList = this.manualList.filter(item =>
        item.fileUrl && (item.fileUrl.toLowerCase().includes('.pdf') || item.title.includes('PDF') || item.title.includes('文档'))
      ).map(item => ({
        id: item.id,
        name: item.title,
        url: item.fileUrl,
        date: this.formatDate(item.updateTime),
        description: item.description
      }))

      this.imageList = this.manualList.filter(item =>
        item.fileUrl && (item.fileUrl.toLowerCase().match(/\.(jpg|jpeg|png|gif|webp)$/) || item.title.includes('图片') || item.title.includes('图'))
      ).map(item => ({
        id: item.id,
        name: item.title,
        url: item.fileUrl || item.coverImage,
        description: item.description
      }))

      this.videoList = this.manualList.filter(item =>
        item.fileUrl && (item.fileUrl.toLowerCase().match(/\.(mp4|avi|mov|wmv|flv)$/) || item.title.includes('视频') || item.title.includes('教程'))
      ).map(item => ({
        id: item.id,
        name: item.title,
        url: item.fileUrl,
        date: this.formatDate(item.updateTime),
        description: item.description
      }))
    },

    loadDefaultData() {
      // 备用静态数据
      this.pdfList = [
        { name: '用户操作指南', url: '/static/manuals/user_guide.pdf', date: '2023-05-10' },
        { name: '产品说明书', url: '/static/manuals/product_manual.pdf', date: '2023-04-22' },
        { name: '常见问题解答', url: '/static/manuals/faq.pdf', date: '2023-03-15' }
      ]
      this.imageList = [
        { name: '产品示意图', url: '/static/manuals/images/product_diagram.jpg' },
        { name: '使用流程图', url: '/static/manuals/images/usage_flow.jpg' },
        { name: '功能介绍', url: '/static/manuals/images/features.jpg' }
      ]
      this.videoList = [
        { name: '新手入门教程', url: '/static/manuals/videos/beginner_tutorial.mp4', date: '2023-06-01' },
        { name: '高级功能演示', url: '/static/manuals/videos/advanced_demo.mp4', date: '2023-05-15' },
        { name: '使用技巧分享', url: '/static/manuals/videos/tips.mp4', date: '2023-04-20' }
      ]
    },

    formatDate(dateString) {
      if (!dateString) return ''
      const date = new Date(dateString)
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`
    },
    openResource(item) {
      // 打开PDF文档
      uni.showLoading({
        title: '加载中'
      });
      
      uni.downloadFile({
        url: item.url,
        success: (res) => {
          uni.hideLoading();
          if (res.statusCode === 200) {
            uni.openDocument({
              filePath: res.tempFilePath,
              success: function() {
                console.log('打开文档成功');
              },
              fail: function() {
                uni.showToast({
                  title: '打开文档失败',
                  icon: 'none'
                });
              }
            });
          }
        },
        fail: (err) => {
          uni.hideLoading();
          uni.showToast({
            title: '下载文件失败',
            icon: 'none'
          });
          console.error('下载文件失败', err);
        }
      });
    },
    previewImage(item) {
      uni.previewImage({
        urls: this.imageList.map(img => img.url),
        current: item.url
      });
    },
    playVideo(item) {
      uni.navigateTo({
        url: `/pages/my/video-player?url=${encodeURIComponent(item.url)}&name=${encodeURIComponent(item.name)}`,
        success: function() {
          console.log('跳转到视频播放页面成功');
        },
        fail: function() {
          uni.showToast({
            title: '视频播放失败',
            icon: 'none'
          });
        }
      });
    }
  }
}
</script>

<style lang="scss">
.manual-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100%;
  background-color: #f7f8fa;
  box-sizing: border-box;
}

.header {
  display: flex;
  align-items: center;
  height: 88rpx;
  background-color: #ffffff;
  padding: 0 30rpx;
  position: relative;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.back-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.title {
  flex: 1;
  text-align: center;
  font-size: 36rpx;
  font-weight: 500;
  margin-right: 60rpx;
}

.content {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 20rpx;
}

.loading-container, .error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400rpx;
  color: #999;
}

.retry-btn {
  margin-top: 20rpx;
  // padding: 10rpx 30rpx;
  background-color: #007AFF;
  color: white;
  border-radius: 10rpx;
  // border: none;
}

.category-tabs {
  display: flex;
  background-color: #ffffff;
  border-radius: 10rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
}

.tab-item {
  flex: 1;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  color: #666;
  position: relative;
}

.tab-item.active {
  color: #007AFF;
  font-weight: 500;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 4rpx;
  background-color: #007AFF;
  border-radius: 2rpx;
}

.resource-list {
  flex: 1;
}

.resource-item {
  display: flex;
  align-items: center;
  background-color: #ffffff;
  padding: 30rpx;
  margin-bottom: 20rpx;
  border-radius: 10rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.resource-icon {
  width: 80rpx;
  height: 80rpx;
  background-color: #f0f5ff;
  border-radius: 10rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
}

.resource-icon .iconfont {
  font-size: 40rpx;
  color: #007AFF;
}

.resource-info {
  flex: 1;
}

.resource-name {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 10rpx;
}

.resource-desc {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 10rpx;
}

.resource-meta {
  display: flex;
  align-items: center;
}

.resource-size, .resource-date {
  font-size: 22rpx;
  color: #b2b2b2;
}

.resource-size {
  margin-right: 20rpx;
}

.resource-action {
  display: flex;
  align-items: center;
}

.resource-action .iconfont {
  font-size: 40rpx;
  color: #007AFF;
  padding: 10rpx;
}

.image-list {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -10rpx;
  
  .image-item {
    width: calc(33.33% - 20rpx);
    margin: 0 10rpx 20rpx;
    background-color: #fff;
    border-radius: 8rpx;
    overflow: hidden;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
    
    .thumbnail {
      width: 100%;
      height: 180rpx;
      background-color: #f5f5f5;
    }
    
    .image-name {
      font-size: 24rpx;
      color: #333;
      padding: 16rpx;
      text-align: center;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}

.video-icon {
  background-color: #fff0f0;
}

.video-icon .iconfont {
  color: #ff4d4f;
}
</style> 