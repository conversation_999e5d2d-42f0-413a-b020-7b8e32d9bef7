<view class="container"><view class="device-info"><text class="device-name">芭佰邑-{{a}}</text><text class="{{['device-status', c]}}">{{b}}</text></view><view class="comparison-section"><view class="image-container"><view class="before-image"><image wx:if="{{d}}" src="{{e}}" mode="aspectFill" class="hair-image" bindtap="{{f}}"></image><view wx:else class="placeholder-image" bindtap="{{g}}"><view class="add-icon">+</view><text class="placeholder-text">上传染发前照片</text></view><text class="image-label">染发前</text></view><view class="after-image"><image wx:if="{{h}}" src="{{i}}" mode="aspectFill" class="hair-image" bindtap="{{j}}"></image><view wx:else class="placeholder-image" bindtap="{{k}}"><view class="add-icon">+</view><text class="placeholder-text">上传染发后照片</text><text class="placeholder-subtext">点击上传</text></view><text class="image-label">染发后</text></view></view><view class="color-title">{{l}}</view></view><view class="color-structure"><view class="structure-title">颜色构造</view><view class="color-composition"><view wx:for="{{m}}" wx:for-item="item" wx:key="d" class="color-item"><view class="color-block" style="{{'background-color:' + item.a}}"></view><view class="color-info"><text class="color-name">{{item.b}}</text><text class="color-amount">{{item.c}}</text></view></view></view></view><view class="bottom-actions"><view class="action-icon like" bindtap="{{p}}"><text class="icon">{{n}}</text><text class="count">{{o}}</text></view><view class="action-icon star" bindtap="{{s}}"><text class="icon">{{q}}</text><text class="count">{{r}}</text></view><view class="action-icon share" bindtap="{{v}}"><text class="icon">↗</text><text class="count">{{t}}</text></view><view class="generate-btn" bindtap="{{w}}"> 生成新配方 </view></view></view>