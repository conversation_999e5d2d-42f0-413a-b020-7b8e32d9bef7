<template>
  <view class="container">
    <!-- 政策列表 -->
    <view class="policy-list">
      <view class="policy-item" @click="navigateToUserAgreement">
        <text class="policy-text">用户协议</text>
        <text class="arrow-icon">›</text>
      </view>
      <view class="policy-item" @click="navigateToPrivacyPolicy">
        <text class="policy-text">隐私政策</text>
        <text class="arrow-icon">›</text>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {};
  },
  methods: {
    goBack() {
      uni.navigateBack();
    },
    navigateToUserAgreement() {
      uni.navigateTo({
        url: '/pages/my/user-agreement'
      });
    },
    navigateToPrivacyPolicy() {
      uni.navigateTo({
        url: '/pages/my/privacy-policy'
      });
    }
  }
};
</script>

<style lang="scss">
.container {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx;
  background-color: #4285f4;
  color: #fff;
}

.back-btn {
  font-size: 40rpx;
  width: 60rpx;
  display: flex;
  align-items: center;
}

.title {
  font-size: 36rpx;
  font-weight: 500;
}

.header-icons {
  display: flex;
  align-items: center;
}

.header-icon {
  margin-left: 15rpx;
  font-size: 24rpx;
}

.policy-list {
  margin-top: 20rpx;
  background-color: #fff;
}

.policy-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1px solid #f2f2f2;
}

.policy-text {
  font-size: 32rpx;
  color: #333;
}

.arrow-icon {
  font-size: 36rpx;
  color: #ccc;
}
</style> 