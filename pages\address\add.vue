<template>
	<view class="add-address">
		<view class="form-container">
			<view class="form-item">
				<text class="label">收货人</text>
				<input type="text" class="input" placeholder="请输入您的名称" v-model="address.name" />
				<view class="input-icon" v-if="address.name">
					<text class="clear-btn" @click="clearInput('name')">×</text>
				</view>
			</view>
			
			<view class="form-item">
				<text class="label">联系电话</text>
				<input type="number" class="input" placeholder="请输入手机号" v-model="address.phone" maxlength="11" />
				<view class="input-icon" v-if="address.phone">
					<text class="clear-btn" @click="clearInput('phone')">×</text>
				</view>
			</view>
			
			<view class="form-item" @click="openAreaPicker">
				<text class="label">所在地区</text>
				<view class="input area-input">
					<text v-if="address.province">{{address.province}} {{address.city}} {{address.district}}</text>
					<text v-else class="placeholder">省、市、区、街道</text>
				</view>
				<view class="input-icon">
					<text class="arrow">></text>
				</view>
			</view>
			
			<view class="form-item">
				<text class="label">详细地址</text>
				<textarea class="textarea" placeholder="请输入详细地址" v-model="address.detailAddress" />
			</view>
			
			<view class="form-item switch-item">
				<text class="label">设置默认地址</text>
				<switch color="#4285f4" :checked="address.isDefault" @change="switchChange" />
			</view>
		</view>
		
		<view class="bottom-btn">
			<button class="save-btn" :disabled="saving" @click="saveAddress">
				{{saving ? '保存中...' : '保存地址'}}
			</button>
		</view>
	</view>
</template>

<script>
import { api } from '@/utils/request.js'

export default {
	data() {
		return {
			address: {
				name: '',
				phone: '',
				province: '',
				city: '',
				district: '',
				detailAddress: '',
				isDefault: false
			},
			saving: false
		}
	},
	methods: {
		goBack() {
			uni.navigateBack();
		},
		clearInput(field) {
			this.address[field] = '';
		},
		openAreaPicker() {
			// 使用uni-app提供的省市区选择器
			uni.showLoading({
				title: '加载中'
			});
			
			setTimeout(() => {
				uni.hideLoading();
				uni.navigateTo({
					url: '/pages/address/region-picker'
				});
			}, 300);
		},
		switchChange(e) {
			this.address.isDefault = e.detail.value;
		},
		async saveAddress() {
			// 防抖处理 - 如果正在保存中，直接返回
			if (this.saving) {
				return;
			}

			// 表单验证
			if (!this.address.name) {
				uni.showToast({
					title: '请输入收货人姓名',
					icon: 'none'
				});
				return;
			}

			if (!this.address.phone) {
				uni.showToast({
					title: '请输入联系电话',
					icon: 'none'
				});
				return;
			}

			if (!/^1\d{10}$/.test(this.address.phone)) {
				uni.showToast({
					title: '手机号格式不正确',
					icon: 'none'
				});
				return;
			}

			if (!this.address.province || !this.address.city || !this.address.district) {
				uni.showToast({
					title: '请选择所在地区',
					icon: 'none'
				});
				return;
			}

			if (!this.address.detailAddress) {
				uni.showToast({
					title: '请输入详细地址',
					icon: 'none'
				});
				return;
			}

			// 调用后端API保存地址
			try {
				this.saving = true;
				uni.showLoading({
					title: '保存中...'
				});

				// 转换为后端需要的数据格式
				const addressData = {
					receiverName: this.address.name,
					receiverPhone: this.address.phone,
					province: this.address.province,
					city: this.address.city,
					district: this.address.district,
					detailAddress: this.address.detailAddress,
					isDefault: this.address.isDefault ? 1 : 0
				};

				const response = await api.mall.address.add(addressData);

				if (response.code === 200) {
					uni.hideLoading();
					uni.showToast({
						title: '保存成功',
						icon: 'success'
					});

					// 延迟返回上一页
					setTimeout(() => {
						uni.navigateBack();
					}, 1000);
				} else {
					uni.hideLoading();
					uni.showToast({
						title: response.message || '保存失败',
						icon: 'none'
					});
				}
			} catch (error) {
				console.error('保存地址失败:', error);
				uni.hideLoading();
				uni.showToast({
					title: '网络错误，请稍后重试',
					icon: 'none'
				});
			} finally {
				this.saving = false;
			}
		}
	}
}
</script>

<style lang="scss">
.add-address {
	min-height: 100vh;
	background-color: #f5f5f5;
	width: 100%;
	box-sizing: border-box;
	padding-bottom: 180rpx; /* 为底部按钮留出空间 */
}

.form-container {
	background-color: #ffffff;
	margin-top: 20rpx;
}

.form-item {
	display: flex;
	align-items: center;
	padding: 30rpx 20rpx;
	border-bottom: 1rpx solid #f0f0f0;
	position: relative;
}

.label {
	width: 160rpx;
	font-size: 28rpx;
	color: #333333;
}

.input {
	flex: 1;
	height: 60rpx;
	font-size: 28rpx;
}

.textarea {
	flex: 1;
	height: 120rpx;
	font-size: 28rpx;
}

.placeholder {
	color: #999999;
}

.input-icon {
	padding: 0 10rpx;
}

.clear-btn {
	font-size: 36rpx;
	color: #999999;
}

.arrow {
	font-size: 30rpx;
	color: #999999;
}

.area-input {
	display: flex;
	align-items: center;
}

.switch-item {
	justify-content: space-between;
}

.bottom-btn {
	padding: 40rpx 20rpx;
	position: fixed;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: #fff;
	box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.1);
	/* 适配底部安全区域 */
	padding-bottom: calc(40rpx + env(safe-area-inset-bottom));
}

.save-btn {
	height: 90rpx;
	line-height: 90rpx;
	text-align: center;
	background-color: #4285f4;
	color: #ffffff;
	border-radius: 45rpx;
	font-size: 32rpx;
	width: 100%;
	box-sizing: border-box;

	&[disabled] {
		background-color: #ccc;
		color: #999;
	}
}

/* 页面级别样式重置 */
page {
	width: 100%;
	height: 100%;
	margin: 0;
	padding: 0;
	box-sizing: border-box;
}
</style>