"use strict";
const utils_debounce = require("../../utils/debounce.js");
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  mixins: [utils_debounce.buttonDebounce],
  data() {
    return {
      saving1: false,
      debounceHelper: new utils_debounce.DebounceHelper(),
      logs: []
    };
  },
  created() {
    this.debouncedSave = utils_debounce.debounce(this.normalSave, 300);
  },
  methods: {
    // 方法1：使用状态变量
    async saveMethod1() {
      if (this.saving1)
        return;
      this.saving1 = true;
      this.addLog("方法1：开始保存...");
      try {
        await this.simulateAsync(2e3);
        this.addLog("方法1：保存成功！");
      } catch (error) {
        this.addLog("方法1：保存失败 - " + error.message);
      } finally {
        this.saving1 = false;
      }
    },
    // 方法2：使用防抖工具类
    async saveMethod2() {
      if (this.debounceHelper.isProcessing("save"))
        return;
      this.debounceHelper.start("save");
      this.addLog("方法2：开始提交...");
      try {
        await this.simulateAsync(1500);
        this.addLog("方法2：提交成功！");
      } catch (error) {
        this.addLog("方法2：提交失败 - " + error.message);
      } finally {
        this.debounceHelper.end("save");
      }
    },
    // 方法3：使用混入
    async saveMethod3() {
      if (this.isButtonDisabled("submit"))
        return;
      this.disableButton("submit");
      this.addLog("方法3：开始提交订单...");
      try {
        await this.simulateAsync(2500);
        this.addLog("方法3：订单提交成功！");
      } catch (error) {
        this.addLog("方法3：订单提交失败 - " + error.message);
      } finally {
        this.enableButton("submit");
      }
    },
    // 方法4：普通保存函数（会被防抖包装）
    normalSave() {
      this.addLog("方法4：执行保存操作（防抖后）");
    },
    // 模拟异步操作
    simulateAsync(delay) {
      return new Promise((resolve, reject) => {
        setTimeout(() => {
          if (Math.random() > 0.1) {
            resolve();
          } else {
            reject(new Error("模拟网络错误"));
          }
        }, delay);
      });
    },
    // 添加日志
    addLog(message) {
      const timestamp = (/* @__PURE__ */ new Date()).toLocaleTimeString();
      this.logs.unshift(`[${timestamp}] ${message}`);
      if (this.logs.length > 20) {
        this.logs = this.logs.slice(0, 20);
      }
    },
    // 清除日志
    clearLogs() {
      this.logs = [];
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.t($data.saving1 ? "保存中..." : "保存数据"),
    b: $data.saving1,
    c: common_vendor.o((...args) => $options.saveMethod1 && $options.saveMethod1(...args)),
    d: common_vendor.t($data.debounceHelper.isProcessing("save") ? "处理中..." : "提交表单"),
    e: $data.debounceHelper.isProcessing("save"),
    f: common_vendor.o((...args) => $options.saveMethod2 && $options.saveMethod2(...args)),
    g: common_vendor.t(_ctx.isButtonDisabled("submit") ? "提交中..." : "提交订单"),
    h: _ctx.isButtonDisabled("submit"),
    i: common_vendor.o((...args) => $options.saveMethod3 && $options.saveMethod3(...args)),
    j: common_vendor.o((...args) => _ctx.debouncedSave && _ctx.debouncedSave(...args)),
    k: common_vendor.f($data.logs, (log, index, i0) => {
      return {
        a: common_vendor.t(log),
        b: index
      };
    }),
    l: common_vendor.o((...args) => $options.clearLogs && $options.clearLogs(...args))
  };
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/test/debounce-demo.js.map
