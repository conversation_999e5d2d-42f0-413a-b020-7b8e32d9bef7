<template>
  <view class="content">
    <!-- 顶部蓝色区域 -->
    <view class="header">
      <view class="title-center">设备管理</view>
      <view class="controls-right">
          <text class="dots">•••</text>
          <text class="line">—</text>
          <view class="circle">⊙</view>
        </view>
    </view>
    
    <!-- 连接方式选项卡 - 改为圆形单选按钮样式 -->
    <view class="tab-container">
      <view class="tab-item" @click="switchTab('network')">
        <view class="radio-circle" :class="{ active: currentTab === 'network' }"></view>
        <text class="tab-text">连网</text>
      </view>
      <view class="tab-item" @click="switchTab('bluetooth')">
        <view class="radio-circle" :class="{ active: currentTab === 'bluetooth' }"></view>
        <text class="tab-text">蓝牙</text>
      </view>
      <view class="tab-item" @click="switchTab('wifi')">
        <view class="radio-circle" :class="{ active: currentTab === 'wifi' }"></view>
        <text class="tab-text">WIFI</text>
      </view>
    </view>
    
    <!-- 选项卡下方的蓝色线条 -->
    <view class="indicator-container">
      <view class="indicator" :style="{ left: indicatorPosition }"></view>
    </view>
    
    <!-- 设备列表区域 -->
    <view class="empty-container">
      <view class="empty-circle"></view>
      <text class="empty-text">暂无设备</text>
    </view>
    
    <!-- 扫描设备按钮 -->
    <view class="scan-btn" @click="scanDevice">
      <text class="scan-btn-text">搜索蓝牙</text>
    </view>
    
    <!-- 底部导航栏 -->
    <view class="tabbar">
      <view class="tabbar-item">
        <image class="tabbar-icon" src="/static/tabbar/home.png"></image>
        <text class="tabbar-text">首页</text>
      </view>
      <view class="tabbar-item">
        <image class="tabbar-icon" src="/static/tabbar/palette.png"></image>
        <text class="tabbar-text">色板</text>
      </view>
      <view class="tabbar-item active">
        <image class="tabbar-icon" src="/static/tabbar/device_selected.png"></image>
        <text class="tabbar-text">设备</text>
      </view>
      <view class="tabbar-item">
        <image class="tabbar-icon" src="/static/tabbar/user.png"></image>
        <text class="tabbar-text">我的</text>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      currentTab: 'network',
      locationEnabled: false,
      bluetoothEnabled: false,
      wifiEnabled: false,
      wifiForm: {
        deviceId: '',
        name: '',
        password: ''
      },
      devices: [] // 存储扫描到的设备列表
    }
  },
  computed: {
    indicatorPosition() {
      // 计算指示器位置
      const tabWidth = 100 / 3; // 每个选项卡宽度占总宽度的三分之一
      let position = 0;
      
      if (this.currentTab === 'network') {
        position = 0;
      } else if (this.currentTab === 'bluetooth') {
        position = tabWidth;
      } else if (this.currentTab === 'wifi') {
        position = tabWidth * 2;
      }
      
      return position + '%';
    }
  },
  onLoad() {
    // 页面加载时只获取状态，不主动申请权限
    this.getDeviceStatusWithoutRequest();
  },
  methods: {
    switchTab(tab) {
      this.currentTab = tab;
    },
    getDeviceStatusWithoutRequest() {
      // 检查位置服务状态(不申请权限)
      try {
        // 只判断是否已授权，不触发权限申请
        // #ifdef APP-PLUS
        var isLocationAuthorized = plus.android.invoke(
          plus.android.importClass("android.support.v4.content.ContextCompat"),
          "checkSelfPermission",
          plus.android.runtimeMainActivity(),
          "android.permission.ACCESS_FINE_LOCATION"
        );
        this.locationEnabled = (isLocationAuthorized === 0);
        // #endif
        
        // #ifdef MP-WEIXIN
        uni.getSetting({
          success: (res) => {
            this.locationEnabled = res.authSetting['scope.userLocation'] === true;
          }
        });
        // #endif
      } catch (e) {
        this.locationEnabled = false;
      }
      
      // 检查蓝牙状态(不申请权限)
      try {
        // #ifdef MP-WEIXIN
        uni.getSetting({
          success: (res) => {
            this.bluetoothEnabled = res.authSetting['scope.bluetooth'] === true;
          }
        });
        // #endif
        
        // #ifdef APP-PLUS
        var bluetoothManager = plus.android.importClass("android.bluetooth.BluetoothManager");
        var bluetoothAdapter = plus.android.importClass("android.bluetooth.BluetoothAdapter");
        var adapter = bluetoothAdapter.getDefaultAdapter();
        this.bluetoothEnabled = adapter.isEnabled();
        // #endif
      } catch (e) {
        this.bluetoothEnabled = false;
      }
      
      // 检查WIFI状态(不申请权限)
      try {
        // #ifdef MP-WEIXIN
        uni.getSetting({
          success: (res) => {
            this.wifiEnabled = res.authSetting['scope.userLocation'] === true;
          }
        });
        // #endif
        
        // #ifdef APP-PLUS
        uni.getNetworkType({
          success: (res) => {
            this.wifiEnabled = res.networkType === 'wifi';
          },
          fail: () => {
            this.wifiEnabled = false;
          }
        });
        // #endif
      } catch (e) {
        this.wifiEnabled = false;
      }
    },
    requestLocationPermission() {
      // 不自动请求，由用户点击触发
      // #ifdef MP-WEIXIN
      uni.getLocation({
        type: 'gcj02',
        success: (res) => {
          this.locationEnabled = true;
          uni.showToast({
            title: '已获取位置权限',
            icon: 'success'
          });
        },
        fail: (err) => {
          this.locationEnabled = false;
          // 请求用户授权
          uni.showModal({
            title: '提示',
            content: '需要位置权限才能扫描设备，是否前往设置页面开启？',
            confirmText: '去设置',
            cancelText: '取消',
            success: (res) => {
              if (res.confirm) {
                uni.openSetting({
                  success: (settingRes) => {
                    console.log('设置结果：', settingRes);
                  }
                });
              }
            }
          });
        }
      });
      // #endif
      
      // #ifdef APP-PLUS
      uni.authorize({
        scope: 'scope.userLocation',
        success: () => {
          this.locationEnabled = true;
          uni.showToast({
            title: '已授权位置权限',
            icon: 'success'
          });
        },
        fail: () => {
          this.locationEnabled = false;
          uni.showModal({
            title: '提示',
            content: '需要位置权限才能扫描设备，是否前往设置页面开启？',
            confirmText: '去设置',
            cancelText: '取消',
            success: (res) => {
              if (res.confirm) {
                // 跳转到应用权限设置页面
                if (uni.getSystemInfoSync().platform === 'android') {
                  const main = plus.android.runtimeMainActivity();
                  const Intent = plus.android.importClass('android.content.Intent');
                  const Settings = plus.android.importClass('android.provider.Settings');
                  const intent = new Intent(Settings.ACTION_LOCATION_SOURCE_SETTINGS);
                  main.startActivity(intent);
                } else if (uni.getSystemInfoSync().platform === 'ios') {
                  plus.runtime.openURL('App-Prefs:root=Privacy&path=LOCATION');
                }
              }
            }
          });
        }
      });
      // #endif
    },
    requestBluetoothPermission() {
      // 不自动请求，由用户点击触发
      // #ifdef MP-WEIXIN
      uni.openBluetoothAdapter({
        success: () => {
          this.bluetoothEnabled = true;
          uni.showToast({
            title: '蓝牙已开启',
            icon: 'success'
          });
        },
        fail: () => {
          this.bluetoothEnabled = false;
          uni.showModal({
            title: '提示',
            content: '需要开启蓝牙才能连接设备，是否开启？',
            confirmText: '去设置',
            cancelText: '取消',
            success: (res) => {
              if (res.confirm) {
                uni.openSetting();
              }
            }
          });
        }
      });
      // #endif
      
      // #ifdef APP-PLUS
      if (!this.bluetoothEnabled) {
        uni.showModal({
          title: '提示',
          content: '请开启手机蓝牙',
          confirmText: '去设置',
          cancelText: '取消',
          success: (res) => {
            if (res.confirm) {
              if (uni.getSystemInfoSync().platform === 'android') {
                const main = plus.android.runtimeMainActivity();
                const Intent = plus.android.importClass('android.content.Intent');
                const Settings = plus.android.importClass('android.provider.Settings');
                const intent = new Intent(Settings.ACTION_BLUETOOTH_SETTINGS);
                main.startActivity(intent);
              } else if (uni.getSystemInfoSync().platform === 'ios') {
                plus.runtime.openURL('App-Prefs:root=Bluetooth');
              }
            }
          }
        });
      } else {
        uni.showToast({
          title: '蓝牙已开启',
          icon: 'success'
        });
      }
      // #endif
    },
    requestWifiPermission() {
      // #ifdef MP-WEIXIN
      uni.getNetworkType({
        success: (res) => {
          if (res.networkType === 'wifi') {
            this.wifiEnabled = true;
            uni.showToast({
              title: 'WiFi已连接',
              icon: 'success'
            });
          } else {
            this.wifiEnabled = false;
            uni.showModal({
              title: '提示',
              content: '未连接WiFi网络，是否前往设置页面连接？',
              confirmText: '去设置',
              cancelText: '取消',
              success: (res) => {
                if (res.confirm) {
                  uni.openSetting();
                }
              }
            });
          }
        }
      });
      // #endif
      
      // #ifdef APP-PLUS
      uni.getNetworkType({
        success: (res) => {
          if (res.networkType === 'wifi') {
            this.wifiEnabled = true;
            uni.showToast({
              title: 'WiFi已连接',
              icon: 'success'
            });
          } else {
            this.wifiEnabled = false;
            uni.showModal({
              title: '提示',
              content: '未连接WiFi网络，是否前往设置页面连接？',
              confirmText: '去设置',
              cancelText: '取消',
              success: (res) => {
                if (res.confirm) {
                  if (uni.getSystemInfoSync().platform === 'android') {
                    const main = plus.android.runtimeMainActivity();
                    const Intent = plus.android.importClass('android.content.Intent');
                    const Settings = plus.android.importClass('android.provider.Settings');
                    const intent = new Intent(Settings.ACTION_WIFI_SETTINGS);
                    main.startActivity(intent);
                  } else if (uni.getSystemInfoSync().platform === 'ios') {
                    plus.runtime.openURL('App-Prefs:root=WIFI');
                  }
                }
              }
            });
          }
        }
      });
      // #endif
    },
    scanDevice() {
      if (!this.bluetoothEnabled) {
        uni.showModal({
          title: '提示',
          content: '请先开启蓝牙',
          showCancel: false
        });
        return;
      }
      
      if (!this.locationEnabled) {
        uni.showModal({
          title: '提示',
          content: '请先开启定位权限',
          showCancel: false
        });
        return;
      }
      
      if (!this.wifiEnabled) {
        uni.showModal({
          title: '提示',
          content: '请先连接WiFi',
          showCancel: false
        });
        return;
      }
      
      // 如果所有权限都已获取，则开始扫描
      this.startScanningDevices();
    },
    startScanningDevices() {
      uni.showLoading({
        title: '正在扫描设备...'
      });
      
      // 初始化蓝牙模块
      uni.openBluetoothAdapter({
        success: (res) => {
          // 开始搜寻附近的蓝牙外围设备
          uni.startBluetoothDevicesDiscovery({
            services: [], // 可以指定特定的服务UUID
            allowDuplicatesKey: false,
            success: (res) => {
              console.log('开始搜索蓝牙设备');
              this.devices = []; // 清空设备列表
              
              // 监听寻找到新设备的事件
              uni.onBluetoothDeviceFound((res) => {
                console.log('找到新设备:', res);
                // 将找到的设备添加到列表中
                if (res.devices && res.devices.length > 0) {
                  res.devices.forEach(device => {
                    if (device.name) { // 只添加有名称的设备
                      this.devices.push({
                        deviceId: device.deviceId,
                        name: device.name || '未知设备',
                        status: '可连接'
                      });
                    }
                  });
                }
              });
              
              // 10秒后停止搜索
              setTimeout(() => {
                uni.stopBluetoothDevicesDiscovery({
                  success: (res) => {
                    uni.hideLoading();
                    uni.showToast({
                      title: '扫描完成',
                      icon: 'success'
                    });
                  }
                });
              }, 10000);
            },
            fail: (err) => {
              uni.hideLoading();
              uni.showToast({
                title: '搜索蓝牙设备失败',
                icon: 'none'
              });
            }
          });
        },
        fail: (err) => {
          uni.hideLoading();
          // 初始化蓝牙模块失败
          if (err.errCode === 10001) {
            uni.showModal({
              title: '提示',
              content: '请开启手机蓝牙',
              showCancel: false
            });
          } else {
      uni.showToast({
              title: '初始化蓝牙失败: ' + err.errMsg,
        icon: 'none'
            });
          }
        }
      });
    },
    connectWifi() {
      if (!this.wifiForm.deviceId || !this.wifiForm.name || !this.wifiForm.password) {
        uni.showToast({
          title: '请填写完整信息',
          icon: 'none'
        });
        return;
      }
      
      uni.showToast({
        title: '正在连接WiFi',
        icon: 'none'
      });
    }
  }
}
</script>

<style>
.content {
  display: flex;
  flex-direction: column;
  align-items: stretch;
  height: 100vh;
  background-color: #ffffff;
  position: relative;
  width: 100%;
  box-sizing: border-box;
  overflow-x: hidden;
}

.header {
  background-color: #4285f4;
  color: white;
  position: relative;
  padding: 40px 15px 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  box-sizing: border-box;
}

.title-center {
  font-size: 18px;
  font-weight: bold;
  text-align: center;
  flex: 1;
}

.controls-right {
  display: flex;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.3);
  border-radius: 20px;
  padding: 5px 10px;
  position: absolute;
  right: 15px;
}

.dots, .line {
  margin-right: 10px;
}

.circle {
  font-size: 16px;
}

/* 新的标签栏样式 */
.tab-container {
  display: flex;
  background-color: #ffffff;
  padding: 15px 0;
  width: 100%;
}

.tab-item {
  flex: 1;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
}

.radio-circle {
  width: 22px;
  height: 22px;
  border-radius: 50%;
  border: 2px solid #4285f4;
  margin-right: 8px;
  position: relative;
}

.radio-circle.active::after {
  content: '';
  position: absolute;
  width: 14px;
  height: 14px;
  border-radius: 50%;
  background-color: #4285f4;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.tab-text {
  font-size: 16px;
  color: #4285f4;
}

/* 指示器样式 */
.indicator-container {
  width: 100%;
  height: 3px;
  background-color: #f0f0f0;
  position: relative;
}

.indicator {
  position: absolute;
  width: 33.33%;
  height: 100%;
  background-color: #4285f4;
  transition: left 0.3s;
}

/* 空状态样式 */
.empty-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-top: 50px;
  margin-bottom: 50px;
}

.empty-circle {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  background-color: #f0f0f0;
  margin-bottom: 20px;
}

.empty-text {
  font-size: 16px;
  color: #aaaaaa;
}

/* 扫描按钮样式 */
.scan-btn {
  margin: 0 20px 20px;
  height: 50px;
  background-color: #4285f4;
  border-radius: 25px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.scan-btn-text {
  color: white;
  font-size: 16px;
}

/* 底部导航栏样式 */
.tabbar {
  display: flex;
  height: 50px;
  border-top: 1px solid #f0f0f0;
  background-color: white;
}

.tabbar-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.tabbar-icon {
  width: 24px;
  height: 24px;
  margin-bottom: 4px;
}

.tabbar-text {
  font-size: 12px;
  color: #999999;
}

.tabbar-item.active .tabbar-text {
  color: #4285f4;
}

/* 设备尺寸适配 */
@media screen and (max-width: 390px) {
  .content, .header, .tabs-container, .tab-indicator-container, 
  .device-status-cards, .scan-btn {
    width: 100% !important;
    box-sizing: border-box !important;
    margin-left: 0 !important;
    margin-right: 0 !important;
  }
  
  .status-icon {
    width: 70px;
    height: 70px;
  }
  
  .icon-inner {
    width: 35px;
    height: 35px;
  }
}

/* iPhone X 及以上机型底部安全区域适配 */
@supports (padding-bottom: constant(safe-area-inset-bottom)) or (padding-bottom: env(safe-area-inset-bottom)) {
  .content {
    padding-bottom: calc(50px + constant(safe-area-inset-bottom));
    padding-bottom: calc(50px + env(safe-area-inset-bottom));
  }
  
  .tabbar-placeholder {
    height: calc(50px + constant(safe-area-inset-bottom));
    height: calc(50px + env(safe-area-inset-bottom));
  }
}
</style> 