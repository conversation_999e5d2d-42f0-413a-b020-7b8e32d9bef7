<template>
  <view class="container">
    <!-- 消息标签页 -->
    <view class="message-tabs">
      <view class="unread-tab">
        <text>未读消息(0)</text>
      </view>
      <view class="read-all-btn" @click="markAllAsRead">
        <text>一键已读</text>
      </view>
    </view>

    <!-- 空消息提示 -->
    <view class="empty-message">
      <!-- 这里可以添加一个空状态图片 -->
      <text class="empty-text">暂无消息</text>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      messages: []
    };
  },
  methods: {
    goBack() {
      uni.navigateBack();
    },
    markAllAsRead() {
      if (this.messages.length === 0) {
        uni.showToast({
          title: '暂无消息',
          icon: 'none'
        });
        return;
      }
      
      // 实际应用中调用API标记所有消息为已读
      uni.showToast({
        title: '已全部标记为已读',
        icon: 'success'
      });
    }
  }
};
</script>

<style lang="scss">
.container {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx;
  background-color: #4285f4;
  color: #fff;
}

.back-btn {
  font-size: 40rpx;
  width: 60rpx;
  display: flex;
  align-items: center;
}

.title {
  font-size: 36rpx;
  font-weight: 500;
}

.header-icons {
  display: flex;
  align-items: center;
}

.header-icon {
  margin-left: 15rpx;
  font-size: 24rpx;
}

.message-tabs {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  background-color: #4285f4;
  color: #fff;
}

.unread-tab {
  font-size: 30rpx;
}

.read-all-btn {
  font-size: 30rpx;
  display: flex;
  align-items: center;
}

.read-all-btn:before {
  content: '';
  display: inline-block;
  width: 40rpx;
  height: 40rpx;
  margin-right: 6rpx;
  background-size: contain;
}

.empty-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400rpx;
}

.empty-text {
  margin-top: 30rpx;
  font-size: 30rpx;
  color: #999;
}
</style> 