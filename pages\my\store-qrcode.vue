<template>
  <view class="container">
    <!-- 内容区域 -->
    <view class="content-wrapper">
      <view class="store-info-card">
        <view class="store-status">未加入门店</view>
        <view class="bind-code">绑定码: 空</view>
        <view class="scan-tip">扫一扫，快速加入我们的团队</view>
        
        <!-- 二维码区域 -->
        <view class="qrcode-area">
          <image class="qrcode-image" src="/static/images/erweima.jpg" mode="aspectFit" />
        </view>
        
        <!-- 品牌信息 -->
        <view class="brand-info">
          <text>芭佰邑-智能染发机</text>
        </view>
      </view>
      
      <!-- 底部分享按钮 -->
      <view class="share-btn" @click="shareStore">
        <text>分享门店</text>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      storeInfo: {
        name: '',
        bindCode: '',
        qrCode: ''
      },
      isMember: false
    };
  },
  onLoad() {
    // 加载门店信息
    this.loadStoreInfo();
  },
  methods: {
    goBack() {
      uni.navigateBack();
    },
    loadStoreInfo() {
      // 模拟从本地或服务器获取门店信息
      const storeData = uni.getStorageSync('storeInfo');
      if (storeData) {
        this.storeInfo = JSON.parse(storeData);
        // 判断是否已加入门店
        this.isMember = !!this.storeInfo.bindCode && this.storeInfo.bindCode !== '空';
      }
    },
    shareStore() {
      // 分享门店逻辑
      if (!this.isMember) {
        uni.showToast({
          title: '请先加入门店',
          icon: 'none'
        });
        return;
      }
      
      // 调用分享API
      uni.showModal({
        title: '分享',
        content: '分享功能开发中',
        showCancel: false
      });
    }
  }
};
</script>

<style lang="scss">
.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f5f5;
}

.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx;
  background-color: #4285f4;
  color: #fff;
}

.back-btn {
  font-size: 40rpx;
  width: 60rpx;
  display: flex;
  align-items: center;
}

.title {
  font-size: 36rpx;
  font-weight: 500;
}

.header-icons {
  display: flex;
  align-items: center;
}

.header-icon {
  margin-left: 15rpx;
  font-size: 24rpx;
}

.content-wrapper {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 30rpx;
}

.store-info-card {
  width: 100%;
  background-color: #fff;
  border-radius: 20rpx;
  padding: 40rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  display: flex;
  flex-direction: column;
  align-items: center;
}

.store-status {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
}

.bind-code {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 20rpx;
}

.scan-tip {
  font-size: 28rpx;
  color: #999;
  margin-bottom: 40rpx;
}

.qrcode-area {
  width: 300rpx;
  height: 300rpx;
  background-color: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 40rpx;
  border-radius: 10rpx;
  overflow: hidden;
}

.qrcode-image {
  width: 100%;
  height: 100%;
}

.qrcode-placeholder {
  font-size: 28rpx;
  color: #999;
}

.brand-info {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}

.share-btn {
  margin-top: 60rpx;
  width: 100%;
  height: 90rpx;
  background-color: #4285f4;
  border-radius: 45rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #fff;
  font-size: 32rpx;
}
</style> 