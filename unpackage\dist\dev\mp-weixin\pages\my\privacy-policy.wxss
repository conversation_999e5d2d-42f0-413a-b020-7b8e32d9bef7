/* 水平间距 */
/* 水平间距 */
/* 全局样式修复 - 确保页面占满屏幕宽度 */
page {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
}

/* 通用容器样式 */
.container, .page-container {
  width: 100%;
  min-height: 100vh;
  box-sizing: border-box;
}

/* 修复小程序页面宽度问题 */
uni-page-body {
  width: 100% !important;
}

/* 确保所有view元素正确继承宽度 */
view {
  box-sizing: border-box;
}
.container {
  min-height: 100vh;
  background-color: #f5f5f5;
}
.content {
  height: 100vh;
  background-color: #fff;
}
.policy-content {
  padding: 40rpx;
}
.section {
  margin-bottom: 40rpx;
}
.section-title {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  line-height: 1.6;
}
.section-text {
  display: block;
  font-size: 28rpx;
  color: #666;
  line-height: 1.8;
  text-align: justify;
}