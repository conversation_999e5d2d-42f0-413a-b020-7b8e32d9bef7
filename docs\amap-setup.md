# 高德地图配置指南

本项目专门使用高德地图服务，提供精确的位置定位和地址解析功能。

## 1. 获取高德地图API密钥

### 步骤1：注册高德开发者账号
1. 访问 [高德开放平台](https://lbs.amap.com/)
2. 点击"注册"创建开发者账号
3. 完成实名认证

### 步骤2：创建应用
1. 登录后进入控制台
2. 点击"应用管理" -> "我的应用"
3. 点击"创建新应用"
4. 填写应用信息：
   - 应用名称：索瑞达智能染发机
   - 应用类型：移动应用

### 步骤3：添加Key
1. 在应用详情页点击"添加Key"
2. 填写Key信息：
   - Key名称：位置服务Key
   - 服务平台：Web服务API
   - 绑定服务：勾选"逆地理编码"
3. 点击"提交"创建Key

## 2. 配置项目

### 修改配置文件
编辑 `config/map.js` 文件：

```javascript
// 高德地图配置
amap: {
  key: 'YOUR_ACTUAL_AMAP_KEY', // 替换为您获取的真实API key
  geocoderUrl: 'https://restapi.amap.com/v3/geocode/regeo',
  enabled: true // 启用高德地图服务
},
```

### 配置示例
```javascript
// 高德地图配置
amap: {
  key: 'a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6', // 示例密钥
  geocoderUrl: 'https://restapi.amap.com/v3/geocode/regeo',
  poiSearchUrl: 'https://restapi.amap.com/v3/place/text',
  enabled: true
},
```

## 3. 功能说明

### 逆地理编码
- 将经纬度坐标转换为具体地址
- 支持精确到街道级别的地址信息
- 自动缓存结果，提高性能

### POI搜索
- 支持按关键词搜索地点
- 可指定城市范围搜索
- 返回详细的地点信息

### 位置获取流程
1. 获取用户授权
2. 调用uni.getLocation获取坐标
3. 使用高德API进行逆地理编码
4. 返回格式化的地址信息

### 地址格式
返回的地址格式为：省份 + 城市 + 区县 + 街道
例如：广东省东莞市南城街道

## 4. 权限配置

### 小程序权限
在 `manifest.json` 中已配置：
```json
"permission": {
  "scope.userLocation": {
    "desc": "您的位置信息将用于设备搜索"
  }
},
"requiredPrivateInfos": ["getLocation"]
```

### App权限
在 `manifest.json` 中已配置：
```json
"permissions": [
  "<uses-permission android:name=\"android.permission.ACCESS_COARSE_LOCATION\"/>",
  "<uses-permission android:name=\"android.permission.ACCESS_FINE_LOCATION\"/>"
]
```

## 5. 使用方法

### 在页面中使用
```javascript
import locationService from '@/utils/location.js';

// 获取当前位置
async getCurrentLocation() {
  try {
    const locationData = await locationService.getCurrentLocation();
    console.log('位置信息:', locationData);
    // locationData.address 包含具体地址
  } catch (error) {
    console.error('获取位置失败:', error);
  }
}
```

### 位置选择页面
- 点击商城页面顶部位置栏
- 打开位置选择页面
- 支持搜索和选择位置
- 确认后返回并更新显示

## 6. 注意事项

1. **API配额**：高德地图有每日调用次数限制，请根据需要选择合适的套餐
2. **网络环境**：确保应用能够访问高德API服务器
3. **错误处理**：当API调用失败时，系统会自动使用模拟地址
4. **缓存机制**：相同坐标的地址会被缓存30分钟，减少API调用

## 7. 故障排除

### 常见问题
1. **定位失败**：检查权限是否授权
2. **地址显示坐标**：检查API密钥是否正确配置
3. **网络错误**：检查网络连接和API服务状态

### 调试方法
1. 查看控制台日志
2. 检查网络请求状态
3. 验证API密钥有效性

## 8. 更新日志

- 2024-06-04：配置高德地图作为主要地图服务
- 2024-06-04：优化位置选择界面
- 2024-06-04：添加地址缓存机制
