"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      feedbackContent: "",
      imageList: [],
      contactInfo: "",
      deviceCode: "",
      // 设备编码
      deviceName: ""
      // 设备名称
    };
  },
  onLoad(options) {
    if (options.deviceCode) {
      this.deviceCode = options.deviceCode;
    }
    if (options.deviceName) {
      this.deviceName = decodeURIComponent(options.deviceName);
    }
    this.getUserInfo();
  },
  methods: {
    goBack() {
      common_vendor.index.navigateBack();
    },
    getUserInfo() {
      const userInfo = common_vendor.index.getStorageSync("userInfo");
      if (userInfo && userInfo.phone) {
        this.contactInfo = userInfo.phone;
      }
    },
    chooseImage() {
      common_vendor.index.chooseImage({
        count: 3,
        // 最多选择3张图片
        sizeType: ["compressed"],
        sourceType: ["album", "camera"],
        success: (res) => {
          this.imageList = this.imageList.concat(res.tempFilePaths);
          this.uploadImages(res.tempFilePaths);
        },
        fail: (err) => {
          common_vendor.index.__f__("error", "at pages/my/repair.vue:100", "选择图片失败：", err);
          common_vendor.index.showToast({
            title: "选择图片失败",
            icon: "none"
          });
        }
      });
    },
    uploadImages(imagePaths) {
      imagePaths.forEach((imagePath) => {
        common_vendor.index.uploadFile({
          url: "https://www.narenqiqige.com/api/upload/image",
          filePath: imagePath,
          name: "file",
          header: {
            "Authorization": "Bearer " + common_vendor.index.getStorageSync("token")
          },
          success: (uploadRes) => {
            common_vendor.index.__f__("log", "at pages/my/repair.vue:120", "图片上传成功：", uploadRes);
          },
          fail: (err) => {
            common_vendor.index.__f__("error", "at pages/my/repair.vue:124", "图片上传失败：", err);
          }
        });
      });
    },
    removeImage(index) {
      this.imageList.splice(index, 1);
    },
    submitFeedback() {
      if (!this.feedbackContent.trim()) {
        common_vendor.index.showToast({
          title: "请输入问题描述",
          icon: "none"
        });
        return;
      }
      if (!this.contactInfo.trim()) {
        common_vendor.index.showToast({
          title: "请输入联系方式",
          icon: "none"
        });
        return;
      }
      const repairData = {
        deviceCode: this.deviceCode,
        deviceName: this.deviceName,
        problemDesc: this.feedbackContent.trim(),
        contactInfo: this.contactInfo.trim(),
        images: this.imageList.join(",")
        // 多个图片用逗号分隔
      };
      common_vendor.index.showLoading({
        title: "提交中..."
      });
      common_vendor.index.request({
        url: "https://www.narenqiqige.com/api/device-repair/submit",
        method: "POST",
        header: {
          "Content-Type": "application/json",
          "Authorization": "Bearer " + common_vendor.index.getStorageSync("token")
        },
        data: repairData,
        success: (res) => {
          common_vendor.index.hideLoading();
          if (res.data.code === 200) {
            common_vendor.index.showToast({
              title: "报修提交成功",
              icon: "success",
              duration: 2e3,
              success: () => {
                setTimeout(() => {
                  common_vendor.index.navigateBack();
                }, 2e3);
              }
            });
          } else {
            common_vendor.index.showToast({
              title: res.data.message || "提交失败",
              icon: "none"
            });
          }
        },
        fail: (err) => {
          common_vendor.index.hideLoading();
          common_vendor.index.__f__("error", "at pages/my/repair.vue:197", "提交报修失败：", err);
          common_vendor.index.showToast({
            title: "网络错误，请重试",
            icon: "none"
          });
        }
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.feedbackContent,
    b: common_vendor.o(($event) => $data.feedbackContent = $event.detail.value),
    c: $data.imageList.length > 0
  }, $data.imageList.length > 0 ? {
    d: common_vendor.f($data.imageList, (image, index, i0) => {
      return {
        a: image,
        b: common_vendor.o(($event) => $options.removeImage(index), index),
        c: index
      };
    })
  } : {}, {
    e: $data.imageList.length < 3
  }, $data.imageList.length < 3 ? {
    f: common_vendor.o((...args) => $options.chooseImage && $options.chooseImage(...args))
  } : {}, {
    g: $data.contactInfo,
    h: common_vendor.o(($event) => $data.contactInfo = $event.detail.value),
    i: common_vendor.o((...args) => $options.submitFeedback && $options.submitFeedback(...args))
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/my/repair.js.map
