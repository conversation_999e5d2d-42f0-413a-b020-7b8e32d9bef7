<view class="container"><view class="status-tabs"><scroll-view scroll-x class="tab-scroll" scroll-left="{{b}}"><view class="tab-container"><view wx:for="{{a}}" wx:for-item="tab" wx:key="b" class="{{['tab-item', tab.c && 'active']}}" bindtap="{{tab.d}}" id="{{tab.e}}"><text>{{tab.a}}</text></view></view></scroll-view><view class="active-indicator" style="{{c}}"></view></view><scroll-view class="device-list" scroll-y refresher-enabled="{{true}}" refresher-triggered="{{j}}" bindrefresherrefresh="{{k}}" bindscrolltolower="{{l}}"><view wx:if="{{d}}" class="empty-device"><image class="empty-image" src="{{e}}" mode="aspectFit"></image><text class="empty-text">暂无设备</text></view><view wx:for="{{f}}" wx:for-item="device" wx:key="r" class="device-item"><view class="device-icon"><image class="icon-image" src="{{device.a}}"></image></view><view class="device-info"><view class="name-row"><text class="device-name">{{device.b}}</text><text class="{{['device-status', device.d]}}">{{device.c}}</text></view><text class="device-id">设备ID: {{device.e}}</text><view class="device-meta"><text wx:if="{{device.f}}" class="device-time"><uni-icons wx:if="{{device.h}}" u-i="{{device.g}}" bind:__l="__l" u-p="{{device.h}}"></uni-icons> {{device.i}}</text></view></view><view wx:if="{{device.j}}" class="device-actions" catchtap="{{device.n}}"><view wx:if="{{device.k}}" class="used-btn" catchtap="{{device.l}}"><text>断开</text></view><view wx:else class="use-btn" catchtap="{{device.m}}"><text>使用</text></view></view><view wx:if="{{device.o}}" class="device-actions" catchtap="{{device.q}}"><view class="used-btn" catchtap="{{device.p}}"><text>解除绑定</text></view></view></view><view wx:if="{{g}}" class="load-more"><uni-icons wx:if="{{h}}" class="loading-icon" u-i="defba7c2-1" bind:__l="__l" u-p="{{h}}"></uni-icons><text>加载中...</text></view><view wx:if="{{i}}" class="no-more"><text>没有更多设备了</text></view></scroll-view></view>