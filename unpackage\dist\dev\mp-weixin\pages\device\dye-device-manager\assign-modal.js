"use strict";
const common_vendor = require("../../../common/vendor.js");
const _sfc_main = {
  name: "AssignPermissionPopup",
  props: {
    // 内容区域最大高度（根据屏幕高度自动计算）
    contentMaxHeight: {
      type: Number,
      default: 400
    }
  },
  data() {
    return {
      deviceInfo: {},
      // 设备信息
      staffList: [],
      // 发型师列表
      selectedStaff: [],
      // 已选中的发型师
      selectedPermissionLevel: 2,
      // 选中的权限级别，默认控制权限
      isLoading: false,
      // 加载状态
      isVisible: false
      // 弹窗显示状态
    };
  },
  methods: {
    // 打开弹窗
    async open(device) {
      this.deviceInfo = device || {};
      this.isLoading = true;
      try {
        await this.fetchStaffList();
        await this.fetchAssignedStaff();
        this.$refs.popup.open();
        this.isVisible = true;
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/device/dye-device-manager/assign-modal.vue:125", "弹窗打开失败:", error);
        common_vendor.index.showToast({
          title: "加载失败",
          icon: "none"
        });
      } finally {
        this.isLoading = false;
      }
    },
    // 关闭弹窗
    close() {
      if (this.isLoading)
        return;
      this.$refs.popup.close();
      this.isVisible = false;
    },
    // 弹窗状态变化
    handlePopupChange(e) {
      this.isVisible = e.show;
      if (!e.show) {
        this.$emit("close");
      }
    },
    // 手动关闭
    handleClose() {
      this.close();
      this.$emit("cancel");
    },
    // 获取可分配权限的员工列表
    async fetchStaffList() {
      try {
        const response = await this.$api.device.getAvailableStaff(this.deviceInfo.id);
        this.staffList = response.data || [];
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/device/dye-device-manager/assign-modal.vue:162", "获取员工列表失败:", error);
        common_vendor.index.showToast({
          title: "获取员工列表失败",
          icon: "none"
        });
        this.staffList = [];
      }
    },
    // 获取已分配权限的员工
    async fetchAssignedStaff() {
      try {
        const response = await this.$api.device.getAssignedStaff(this.deviceInfo.id);
        const assignedStaff = response.data || [];
        this.selectedStaff = assignedStaff.map((staff) => staff.userId);
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/device/dye-device-manager/assign-modal.vue:179", "获取已分配员工失败:", error);
        this.selectedStaff = [];
      }
    },
    // 选择发型师
    handleStaffSelection(e) {
      this.selectedStaff = e.detail.value;
    },
    // 获取用户类型文本
    getUserTypeText(userType) {
      const typeMap = {
        1: "普通用户",
        2: "商家用户",
        3: "管理员",
        4: "系统管理员"
      };
      return typeMap[userType] || "未知";
    },
    // 确认分配
    async handleConfirm() {
      if (this.isLoading)
        return;
      this.isLoading = true;
      try {
        const response = await this.$api.device.assignPermission({
          deviceId: this.deviceInfo.id,
          userIds: this.selectedStaff,
          permissionLevel: this.selectedPermissionLevel || 2
          // 默认控制权限
        });
        if (response.success) {
          common_vendor.index.showToast({
            title: "权限分配成功",
            icon: "success"
          });
          this.$emit("confirm", {
            deviceId: this.deviceInfo.id,
            staffIds: this.selectedStaff
          });
        } else {
          throw new Error(response.message || "权限分配失败");
        }
        this.close();
      } catch (error) {
        common_vendor.index.showToast({
          title: error.message || "分配失败",
          icon: "none"
        });
      } finally {
        this.isLoading = false;
      }
    }
  }
};
if (!Array) {
  const _easycom_uni_icons2 = common_vendor.resolveComponent("uni-icons");
  const _easycom_uni_popup2 = common_vendor.resolveComponent("uni-popup");
  (_easycom_uni_icons2 + _easycom_uni_popup2)();
}
const _easycom_uni_icons = () => "../../../uni_modules/uni-icons/components/uni-icons/uni-icons.js";
const _easycom_uni_popup = () => "../../../uni_modules/uni-popup/components/uni-popup/uni-popup.js";
if (!Math) {
  (_easycom_uni_icons + _easycom_uni_popup)();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  var _a;
  return {
    a: common_vendor.o($options.handleClose),
    b: common_vendor.p({
      type: "closeempty",
      size: "22",
      color: "#999"
    }),
    c: common_vendor.t($data.deviceInfo.name || "--"),
    d: common_vendor.t($data.deviceInfo.sn || $data.deviceInfo.device_code || "--"),
    e: common_vendor.t(((_a = $data.deviceInfo.manager) == null ? void 0 : _a.name) || "未绑定"),
    f: common_vendor.f($data.staffList, (staff, k0, i0) => {
      return {
        a: staff.id,
        b: $data.selectedStaff.includes(staff.id),
        c: common_vendor.t(staff.nickname || staff.username),
        d: common_vendor.t($options.getUserTypeText(staff.userType)),
        e: staff.id
      };
    }),
    g: common_vendor.o((...args) => $options.handleStaffSelection && $options.handleStaffSelection(...args)),
    h: $props.contentMaxHeight + "px",
    i: common_vendor.o((...args) => $options.handleClose && $options.handleClose(...args)),
    j: $data.isLoading,
    k: common_vendor.o((...args) => $options.handleConfirm && $options.handleConfirm(...args)),
    l: $data.isLoading,
    m: common_vendor.sr("popup", "a4b747c5-0"),
    n: common_vendor.o($options.handlePopupChange),
    o: common_vendor.p({
      type: "bottom",
      ["safe-area"]: true
    })
  };
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-a4b747c5"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/pages/device/dye-device-manager/assign-modal.js.map
