/* 水平间距 */
/* 水平间距 */
/* 全局样式修复 - 确保页面占满屏幕宽度 */
page {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
}

/* 通用容器样式 */
.container, .page-container {
  width: 100%;
  min-height: 100vh;
  box-sizing: border-box;
}

/* 修复小程序页面宽度问题 */
uni-page-body {
  width: 100% !important;
}

/* 确保所有view元素正确继承宽度 */
view {
  box-sizing: border-box;
}
.success-container {
  min-height: 100vh;
  background-color: #fff;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 100rpx 40rpx;
  box-sizing: border-box;
  width: 100%;
}
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
}
.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #007aff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}
@keyframes spin {
0% {
    transform: rotate(0deg);
}
100% {
    transform: rotate(360deg);
}
}
.loading-text {
  color: #666;
  font-size: 28rpx;
}
.success-content {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.success-icon {
  width: 160rpx;
  height: 160rpx;
  background-color: #4caf50;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 40rpx;
}
.success-icon .icon {
  color: #fff;
  font-size: 100rpx;
}
.success-title {
  font-size: 40rpx;
  font-weight: bold;
  margin-bottom: 60rpx;
  color: #333;
}
.order-info {
  width: 100%;
  max-width: 600rpx;
  background-color: #f9f9f9;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 60rpx;
  box-sizing: border-box;
}
.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  font-size: 28rpx;
}
.info-item:last-child {
  margin-bottom: 0;
}
.label {
  color: #666;
  flex-shrink: 0;
}
.value {
  color: #333;
  font-weight: bold;
  text-align: right;
  word-break: break-all;
}
.action-btns {
  width: 100%;
  max-width: 600rpx;
}
.btn {
  width: 100%;
  height: 90rpx;
  line-height: 90rpx;
  border-radius: 45rpx;
  margin-bottom: 30rpx;
  font-size: 30rpx;
  border: none;
}
.btn.primary {
  background-color: #f44336;
  color: #fff;
}
.btn.secondary {
  background-color: #ff9800;
  color: #fff;
}
.btn.outline {
  background-color: #fff;
  color: #666;
  border: 2rpx solid #ddd;
}
.btn:last-child {
  margin-bottom: 0;
}