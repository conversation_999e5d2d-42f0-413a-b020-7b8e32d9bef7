/* 水平间距 */
/* 水平间距 */
/* 全局样式修复 - 确保页面占满屏幕宽度 */
page {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
}

/* 通用容器样式 */
.container, .page-container {
  width: 100%;
  min-height: 100vh;
  box-sizing: border-box;
}

/* 修复小程序页面宽度问题 */
uni-page-body {
  width: 100% !important;
}

/* 确保所有view元素正确继承宽度 */
view {
  box-sizing: border-box;
}
.settings-container {
  width: 100%;
  height: 100%;
  padding: 32rpx;
  background-color: #f8f9fa;
  min-height: 100vh;
}
.time-display {
  font-size: 28rpx;
  color: #6c757d;
  margin-bottom: 48rpx;
  font-weight: 500;
}
.settings-title {
  font-size: 48rpx;
  font-weight: 700;
  margin-bottom: 48rpx;
  color: #212529;
  letter-spacing: 0.5rpx;
}
.settings-list {
  background-color: #fff;
  border-radius: 24rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}
.setting-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1rpx solid #f1f3f5;
  font-size: 32rpx;
  transition: all 0.2s ease;
}
.setting-item:active {
  background-color: #f8f9fa;
}
.setting-item:last-child {
  border-bottom: none;
}
.arrow-icon {
  width: 32rpx;
  height: 32rpx;
  opacity: 0.6;
}
.logout {
  color: #495057;
  font-weight: 500;
}
.delete-account {
  color: #ff4d4f;
  font-weight: 500;
}