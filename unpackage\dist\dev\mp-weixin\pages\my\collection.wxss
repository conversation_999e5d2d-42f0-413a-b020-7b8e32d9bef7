/* 水平间距 */
/* 水平间距 */
/* 全局样式修复 - 确保页面占满屏幕宽度 */
page {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
}

/* 通用容器样式 */
.container, .page-container {
  width: 100%;
  min-height: 100vh;
  box-sizing: border-box;
}

/* 修复小程序页面宽度问题 */
uni-page-body {
  width: 100% !important;
}

/* 确保所有view元素正确继承宽度 */
view {
  box-sizing: border-box;
}
.user-home {
  background-color: #fff;
  min-height: 100vh;
  /* 用户信息区域 */
  /* 内容分类导航 */
  /* 网格布局样式 */
  /* 响应式调整 */
  /* 内容列表 */
  /* 图片上传样式 */
}
.user-home .user-info {
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}
.user-home .user-info .user-basic {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 30rpx;
}
.user-home .user-info .user-basic .user-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: 20rpx;
  border: 1rpx solid #f0f0f0;
}
.user-home .user-info .user-basic .username {
  font-size: 36rpx;
  font-weight: bold;
  flex: 1;
}
.user-home .user-info .user-basic .edit-btn {
  background-color: transparent;
  color: #666;
  font-size: 26rpx;
  padding: 0;
  margin: 0;
  line-height: 1;
}
.user-home .user-info .user-basic .edit-btn::after {
  border: none;
}
.user-home .user-info .user-desc {
  font-size: 30rpx;
  color: #333;
  margin-bottom: 30rpx;
}
.user-home .user-info .user-stats {
  display: flex;
  justify-content: space-around;
  text-align: center;
}
.user-home .user-info .user-stats .stat-item {
  flex: 1;
}
.user-home .user-info .user-stats .stat-item .stat-num {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}
.user-home .user-info .user-stats .stat-item .stat-label {
  display: block;
  font-size: 26rpx;
  color: #999;
  margin-top: 5rpx;
}
.user-home .content-tabs {
  display: flex;
  border-bottom: 1rpx solid #f0f0f0;
}
.user-home .content-tabs .tab-item {
  flex: 1;
  text-align: center;
  padding: 25rpx 0;
  font-size: 30rpx;
  color: #666;
  position: relative;
}
.user-home .content-tabs .tab-item.active {
  color: #000;
  font-weight: bold;
}
.user-home .content-tabs .tab-item.active::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 80rpx;
  height: 4rpx;
  background-color: #000;
  border-radius: 2rpx;
}
.user-home .content-grid {
  display: flex;
  flex-wrap: wrap;
  padding: 20rpx;
  justify-content: space-between;
}
.user-home .content-grid .grid-item {
  width: 48%;
  margin-bottom: 20rpx;
  background-color: #fff;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}
.user-home .content-grid .grid-item .grid-image {
  width: 100%;
  height: 300rpx;
  display: block;
}
.user-home .content-grid .grid-item .grid-info {
  padding: 20rpx;
}
.user-home .content-grid .grid-item .grid-info .grid-text {
  font-size: 28rpx;
  color: #333;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-bottom: 15rpx;
  line-height: 1.4;
}
.user-home .content-grid .grid-item .grid-info .grid-meta {
  display: flex;
  justify-content: space-between;
  font-size: 24rpx;
  color: #999;
}
.user-home .content-grid .grid-item .grid-info .grid-meta .meta-user {
  max-width: 60%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
@media (max-width: 400px) {
.user-home .content-grid .grid-item {
    width: 48%;
}
.user-home .content-grid .grid-item .grid-image {
    height: 250rpx;
}
}
@media (min-width: 600px) {
.user-home .content-grid .grid-item {
    width: 48%;
}
.user-home .content-grid .grid-item .grid-image {
    height: 350rpx;
}
}
.user-home .content-list {
  padding: 20rpx;
}
.user-home .content-list .content-item {
  margin-bottom: 40rpx;
  border-bottom: 1rpx solid #f0f0f0;
  padding-bottom: 20rpx;
}
.user-home .content-list .content-item .content-row {
  display: flex;
  flex-direction: column;
}
.user-home .content-list .content-item .content-row .content-text {
  font-size: 32rpx;
  color: #333;
  margin-bottom: 20rpx;
}
.user-home .content-list .content-item .content-row .content-image {
  width: 100%;
  height: 400rpx;
  border-radius: 8rpx;
  margin-bottom: 20rpx;
}
.user-home .content-list .content-item .content-meta {
  display: flex;
  justify-content: space-between;
  font-size: 26rpx;
  color: #999;
}
.user-home .content-list .content-item .content-meta .meta-item {
  display: flex;
  align-items: center;
}
.user-home .fab {
  position: fixed;
  right: 40rpx;
  bottom: 80rpx;
  width: 100rpx;
  height: 100rpx;
  background-color: #007AFF;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.2);
  z-index: 999;
}
.user-home .fab .fab-icon {
  color: white;
  font-size: 50rpx;
  font-weight: bold;
  margin-bottom: 6rpx;
}
.user-home .modal-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}
.user-home .modal-container {
  width: 90%;
  max-height: 80vh;
  background-color: #fff;
  border-radius: 20rpx;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}
.user-home .modal-header {
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.user-home .modal-header .modal-title {
  font-size: 36rpx;
  font-weight: bold;
}
.user-home .modal-header .modal-close {
  font-size: 50rpx;
  color: #999;
}
.user-home .modal-content {
  padding: 30rpx;
  flex: 1;
  overflow-y: auto;
}
.user-home .modal-content .content-input {
  width: 100%;
  min-height: 200rpx;
  font-size: 32rpx;
  margin-bottom: 30rpx;
}
.user-home .modal-content .insert-product {
  color: #007AFF;
  font-size: 28rpx;
  margin-bottom: 40rpx;
  padding: 10rpx 0;
}
.user-home .subject-grid {
  margin-top: 20rpx;
}
.user-home .subject-row {
  display: flex;
  margin-bottom: 20rpx;
}
.user-home .subject-item {
  flex: 1;
  text-align: center;
  padding: 15rpx 0;
  font-size: 28rpx;
  color: #333;
  border: 1rpx solid #f0f0f0;
  margin: 0 10rpx;
  border-radius: 8rpx;
  transition: all 0.3s;
}
.user-home .subject-item:active {
  background-color: #f0f0f0;
}
.user-home .subject-item.selected {
  background-color: #007AFF;
  color: white;
  border-color: #007AFF;
}
.user-home .modal-footer {
  display: flex;
  padding: 20rpx 30rpx;
  border-top: 1rpx solid #f0f0f0;
}
.user-home .modal-footer button {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  font-size: 32rpx;
  margin: 0 10rpx;
}
.user-home .modal-footer button::after {
  border: none;
}
.user-home .modal-footer .cancel-btn {
  background-color: #f0f0f0;
  color: #333;
}
.user-home .modal-footer .confirm-btn {
  background-color: #007AFF;
  color: white;
}
.user-home .image-upload {
  margin-bottom: 40rpx;
}
.user-home .image-upload .upload-title {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 20rpx;
}
.user-home .image-upload .image-preview-container {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}
.user-home .image-upload .image-preview-container .image-preview {
  width: 160rpx;
  height: 160rpx;
  position: relative;
}
.user-home .image-upload .image-preview-container .image-preview .preview-image {
  width: 100%;
  height: 100%;
}
.user-home .image-upload .image-preview-container .image-preview .remove-image {
  position: absolute;
  top: 0;
  right: 0;
  width: 40rpx;
  height: 40rpx;
  background: rgba(0, 0, 0, 0.5);
  color: white;
}
.user-home .image-upload .image-preview-container .upload-btn {
  width: 160rpx;
  height: 160rpx;
  border: 1rpx dashed #ccc;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  /* 新增：垂直居中 */
  justify-content: center;
  /* 新增：水平居中 */
}
.user-home .image-upload .image-preview-container .upload-btn .upload-icon {
  font-size: 60rpx;
  color: #999;
  margin-bottom: 0;
  /* 移除原有下边距 */
  line-height: 1;
  /* 防止文本偏移 */
}
.user-home .image-upload .upload-tip {
  font-size: 24rpx;
  color: #999;
}