<template>
  <view class="guide-container">
    <!-- <view class="header">
      <view class="back-btn" @click="goBack">
        <text class="iconfont icon-arrow-left"></text>
      </view>
      <view class="title">新手引导</view>
    </view> -->
    
    <view class="content">
      <!-- 加载状态 -->
      <view v-if="loading" class="loading-container">
        <text>加载中...</text>
      </view>
      
      <!-- 错误状态 -->
      <view v-else-if="error" class="error-container">
        <text>{{ error }}</text>
        <button @click="loadGuideList" class="retry-btn">重试</button>
      </view>
      
      <!-- 引导列表 -->
      <view v-else class="guide-list">
        <view 
          v-for="(item, index) in guideList" 
          :key="item.id" 
          class="guide-item"
          @click="viewGuide(item)"
        >
          <view class="guide-icon">
            <text class="step-number">{{ item.stepOrder || index + 1 }}</text>
          </view>
          <view class="guide-info">
            <text class="guide-title">{{ item.title }}</text>
            <text class="guide-desc">{{ item.description || '点击查看详细内容' }}</text>
            <view class="guide-meta">
              <text class="guide-type">{{ getGuideTypeText(item.guideType) }}</text>
              <text class="view-count">查看 {{ item.viewCount || 0 }} 次</text>
            </view>
          </view>
          <view class="guide-arrow">
            <text class="iconfont icon-arrow-right"></text>
          </view>
        </view>
        
        <!-- 空状态 -->
        <view v-if="guideList.length === 0" class="empty-container">
          <text>暂无新手引导内容</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { get } from '@/utils/request.js'

export default {
  data() {
    return {
      guideList: [],
      loading: false,
      error: ''
    }
  },
  onLoad() {
    this.loadGuideList()
  },
  methods: {
    goBack() {
      uni.navigateBack({
        delta: 1
      });
    },
    
    async loadGuideList() {
      try {
        this.loading = true
        this.error = ''
        
        const response = await get('/api/guide/list')
        
        if (response && response.code === 200) {
          this.guideList = response.data || []
        } else {
          this.error = response?.message || '获取引导列表失败'
        }
      } catch (error) {
        console.error('获取引导列表失败:', error)
        this.error = '网络错误，请稍后重试'
      } finally {
        this.loading = false
      }
    },
    
    viewGuide(guide) {
      uni.navigateTo({
        url: `/pages/my/guide-detail?id=${guide.id}&title=${encodeURIComponent(guide.title)}`,
        success: function() {
          console.log('跳转到引导详情成功')
        },
        fail: function() {
          uni.showToast({
            title: '页面跳转失败',
            icon: 'none'
          })
        }
      })
    },
    
    getGuideTypeText(type) {
      const typeMap = {
        'general': '通用',
        'device': '设备',
        'operation': '操作',
        'maintenance': '维护'
      }
      return typeMap[type] || '通用'
    }
  }
}
</script>

<style lang="scss">
.guide-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100%;
  background-color: #f7f8fa;
  box-sizing: border-box;
}

.header {
  display: flex;
  align-items: center;
  height: 88rpx;
  background-color: #ffffff;
  padding: 0 30rpx;
  position: relative;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.back-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.title {
  flex: 1;
  text-align: center;
  font-size: 36rpx;
  font-weight: 500;
  margin-right: 60rpx;
}

.content {
  flex: 1;
  padding: 20rpx;
}

.loading-container, .error-container, .empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400rpx;
  color: #999;
}

.retry-btn {
  margin-top: 20rpx;
  padding: 10rpx 30rpx;
  background-color: #007AFF;
  color: white;
  border-radius: 10rpx;
  border: none;
}

.guide-list {
  display: flex;
  flex-direction: column;
}

.guide-item {
  display: flex;
  align-items: center;
  background-color: #ffffff;
  padding: 30rpx;
  margin-bottom: 20rpx;
  border-radius: 10rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.guide-icon {
  width: 80rpx;
  height: 80rpx;
  background: linear-gradient(135deg, #007AFF, #5AC8FA);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
}

.step-number {
  font-size: 32rpx;
  font-weight: bold;
  color: white;
}

.guide-info {
  flex: 1;
}

.guide-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 10rpx;
  display: block;
}

.guide-desc {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 10rpx;
  display: block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.guide-meta {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.guide-type {
  font-size: 22rpx;
  color: #007AFF;
  background-color: #f0f5ff;
  padding: 4rpx 12rpx;
  border-radius: 8rpx;
}

.view-count {
  font-size: 22rpx;
  color: #999;
}

.guide-arrow {
  display: flex;
  align-items: center;
  color: #ccc;
  font-size: 24rpx;
}
</style>
