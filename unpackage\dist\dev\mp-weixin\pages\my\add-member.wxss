
.container.data-v-7eedd94a {
  min-height: 100vh;
  background-color: #f5f5f5;
}

/* 顶部导航栏 */
.header.data-v-7eedd94a {
  background: linear-gradient(135deg, #4285f4 0%, #5a9bff 100%);
  padding-top: var(--status-bar-height, 44rpx);
}
.nav-bar.data-v-7eedd94a {
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20rpx;
  position: relative;
}
.nav-left.data-v-7eedd94a {
  width: 80rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.back-icon.data-v-7eedd94a {
  font-size: 40rpx;
  color: #fff;
  font-weight: 300;
}
.nav-title.data-v-7eedd94a {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  font-size: 32rpx;
  color: #fff;
  font-weight: 500;
}
.nav-right.data-v-7eedd94a {
  width: 80rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 15rpx;
}
.more-icon.data-v-7eedd94a, .record-icon.data-v-7eedd94a {
  font-size: 28rpx;
  color: #fff;
}

/* 表单容器 */
.form-container.data-v-7eedd94a {
  padding: 60rpx 40rpx;
}

/* 手机号输入组 */
.input-group.data-v-7eedd94a {
  display: flex;
  align-items: center;
  background-color: #fff;
  border-radius: 12rpx;
  padding: 0 30rpx;
  margin-bottom: 40rpx;
  height: 100rpx;
}
.country-code.data-v-7eedd94a {
  font-size: 28rpx;
  color: #333;
  margin-right: 20rpx;
  flex-shrink: 0;
}
.phone-input.data-v-7eedd94a {
  flex: 1;
  font-size: 28rpx;
  color: #333;
  height: 100%;
}

/* 普通输入框 */
.input-item.data-v-7eedd94a {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 0 30rpx;
  margin-bottom: 40rpx;
  height: 100rpx;
  display: flex;
  align-items: center;
}
.text-input.data-v-7eedd94a {
  width: 100%;
  font-size: 28rpx;
  color: #333;
  height: 100%;
}

/* 提交按钮 */
.submit-btn.data-v-7eedd94a {
  background: linear-gradient(135deg, #4285f4 0%, #5a9bff 100%);
  border-radius: 50rpx;
  height: 100rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 60rpx;
}
.btn-text.data-v-7eedd94a {
  font-size: 32rpx;
  color: #fff;
  font-weight: 500;
}

/* 输入框占位符样式 */
.phone-input.data-v-7eedd94a::-webkit-input-placeholder, .text-input.data-v-7eedd94a::-webkit-input-placeholder {
  color: #999;
  font-size: 28rpx;
}
.phone-input.data-v-7eedd94a::placeholder,
.text-input.data-v-7eedd94a::placeholder {
  color: #999;
  font-size: 28rpx;
}
