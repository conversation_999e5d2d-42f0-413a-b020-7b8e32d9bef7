<template>
	<view class="content">
		<!-- 连接方式选项卡 -->
		<!-- <view class="tab-container">
			<view class="tab-item" @click="switchTab('network')">
				<view class="circle-icon" :class="{ active: currentTab === 'network' }"></view>
				<text class="tab-text" :class="{ active: currentTab === 'network' }">连网</text>
			</view>
			<view class="tab-item" @click="switchTab('bluetooth')">
				<view class="circle-icon" :class="{ active: currentTab === 'bluetooth' }"></view>
				<text class="tab-text" :class="{ active: currentTab === 'bluetooth' }">蓝牙</text>
			</view>
			<view class="tab-item" @click="switchTab('wifi')">
				<view class="circle-icon" :class="{ active: currentTab === 'wifi' }"></view>
				<text class="tab-text" :class="{ active: currentTab === 'wifi' }">WIFI</text>
			</view>
		</view> -->

		<!-- 选项卡下方的蓝色线条 -->
		<!-- <view class="indicator-container">
			<view class="indicator" :style="{ left: indicatorPosition }"></view>
		</view> -->

		<!-- 设备列表 -->
		<scroll-view class="device-list" scroll-y>
			<view v-for="(item, index) in devices" :key="item.deviceId" class="device-item"
				@click="connectDevice(item)">
				<view class="device-info">
					<text class="device-name">{{ item.name || '未知设备' }}</text>
					<text class="device-id">{{ item.deviceId }}</text>
					<text class="device-rssi">信号强度: {{ item.RSSI }}dBm</text>
				</view>
				<view class="device-actions">
					<text class="connect-btn" click="">连接</text>
				</view>
			</view>

			<view v-if="devices.length === 0" class="empty-tip">
				<text>未发现设备，请点击"开始扫描"按钮</text>
			</view>
		</scroll-view>

		<!-- 设备列表区域 -->
		<!-- <view class="empty-container" v-if="!devices">
			<view class="empty-circle"></view>
			<text class="empty-text">暂无设备</text>
		</view> -->

		<!-- 扫描设备按钮 -->
		<view class="scan-btn" @click="scanDevice">
			<text class="scan-btn-text">搜索蓝牙</text>
		</view>

		<!-- 底部导航栏 -->
		<!-- <view class="tabbar">
      <view class="tabbar-item">
        <image class="tabbar-icon" src="/static/tabbar/home.png"></image>
        <text class="tabbar-text">首页</text>
      </view>
      <view class="tabbar-item">
        <image class="tabbar-icon" src="/static/tabbar/palette.png"></image>
        <text class="tabbar-text">色板</text>
      </view>
      <view class="tabbar-item active">
        <image class="tabbar-icon" src="/static/tabbar/device_selected.png"></image>
        <text class="tabbar-text">设备</text>
      </view>
      <view class="tabbar-item">
        <image class="tabbar-icon" src="/static/tabbar/user.png"></image>
        <text class="tabbar-text">我的</text>
      </view>
    </view> -->
	</view>
</template>

<script>
	import bluetooth from '../../utils/bluetooth';
	export default {
		data() {
			return {
				currentTab: 'bluetooth', // 默认选中蓝牙标签
				locationEnabled: false,
				bluetoothEnabled: false,
				wifiEnabled: false,
				wifiForm: {
					deviceId: '',
					name: '',
					password: ''
				},
				devices: [], // 存储扫描到的设备列表
				connected: false, // 连接状态
				logs: [], // 操作日志
				scanning: false // 扫描状态
			}
		},
		computed: {
			indicatorPosition() {
				// 计算指示器位置
				const tabWidth = 100 / 3; // 每个选项卡宽度占总宽度的三分之一
				let position = 0;

				if (this.currentTab === 'network') {
					position = 0;
				} else if (this.currentTab === 'bluetooth') {
					position = tabWidth;
				} else if (this.currentTab === 'wifi') {
					position = tabWidth * 2;
				}

				return position + '%';
			}
		},
		onLoad() {
			// 页面加载时默认选择蓝牙标签
			this.currentTab = 'bluetooth';
			bluetooth.initBluetooth()
		},
		methods: {
			switchTab(tab) {
				this.currentTab = tab;
			},
			//搜索蓝牙设备
			scanDevice() {
				uni.showToast({
					title: '正在搜索蓝牙设备...',
					icon: 'loading',
					duration: 2000
				});
				// 开始扫描设备

				// const that = this;
				// if (this.data.scanning) return;

				// this.setData({
				// 	scanning: true
				// });
				console.log('开始扫描蓝牙设备...');

				bluetooth.searchDevices()
					.then(async res => {
						console.log("搜索到的设备", res)
						this.devices = res
						const targetDevice = devices.find(device => device.name === "IoT-BLE-Device");
						if (targetDevice) {
							// 停止搜索
							await bluetooth.stopSearch();
							// 连接设备
							this.connectDevice(targetDevice.deviceId);
						} else {
							uni.showToast({
								title: '未找到目标设备',
								icon: 'none'
							});
						}
					}).catch(err => {

					})


				// console.log("搜索到的设备")
				// console.log("devices", devices)

				// this.setData({
				// 	devices: devices
				// });

				// console.log("这个数据",JSON.parse(this.devices))

				// wx.startBluetoothDevicesDiscovery({
				// 	allowDuplicatesKey: true,
				// 	success() {
				// 		that.log('扫描启动成功');
				// 		wx.onBluetoothDeviceFound((res) => {
				// 			res.devices.forEach(device => {
				// 				if (!device.name && !device.localName) return;
				// 				that.updateDeviceList(device);
				// 			});
				// 		});
				// 	}
				// });
				// 模拟扫描过程
				// setTimeout(() => {
				//   uni.showToast({
				//     title: '未找到设备',
				//     icon: 'none',
				//     duration: 2000
				//   });
				// }, 2000);
			},
			// 连接设备
			connect(e) {

			},
			// 更新设备列表
			updateDeviceList(device) {
				const devices = this.data.devices;
				const index = devices.findIndex(d => d.deviceId === device.deviceId);

				const info = {
					name: device.name || device.localName,
					deviceId: device.deviceId,
					rssi: device.RSSI,
					advertisData: device.advertisData
				};

				if (index === -1) {
					devices.push(info);
				} else {
					devices[index] = info;
				}

				this.setData({
					devices
				});
			},
			// 停止扫描
			stopScan() {
				const that = this;
				wx.stopBluetoothDevicesDiscovery({
					success() {
						that.setData({
							scanning: false
						});
						that.log('已停止扫描');
					}
				});
			},

			// 连接设备
			connectDevice(e) {
				const deviceId = e.currentTarget.dataset.deviceId;
				const device = this.data.devices.find(d => d.deviceId === deviceId);
				const that = this;

				this.log(`正在连接 ${device.name}...`);

				wx.createBLEConnection({
					deviceId,
					success() {
						that.setData({
							connected: true
						});
						that.log('连接成功');
						that.getBLEDeviceServices(deviceId);
					},
					fail(err) {
						that.log('连接失败: ' + JSON.stringify(err));
					}
				});
			},
			// 获取设备服务
			getBLEDeviceServices(deviceId) {
				const that = this;
				wx.getBLEDeviceServices({
					deviceId,
					success(res) {
						res.services.forEach(serviceId => {
							if (serviceId.isPrimary) {
								that.getBLEDeviceCharacteristics(deviceId, serviceId.uuid);
							}
						});
					}
				});
			},
			// 获取特征值
			getBLEDeviceCharacteristics(deviceId, serviceId) {
				wx.getBLEDeviceCharacteristics({
					deviceId,
					serviceId,
					success(res) {
						res.characteristics.forEach(characteristic => {
							if (characteristic.properties.notify) {
								// 启用特征值订阅
								wx.notifyBLECharacteristicValueChange({
									deviceId,
									serviceId,
									characteristicId: characteristic.uuid,
									state: true
								});
							}
						});
					}
				});
			},
			// 断开连接
			disconnect() {
				const that = this;
				wx.closeBLEConnection({
					deviceId: this.data.currentDeviceId,
					success() {
						that.setData({
							connected: false
						});
						that.log('已断开连接');
					}
				});
			},

			// 日志记录
			log(msg) {
				const logs = this.data.logs;
				logs.unshift(msg);
				this.setData({
					logs: logs.slice(0, 50)
				}); // 最多保留50条
			},

			// 设备发现回调
			handleDeviceFound(devices) {
				const newDevices = devices.devices.filter(device => device.name);
				this.deviceList = [...this.deviceList, ...newDevices];
				console.log('发现设备列表:', this.deviceList);
			},
		}
	}
</script>

<style>
	.content {
		display: flex;
		flex-direction: column;
		align-items: stretch;
		height: 100vh;
		background-color: #ffffff;
		position: relative;
		width: 100%;
		box-sizing: border-box;
		overflow-x: hidden;
	}

	.header {
		background-color: #4285f4;
		color: white;
		position: relative;
		padding: 40px 15px 15px;
		display: flex;
		align-items: center;
		justify-content: center;
		width: 100%;
		box-sizing: border-box;
	}

	.title-center {
		font-size: 18px;
		font-weight: bold;
		text-align: center;
		flex: 1;
	}

	.controls-right {
		display: flex;
		align-items: center;
		background-color: rgba(255, 255, 255, 0.3);
		border-radius: 20px;
		padding: 5px 10px;
		position: absolute;
		right: 15px;
	}

	.dots,
	.line {
		margin-right: 10px;
		color: white;
	}

	.circle {
		font-size: 16px;
		color: white;
	}

	/* 标签栏样式 */
	.tab-container {
		display: flex;
		background-color: #ffffff;
		padding: 15px 0;
		width: 100%;
	}

	.tab-item {
		flex: 1;
		display: flex;
		flex-direction: row;
		align-items: center;
		justify-content: center;
	}

	.circle-icon {
		width: 22px;
		height: 22px;
		border-radius: 50%;
		border: 2px solid #dddddd;
		margin-right: 8px;
	}

	.circle-icon.active {
		border-color: #4285f4;
	}

	.tab-text {
		font-size: 16px;
		color: #333333;
	}

	.tab-text.active {
		color: #4285f4;
	}

	/* 指示器样式 */
	.indicator-container {
		width: 100%;
		height: 3px;
		background-color: #f0f0f0;
		position: relative;
	}

	.indicator {
		position: absolute;
		width: 33.33%;
		height: 100%;
		background-color: #4285f4;
		transition: left 0.3s;
	}

	/* 空状态样式 */
	.empty-container {
		flex: 1;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		margin-top: 50px;
		margin-bottom: 50px;
	}

	.empty-circle {
		width: 120px;
		height: 120px;
		border-radius: 50%;
		background-color: #f0f0f0;
		margin-bottom: 20px;
	}

	.empty-text {
		font-size: 16px;
		color: #aaaaaa;
	}

	/* 扫描按钮样式 */
	.scan-btn {
		margin: 0 20px 20px;
		height: 50px;
		background-color: #4285f4;
		border-radius: 25px;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.scan-btn-text {
		color: white;
		font-size: 16px;
	}

	/* 底部导航栏样式 */
	.tabbar {
		display: flex;
		height: 50px;
		border-top: 1px solid #f0f0f0;
		background-color: white;
	}

	.tabbar-item {
		flex: 1;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
	}

	.tabbar-icon {
		width: 24px;
		height: 24px;
		margin-bottom: 4px;
	}

	.tabbar-text {
		font-size: 12px;
		color: #999999;
	}

	.tabbar-item.active .tabbar-text {
		color: #4285f4;
	}

	/* 设备列表 */
	.device-list {
		flex: 1;
		background-color: white;
		border-radius: 12rpx;
		padding: 0 20rpx;
		box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
	}

	.device-item {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 25rpx 0;
		border-bottom: 1rpx solid #f0f0f0;
	}

	.device-info {
		flex: 1;
		display: flex;
		flex-direction: column;
	}

	.device-name {
		font-size: 30rpx;
		font-weight: bold;
		color: #333;
		margin-bottom: 8rpx;
	}

	.device-id {
		font-size: 22rpx;
		color: #999;
		word-break: break-all;
		margin-bottom: 8rpx;
	}

	.device-rssi {
		font-size: 22rpx;
		color: #666;
	}

	.device-actions {
		margin-left: 20rpx;
	}

	.connect-btn {
		background-color: #1aad19;
		color: white;
		padding: 10rpx 20rpx;
		border-radius: 8rpx;
		font-size: 24rpx;
	}

	.disconnect-btn {
		background-color: #e64340;
		color: white;
		padding: 10rpx 20rpx;
		border-radius: 8rpx;
		font-size: 24rpx;
	}

	/* 空状态提示 */
	.empty-tip {
		text-align: center;
		padding: 40rpx 0;
		font-size: 26rpx;
		color: #999;
	}

	/* 已连接设备区域 */
	.connected-device {
		margin-top: 30rpx;
		background-color: white;
		border-radius: 12rpx;
		padding: 20rpx;
		box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
	}

	.section-title {
		font-size: 28rpx;
		font-weight: bold;
		color: #333;
		margin-bottom: 20rpx;
		display: block;
	}

	/* 设备尺寸适配 */
	@media screen and (max-width: 390px) {

		.content,
		.header,
		.tab-container,
		.indicator-container,
		.empty-container,
		.scan-btn {
			width: 100% !important;
			box-sizing: border-box !important;
			margin-left: 0 !important;
			margin-right: 0 !important;
		}
	}

	/* iPhone X 及以上机型底部安全区域适配 */
	@supports (padding-bottom: constant(safe-area-inset-bottom)) or (padding-bottom: env(safe-area-inset-bottom)) {
		.content {
			padding-bottom: calc(50px + constant(safe-area-inset-bottom));
			padding-bottom: calc(50px + env(safe-area-inset-bottom));
		}

		.tabbar {
			padding-bottom: constant(safe-area-inset-bottom);
			padding-bottom: env(safe-area-inset-bottom);
		}
	}
</style>