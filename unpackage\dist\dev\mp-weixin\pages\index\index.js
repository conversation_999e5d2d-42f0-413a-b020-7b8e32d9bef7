"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_request = require("../../utils/request.js");
const utils_swatchesCache = require("../../utils/swatchesCache.js");
const common_assets = require("../../common/assets.js");
const _sfc_main = {
  data() {
    return {
      baseurl: "https://www.narenqiqige.com/api",
      // array: ['红色', '黄色', '绿色', '紫色'],
      activeIndex: -1,
      // 初始化为-1，表示未选中状态
      // currentCategory: '潮色',
      currentCategory: "",
      colorName: "色调/Swatche",
      // 控制滚动行为的变量
      scrollToCategoryId: "",
      scrollToImageId: "",
      scrollLeft: 0,
      currentScrollLeft: 0,
      // 跟踪实际滚动位置
      colorList: [
        // {
        // 	"id": 1,
        // 	"name": "测试配方5g",
        // 	"programCode": null,
        // 	"gramWeight": 0,
        // 	"colorType": "一步染/One-step",
        // 	"categoryName": null,
        // 	"iconPath": null,
        // 	"iconPaths": null,
        // 	"colorComponents": {
        // 		"gray": 5,
        // 		"green": 5,
        // 		"yellow": 5,
        // 		"orange": 5,
        // 		"red": 5,
        // 		"purple": 5,
        // 		"brown": 5,
        // 		"blue": 5,
        // 		"black": 5,
        // 		"faded_bleached": 5,
        // 		"bleached": 5,
        // 		"silver": 5,
        // 		"gold": 5,
        // 		"linen": 5,
        // 		"custom1": 5,
        // 		"custom2": 5,
        // 		"custom3": 5,
        // 		"custom4": 5
        // 	},
        // 	"developerComponents": {
        // 		"hydrogenPeroxide3Percent": 5,
        // 		"hydrogenPeroxide6Percent": 5,
        // 		"hydrogenPeroxide9Percent": 5,
        // 		"hydrogenPeroxide12Percent": 5
        // 	},
        // 	"sortOrder": 0,
        // 	"description": "",
        // 	"creatorId": 1,
        // 	"createTime": 1749004276000,
        // 	"updateTime": 1749004276000
        // },
        // {
        // 	"id": 2,
        // 	"name": "测试配方3g",
        // 	"programCode": 3,
        // 	"gramWeight": 16,
        // 	"colorType": "一步染/One-step",
        // 	"categoryName": null,
        // 	"iconPath": null,
        // 	"iconPaths": null,
        // 	"colorComponents": {
        // 		"gray": 3,
        // 		"green": 3,
        // 		"yellow": 3,
        // 		"orange": 3,
        // 		"red": 3,
        // 		"purple": 3,
        // 		"brown": 3,
        // 		"blue": 3,
        // 		"black": 3,
        // 		"faded_bleached": 3,
        // 		"bleached": 3,
        // 		"silver": 3,
        // 		"gold": 3,
        // 		"linen": 3,
        // 		"custom1": 3,
        // 		"custom2": 3,
        // 		"custom3": 3,
        // 		"custom4": 3
        // 	},
        // 	"developerComponents": {
        // 		"hydrogenPeroxide3Percent": 3,
        // 		"hydrogenPeroxide6Percent": 3,
        // 		"hydrogenPeroxide9Percent": 3,
        // 		"hydrogenPeroxide12Percent": 3
        // 	},
        // 	"sortOrder": 0,
        // 	"description": "一步染发技术全解析\n\n适用范围\n\n本技术适用于各类发质，头发底色在3-6度时效果堪称最佳。为了安全染出理想发色，染发前务必进行皮肤过敏测试，避免过敏反应。\n\n发根操作\n\n1.调配:根据想要的发色，将目标配方与20VOL双氧奶以1:1的比例倒入调色碗，再用染发刷充分搅拌均匀。\n\n2.涂抹:用染发刷从贴近头皮却不触碰头皮之处涂抹染膏，仔细梳理，确保发根都被染膏覆盖。\n\n3.强化:取用相同目标配方，与40VOL双氧奶按1:1混合，增强染膏的上色能力。\n\n4.全涂:从发根开始，顺着发丝将强化后的染膏涂抹至发尾，让每一处头发都浸满染膏。\n\n5.等待:涂抹完毕后，让头发自然停放约40分钟，具体时长参考染发剂说明书，结合期望色度调整。\n\n6.冲洗:待头发色度统一，用温水彻底冲洗染膏，直至冲洗的水变得清澈。\n\n发尾操作(7度以上发色)\n\n1.预处理(可选):\n\n·温水打底:若发尾受损，将目标配方与温水按1:1混合，涂抹在发尾，停放10分钟。\n\n·特殊配方打底:把“0/77+0/43 +2/0 (10:3:1)”和温水1:1混合，涂在发尾7-9度位置，停放10分钟不冲洗，接着用目标配方与6%双氧奶1:1混合涂抹发尾，等色度统一后冲洗。\n\n2.上色:无论是否预处理，都将目标配方和40VOL双氧奶按1:1混合调配染膏。\n\n3.涂抹:从发根开始，用6%双氧奶调配的染膏着重涂抹发根，发中与发尾部分则用12%双氧奶调配的染膏顺着发片均匀涂抹。\n\n4.等待:涂抹完成后停放40分钟以上，直至发尾色度与其他部位一致。\n\n5.冲洗:发尾色度达标后，用温水冲净染膏。\n\n注意事项1.选双氧奶:双氧奶浓度越高上色越快，不过对头发损伤越大，需依据自身发质和期望染发效果合理选择。\n\n2.监测头皮:染发时若头皮感到不适、刺痛，立即停止操作，及时找专业人士求助。\n\n3.染后护理:染完发后，建议日常使用滋润型洗发水和护发素，定期使用发膜，减少染发对头发的损伤，维持头发健康亮泽A Comprehensive Analysis of One-Step Hair Dyeing Technology\n\nScope of Application\nThis technology is applicable to all types of hair textures. It achieves the best results when the base color of the hair is between 3 and 6 degrees. To safely achieve the desired hair color, it is essential to conduct a skin allergy test before hair dyeing to avoid allergic reactions.\n\nRoot Operation\n1. Preparation: According to the desired hair color, pour the target formula and 20VOL hydrogen peroxide cream into a mixing bowl at a ratio of 1:1, and then stir thoroughly with a hair dye brush.\n2. Application: Use a hair dye brush to apply the hair dye cream starting from a point close to the scalp but not touching it. Comb carefully to ensure that all hair roots are covered with the hair dye cream.\n3. Enhancement: Take the same target formula and mix it with 40VOL hydrogen peroxide cream at a ratio of 1:1 to enhance the color - fixing ability of the hair dye cream.\n4. Full Application: Starting from the hair roots, apply the enhanced hair dye cream along the hair strands to the hair ends, ensuring that every part of the hair is saturated with the hair dye cream.\n5. Waiting: After application, let the hair rest naturally for about 40 minutes. The specific duration should refer to the hair dye instructions and be adjusted according to the desired color intensity.\n6. Rinsing: When the hair color is uniform, thoroughly rinse the hair dye cream with warm water until the rinsing water becomes clear.\n\nEnd Operation (for hair color above 7 degrees)\n1. Pretreatment (Optional):\n· Warm Water Priming: If the hair ends are damaged, mix the target formula with warm water at a ratio of 1:1, apply it to the hair ends, and let it rest for 10 minutes.\n· Special Formula Priming: Mix \"0/77 + 0/43 + 2/0 (10:3:1)\" with warm water at a ratio of 1:1, apply it to the 7 - 9 degree position of the hair ends, let it rest for 10 minutes without rinsing, then mix the target formula with 6% hydrogen peroxide cream at a ratio of 1:1 and apply it to the hair ends. Rinse after the color intensity is uniform.\n2. Coloring: Whether or not pretreatment is carried out, mix the target formula and 40VOL hydrogen peroxide cream at a ratio of 1:1 to prepare the hair dye cream.\n3. Application: Starting from the hair roots, apply the hair dye cream prepared with 6% hydrogen peroxide cream to the hair roots with emphasis. For the middle and end parts of the hair, apply the hair dye cream prepared with 12% hydrogen peroxide cream evenly along the hair sections.\n4. Waiting: After application, let it rest for more than 40 minutes until the color intensity of the hair ends is the same as that of other parts.\n5. Rinsing: After the color intensity of the hair ends meets the standard, rinse the hair dye cream thoroughly with warm water.\n\nPrecautions\n1. Selection of Hydrogen Peroxide Cream: The higher the concentration of hydrogen peroxide cream, the faster the color - fixing, but the greater the damage to the hair. It is necessary to choose reasonably according to your own hair texture and desired hair - dyeing effect.\n2. Monitor the Scalp: If you feel discomfort or tingling on the scalp during hair dyeing, stop the operation immediately and seek help from a professional in a timely manner.\n3. Post - Dyeing Care: After hair dyeing, it is recommended to use moisturizing shampoos and conditioners daily, and use hair masks regularly to reduce the damage to the hair caused by hair dyeing and maintain the health and luster of the hair.",
        // 	"creatorId": 1,
        // 	"createTime": 1749004276000,
        // 	"updateTime": 1749376513000
        // },
        // {
        // 	"id": 3,
        // 	"name": "深粉棕色",
        // 	"programCode": null,
        // 	"gramWeight": 15,
        // 	"colorType": "一步染/One-step",
        // 	"categoryName": null,
        // 	"iconPath": null,
        // 	"iconPaths": null,
        // 	"colorComponents": {
        // 		"gray": 1,
        // 		"green": null,
        // 		"yellow": 1,
        // 		"orange": null,
        // 		"red": 4,
        // 		"purple": null,
        // 		"brown": null,
        // 		"blue": 1,
        // 		"black": null,
        // 		"faded_bleached": null,
        // 		"bleached": null,
        // 		"silver": null,
        // 		"gold": null,
        // 		"linen": null,
        // 		"custom1": null,
        // 		"custom2": null,
        // 		"custom3": null,
        // 		"custom4": null
        // 	},
        // 	"developerComponents": {
        // 		"hydrogenPeroxide3Percent": null,
        // 		"hydrogenPeroxide6Percent": null,
        // 		"hydrogenPeroxide9Percent": null,
        // 		"hydrogenPeroxide12Percent": 8
        // 	},
        // 	"sortOrder": 0,
        // 	"description": "",
        // 	"creatorId": 1,
        // 	"createTime": 1749004276000,
        // 	"updateTime": 1749004276000
        // },
        // {
        // 	"id": 4,
        // 	"name": "6度底色",
        // 	"programCode": null,
        // 	"gramWeight": 20,
        // 	"colorType": "一步染/One-step",
        // 	"categoryName": null,
        // 	"iconPath": null,
        // 	"iconPaths": null,
        // 	"colorComponents": {
        // 		"gray": null,
        // 		"green": null,
        // 		"yellow": null,
        // 		"orange": null,
        // 		"red": null,
        // 		"purple": null,
        // 		"brown": null,
        // 		"blue": null,
        // 		"black": null,
        // 		"faded_bleached": null,
        // 		"bleached": null,
        // 		"silver": null,
        // 		"gold": null,
        // 		"linen": null,
        // 		"custom1": null,
        // 		"custom2": null,
        // 		"custom3": null,
        // 		"custom4": null
        // 	},
        // 	"developerComponents": {
        // 		"hydrogenPeroxide3Percent": null,
        // 		"hydrogenPeroxide6Percent": null,
        // 		"hydrogenPeroxide9Percent": null,
        // 		"hydrogenPeroxide12Percent": 10
        // 	},
        // 	"sortOrder": 0,
        // 	"description": "",
        // 	"creatorId": 1,
        // 	"createTime": 1749004276000,
        // 	"updateTime": 1749004276000
        // }
      ],
      // 初始化示例数据（根据实际业务调整）
      colorTypeList: [
        "一步染/Apply at one time",
        "人工色/免漂染(Bath dye / Painting dye)",
        "冷棕色系/Cool brown",
        "多段色/Multi-segment color (different base colors)",
        "微潮色系/Slightly trendy color",
        "日系/Japanese and Korean series color chart",
        "暖棕色系/Warm brown",
        "欧系/European series hair dye",
        "浅染深/Light brown",
        "深染浅/Dark to light",
        "潮色系/Trendy color",
        "爆顶染/Burst top dye",
        "生活色系/Life Color",
        "画染/Painting and dyeing",
        "盖白发/Covering white hair",
        "網紅色系/Internet-famous color"
      ],
      colorNameList: [
        "色调/Swatche",
        "暖棕/Warm Brown",
        "淡化剂/Faded",
        "灰色/Grey",
        "绿色/Green",
        "黄色/Yellow",
        "橙色/Orange",
        "红色/Red",
        "紫色/Purple",
        "深棕/Dark Brown",
        "浅棕/Light Brown",
        "蓝色/Blue",
        "黑色/Black",
        "冷棕/Cold Brown",
        "棕色/Brown",
        "灰色"
      ],
      colorTypeIcons: [
        "/uploads/file/1.jpg",
        "/uploads/file/2.jpg",
        "/uploads/file/4.jpg",
        "/uploads/file/5.jpg",
        "/uploads/file/6.jpg",
        "/uploads/file/7.jpg",
        "/uploads/file/8.jpg",
        "/uploads/file/9.jpg",
        "/uploads/file/10.jpg",
        "/uploads/file/11.jpg",
        "/uploads/file/12.jpg",
        "/uploads/file/13.jpg",
        "/uploads/file/15.jpg",
        "/uploads/file/16.jpg",
        "/uploads/file/17.jpg",
        "/uploads/file/18.jpg"
      ]
    };
  },
  onLoad() {
    this.getSwatches();
  },
  onShow() {
  },
  methods: {
    // 处理滚动事件，更新实际滚动位置
    onCategoryScroll(e) {
      this.currentScrollLeft = e.detail.scrollLeft;
    },
    // 滚动到指定索引的居中位置
    scrollToCenter(index) {
      this.$nextTick(() => {
        const query = common_vendor.index.createSelectorQuery().in(this);
        query.select(".color-images").boundingClientRect();
        query.select(`#image-${index}`).boundingClientRect();
        query.exec((res) => {
          const scrollViewRect = res[0];
          const cardRect = res[1];
          if (scrollViewRect && cardRect) {
            common_vendor.index.getSystemInfoSync().windowWidth;
            const cardRelativeLeft = cardRect.left - scrollViewRect.left;
            const cardCenter = cardRelativeLeft + cardRect.width / 2;
            const scrollViewCenter = scrollViewRect.width / 2;
            const scrollDistance = cardCenter - scrollViewCenter;
            const currentScrollLeft = this.currentScrollLeft || 0;
            const newScrollLeft = currentScrollLeft + scrollDistance;
            this.scrollLeft = Math.max(0, newScrollLeft);
            common_vendor.index.__f__("log", "at pages/index/index.vue:374", "精确滚动计算:", {
              index,
              scrollViewRect,
              cardRect,
              cardRelativeLeft,
              cardCenter,
              scrollViewCenter,
              scrollDistance,
              currentScrollLeft,
              newScrollLeft: this.scrollLeft
            });
          }
        });
      });
      this.scrollToCategoryId = "category-" + index;
      setTimeout(() => {
        this.scrollToCategoryId = "";
      }, 500);
    },
    handleColorNameChange(e) {
      const index = e.detail.value;
      common_vendor.index.__f__("log", "at pages/index/index.vue:400", "赛选颜色改变的按钮 数据:", index);
      if (index == 0) {
        this.$data.colorName = this.$data.colorNameList[0];
        this.getSwatches();
        return;
      }
      this.$data.colorName = this.$data.colorNameList[index];
      this.getSwatches();
    },
    // handleColorName() {
    // 	uni.__f__('log','at pages/index/index.vue:410',"点击筛选颜色的按钮")
    // },
    async getSwatches() {
      const colorName = this.$data.colorNameList[0] == this.$data.colorName ? null : this.$data.colorName;
      const data = {
        colorType: this.$data.currentCategory,
        colorName
      };
      let result = { data: utils_swatchesCache.swatchesCache.get(data) };
      if (!result.data) {
        result = await utils_request.apiService.formula.getSwatches(data);
        common_vendor.index.__f__("log", "at pages/index/index.vue:428", "API返回结果", result);
        if (result.code === 200 && result.data) {
          utils_swatchesCache.swatchesCache.set(data, result.data);
        }
      } else {
        common_vendor.index.__f__("log", "at pages/index/index.vue:435", "从缓存获取色板数据");
        result.code = 200;
      }
      if (result.code === 200 && result.data) {
        this.colorList = result.data.formulas;
        this.colorTypeList = result.data.colorTypes;
        if (this.activeIndex === -1) {
          this.currentCategory = result.data.currentCategory;
          if (this.colorTypeList && this.colorTypeList.length > 2) {
            this.activeIndex = 2;
            this.currentCategory = this.colorTypeList[2];
            this.$nextTick(() => {
              this.scrollToCenter(2);
            });
          }
        } else {
          if (this.activeIndex >= 0 && this.activeIndex < this.colorTypeList.length) {
            this.currentCategory = this.colorTypeList[this.activeIndex];
          } else {
            this.activeIndex = 0;
            this.currentCategory = this.colorTypeList[0];
          }
        }
        this.colorNameList = ["色调/Swatche", ...result.data.colorNames];
        this.colorTypeIcons = result.data.iconPaths;
      }
    },
    // 点击颜色分类标签事件
    handleCategoryTap(e) {
      const dataset = e.currentTarget.dataset;
      const clickedIndex = parseInt(dataset.index);
      this.activeIndex = clickedIndex;
      this.currentCategory = dataset.category;
      this.scrollToCenter(clickedIndex);
      this.getSwatches();
    },
    // 点击颜色类别图片分类事件
    handleColorTap(e) {
      const dataset = e.currentTarget.dataset;
      const clickedIndex = parseInt(dataset.index);
      this.activeIndex = clickedIndex;
      this.currentCategory = dataset.category;
      this.scrollToCenter(clickedIndex);
      this.getSwatches();
    },
    selectColor(color) {
      common_vendor.index.__f__("log", "at pages/index/index.vue:511", "选择颜色:", color.name);
      const isLogin = common_vendor.index.getStorageSync("isLoggedIn");
      common_vendor.index.__f__("log", "at pages/index/index.vue:514", "是否登录: ", isLogin);
      if (!isLogin) {
        common_vendor.index.showModal({
          title: "未登录请先登录"
        });
        return;
      } else {
        common_vendor.index.setStorageSync("selectedColorInfo", {
          id: color.id,
          name: color.name,
          category: this.currentCategory,
          hex: color.hex
        });
        common_vendor.index.showLoading({
          title: "正在加载..."
        });
        common_vendor.index.__f__("log", "at pages/index/index.vue:534", color.description);
        const url = "/pages/mall/color/detail?name=" + encodeURIComponent(color.name) + "&category=" + encodeURIComponent(this.currentCategory) + "&color=" + encodeURIComponent(color.hex) + "&description=" + encodeURIComponent(color.description) + "&iconPath=" + encodeURIComponent(color.iconPath) + "&colorComponents=" + encodeURIComponent(JSON.stringify(color.colorComponents)) + "&developerComponents=" + encodeURIComponent(JSON.stringify(color.developerComponents));
        common_vendor.index.__f__("log", "at pages/index/index.vue:546", "url", url);
        common_vendor.index.__f__("log", "at pages/index/index.vue:547", "颜色构造", color.colorComponents);
        setTimeout(() => {
          common_vendor.index.hideLoading();
          common_vendor.index.navigateTo({
            url,
            fail: (err) => {
              common_vendor.index.__f__("error", "at pages/index/index.vue:556", "页面跳转失败:", err);
              common_vendor.index.showModal({
                title: "跳转失败",
                content: "无法打开颜色详情页: " + JSON.stringify(err),
                showCancel: false
              });
            },
            success: () => {
              common_vendor.index.__f__("log", "at pages/index/index.vue:564", "跳转成功到:", url);
            }
          });
        }, 300);
      }
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.f($data.colorTypeList, (item, index, i0) => {
      return {
        a: common_vendor.t(item),
        b: $data.activeIndex === index ? 1 : "",
        c: index,
        d: "category-" + index,
        e: common_vendor.o((...args) => $options.handleCategoryTap && $options.handleCategoryTap(...args), index),
        f: index,
        g: item
      };
    }),
    b: $data.scrollToCategoryId,
    c: common_vendor.f($data.colorTypeList, (item, index, i0) => {
      return common_vendor.e({
        a: $data.colorTypeIcons[index]
      }, $data.colorTypeIcons[index] ? {
        b: $data.baseurl + $data.colorTypeIcons[index]
      } : {
        c: item.color || "#DCDCDC"
      }, {
        d: (item.name || item || "未命名").includes("/")
      }, (item.name || item || "未命名").includes("/") ? {
        e: common_vendor.t((item.name || item || "未命名").split("/")[0]),
        f: common_vendor.t((item.name || item || "未命名").split("/")[1])
      } : {
        g: common_vendor.t(item.name || item || "未命名")
      }, {
        h: $data.activeIndex === index ? 1 : "",
        i: index,
        j: "image-" + index,
        k: common_vendor.o((...args) => $options.handleColorTap && $options.handleColorTap(...args), index),
        l: index,
        m: item
      });
    }),
    d: $data.scrollLeft,
    e: common_vendor.o((...args) => $options.onCategoryScroll && $options.onCategoryScroll(...args)),
    f: common_vendor.t($data.currentCategory),
    g: common_vendor.t($data.colorList ? $data.colorList.length : 0),
    h: common_assets._imports_0,
    i: common_vendor.t($data.colorName),
    j: $data.colorNameList,
    k: common_vendor.o((...args) => $options.handleColorNameChange && $options.handleColorNameChange(...args)),
    l: common_vendor.f($data.colorList, (color, index, i0) => {
      return common_vendor.e({
        a: color.iconPath,
        b: color.name && color.name.includes("/")
      }, color.name && color.name.includes("/") ? {
        c: common_vendor.t(color.name.split("/")[0]),
        d: common_vendor.t(color.name.split("/")[1])
      } : {
        e: common_vendor.t(color.name)
      }, {
        f: index,
        g: common_vendor.o(($event) => $options.selectColor(color), index)
      });
    })
  };
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/index/index.js.map
