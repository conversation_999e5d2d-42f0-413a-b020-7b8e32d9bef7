/* 水平间距 */
/* 水平间距 */
/* 全局样式修复 - 确保页面占满屏幕宽度 */
page {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
}

/* 通用容器样式 */
.container, .page-container {
  width: 100%;
  min-height: 100vh;
  box-sizing: border-box;
}

/* 修复小程序页面宽度问题 */
uni-page-body {
  width: 100% !important;
}

/* 确保所有view元素正确继承宽度 */
view {
  box-sizing: border-box;
}
.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f5f5;
}
.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx;
  background-color: #4285f4;
  color: #fff;
}
.back-btn {
  font-size: 40rpx;
  width: 60rpx;
  display: flex;
  align-items: center;
}
.title {
  font-size: 36rpx;
  font-weight: 500;
}
.header-icons {
  display: flex;
  align-items: center;
}
.header-icon {
  margin-left: 15rpx;
  font-size: 24rpx;
}
.content-wrapper {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 30rpx;
}
.store-info-card {
  width: 100%;
  background-color: #fff;
  border-radius: 20rpx;
  padding: 40rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  display: flex;
  flex-direction: column;
  align-items: center;
}
.store-status {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
}
.bind-code {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 20rpx;
}
.scan-tip {
  font-size: 28rpx;
  color: #999;
  margin-bottom: 40rpx;
}
.qrcode-area {
  width: 300rpx;
  height: 300rpx;
  background-color: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 40rpx;
  border-radius: 10rpx;
  overflow: hidden;
}
.qrcode-image {
  width: 100%;
  height: 100%;
}
.qrcode-placeholder {
  font-size: 28rpx;
  color: #999;
}
.brand-info {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}
.share-btn {
  margin-top: 60rpx;
  width: 100%;
  height: 90rpx;
  background-color: #4285f4;
  border-radius: 45rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #fff;
  font-size: 32rpx;
}