"use strict";
const common_vendor = require("../../../common/vendor.js");
const utils_request = require("../../../utils/request.js");
const common_assets = require("../../../common/assets.js");
const _sfc_main = {
  data() {
    return {
      baseUrl: "https://www.narenqiqige.com/api",
      searchText: "",
      showResults: false,
      searchResults: [],
      categoryId: null,
      categoryName: "",
      searchHistory: [],
      historyTimes: [],
      recommends: [],
      loading: false
    };
  },
  onLoad(options) {
    this.loadLocalSearchHistory();
    this.loadSearchData();
    if (options.keyword) {
      this.searchText = options.keyword;
      this.doSearch();
    }
    if (options.categoryId) {
      this.categoryId = options.categoryId;
      this.categoryName = options.categoryName || "";
      this.searchText = this.categoryName;
      this.doSearch();
    }
  },
  computed: {
    // 显示最近10个搜索历史
    displaySearchHistory() {
      return this.searchHistory.slice(0, 10);
    }
  },
  methods: {
    // 加载搜索数据
    async loadSearchData() {
      this.loading = true;
      try {
        const [recommendsResult, historyResult] = await Promise.allSettled([
          this.loadRecommendations(),
          this.loadSearchHistory()
        ]);
        if (recommendsResult.status === "fulfilled") {
          this.recommends = recommendsResult.value || [];
        } else {
          common_vendor.index.__f__("warn", "at pages/mall/search/search.vue:143", "加载推荐词失败:", recommendsResult.reason);
          this.recommends = [];
        }
        if (historyResult.status === "fulfilled") {
          const remoteHistory = historyResult.value;
          if (remoteHistory && remoteHistory.keywords && remoteHistory.keywords.length > 0) {
            this.searchHistory = remoteHistory.keywords;
            this.historyTimes = remoteHistory.times;
          }
        } else {
          common_vendor.index.__f__("warn", "at pages/mall/search/search.vue:157", "加载搜索历史失败:", historyResult.reason);
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/mall/search/search.vue:161", "加载搜索数据异常:", error);
        this.recommends = [];
      } finally {
        this.loading = false;
      }
    },
    // 加载热门关键词
    async loadRecommendations() {
      try {
        const response = await utils_request.apiService.mall.search.hotKeywords();
        if (response.code === 200 && response.data) {
          return response.data || [];
        }
        throw new Error("热门关键词数据格式错误");
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/mall/search/search.vue:178", "获取热门关键词失败:", error);
        throw error;
      }
    },
    // 加载搜索历史
    async loadSearchHistory() {
      try {
        const response = await utils_request.apiService.mall.search.history();
        if (response.code === 200 && response.data) {
          const historyData = response.data.slice(0, 10);
          return {
            keywords: historyData.map((item) => item.keyword || item),
            times: historyData.map((item) => this.formatTime(item.createTime || item.time))
          };
        }
        throw new Error("搜索历史数据格式错误");
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/mall/search/search.vue:196", "获取搜索历史失败:", error);
        throw error;
      }
    },
    // 加载本地搜索历史（降级方案）
    loadLocalSearchHistory() {
      const savedHistory = common_vendor.index.getStorageSync("searchHistory");
      const savedTimes = common_vendor.index.getStorageSync("historyTimes");
      if (savedHistory && Array.isArray(savedHistory)) {
        this.searchHistory = savedHistory.slice(0, 10);
      }
      if (savedTimes && Array.isArray(savedTimes)) {
        this.historyTimes = savedTimes.slice(0, 10);
      }
    },
    // 格式化时间
    formatTime(timeStr) {
      if (!timeStr)
        return "未知";
      try {
        const time = new Date(timeStr);
        const now = /* @__PURE__ */ new Date();
        const diff = now - time;
        const minutes = Math.floor(diff / (1e3 * 60));
        const hours = Math.floor(diff / (1e3 * 60 * 60));
        const days = Math.floor(diff / (1e3 * 60 * 60 * 24));
        const weeks = Math.floor(days / 7);
        if (minutes < 1)
          return "刚刚";
        if (minutes < 60)
          return `${minutes}分钟前`;
        if (hours < 24)
          return `${hours}小时前`;
        if (days < 7)
          return `${days}天前`;
        if (weeks < 52)
          return `${weeks}w`;
        return `${Math.floor(weeks / 52)}年前`;
      } catch (error) {
        return timeStr;
      }
    },
    clearSearch() {
      this.searchText = "";
      this.showResults = false;
    },
    doSearch() {
      if (!this.searchText.trim()) {
        common_vendor.index.showToast({
          title: "请输入搜索内容",
          icon: "none"
        });
        return;
      }
      this.saveSearchHistory(this.searchText);
      common_vendor.index.showLoading({
        title: "搜索中..."
      });
      this.performSearch();
    },
    // 保存搜索历史
    async saveSearchHistory(keyword) {
      if (!keyword || this.searchHistory.includes(keyword)) {
        return;
      }
      try {
        await utils_request.apiService.mall.search.saveHistory(keyword);
        this.searchHistory.unshift(keyword);
        this.historyTimes.unshift("刚刚");
        if (this.searchHistory.length > 10) {
          this.searchHistory.pop();
          this.historyTimes.pop();
        }
        common_vendor.index.setStorageSync("searchHistory", this.searchHistory);
        common_vendor.index.setStorageSync("historyTimes", this.historyTimes);
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/mall/search/search.vue:292", "保存搜索历史失败:", error);
        this.searchHistory.unshift(keyword);
        this.historyTimes.unshift("刚刚");
        if (this.searchHistory.length > 10) {
          this.searchHistory.pop();
          this.historyTimes.pop();
        }
        common_vendor.index.setStorageSync("searchHistory", this.searchHistory);
        common_vendor.index.setStorageSync("historyTimes", this.historyTimes);
      }
    },
    // 执行搜索
    async performSearch() {
      try {
        const params = {
          keyword: this.searchText,
          page: 1,
          pageSize: 20
        };
        if (this.categoryId) {
          params.categoryId = this.categoryId;
        }
        const response = await utils_request.apiService.mall.search.products(params);
        common_vendor.index.hideLoading();
        if (response.code === 200) {
          const products = response.data.products || response.data.records || response.data || [];
          this.searchResults = products.map((product) => ({
            id: product.id,
            name: product.productName || product.name,
            price: product.price,
            sold: product.sales || 0,
            image: this.getProductImage(product)
          }));
          common_vendor.index.__f__("log", "at pages/mall/search/search.vue:335", "搜索返回的结果", this.searchResults);
        } else {
          this.searchResults = [];
          common_vendor.index.__f__("error", "at pages/mall/search/search.vue:338", "搜索失败:", response.message);
        }
        this.showResults = true;
      } catch (error) {
        common_vendor.index.hideLoading();
        common_vendor.index.__f__("error", "at pages/mall/search/search.vue:344", "搜索异常:", error);
        common_vendor.index.showToast({
          title: "搜索失败，请重试",
          icon: "none"
        });
        this.searchResults = [];
        this.showResults = true;
      }
    },
    // 获取商品图片
    getProductImage(product) {
      if (!product.images)
        return "";
      let imageUrl = "";
      if (typeof product.images === "string") {
        const imageArray = product.images.split(",");
        imageUrl = imageArray[0] || "";
      } else {
        imageUrl = product.images[0] || "";
      }
      return this.getFullImageUrl(imageUrl);
    },
    // 获取完整的图片URL
    getFullImageUrl(url) {
      if (!url)
        return "";
      if (url.startsWith("http")) {
        return url;
      }
      if (url.startsWith("/static") || url.startsWith("/uploads")) {
        return "https://www.narenqiqige.com/api" + url;
      }
      if (!url.startsWith("/")) {
        return "https://www.narenqiqige.com/api/uploads/" + url;
      }
      return url;
    },
    quickSearch(keyword) {
      this.searchText = keyword;
      this.doSearch();
    },
    // 清空搜索历史
    async clearHistory() {
      try {
        common_vendor.index.showModal({
          title: "提示",
          content: "确定要清空搜索历史吗？",
          success: async (res) => {
            if (res.confirm) {
              await utils_request.apiService.mall.search.clearHistory();
              this.searchHistory = [];
              this.historyTimes = [];
              common_vendor.index.removeStorageSync("searchHistory");
              common_vendor.index.removeStorageSync("historyTimes");
              common_vendor.index.showToast({
                title: "已清空历史",
                icon: "none"
              });
            }
          }
        });
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/mall/search/search.vue:415", "清空搜索历史失败:", error);
        common_vendor.index.showToast({
          title: "清空失败",
          icon: "none"
        });
      }
    },
    goToProduct(product) {
      common_vendor.index.navigateTo({
        url: "/pages/mall/product/detail?id=" + product.id
      });
    },
    // 清除分类筛选
    clearCategoryFilter() {
      this.categoryId = null;
      this.categoryName = "";
      this.doSearch();
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.o((...args) => $options.doSearch && $options.doSearch(...args)),
    b: $data.searchText,
    c: common_vendor.o(($event) => $data.searchText = $event.detail.value),
    d: $data.searchText
  }, $data.searchText ? {
    e: common_vendor.o((...args) => $options.clearSearch && $options.clearSearch(...args))
  } : {}, {
    f: common_vendor.o((...args) => $options.doSearch && $options.doSearch(...args)),
    g: !$data.showResults
  }, !$data.showResults ? common_vendor.e({
    h: $data.searchHistory.length > 0
  }, $data.searchHistory.length > 0 ? {
    i: common_vendor.o((...args) => $options.clearHistory && $options.clearHistory(...args)),
    j: common_vendor.f($options.displaySearchHistory, (item, index, i0) => {
      return {
        a: common_vendor.t(item),
        b: common_vendor.t($data.historyTimes[index]),
        c: "his-" + index,
        d: common_vendor.o(($event) => $options.quickSearch(item), "his-" + index)
      };
    })
  } : {}, {
    k: $data.recommends.length > 0
  }, $data.recommends.length > 0 ? {
    l: common_vendor.f($data.recommends, (item, index, i0) => {
      return {
        a: common_vendor.t(item),
        b: "rec-" + index,
        c: common_vendor.o(($event) => $options.quickSearch(item), "rec-" + index)
      };
    })
  } : {}, {
    m: $data.searchHistory.length === 0 && $data.recommends.length === 0 && !$data.loading
  }, $data.searchHistory.length === 0 && $data.recommends.length === 0 && !$data.loading ? {} : {}) : common_vendor.e({
    n: $data.categoryName
  }, $data.categoryName ? {
    o: common_vendor.t($data.categoryName),
    p: common_vendor.o((...args) => $options.clearCategoryFilter && $options.clearCategoryFilter(...args))
  } : {}, {
    q: common_vendor.t($data.searchResults.length),
    r: common_vendor.f($data.searchResults, (item, index, i0) => {
      return {
        a: item.image,
        b: common_vendor.t(item.name),
        c: common_vendor.t(item.price),
        d: common_vendor.t(item.sold),
        e: index,
        f: common_vendor.o(($event) => $options.goToProduct(item), index)
      };
    }),
    s: $data.searchResults.length > 0
  }, $data.searchResults.length > 0 ? {} : {
    t: common_assets._imports_0$6
  }));
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/pages/mall/search/search.js.map
