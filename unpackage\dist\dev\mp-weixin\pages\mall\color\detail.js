"use strict";
const common_vendor = require("../../../common/vendor.js");
const _sfc_main = {
  // 添加meta信息
  meta: {
    viewportWidth: "device-width",
    viewportInitialScale: 1,
    viewportMaximum: 1,
    viewportScalable: false
  },
  data() {
    return {
      iconPath: "",
      categoryName: "",
      colorName: "",
      colorHex: "",
      dyeCount: 1,
      description: "",
      colorData: [
        // {
        // 	colorClass: 'red',
        // 	weight: 20,
        // 	labels: ['红色', '10g']
        // },
        // {
        // 	colorClass: 'blue',
        // 	weight: 5,
        // 	labels: ['蓝色', '5g']
        // },
        // {
        // 	colorClass: 'black',
        // 	weight: 1,
        // 	labels: ['自然黑', '1g']
        // },
        // {
        // 	colorClass: 'white',
        // 	weight: 10.67,
        // 	labels: ['双氧乳', '10VOL', '10.67g']
        // },
        // {
        // 	colorClass: 'white',
        // 	weight: 5.33,
        // 	labels: ['双氧乳', '40VOL', '5.33g']
        // }
      ],
      developerComponents: null,
      colorComponents: null
    };
  },
  computed: {
    normalizedColorData() {
      const maxWeight = 20;
      const adjustedColorData = this.colorData.map((item) => {
        const adjustedWeight = Math.min(item.weight, maxWeight);
        return {
          ...item,
          adjustedWeight
        };
      });
      const totalWeight = adjustedColorData.reduce((acc, item) => acc + item.adjustedWeight, 0);
      const maxHeight = 100;
      return adjustedColorData.map((item) => {
        const percentage = item.adjustedWeight / totalWeight;
        const height = percentage * maxHeight;
        return {
          ...item,
          height
        };
      });
    }
  },
  onLoad(options) {
    common_vendor.index.__f__("log", "at pages/mall/color/detail.vue:181", "颜色详情页面加载，参数:", options);
    if (options && options.name) {
      this.colorName = decodeURIComponent(options.name || "未知颜色");
      this.categoryName = decodeURIComponent(options.category || "未知分类");
      this.colorHex = decodeURIComponent(options.color || "#000000");
      this.description = decodeURIComponent(options.description || "未设置使用方法");
      this.iconPath = decodeURIComponent(options.iconPath || "未知图片");
      this.colorComponents = decodeURIComponent(options.colorComponents || []);
      this.developerComponents = decodeURIComponent(options.developerComponents || []);
      this.colorComponents = JSON.parse(this.colorComponents);
      this.developerComponents = JSON.parse(this.developerComponents);
      this.colorData = [
        // private BigDecimal gray;           // 灰色0.1 (原字段)
        //    private BigDecimal green;         // 绿色G9.33 (原字段)
        //    private BigDecimal yellow;        // 黄色Y9.33 (原字段)
        //    private BigDecimal orange;        // 橙色6.64 (原字段)
        //    private BigDecimal red;           // 红色0.6 (原字段)
        //    private BigDecimal purple;        // 紫色0.2 (原字段)
        //    private BigDecimal brown;         // 棕色5.52 (原字段)
        //    private BigDecimal blue;         // 蓝色B8.11 (原字段)
        //    private BigDecimal black;        // 基色（自然黑）3.0 (原字段)
        //    private BigDecimal faded_bleached; // 0/00（退色）(原字段)
        {
          colorClass: "gray",
          weight: this.colorComponents.gray,
          labels: ["灰色", `${this.colorComponents.gray}g`]
        },
        {
          colorClass: "green",
          weight: this.colorComponents.green,
          labels: ["绿色", `${this.colorComponents.green}g`]
        },
        {
          colorClass: "yellow",
          weight: this.colorComponents.yellow,
          labels: ["黄色", `${this.colorComponents.yellow}g`]
        },
        {
          colorClass: "orange",
          weight: this.colorComponents.orange,
          labels: ["橙色", `${this.colorComponents.orange}g`]
        },
        {
          colorClass: "red",
          weight: this.colorComponents.red,
          labels: ["红色", `${this.colorComponents.red}g`]
        },
        {
          colorClass: "purple",
          weight: this.colorComponents.purple,
          labels: ["紫色", `${this.colorComponents.purple}g`]
        },
        {
          colorClass: "brown",
          weight: this.colorComponents.brown,
          labels: ["棕色", `${this.colorComponents.brown}g`]
        },
        {
          colorClass: "blue",
          weight: this.colorComponents.blue,
          labels: ["蓝色", `${this.colorComponents.blue}g`]
        },
        {
          colorClass: "black",
          weight: this.colorComponents.black,
          labels: ["黑色", `${this.colorComponents.black}g`]
        },
        {
          colorClass: "faded_bleached",
          weight: this.colorComponents.faded_bleached,
          labels: ["淡化剂", `${this.colorComponents.faded_bleached}g`]
        },
        // private BigDecimal bleached;      // 极浅灰金色12.11 (用bleached字段对应)
        //     private BigDecimal silver;        // 梦幻紫V10.21 (用silver字段对应)
        //     private BigDecimal gold;         // 浅棕色5.0 (用gold字段对应)
        //     private BigDecimal linen;        // 深灰亚麻色7.11 (原字段)
        {
          colorClass: "bleached",
          weight: this.colorComponents.bleached,
          labels: ["极浅灰金色", `${this.colorComponents.bleached}g`]
        },
        {
          colorClass: "silver",
          weight: this.colorComponents.silver,
          labels: ["梦幻紫", `${this.colorComponents.silver}g`]
        },
        {
          colorClass: "gold",
          weight: this.colorComponents.gold,
          labels: ["浅棕色", `${this.colorComponents.gold}g`]
        },
        {
          colorClass: "linen",
          weight: this.colorComponents.linen,
          labels: ["深灰亚麻色", `${this.colorComponents.linen}g`]
        },
        //双氧乳显示
        // private BigDecimal hydrogenPeroxide3Percent;
        //     private BigDecimal hydrogenPeroxide12Percent;
        {
          colorClass: "white",
          weight: this.developerComponents.hydrogenPeroxide3Percent,
          labels: ["双氧乳10VOL", `${this.developerComponents.hydrogenPeroxide3Percent}g`]
        },
        {
          colorClass: "white",
          weight: this.developerComponents.hydrogenPeroxide3Percent,
          labels: ["双氧乳20VOL2:1", `${this.developerComponents.hydrogenPeroxide6Percent}g`]
        },
        {
          colorClass: "white",
          weight: this.developerComponents.hydrogenPeroxide3Percent,
          labels: ["双氧乳30VOL1:2", `${this.developerComponents.hydrogenPeroxide9Percent}g`]
        },
        {
          colorClass: "white",
          weight: this.developerComponents.hydrogenPeroxide12Percent,
          labels: ["双氧乳40VOL", `${this.developerComponents.hydrogenPeroxide12Percent}g`]
        }
      ];
      this.$nextTick(() => {
        this.$forceUpdate();
      });
    } else {
      try {
        const cachedData = common_vendor.index.getStorageSync("selectedColorInfo");
        if (cachedData) {
          this.colorName = cachedData.name || "未知颜色";
          this.categoryName = cachedData.category || "未知分类";
          this.colorHex = cachedData.hex || "#000000";
          common_vendor.index.__f__("log", "at pages/mall/color/detail.vue:323", "从缓存读取数据成功");
        } else {
          common_vendor.index.__f__("error", "at pages/mall/color/detail.vue:325", "无法获取颜色信息");
          common_vendor.index.showToast({
            title: "加载失败",
            icon: "none"
          });
        }
      } catch (e) {
        common_vendor.index.__f__("error", "at pages/mall/color/detail.vue:332", "读取缓存失败:", e);
      }
    }
  },
  methods: {
    goBack() {
      common_vendor.index.navigateBack();
    },
    startDye() {
      try {
        common_vendor.index.setStorageSync("dyeColorInfo", {
          name: this.colorName,
          category: this.categoryName,
          hex: this.colorHex,
          mode: "smart",
          // 默认标记为智能模式
          colorComponents: this.colorComponents,
          developerComponents: this.developerComponents
        });
        common_vendor.index.__f__("log", "at pages/mall/color/detail.vue:351", "保存颜色信息到缓存成功");
      } catch (e) {
        common_vendor.index.__f__("error", "at pages/mall/color/detail.vue:353", "保存颜色信息到缓存失败:", e);
      }
      const url = "/pages/mall/color/confirm?mode=smart";
      common_vendor.index.navigateTo({
        url,
        success: () => {
          common_vendor.index.__f__("log", "at pages/mall/color/detail.vue:363", "跳转到确认调色页面成功");
        },
        fail: (err) => {
          common_vendor.index.__f__("error", "at pages/mall/color/detail.vue:366", "跳转失败:", err);
          common_vendor.index.showModal({
            title: "提示",
            content: "跳转失败，请退出应用后重试",
            showCancel: false
          });
        }
      });
    },
    switchToMasterMode() {
      common_vendor.index.__f__("log", "at pages/mall/color/detail.vue:378", "切换到大师模式");
      try {
        common_vendor.index.setStorageSync("dyeColorInfo", {
          name: this.colorName,
          category: this.categoryName,
          hex: this.colorHex,
          mode: "master"
          // 标记为大师模式
        });
        common_vendor.index.__f__("log", "at pages/mall/color/detail.vue:388", "保存颜色信息到缓存成功，模式：大师模式");
      } catch (e) {
        common_vendor.index.__f__("error", "at pages/mall/color/detail.vue:390", "保存颜色信息到缓存失败:", e);
      }
      const url = "/pages/mall/color/confirm?mode=master";
      common_vendor.index.__f__("log", "at pages/mall/color/detail.vue:395", "尝试跳转到页面:", url);
      common_vendor.index.navigateTo({
        url,
        success: () => {
          common_vendor.index.__f__("log", "at pages/mall/color/detail.vue:400", "跳转到大师模式页面成功");
        },
        fail: (err) => {
          common_vendor.index.__f__("error", "at pages/mall/color/detail.vue:403", "跳转失败:", err);
          common_vendor.index.showModal({
            title: "提示",
            content: "跳转失败，请退出应用后重试",
            showCancel: false
          });
        }
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.t($data.categoryName),
    b: $data.iconPath,
    c: common_vendor.t($data.colorName),
    d: common_vendor.t($data.categoryName),
    e: common_vendor.t($data.dyeCount),
    f: common_vendor.f($options.normalizedColorData, (item, index, i0) => {
      return common_vendor.e({
        a: item.weight
      }, item.weight ? {
        b: common_vendor.n(item.colorClass),
        c: item.height + "px"
      } : {}, {
        d: item.weight
      }, item.weight ? {
        e: common_vendor.f(item.labels, (text, textIndex, i1) => {
          return {
            a: common_vendor.t(text),
            b: textIndex
          };
        })
      } : {}, {
        f: index
      });
    }),
    g: common_vendor.t($data.description),
    h: common_vendor.o((...args) => $options.startDye && $options.startDye(...args))
  };
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-14fa498a"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/pages/mall/color/detail.js.map
