<!-- bind-modal.vue -->
<template>
	<uni-popup ref="popup" type="center" :mask-click="false" class="bind-popup">
		<view class="popup-container">
			<view class="popup-header">
				<text class="popup-title">绑定新设备</text>
				<uni-icons type="closeempty" size="24" color="#999" @click="close"></uni-icons>
			</view>

			<view class="popup-body">
				<!-- 步骤指示器 -->
				<view class="step-indicator">
					<view class="step" :class="{active: currentStep === 1}">
						<text class="step-number">1</text>
						<text class="step-text">输入设备码</text>
					</view>
					<view class="step-line"></view>
					<view class="step" :class="{active: currentStep === 2}">
						<text class="step-number">2</text>
						<text class="step-text">验证设备</text>
					</view>
				</view>

				<!-- 步骤1: 输入设备码 -->
				<view class="step-content" v-show="currentStep === 1">
					<view class="input-group">
						<text class="input-label">设备码</text>
						<input class="input-field" type="text" v-model="deviceCode" placeholder="请输入设备机身码或二维码"
							placeholder-class="placeholder" @confirm="validateDeviceCode" />
						<view class="scan-btn" @click="scanQRCode">
							<uni-icons type="scan" size="20" color="#2979ff"></uni-icons>
							<text>扫码</text>
						</view>
					</view>

					<view class="tips">
						<uni-icons type="info" size="14" color="#999"></uni-icons>
						<text class="tip-text">设备码通常位于设备底部或侧面标签上</text>
					</view>
				</view>

				<!-- 步骤2: 验证设备信息 -->
				<view class="step-content" v-show="currentStep === 2">
					<view class="device-info-card">
						<image class="device-image" :src="deviceInfo.image || '/static/icons/device.png'" mode="aspectFit"></image>
						<view class="device-details">
							<text class="device-name">{{deviceInfo.name || '未知设备'}}</text>
							<text class="device-sn">SN: {{deviceInfo.sn || deviceCode}}</text>
							<text class="device-type">类型: {{deviceInfo.type || '未知类型'}}</text>
						</view>
					</view>

					<view class="form-item">
						<text class="label">绑定到门店</text>
						<picker mode="selector" :range="storeList" range-key="name" @change="onStoreChange">
							<view class="picker">
								{{ selectedStore.name || '请选择门店' }}
								<uni-icons type="arrowdown" size="14" color="#999"></uni-icons>
							</view>
						</picker>
					</view>
				</view>
			</view>

			<view class="popup-footer">
				<button class="footer-btn" :class="{'cancel-btn': currentStep === 1}"
					@click="currentStep === 1 ? close() : currentStep--">
					{{ currentStep === 1 ? '取消' : '上一步' }}
				</button>
				<button class="footer-btn confirm-btn" type="primary"
					@click="currentStep === 1 ? validateDeviceCode() : confirmBind()"
					:disabled="(currentStep === 1 && !deviceCode) || (currentStep === 2 && !selectedStore.id)">
					{{ currentStep === 1 ? '下一步' : '确认绑定' }}
				</button>
			</view>

			<!-- 加载状态 -->
			<view class="loading-mask" v-if="loading">
				<uni-load-more status="loading" :icon-size="16"
					:content-text="{contentrefresh: '处理中...'}"></uni-load-more>
			</view>
		</view>
	</uni-popup>
</template>

<script>
	import {
		apiService
	} from '../../../utils/request'

	export default {
		name: 'BindModal',
		  props: {
			
		  },
		data() {
			return {
				currentStep: 1,
				deviceCode: '',
				deviceInfo: {},
				storeList: [],
				selectedStore: {},
				loading: false
			}
		},
		methods: {
			open() {
				this.$refs.popup.open()
				this.currentStep = 1
				this.deviceCode = ''
				this.deviceInfo = {}
				this.selectedStore = {}
				this.loadStores()
			},

			close() {
				this.$refs.popup.close()
			},

			async loadStores() {
				try {
					const userInfo = uni.getStorageSync('userInfo');
					const response = await apiService.mall.stores.userStores(userInfo.id);
					this.storeList = response.data
				} catch(e) {
					  console.error('获取门店列表失败:', e)
					  uni.showToast({
					    title: '获取门店列表失败',
					    icon: 'none'
					  })
				}
				// try {
				//   const response = await apiService.mall.stores.getMyStores()
				//   this.storeList = response.data
				// } catch (e) {
				//   console.error('获取门店列表失败:', e)
				//   uni.showToast({
				//     title: '获取门店列表失败',
				//     icon: 'none'
				//   })
				// }
			},

			async validateDeviceCode() {
				if (!this.deviceCode) {
					uni.showToast({
						title: '请输入设备码',
						icon: 'none'
					})
					return
				}

				this.loading = true
				try {
					// 调用API验证设备码
					// const response = await apiService.device.validate({ code: this.deviceCode })
					const response = {
						code: 200,
						data: {
							image: '',
							sn: '',
							type: ''
						}
					}

					if (response.code === 200) {
						this.deviceInfo = response.data
						this.currentStep = 2
					} else {
						uni.showToast({
							title: response.message || '设备码无效',
							icon: 'none'
						})
					}
				} catch (e) {
					uni.showToast({
						title: e.message || '验证设备码失败',
						icon: 'none'
					})
				} finally {
					this.loading = false
				}
			},

			scanQRCode() {
				uni.scanCode({
					onlyFromCamera: true,
					scanType: ['qrCode'],
					success: (res) => {
						try {
							console.log('扫码结果:', res.result);
						const device = JSON.parse(res.result);

						if (!device.deviceId) {
							uni.showToast({
								title: '不是有效设备编号',
								icon: 'none',
								duration: 1500
							});
							return;
						}
						this.deviceCode = device.deviceId
						this.validateDeviceCode()
						} catch(e) {
							uni.showToast({
								title: '扫码失败,不是设备支持的类型',
								icon: 'none',
								duration: 1500
							});
						}
						
					},
					fail: (err) => {
						console.error('扫码失败:', err)
						uni.showToast({
							title: '扫码失败',
							icon: 'none'
						})
					}
				})
			},

			onStoreChange(e) {
				const index = e.detail.value
				this.selectedStore = this.storeList[index]
			},

			async confirmBind() {
				if (!this.selectedStore.id) {
					uni.showToast({
						title: '请选择门店',
						icon: 'none'
					})
					return
				}

				this.loading = true
				try {
					const data = {
						deviceCode: this.deviceCode,
						storeId: this.selectedStore.id
					}
					
					const response = await apiService.mall.stores.storeBindDevice(data)

					if (response.code === 200) {
						uni.showToast({
							title: '绑定成功',
							icon: 'success'
						})
						this.$emit('success')
						this.close()
					} else {
						uni.showToast({
							title: response.message || '绑定失败',
							icon: 'none'
						})
					}
				} catch (e) {
					uni.showToast({
						title: e.message || '绑定失败',
						icon: 'none'
					})
				} finally {
					this.loading = false
				}
			}
		}
	}
</script>

<style lang="scss" scoped>
	.bind-popup {
		.popup-container {
			width: 90vw;
			max-width: 500px;
			background-color: #fff;
			border-radius: 12px;
			overflow: hidden;
		}

		.popup-header {
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 16px 20px;
			border-bottom: 1px solid #f5f5f5;

			.popup-title {
				font-size: 18px;
				font-weight: bold;
				color: #333;
			}
		}

		.popup-body {
			padding: 20px;

			.step-indicator {
				display: flex;
				align-items: center;
				justify-content: center;
				margin-bottom: 24px;

				.step {
					display: flex;
					flex-direction: column;
					align-items: center;

					.step-number {
						width: 24px;
						height: 24px;
						line-height: 24px;
						text-align: center;
						border-radius: 50%;
						background-color: #eee;
						color: #999;
						font-size: 14px;
						margin-bottom: 4px;
					}

					.step-text {
						font-size: 12px;
						color: #999;
					}

					&.active {
						.step-number {
							background-color: #2979ff;
							color: #fff;
						}

						.step-text {
							color: #2979ff;
							font-weight: bold;
						}
					}
				}

				.step-line {
					width: 40px;
					height: 1px;
					background-color: #eee;
					margin: 0 8px;
				}
			}

			.step-content {
				.input-group {
					position: relative;
					margin-bottom: 16px;

					.input-label {
						display: block;
						font-size: 14px;
						color: #666;
						margin-bottom: 8px;
					}

					.input-field {
						height: 48px;
						padding: 0 100px 0 15px;
						border: 1px solid #eee;
						border-radius: 8px;
						font-size: 16px;
						background-color: #fafafa;
					}

					.scan-btn {
						position: absolute;
						right: 10px;
						top: 30px;
						display: flex;
						align-items: center;
						padding: 4px 8px;
						background-color: #f0f7ff;
						border-radius: 4px;
						color: #2979ff;
						font-size: 12px;

						text {
							margin-left: 4px;
						}
					}
				}

				.tips {
					display: flex;
					align-items: center;
					margin-top: 8px;

					.tip-text {
						font-size: 12px;
						color: #999;
						margin-left: 4px;
					}
				}

				.device-info-card {
					display: flex;
					padding: 12px;
					margin-bottom: 20px;
					border: 1px solid #eee;
					border-radius: 8px;
					background-color: #fafafa;

					.device-image {
						width: 80px;
						height: 80px;
						margin-right: 12px;
					}

					.device-details {
						flex: 1;
						display: flex;
						flex-direction: column;
						justify-content: center;

						.device-name {
							font-size: 16px;
							font-weight: bold;
							margin-bottom: 4px;
						}

						.device-sn,
						.device-type {
							font-size: 12px;
							color: #666;
							margin-bottom: 2px;
						}
					}
				}

				.form-item {
					margin-bottom: 16px;

					.label {
						display: block;
						font-size: 14px;
						color: #666;
						margin-bottom: 8px;
					}

					.picker {
						padding: 12px 15px;
						border: 1px solid #eee;
						border-radius: 8px;
						font-size: 15px;
						color: #333;
						display: flex;
						justify-content: space-between;
						align-items: center;
					}
				}
			}
		}

		.popup-footer {
			display: flex;
			padding: 12px 20px;
			border-top: 1px solid #f5f5f5;

			.footer-btn {
				flex: 1;
				height: 44px;
				line-height: 44px;
				border-radius: 8px;
				font-size: 16px;
				margin: 0 8px;

				&.cancel-btn {
					background-color: #f8f8f8;
					color: #666;
				}

				&.confirm-btn {
					background-color: #2979ff;
					color: #fff;
				}

				&[disabled] {
					opacity: 0.6;
				}
			}
		}

		.loading-mask {
			position: absolute;
			top: 0;
			left: 0;
			right: 0;
			bottom: 0;
			background-color: rgba(255, 255, 255, 0.7);
			display: flex;
			align-items: center;
			justify-content: center;
			z-index: 10;
		}
	}
</style>