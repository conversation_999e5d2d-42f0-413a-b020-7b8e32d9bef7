/* 水平间距 */
/* 水平间距 */
/* 全局样式修复 - 确保页面占满屏幕宽度 */
page {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
}

/* 通用容器样式 */
.container, .page-container {
  width: 100%;
  min-height: 100vh;
  box-sizing: border-box;
}

/* 修复小程序页面宽度问题 */
uni-page-body {
  width: 100% !important;
}

/* 确保所有view元素正确继承宽度 */
view {
  box-sizing: border-box;
}
.content {
  width: 100%;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: #FFFFFF;
}

/* 色彩分类标签样式 - 卡片式设计 */
.color-category {
  width: 100%;
  white-space: nowrap;
  padding: 20rpx 0;
  background-color: #f8f9fa;
  display: flex;
  align-items: center;
  height: 120rpx;
  box-sizing: border-box;
  position: relative;
  justify-content: center;
}
.category-item {
  display: inline-flex;
  padding: 20rpx 30rpx;
  margin: 0 15rpx;
  font-size: 28rpx;
  color: #666666;
  position: relative;
  transition: all 0.3s ease;
  align-items: center;
  border-radius: 25rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
  border: 2rpx solid transparent;
  min-width: 120rpx;
  justify-content: center;
  flex-shrink: 0;
}
.category-item.active {
  color: #ffffff;
  font-weight: 600;
  background: linear-gradient(135deg, #4285f4 0%, #5a9bff 100%);
  box-shadow: 0 6rpx 20rpx rgba(66, 133, 244, 0.3);
  transform: translateY(-2rpx);
  border: 2rpx solid #4285f4;
}
.category-item:hover {
  transform: translateY(-1rpx);
  box-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.12);
}

/* 移除重复的分类标签样式定义 */
/* 颜色图片区域 - 现代化卡片设计 */
.color-images {
  width: 100%;
  white-space: nowrap;
  background: linear-gradient(180deg, #f8f9fa 0%, #ffffff 100%);
  position: relative;
  z-index: 1;
  min-height: 260rpx;
  display: flex;
  justify-content: flex-start;
  /* 改为flex-start以支持滚动 */
}
.color-images::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2rpx;
  background: linear-gradient(90deg, transparent 0%, #e0e0e0 50%, transparent 100%);
}

/* 确保 scroll-view 不裁剪内容 */
scroll-view.color-images {
  overflow: visible !important;
  -webkit-scroll-snap-type: x proximity;
          scroll-snap-type: x proximity;
  scroll-behavior: smooth;
}
.color-image-item {
  width: calc((100vw - 80rpx) / 5);
  height: 220rpx;
  display: inline-block;
  margin: 0 8rpx;
  margin-top: 16rpx;
  position: relative;
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  transform-origin: center;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 25rpx rgba(0, 0, 0, 0.1);
  background: #ffffff;
  flex-shrink: 0;
  scroll-snap-align: center;
}
.color-image-item::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0) 100%);
  z-index: 1;
  pointer-events: none;
}
.color-image-item .color-name {
  position: absolute;
  top: 50%;
  left: 15rpx;
  right: 15rpx;
  transform: translateY(-50%);
  color: #fff;
  font-size: 26rpx;
  font-weight: 600;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.8);
  line-height: 32rpx;
  z-index: 2;
  text-align: center;
  padding: 10rpx 15rpx;
  border-radius: 15rpx;
  -webkit-backdrop-filter: blur(10rpx);
          backdrop-filter: blur(10rpx);
}
.color-image-item .color-name-split {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2rpx;
}
.color-image-item .color-name-chinese {
  font-size: 28rpx;
  font-weight: 700;
  line-height: 1.2;
  white-space: normal;
  word-wrap: break-word;
  overflow: visible;
  text-align: center;
  max-width: 100%;
}
.color-image-item .color-name-english {
  font-size: 22rpx;
  font-weight: 500;
  line-height: 1.2;
  opacity: 0.9;
  white-space: normal;
  word-wrap: break-word;
  word-break: break-word;
  overflow: visible;
  text-align: center;
  max-width: 100%;
}
.color-image-item .color-name-single {
  font-size: 26rpx;
  font-weight: 600;
  line-height: 1.3;
  white-space: normal;
  word-wrap: break-word;
  overflow: visible;
  text-align: center;
  max-width: 100%;
}
.color-image-item.active {
  width: calc((100vw - 40rpx) / 4.5);
  height: 250rpx;
  box-shadow: 0 15rpx 40rpx rgba(66, 133, 244, 0.25);
  border-radius: 25rpx;
  margin-left: -10rpx;
  margin-right: -10rpx;
  z-index: 10;
  transform: translateY(-10rpx) scale(1.02);
}
.color-image-item.active::after {
  content: "";
  position: absolute;
  top: -5rpx;
  left: -5rpx;
  right: -5rpx;
  bottom: -5rpx;
  background: linear-gradient(135deg, #4285f4, #5a9bff);
  border-radius: 30rpx;
  z-index: -1;
  opacity: 0.3;
}
.color-image-placeholder {
  width: 100%;
  height: 100%;
  border-radius: 20rpx;
  object-fit: cover;
}
.trend-title {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 25rpx 30rpx;
  box-sizing: border-box;
  background: #ffffff;
  border-radius: 25rpx 25rpx 0 0;
  position: relative;
}
.trend-title .trend-text {
  flex: 0 0 60%;
  font-size: 32rpx;
  font-weight: 700;
  color: #2c3e50;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  position: relative;
}
.trend-title .trend-text::after {
  content: "";
  position: absolute;
  bottom: -5rpx;
  left: 0;
  width: 60rpx;
  height: 4rpx;
  background: linear-gradient(90deg, #4285f4, #5a9bff);
  border-radius: 2rpx;
}
.trend-title .filter {
  flex: 0 0 40%;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}
.trend-title .filter .filter-container {
  display: flex;
  align-items: center;
  background: #ffffff;
  padding: 12rpx 20rpx;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  max-width: 100%;
}
.trend-title .filter .filter-container .filter-icon-placeholder {
  width: 32rpx;
  height: 32rpx;
  border-radius: 6rpx;
  margin-right: 10rpx;
}
.trend-title .filter .filter-container .filter-text {
  font-size: 26rpx;
  color: #666;
  font-weight: 500;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.trend-title .filter .filter-container:hover {
  box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.12);
  transform: translateY(-2rpx);
}
.trend-title .subtitle {
  font-size: 24rpx;
  color: #7f8c8d;
  font-weight: 400;
  margin-left: 10rpx;
}

/* 移除重复的trend-title相关样式定义 */
.color-grid {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  padding: 20rpx 15rpx;
  box-sizing: border-box;
  background: #ffffff;
  gap: 10rpx;
  justify-content: flex-start;
  flex: 1;
  min-height: 200rpx;
}
.color-grid-item {
  width: calc((100% - 30rpx) / 4);
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 15rpx 8rpx;
  background: transparent;
  border-radius: 15rpx;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  position: relative;
  overflow: hidden;
  margin-bottom: 5rpx;
}
.color-grid-item:hover {
  transform: translateY(-4rpx);
}
.color-circle-container {
  width: 150rpx;
  height: 150rpx;
  border-radius: 50%;
  overflow: hidden;
  transition: all 0.3s ease;
  position: relative;
  z-index: 1;
  display: block;
  border: none;
  outline: none;
  background: transparent;
}
.color-circle {
  width: 100%;
  height: 100%;
  display: block;
  border: none;
  outline: none;
  object-fit: cover;
}
.color-grid-item:hover .color-circle-container {
  transform: scale(1.05);
}
.color-grid-item .color-name {
  margin-top: 10rpx;
  font-size: 22rpx;
  color: #333;
  text-align: center;
  line-height: 28rpx;
  font-weight: 500;
  z-index: 1;
  position: relative;
  transition: color 0.3s ease;
  max-width: 100%;
  white-space: normal;
  overflow: visible;
  min-height: 56rpx;
}
.color-grid-item .color-name-split {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2rpx;
}
.color-grid-item .color-name-chinese {
  font-size: 24rpx;
  font-weight: 600;
  line-height: 1.2;
  color: inherit;
  white-space: nowrap;
  overflow: visible;
}
.color-grid-item .color-name-english {
  font-size: 20rpx;
  font-weight: 400;
  line-height: 1.2;
  color: #666;
  white-space: nowrap;
  overflow: visible;
}
.color-grid-item .color-name-single {
  font-size: 22rpx;
  font-weight: 500;
  line-height: 1.3;
  color: inherit;
  white-space: normal;
  word-wrap: break-word;
  overflow: visible;
}
.color-grid-item:hover .color-name {
  color: #4285f4;
}
.color-grid-item:hover .color-name-chinese {
  color: #4285f4;
}
.color-grid-item:hover .color-name-english {
  color: #5a9bff;
}